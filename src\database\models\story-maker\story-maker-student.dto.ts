import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID, IsBoolean, IsOptional } from 'class-validator';

/**
 * DTO for listing available story maker games to students
 */
export class StoryMakerGameListItemDto {
  @ApiProperty({
    description: 'The ID of the story',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the story',
    example: 'Adventure in the Forest',
  })
  title: string;

  @ApiProperty({
    description: 'The instruction text of the story',
    example: `Instruction :
1.Write 4 realistic descriptions inspired by the provided image.  
2.Craft a story inspired by the provided image using your imagination.`,
  })
  instruction: string;

  @ApiProperty({
    description: 'URL or path to the picture for the story',
    example: 'https://example.com/images/forest.jpg',
  })
  picture: string;

  @ApiProperty({
    description: 'Whether the student has already played this story',
    example: false,
  })
  is_played: boolean;
}

/**
 * DTO for the response containing a list of story maker games
 */
export class StoryMakerGameListResponseDto {
  @ApiProperty({
    description: 'List of available story maker games',
    type: [StoryMakerGameListItemDto],
  })
  games: StoryMakerGameListItemDto[];

  @ApiProperty({
    description: 'Total number of games available',
    example: 10,
  })
  total_count: number;
}

/**
 * DTO for showing a specific story maker game to play
 */
export class StoryMakerGameDetailDto {
  @ApiProperty({
    description: 'The ID of the story',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the story',
    example: 'Adventure in the Forest',
  })
  title: string;

  @ApiProperty({
    description: 'The instruction text in rich text format',
    example: 'Create a story about a magical forest adventure',
  })
  instruction: string;

  @ApiProperty({
    description: 'URL or path to the picture for the story',
    example: 'https://example.com/images/forest.jpg',
  })
  picture: string;

  @ApiPropertyOptional({
    description: 'The deadline in days for completing the story (if set)',
    example: 2,
  })
  deadline?: number;

  @ApiProperty({
    description: 'Whether the student has already played this story',
    example: false,
  })
  is_played: boolean;
}

/**
 * DTO for student to submit a story
 */
export class StoryMakerSubmissionDto {
  @ApiProperty({
    description: 'The ID of the story maker to submit for',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty({ message: 'Story maker ID is required' })
  story_maker_id: string;

  @ApiProperty({
    description: 'The content of the story in rich text format',
    example: '<p>Once upon a time in a magical forest...</p>',
  })
  @IsString()
  @IsNotEmpty({ message: 'Content is required' })
  content: string;
}

/**
 * DTO for viewing a student's participation summary
 */
export class StoryMakerParticipationResponseDto {
  @ApiProperty({
    description: 'The ID of the participation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The ID of the story maker',
    example: '123e4567-e89b-12d3-a456-************',
  })
  story_maker_id: string;

  @ApiProperty({
    description: 'The title of the story maker',
    example: 'Adventure in the Forest',
  })
  story_maker_title: string;

  @ApiProperty({
    description: 'Whether the participation has been evaluated',
    example: true,
  })
  is_evaluated: boolean;

  @ApiPropertyOptional({
    description: 'The score assigned to the participation (only available after evaluation)',
    example: 45,
  })
  score?: number;

  @ApiProperty({
    description: 'When the student first submitted',
    example: '2023-01-01T00:00:00.000Z',
  })
  first_submitted_at: Date;

  @ApiPropertyOptional({
    description: 'When the participation was evaluated (only available after evaluation)',
    example: '2023-01-02T00:00:00.000Z',
  })
  evaluated_at?: Date;

  @ApiPropertyOptional({
    description: 'The ID of the evaluator (only available after evaluation)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  evaluated_by?: string;

  @ApiPropertyOptional({
    description: 'The name of the evaluator (only available after evaluation)',
    example: 'John Doe',
  })
  evaluator_name?: string;

  @ApiPropertyOptional({
    description: 'The profile picture of the evaluator (only available after evaluation)',
    example: 'https://example.com/images/profile.jpg',
  })
  evaluator_profile_picture?: string;

  @ApiProperty({
    description: 'The latest submission content',
    example: '<p>Once upon a time in a magical forest...</p>',
  })
  latest_content: string;

  @ApiPropertyOptional({
    description: 'The latest corrections (if evaluated)',
    example: '<p>Once upon a time in a magical forest, there lived a wise old owl...</p>',
  })
  latest_corrections?: string;

  @ApiPropertyOptional({
    description: 'The latest feedback (if evaluated)',
    example: 'Great use of descriptive language and creative storyline.',
  })
  latest_feedback?: string;

  @ApiProperty({
    description: 'When the latest submission was made',
    example: '2023-01-01T00:00:00.000Z',
  })
  latest_submitted_at: Date;

  @ApiProperty({
    description: 'Total number of submissions',
    example: 3,
  })
  submission_count: number;
}
