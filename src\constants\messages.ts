export const Messages = {
  // Success messages
  OPERATION_SUCCESS: 'Operation completed successfully',
  ENTITY_CREATED: 'Entity created successfully',
  ENTITY_UPDATED: 'Entity updated successfully',
  ENTITY_DELETED: 'Entity deleted successfully',

  // Error messages
  ENTITY_NOT_FOUND: 'Entity not found',
  UNAUTHORIZED: 'You are not authorized to perform this action',
  FOR<PERSON>DDEN: 'You do not have permission to access this resource',
  VALIDATION_ERROR: 'Validation failed',
  INTERNAL_SERVER_ERROR: 'Internal server error',

  // Authentication messages
  AUTH_SUCCESS: 'Authentication successful',
  AUTH_FAILED: 'Authentication failed',
  INVALID_CREDENTIALS: 'Invalid credentials',
  ACCOUNT_NOT_CONFIRMED: 'Account not confirmed. Please verify your email first',
  ACCOUNT_INACTIVE: 'Your account is inactive. Please contact an administrator',
  INVALID_ROLE: 'You do not have the required role to access this resource',

  // User messages
  USER_CREATED: 'User created successfully',
  USER_UPDATED: 'User updated successfully',
  USER_DELETED: 'User deleted successfully',
  USER_NOT_FOUND: 'User not found',

  // Plan messages
  PLAN_CREATED: 'Plan created successfully',
  PLAN_UPDATED: 'Plan updated successfully',
  PLAN_DELETED: 'Plan deleted successfully',
  PLAN_NOT_FOUND: 'Plan not found',
  PLAN_SUBSCRIPTION_SUCCESS: 'Plan subscription successful',
  PLAN_SUBSCRIPTION_FAILED: 'Plan subscription failed',

  // Email messages
  EMAIL_SENT: 'Email sent successfully',
  EMAIL_FAILED: 'Failed to send email',

  // OTP messages
  OTP_SENT: 'OTP sent successfully',
  OTP_VERIFIED: 'OTP verified successfully',
  OTP_INVALID: 'Invalid OTP',
  OTP_EXPIRED: 'OTP has expired',
};
