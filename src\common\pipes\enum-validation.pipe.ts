import { PipeTransform, Injectable, ArgumentMetadata, BadRequestException } from '@nestjs/common';

/**
 * A pipe that validates that a value is a valid enum value
 */
@Injectable()
export class EnumValidationPipe implements PipeTransform {
  constructor(private readonly enumType: any) {}

  transform(value: any, metadata: ArgumentMetadata) {
    // If no value is provided, return undefined (for optional parameters)
    if (value === undefined || value === null || value === '') {
      return undefined;
    }

    // Check if the value is a valid enum value
    const enumValues = Object.values(this.enumType);
    if (!enumValues.includes(value)) {
      const validValues = enumValues.join(', ');
      throw new BadRequestException(`Invalid value for ${metadata.data}. Valid values are: ${validValues}`);
    }

    return value;
  }
}
