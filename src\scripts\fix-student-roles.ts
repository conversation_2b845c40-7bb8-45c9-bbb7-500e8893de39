import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { UsersService } from '../modules/users/users.service';
import { Repository } from 'typeorm';
import { User, UserType } from '../database/entities/user.entity';
import { Role } from '../database/entities/role.entity';
import { UserRole } from '../database/entities/user-role.entity';
import { getRepositoryToken } from '@nestjs/typeorm';

async function fixStudentRoles() {
  const app = await NestFactory.createApplicationContext(AppModule);

  const userRepository = app.get<Repository<User>>(getRepositoryToken(User));
  const roleRepository = app.get<Repository<Role>>(getRepositoryToken(Role));
  const userRoleRepository = app.get<Repository<UserRole>>(getRepositoryToken(UserRole));

  console.log('🔍 Checking student roles...');

  try {
    // Find all students
    const students = await userRepository.find({
      where: { type: UserType.STUDENT },
      relations: ['userRoles', 'userRoles.role'],
    });

    console.log(`📊 Found ${students.length} students`);

    // Check which students don't have roles
    const studentsWithoutRoles = students.filter((student) => !student.userRoles || student.userRoles.length === 0);

    console.log(`❌ Students without roles: ${studentsWithoutRoles.length}`);

    if (studentsWithoutRoles.length > 0) {
      console.log('Students without roles:');
      studentsWithoutRoles.forEach((student) => {
        console.log(`  - ${student.name} (${student.email})`);
      });
    }

    // Find or create student role
    let studentRole = await roleRepository.findOne({ where: { name: 'student' } });

    if (!studentRole) {
      console.log('📝 Creating student role...');
      studentRole = roleRepository.create({ name: 'student' });
      studentRole = await roleRepository.save(studentRole);
      console.log('✅ Student role created');
    } else {
      console.log('✅ Student role exists');
    }

    // Assign student role to students without roles
    let fixedCount = 0;
    for (const student of studentsWithoutRoles) {
      try {
        // Check if role assignment already exists (double-check)
        const existingUserRole = await userRoleRepository.findOne({
          where: {
            userId: student.id,
            roleId: studentRole.id,
          },
        });

        if (!existingUserRole) {
          const userRole = userRoleRepository.create({
            userId: student.id,
            roleId: studentRole.id,
          });

          await userRoleRepository.save(userRole);
          console.log(`✅ Assigned student role to ${student.name}`);
          fixedCount++;
        } else {
          console.log(`ℹ️  ${student.name} already has student role (found in double-check)`);
        }
      } catch (error) {
        console.error(`❌ Error assigning role to ${student.name}: ${error.message}`);
      }
    }

    console.log(`\n🎉 Fixed ${fixedCount} student role assignments`);

    // Final verification
    console.log('\n🔍 Final verification...');
    const studentsAfterFix = await userRepository.find({
      where: { type: UserType.STUDENT },
      relations: ['userRoles', 'userRoles.role'],
    });

    const stillWithoutRoles = studentsAfterFix.filter((student) => !student.userRoles || student.userRoles.length === 0);

    console.log(`📊 Students still without roles: ${stillWithoutRoles.length}`);

    if (stillWithoutRoles.length === 0) {
      console.log('🎉 All students now have roles!');
    } else {
      console.log("❌ Some students still don't have roles:");
      stillWithoutRoles.forEach((student) => {
        console.log(`  - ${student.name} (${student.email})`);
      });
    }
  } catch (error) {
    console.error('❌ Error fixing student roles:', error);
  } finally {
    await app.close();
  }
}

// Run the script
fixStudentRoles().catch(console.error);
