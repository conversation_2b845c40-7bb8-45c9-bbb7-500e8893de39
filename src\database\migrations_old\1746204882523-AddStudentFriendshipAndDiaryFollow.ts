import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStudentFriendshipAndDiaryFollow1746204882523 implements MigrationInterface {
  name = 'AddStudentFriendshipAndDiaryFollow1746204882523';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."student_friendship_status_enum" AS ENUM('pending', 'accepted', 'rejected')`);
    await queryRunner.query(
      `CREATE TABLE "student_friendship" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "requester_id" uuid NOT NULL, "requested_id" uuid NOT NULL, "status" "public"."student_friendship_status_enum" NOT NULL DEFAULT 'pending', "request_message" character varying, "can_view_diary" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_8168e3debf4815a3c91eb495a79" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE TYPE "public"."diary_follow_request_status_enum" AS ENUM('pending', 'accepted', 'rejected')`);
    await queryRunner.query(
      `CREATE TABLE "diary_follow_request" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "requester_id" uuid NOT NULL, "diary_owner_id" uuid NOT NULL, "friendship_id" uuid, "status" "public"."diary_follow_request_status_enum" NOT NULL DEFAULT 'pending', "request_message" character varying, CONSTRAINT "PK_93cc07308159a035772a88790cd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "student_friendship" ADD CONSTRAINT "FK_ef52ab02e59161e08011b4dd02d" FOREIGN KEY ("requester_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "student_friendship" ADD CONSTRAINT "FK_016c1bc57046de066da7c314c51" FOREIGN KEY ("requested_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "diary_follow_request" ADD CONSTRAINT "FK_35520e53515c3f1cf09f1075829" FOREIGN KEY ("requester_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "diary_follow_request" ADD CONSTRAINT "FK_14c9a7804964a36063197f72bca" FOREIGN KEY ("diary_owner_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "diary_follow_request" ADD CONSTRAINT "FK_1f037564abc4dc109fec61786c8" FOREIGN KEY ("friendship_id") REFERENCES "student_friendship"("id") ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "diary_follow_request" DROP CONSTRAINT "FK_1f037564abc4dc109fec61786c8"`);
    await queryRunner.query(`ALTER TABLE "diary_follow_request" DROP CONSTRAINT "FK_14c9a7804964a36063197f72bca"`);
    await queryRunner.query(`ALTER TABLE "diary_follow_request" DROP CONSTRAINT "FK_35520e53515c3f1cf09f1075829"`);
    await queryRunner.query(`ALTER TABLE "student_friendship" DROP CONSTRAINT "FK_016c1bc57046de066da7c314c51"`);
    await queryRunner.query(`ALTER TABLE "student_friendship" DROP CONSTRAINT "FK_ef52ab02e59161e08011b4dd02d"`);
    await queryRunner.query(`DROP TABLE "diary_follow_request"`);
    await queryRunner.query(`DROP TYPE "public"."diary_follow_request_status_enum"`);
    await queryRunner.query(`DROP TABLE "student_friendship"`);
    await queryRunner.query(`DROP TYPE "public"."student_friendship_status_enum"`);
  }
}
