import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAward<PERSON>innerNotificationType1748200000000 implements MigrationInterface {
  name = 'AddAwardWinnerNotificationType1748200000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the new AWARD_WINNER notification type to the enum
    await queryRunner.query(`
      ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'award_winner';
    `);

    // Also add it to the user notification preference enum
    await queryRunner.query(`
      ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'award_winner';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: PostgreSQL doesn't support removing enum values directly
    // This would require recreating the enum type, which is complex
    // For now, we'll leave the enum value in place
    console.log('Rollback note: AWARD_WINNER enum value will remain in the database');
  }
}
