import { Controller, Get, Post, Body, Param, Put, Query, UseGuards } from '@nestjs/common';
import { Api<PERSON><PERSON>s, ApiBearerAuth, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { TutorGuard } from '../../common/guards/tutor.guard';
import { TutorMatchingService } from './tutor-matching.service';
import {
  AssignTutorDto,
  UpdateTutorAssignmentDto,
  TutorAssignmentResponseDto,
  StudentTutorDto,
  TutorStudentDto,
  TutorWorkloadDto,
  AssignmentFilterDto,
  AutoAssignTutorsDto,
  StudentTutorFilterDto,
} from '../../database/models/tutor-matching.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiOkResponseWithArrayType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { GetUser } from '../../common/decorators/get-user.decorator';
import { User } from '../../database/entities/user.entity';
import { MappingStatus } from '../../database/entities/student-tutor-mapping.entity';

@ApiTags('tutor-matching')
@Controller('tutor-matching')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class TutorMatchingController {
  constructor(private readonly tutorMatchingService: TutorMatchingService) {}

  @Post('assign')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Assign a tutor to a student' })
  @ApiOkResponseWithType(TutorAssignmentResponseDto, 'Tutor assigned successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(404, 'Student or tutor not found')
  @ApiErrorResponse(409, 'Student already has a tutor for this module')
  @ApiErrorResponse(500, 'Internal server error')
  async assignTutor(@Body() assignTutorDto: AssignTutorDto): Promise<ApiResponse<TutorAssignmentResponseDto>> {
    const assignment = await this.tutorMatchingService.assignTutor(assignTutorDto);
    return ApiResponse.success(assignment, 'Tutor assigned successfully');
  }

  @Put('assignments/:id')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Update a tutor assignment' })
  @ApiOkResponseWithType(TutorAssignmentResponseDto, 'Assignment updated successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(404, 'Assignment not found')
  @ApiErrorResponse(500, 'Internal server error')
  async updateAssignment(@Param('id') id: string, @Body() updateDto: UpdateTutorAssignmentDto): Promise<ApiResponse<TutorAssignmentResponseDto>> {
    const assignment = await this.tutorMatchingService.updateAssignment(id, updateDto);
    return ApiResponse.success(assignment, 'Assignment updated successfully');
  }

  @Get('assignments')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get all tutor assignments' })
  @ApiQuery({
    name: 'studentName',
    required: false,
    type: String,
    description: 'Filter by student name',
  })
  @ApiQuery({
    name: 'tutorName',
    required: false,
    type: String,
    description: 'Filter by tutor name',
  })
  @ApiQuery({
    name: 'moduleId',
    required: false,
    type: String,
    description: 'Filter by module ID',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['active', 'inactive'],
    description: 'Filter by status',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction',
  })
  @ApiOkResponseWithPagedListType(TutorAssignmentResponseDto, 'Assignments retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getAllAssignments(
    @Query('studentName') studentName?: string,
    @Query('tutorName') tutorName?: string,
    @Query('planFeatureId') planFeatureId?: string,
    @Query('status') status?: MappingStatus,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
  ): Promise<ApiResponse<PagedListDto<TutorAssignmentResponseDto>>> {
    // Create filter DTO
    const filterDto: AssignmentFilterDto = {
      studentName,
      tutorName,
      planFeatureId,
      status,
    };

    // Create pagination DTO
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC',
    };

    const assignments = await this.tutorMatchingService.getAllAssignments(filterDto, paginationDto);
    return ApiResponse.success(assignments, 'Assignments retrieved successfully');
  }

  @Get('assignments/:id')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get a specific assignment' })
  @ApiOkResponseWithType(TutorAssignmentResponseDto, 'Assignment retrieved successfully')
  @ApiErrorResponse(404, 'Assignment not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getAssignmentById(@Param('id') id: string): Promise<ApiResponse<TutorAssignmentResponseDto>> {
    const assignment = await this.tutorMatchingService.getAssignmentById(id);
    return ApiResponse.success(assignment, 'Assignment retrieved successfully');
  }

  @Get('student/:studentId/tutors')
  @ApiOperation({ summary: 'Get all tutors assigned to a student (deprecated, use /student/:studentId/tutors/filter instead)' })
  @ApiOkResponseWithArrayType(StudentTutorDto, 'Tutors retrieved successfully')
  @ApiErrorResponse(404, 'Student not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getStudentTutors(@Param('studentId') studentId: string): Promise<ApiResponse<StudentTutorDto[]>> {
    const result = await this.tutorMatchingService.getStudentTutors(studentId);
    return ApiResponse.success(result.items, 'Tutors retrieved successfully');
  }

  @Get('student/:studentId/tutors/filter')
  @ApiOperation({ summary: 'Get all tutors assigned to a student with filtering and pagination' })
  @ApiQuery({
    name: 'tutorName',
    required: false,
    type: String,
    description: 'Filter by tutor name',
  })
  @ApiQuery({
    name: 'planFeatureId',
    required: false,
    type: String,
    description: 'Filter by module ID',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['active', 'inactive'],
    description: 'Filter by status',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by (name, moduleName, status, assignedDate)',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction',
  })
  @ApiOkResponseWithPagedListType(StudentTutorDto, 'Tutors retrieved successfully')
  @ApiErrorResponse(404, 'Student not found')
  @ApiErrorResponse(500, 'Internal server error')
  async filterStudentTutors(
    @Param('studentId') studentId: string,
    @Query('tutorName') tutorName?: string,
    @Query('planFeatureId') planFeatureId?: string,
    @Query('status') status?: MappingStatus,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
  ): Promise<ApiResponse<PagedListDto<StudentTutorDto>>> {
    // Create filter DTO
    const filterDto: StudentTutorFilterDto = {
      tutorName,
      planFeatureId,
      status,
    };

    // Create pagination DTO
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC',
    };

    const tutors = await this.tutorMatchingService.getStudentTutors(studentId, filterDto, paginationDto);
    return ApiResponse.success(tutors, 'Tutors retrieved successfully');
  }

  @Get('tutor/students')
  @UseGuards(TutorGuard)
  @ApiOperation({ summary: 'Get all students assigned to the current tutor' })
  @ApiOkResponseWithArrayType(TutorStudentDto, 'Students retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getCurrentTutorStudents(@GetUser() user: User): Promise<ApiResponse<TutorStudentDto[]>> {
    const students = await this.tutorMatchingService.getTutorStudents(user.id);
    return ApiResponse.success(students, 'Students retrieved successfully');
  }

  @Get('tutor/:tutorId/students')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get all students assigned to a specific tutor' })
  @ApiOkResponseWithArrayType(TutorStudentDto, 'Students retrieved successfully')
  @ApiErrorResponse(404, 'Tutor not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getTutorStudents(@Param('tutorId') tutorId: string): Promise<ApiResponse<TutorStudentDto[]>> {
    const students = await this.tutorMatchingService.getTutorStudents(tutorId);
    return ApiResponse.success(students, 'Students retrieved successfully');
  }

  @Get('workloads')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Get tutor workload statistics' })
  @ApiOkResponseWithArrayType(TutorWorkloadDto, 'Workloads retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getTutorWorkloads(): Promise<ApiResponse<TutorWorkloadDto[]>> {
    const workloads = await this.tutorMatchingService.getTutorWorkloads();
    return ApiResponse.success(workloads, 'Workloads retrieved successfully');
  }

  @Post('auto-assign')
  @UseGuards(AdminGuard)
  @ApiOperation({ summary: 'Auto-assign tutors to students' })
  @ApiOkResponseWithArrayType(TutorAssignmentResponseDto, 'Tutors auto-assigned successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(404, 'Module not found')
  @ApiErrorResponse(500, 'Internal server error')
  async autoAssignTutors(@Body() autoAssignDto: AutoAssignTutorsDto): Promise<ApiResponse<TutorAssignmentResponseDto[]>> {
    const assignments = await this.tutorMatchingService.autoAssignTutors(autoAssignDto);
    return ApiResponse.success(assignments, 'Tutors auto-assigned successfully');
  }
}
