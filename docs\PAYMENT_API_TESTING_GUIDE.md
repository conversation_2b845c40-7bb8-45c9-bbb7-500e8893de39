# Payment API Testing Guide - Swagger & API Focus

## Table of Contents
1. [Quick Start with Swagger](#quick-start-with-swagger)
2. [Authentication Setup](#authentication-setup)
3. [Frontend Integration Flow](#frontend-integration-flow)
4. [Payment API Testing Scenarios](#payment-api-testing-scenarios)
5. [Shop Integration Testing](#shop-integration-testing)
6. [Error Testing & Validation](#error-testing--validation)
7. [Swagger Testing Best Practices](#swagger-testing-best-practices)
8. [API Testing Checklist](#api-testing-checklist)

## Quick Start with Swagger

The HEC API includes comprehensive Swagger documentation that makes testing payment APIs straightforward and interactive.

<!-- ### 🚀 Accessing Swagger UI

1. **Start the HEC Backend Server**
   ```bash
   npm run start:dev
   # Server runs on http://**************:3010 by default
   ```

2. **Open Swagger Documentation**
   ```
   http://**************:3010/api-docs
   ```

3. **Key Swagger Features Available**
   - ✅ Interactive API testing with "Try it out" buttons
   - ✅ Built-in authentication with JWT tokens
   - ✅ Real-time request/response examples
   - ✅ Automatic request validation
   - ✅ Organized by API tags (Payment, Shop, Auth, etc.)

### 📋 Available API Tags in Swagger
- **`Payment`** - Payment gateway operations
- **`shop`** - Shop items, categories, and cart management
- **`auth`** - Authentication and authorization
- **`plans`** - Subscription plan management
- **`diary`** - Diary and mission management

### 🔧 Swagger Configuration
The API is configured with:
- **JWT Bearer Authentication** - Click "Authorize" button in Swagger UI
- **Multiple Server Environments** - Development, Local, Staging
- **Comprehensive Error Documentation** - All error codes and responses
- **Request/Response Examples** - Real data examples for all endpoints -->

<!-- ## Environment Setup -->

### 🌍 Available Environments

Based on the current configuration, you can test against:

1. **Development Server** - `http://**************:3010` (Primary)
2. **Local Development** - `http://localhost:3010`
3. **Local Staging** - `http://localhost:3012`

<!-- ### 🔑 Environment Variables (for reference)
```env
# The system is already configured with these
API_URL=http://**************:3010
API_URL_DEV_LOCAL=http://localhost:3010
API_URL_LOCAL=http://localhost:3012
``` -->

## Authentication Setup

### 🔐 Step 1: Login via Swagger UI

1. **Navigate to Auth Section** in Swagger UI
2. **Find `POST /auth/login`** endpoint
3. **Click "Try it out"**
4. **Use Test Credentials:**

```json
{
  "userId": "test_student",
  "password": "test_password",
  "selectedRole": "student",
  "rememberMe": false,
  "returnUrl": "/dashboard"
}
```

5. **Execute the Request**
6. **Copy the `access_token` from Response**

### 🔑 Step 2: Authorize in Swagger

1. **Click the "Authorize" button** (🔒 icon) at the top of Swagger UI
2. **Enter:** `Bearer YOUR_ACCESS_TOKEN`
3. **Click "Authorize"**
4. **Click "Close"**

✅ **You're now authenticated!** All subsequent API calls will include your JWT token.

### 📝 Alternative: Manual Authentication

If you prefer using external tools like Postman:

```http
POST http://**************:3010/auth/login
Content-Type: application/json

{
  "userId": "test_student",
  "password": "test_password",
  "selectedRole": "student"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "uuid-here",
      "userId": "test_student",
      "email": "<EMAIL>",
      "role": "student"
    },
    "returnUrl": "/dashboard"
  }
}
```

**Then use in headers:**
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Payment API Testing Scenarios

### 🛒 Scenario 1: Shop Item Purchase Flow (via Swagger)

#### Step 1: Browse Available Shop Items

1. **Go to `shop` section** in Swagger UI
2. **Find `GET /shop/items/available`**
3. **Click "Try it out"**
4. **Execute** to see available items
5. **Note an item ID** for testing

#### Step 2: Add Item to Cart

1. **Find `POST /shop/cart/add`** in Swagger
2. **Click "Try it out"**
3. **Enter request body:**

```json
{
  "itemId": "your-item-id-here",
  "quantity": 1
}
```

4. **Execute** and verify cart creation

#### Step 3: Checkout with Payment

1. **Find `POST /shop/cart/checkout`** in Swagger
2. **Click "Try it out"**
3. **Enter request body:**

```json
{
  "paymentMethod": "card",
  "returnUrl": "http://**************:3010/payment/success",
  "cancelUrl": "http://**************:3010/payment/cancel"
}
```

4. **Execute** and note the `transactionId` from response

#### Step 4: Check Payment Status

1. **Go to `Payment` section** in Swagger
2. **Find `GET /payment/status/{transactionId}`**
3. **Enter the transaction ID** from previous step
4. **Execute** to check payment status

**Expected Response Structure:**
```json
{
  "success": true,
  "data": {
    "transactionId": "TXN-...",
    "status": "pending",
    "amount": 5000,
    "currency": "KRW",
    "paymentMethod": "card"
  }
}
```

### 📋 Scenario 2: Plan Subscription Flow (via Swagger)

#### Step 1: Browse Available Plans

1. **Go to `plans` section** in Swagger UI
2. **Find `GET /plans`**
3. **Execute** to see available subscription plans
4. **Note a plan ID** for testing

#### Step 2: Subscribe to Plan

1. **Find `POST /plans/subscribe`** in Swagger
2. **Click "Try it out"**
3. **Enter request body:**

```json
{
  "planId": "your-plan-id-here",
  "paymentMethod": "card",
  "buyerInfo": {
    "name": "Test Student",
    "email": "<EMAIL>",
    "phone": "010-1234-5678"
  },
  "returnUrl": "http://**************:3010/payment/success",
  "cancelUrl": "http://**************:3010/payment/cancel"
}
```

4. **Execute** and note the `transactionId`

**Expected Response Structure:**
```json
{
  "success": true,
  "data": {
    "subscriptionId": "SUB-...",
    "transactionId": "TXN-...",
    "paymentUrl": "https://stg-spl.kcp.co.kr/...",
    "planName": "Premium Plan",
    "amount": 29900
  }
}
```

### 💳 Scenario 3: Direct Payment Initiation (via Swagger)

#### Step 1: Direct Payment Initiation

1. **Go to `Payment` section** in Swagger UI
2. **Find `POST /payment/initiate`**
3. **Click "Try it out"**
4. **Enter request body:**

```json
{
  "orderId": "TEST-ORDER-12345",
  "amount": 10000,
  "currency": "KRW",
  "productName": "Test Product",
  "buyerName": "Test Student",
  "buyerEmail": "<EMAIL>",
  "buyerPhone": "010-1234-5678",
  "paymentMethod": "kcp_card",
  "purchaseType": "shop_item",
  "referenceId": "test-reference-12345",
  "returnUrl": "http://**************:3010/payment/success",
  "cancelUrl": "http://**************:3010/payment/cancel"
}
```

5. **Execute** and verify payment URL generation

**Expected Response Structure:**
```json
{
  "success": true,
  "data": {
    "transactionId": "TXN-1750146251630-T61AZO",
    "paymentUrl": "http://**************:3011/payment/kcp?site_cd=T0000&ordr_idxx=TEST-ORDER-12345&...",
    "redirectUrl": "http://**************:3011/payment/kcp?...",
    "message": "Payment initiated successfully",
    "expiresAt": "2024-12-17T08:27:37.531Z",
    "kcpFormData": {
      "site_cd": "T0000",
      "site_name": "HEC Payment",
      "ordr_idxx": "TEST-ORDER-12345",
      "good_name": "Test Product",
      "good_mny": "10000",
      "buyr_name": "Test Student",
      "buyr_tel2": "010-1234-5678",
      "buyr_mail": "<EMAIL>",
      "pay_method": "100000000000",
      "quotaopt": "12",
      "tno": "TXN-1750146251644",
      "ordr_chk": "generated_hash",
      "kcp_sign_data": "generated_signature"
    }
  }
}
```

### 🔄 Scenario 4: KCP Payment Processing (UPDATED)

#### Step 1: Simulate KCP Payment Completion

After a user completes payment through KCP's frontend integration, the KCP form submits to the processing endpoint:

1. **Go to `Payment` section** in Swagger UI
2. **Find `POST /payment/kcp/process`**
3. **Click "Try it out"**
4. **Enter request body (simulating successful payment form submission):**

```json
{
  "res_cd": "0000",
  "res_msg": "SUCCESS",
  "tno": "TXN-1750146251644",
  "ordr_idxx": "TEST-ORDER-12345",
  "good_mny": "10000",
  "pay_method": "100000000000",
  "enc_info": "",
  "enc_data": "",
  "tran_cd": "00100000"
}
```

5. **Execute** to process payment completion

**Expected Response Structure:**
```json
{
  "success": true,
  "redirect": "http://**************:3010/payment/success?orderId=TEST-ORDER-12345&transactionId=TXN-456",
  "message": "Payment completed successfully",
  "data": {
    "transactionId": "TXN-456",
    "orderId": "TEST-ORDER-12345",
    "kcpTransactionId": "TXN-1750146251644"
  }
}
```

#### Step 2: Test Payment Failure Processing

1. **Use the same endpoint** `POST /payment/kcp/process`
2. **Enter request body (simulating failed payment):**

```json
{
  "res_cd": "9999",
  "res_msg": "Payment failed",
  "tno": "TXN-1750146251644",
  "ordr_idxx": "TEST-ORDER-12345",
  "good_mny": "10000",
  "pay_method": "100000000000",
  "enc_info": "",
  "enc_data": "",
  "tran_cd": "00100000"
}
```

3. **Execute** to verify failure handling

### 🔄 Scenario 5: Transaction Management (via Swagger)

#### Step 1: Get User Transactions

1. **Go to `Payment` section** in Swagger UI
2. **Find `GET /payment/transactions`**
3. **Click "Try it out"**
4. **Set query parameters:**
   - `page`: 1
   - `limit`: 10
   - `status`: completed (optional)
5. **Execute** to see transaction history

#### Step 2: Process Refund

1. **Find `POST /payment/refund`** in Swagger
2. **Click "Try it out"**
3. **Enter request body:**

```json
{
  "transactionId": "your-transaction-id-here",
  "amount": 10000,
  "reason": "Customer requested refund"
}
```

4. **Execute** to process refund

### 🔗 Scenario 6: Webhook Testing (External Tool Required)

> **Note:** Webhook testing requires external tools like Postman or curl since webhooks are called by KCP, not through Swagger UI.

#### Using curl:
```bash
curl -X POST http://**************:3010/payment/webhook/kcp \
  -H "Content-Type: application/json" \
  -H "X-KCP-Signature: sha256=calculated_hmac_signature" \
  -d '{
    "res_cd": "0000",
    "res_msg": "SUCCESS",
    "tno": "TXN-20240101-001",
    "amount": "10000",
    "pnt_issue": "0",
    "trace_no": "TRACE-123456",
    "app_time": "20240101120000",
    "app_no": "APP-123456",
    "card_cd": "01",
    "card_name": "Test Card"
  }'
```

## KCP Frontend Integration Testing

### 🎯 Testing the New KCP Integration Pattern

Our KCP integration now follows the **official KCP JavaScript SDK pattern**. Here's how to test the complete flow:

#### 1. Backend API Testing (via Swagger)

**Test the checkout API that generates KCP form data:**

1. **Complete a checkout** using any of the scenarios above
2. **Note the response structure** - it now includes `kcpFormData`
3. **Verify the `paymentUrl`** points to your frontend (`/payment/kcp`)
4. **Check `kcpFormData`** contains all required KCP fields

#### 2. Frontend Integration Testing (Manual)

**Test the complete payment flow:**

1. **Visit the `paymentUrl`** from the checkout response
2. **Verify the payment page loads** with KCP form data
3. **Check KCP SDK loads** (`kcp_spay_hub.js`)
4. **Test payment button** triggers KCP payment window
5. **Complete test payment** in KCP staging environment
6. **Verify callback** calls the verification endpoint
7. **Check final redirect** to success/failure page

#### 3. Integration Flow Testing

```
1. Checkout API (Swagger) → 2. Frontend Payment Page → 3. KCP Payment → 4. Verification API (Swagger)
```

**Step-by-step testing:**
1. **Use Swagger** to call checkout API
2. **Copy `paymentUrl`** from response
3. **Open URL in browser** to test frontend
4. **Use Swagger** to test verification endpoint

### 🔧 KCP Integration Test Checklist

#### Backend API Tests (Swagger)
- [ ] **Checkout APIs** return `kcpFormData` and frontend `paymentUrl`
- [ ] **Processing endpoint** (`POST /payment/kcp/process`) works correctly
- [ ] **Payment status** updates after processing
- [ ] **Redirect URLs** generated correctly for success/failure
- [ ] **Error handling** for invalid KCP responses

#### Frontend Integration Tests (Manual)
- [ ] **Payment page** loads with correct form data
- [ ] **KCP SDK** loads successfully
- [ ] **Payment methods** display correctly
- [ ] **Payment button** triggers KCP payment window
- [ ] **Success callback** submits form to processing endpoint
- [ ] **Failure callback** handles errors gracefully

#### End-to-End Tests
- [ ] **Complete flow** from checkout to payment completion
- [ ] **Database updates** reflect payment status changes
- [ ] **Purchase activation** works for shop items and plans
- [ ] **Error scenarios** are handled properly

## Shop Integration Testing

### 🛍️ Testing Shop Features via Swagger

#### 1. Browse Shop Categories

1. **Go to `shop` section** in Swagger UI
2. **Find `GET /shop/categories`**
3. **Execute** to see available categories

#### 2. Browse Shop Items

1. **Find `GET /shop/items`**
2. **Try different filters:**
   - `categoryId`: Filter by category
   - `type`: `free` or `in_app_purchase`
   - `featuredOnly`: `true` for featured items
3. **Execute** to see filtered results

#### 3. Check Item Availability

1. **Find `GET /shop/items/available`**
2. **Execute** to see items you can purchase
3. **Note:** This excludes items you already own

#### 4. Test Shopping Cart

1. **Add items:** `POST /shop/cart/add`
2. **View cart:** `GET /shop/cart`
3. **Update quantities:** `PUT /shop/cart/items/{itemId}`
4. **Remove items:** `DELETE /shop/cart/items/{itemId}`
5. **Clear cart:** `DELETE /shop/cart/clear`

#### 5. Test Reward Points

1. **Check balance:** `GET /shop/reward-points/balance`
2. **View history:** `GET /shop/reward-points/history`

### 🎯 Integration Test Scenarios

#### Scenario A: Free Item Purchase
1. Find a free item (`type: "free"`)
2. Add to cart
3. Checkout (should complete without payment)

#### Scenario B: Paid Item Purchase
1. Find a paid item (`type: "in_app_purchase"`)
2. Add to cart
3. Checkout with payment method
4. Complete payment flow

#### Scenario C: Mixed Cart
1. Add both free and paid items
2. Checkout (should handle mixed payment)

## Error Testing & Validation

### ❌ Testing Error Scenarios via Swagger

#### 1. Authentication Errors

**Test without JWT token:**
1. **Click "Authorize"** and **clear/logout**
2. **Try any protected endpoint**
3. **Expected:** `401 Unauthorized`

**Test with invalid token:**
1. **Enter invalid token** in Authorization
2. **Try any protected endpoint**
3. **Expected:** `401 Unauthorized`

#### 2. Validation Errors

**Test missing required fields:**
1. **Go to `POST /payment/initiate`**
2. **Remove required fields** like `orderId` or `amount`
3. **Execute**
4. **Expected:** `400 Bad Request` with validation details

**Test invalid data types:**
1. **Enter string for `amount`** field
2. **Execute**
3. **Expected:** `400 Bad Request`

**Test invalid enum values:**
1. **Enter invalid `paymentMethod`** (e.g., "invalid_method")
2. **Execute**
3. **Expected:** `400 Bad Request`

#### 3. Business Logic Errors

**Test insufficient permissions:**
1. **Login as different user type** (if available)
2. **Try student-only endpoints**
3. **Expected:** `403 Forbidden`

**Test non-existent resources:**
1. **Use invalid transaction ID** in `GET /payment/status/{id}`
2. **Execute**
3. **Expected:** `404 Not Found`

### 🔍 Common Error Response Format

All errors follow this structure:
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": "Additional details"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### ✅ Validation Test Checklist

- [ ] **Required fields** - Remove each required field and test
- [ ] **Data types** - Send wrong data types for each field
- [ ] **String lengths** - Test minimum/maximum lengths
- [ ] **Numeric ranges** - Test negative numbers, zero, very large numbers
- [ ] **Email format** - Test invalid email formats
- [ ] **Phone format** - Test invalid phone formats
- [ ] **Enum values** - Test invalid enum values
- [ ] **Date formats** - Test invalid date formats

## Swagger Testing Best Practices

### 🎯 Effective Swagger Testing Tips

#### 1. **Use the "Try it out" Feature Systematically**
- Test **happy path scenarios** first
- Then test **edge cases** and **error scenarios**
- **Document your findings** in test results

#### 2. **Leverage Swagger's Built-in Validation**
- Swagger **automatically validates** request schemas
- **Red highlighting** indicates validation errors
- **Green responses** indicate successful requests

#### 3. **Test Different User Roles**
- **Login with different user types** (student, tutor, admin)
- **Test role-based access control**
- **Verify permission restrictions**

#### 4. **Use Realistic Test Data**
- **Use valid email formats** and phone numbers
- **Use reasonable amounts** for payments
- **Use existing item/plan IDs** from the system

#### 5. **Test Pagination and Filtering**
- **Test different page sizes** (1, 10, 50, 100)
- **Test edge cases** (page 0, negative page)
- **Test all available filters** and combinations

#### 6. **Monitor Response Times**
- Swagger shows **request duration**
- **Note slow responses** (>2 seconds)
- **Test during different load conditions**

### 📊 Response Analysis

#### Success Indicators
- ✅ **HTTP 200/201** status codes
- ✅ **`"success": true`** in response body
- ✅ **Expected data structure** returned
- ✅ **Reasonable response time** (<2 seconds)

#### Failure Indicators
- ❌ **HTTP 4xx/5xx** status codes
- ❌ **`"success": false`** in response body
- ❌ **Missing required fields** in response
- ❌ **Slow response times** (>5 seconds)

### 🔄 Testing Workflow

1. **Start with authentication** (`POST /auth/login`)
2. **Test read operations** first (`GET` endpoints)
3. **Test create operations** (`POST` endpoints)
4. **Test update operations** (`PUT/PATCH` endpoints)
5. **Test delete operations** (`DELETE` endpoints)
6. **Test error scenarios** last

## API Testing Checklist

### 🔐 Authentication & Authorization
- [ ] **Login successfully** with valid credentials
- [ ] **Login fails** with invalid credentials
- [ ] **JWT token** works for protected endpoints
- [ ] **Expired token** returns 401 Unauthorized
- [ ] **Invalid token** returns 401 Unauthorized
- [ ] **Missing token** returns 401 Unauthorized
- [ ] **Role-based access** works correctly (student/tutor/admin)

### 💳 Payment API Endpoints
- [ ] **`POST /payment/initiate`** - Creates payment successfully
- [ ] **`GET /payment/status/{id}`** - Returns payment status
- [ ] **`POST /payment/webhook/kcp`** - Processes webhooks (external tool)
- [ ] **`GET /payment/kcp/redirect`** - Handles redirects
- [ ] **`POST /payment/refund`** - Processes refunds
- [ ] **`GET /payment/transactions`** - Lists user transactions

### 🛒 Shop Integration
- [ ] **`GET /shop/items`** - Lists shop items
- [ ] **`GET /shop/items/available`** - Shows available items
- [ ] **`POST /shop/cart/add`** - Adds items to cart
- [ ] **`GET /shop/cart`** - Shows cart contents
- [ ] **`POST /shop/cart/checkout`** - Initiates payment
- [ ] **`DELETE /shop/cart/clear`** - Clears cart

### 📋 Plan Subscription
- [ ] **`GET /plans`** - Lists available plans
- [ ] **`POST /plans/subscribe`** - Initiates subscription payment
- [ ] **`GET /plans/my-plans`** - Shows user's active plans

### ❌ Error Handling
- [ ] **400 Bad Request** - Invalid input data
- [ ] **401 Unauthorized** - Authentication failures
- [ ] **403 Forbidden** - Permission denied
- [ ] **404 Not Found** - Resource not found
- [ ] **409 Conflict** - Duplicate resources
- [ ] **500 Internal Server Error** - Server errors

### 📊 Data Validation
- [ ] **Required fields** - Missing fields return validation errors
- [ ] **Data types** - Wrong types return validation errors
- [ ] **String lengths** - Min/max length validation
- [ ] **Numeric ranges** - Amount validation (minimum 100 KRW)
- [ ] **Email format** - Valid email format required
- [ ] **Phone format** - Valid phone format required
- [ ] **Enum values** - Invalid enums return validation errors

### 🔄 Business Logic
- [ ] **Payment amounts** - Calculated correctly
- [ ] **Transaction status** - Updates properly
- [ ] **User ownership** - Items added to user account
- [ ] **Plan activation** - Subscription activates correctly
- [ ] **Reward points** - Calculated and awarded correctly
- [ ] **Duplicate prevention** - Same order ID rejected

### 📈 Performance
- [ ] **Response times** - Under 2 seconds for most endpoints
- [ ] **Large datasets** - Pagination works correctly
- [ ] **Concurrent requests** - System handles multiple users
- [ ] **Database queries** - No N+1 query problems

### 🔒 Security
- [ ] **Input sanitization** - XSS prevention
- [ ] **SQL injection** - Parameterized queries
- [ ] **Webhook signatures** - HMAC verification
- [ ] **Sensitive data** - Not exposed in logs/responses
- [ ] **Rate limiting** - Prevents abuse (if implemented)

### 📝 Documentation
- [ ] **Swagger UI** - All endpoints documented
- [ ] **Request examples** - Valid examples provided
- [ ] **Response schemas** - Accurate response structures
- [ ] **Error codes** - All error scenarios documented

---

## 🎯 Quick Testing Summary

### **Start Here:**
1. **Open Swagger UI:** `http://**************:3010/api-docs`
2. **Login:** Use `POST /auth/login` with test credentials
3. **Authorize:** Click 🔒 and enter `Bearer YOUR_TOKEN`
4. **Test Payment Flow:** Follow scenarios above

### **Key Endpoints to Test:**
- **`POST /payment/initiate`** - Start payment process
- **`GET /payment/status/{id}`** - Check payment status
- **`POST /shop/cart/checkout`** - Shop integration
- **`POST /plans/subscribe`** - Plan subscription

### **Common Test Data:**
```json
{
  "orderId": "TEST-12345",
  "amount": 10000,
  "currency": "KRW",
  "productName": "Test Product",
  "buyerName": "Test User",
  "buyerEmail": "<EMAIL>",
  "buyerPhone": "010-1234-5678",
  "paymentMethod": "card",
  "purchaseType": "shop_item",
  "referenceId": "ref-12345",
  "returnUrl": "http://**************:3010/payment/success",
  "cancelUrl": "http://**************:3010/payment/cancel"
}
```

### **Success Criteria:**
- ✅ All endpoints return expected HTTP status codes
- ✅ Response format matches API documentation
- ✅ Authentication and authorization work correctly
- ✅ Error handling provides meaningful messages
- ✅ Payment flow integrates properly with shop and plans

### **Need Help?**
- **Swagger UI:** Interactive testing with real-time validation
- **API Documentation:** `/api-docs` for complete endpoint reference
- **Error Messages:** Check response body for detailed error information
- **Logs:** Check server logs for debugging information
