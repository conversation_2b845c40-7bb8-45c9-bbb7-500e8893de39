import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTokenBlacklistTable1721726400000 implements MigrationInterface {
  name = 'CreateTokenBlacklistTable1721726400000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create token_blacklist table
    await queryRunner.query(`
      CREATE TABLE "token_blacklist" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "token_hash" character varying(64) NOT NULL,
        "user_id" uuid NOT NULL,
        "expires_at" TIMESTAMP NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "reason" character varying(50) NOT NULL DEFAULT 'logout',
        CONSTRAINT "PK_token_blacklist_id" PRIMARY KEY ("id")
      )
    `);

    // Create unique index on token_hash
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_token_blacklist_token_hash" ON "token_blacklist" ("token_hash")`);

    // Create index on expires_at for efficient cleanup
    await queryRunner.query(`CREATE INDEX "IDX_token_blacklist_expires_at" ON "token_blacklist" ("expires_at")`);

    // Create index on user_id for user-specific queries
    await queryRunner.query(`CREATE INDEX "IDX_token_blacklist_user_id" ON "token_blacklist" ("user_id")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_token_blacklist_user_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_token_blacklist_expires_at"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_token_blacklist_token_hash"`);

    // Drop table
    await queryRunner.query(`DROP TABLE IF EXISTS "token_blacklist"`);
  }
}
