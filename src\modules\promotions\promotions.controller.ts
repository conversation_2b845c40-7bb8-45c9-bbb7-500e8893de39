import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { PromotionsService } from './promotions.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { StudentGuard } from '../../common/guards/student.guard';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { CreatePromotionDto, UpdatePromotionDto, PromotionResponseDto, ApplyPromotionCodeDto, PromotionApplicationResponseDto, GetApplicablePromotionsDto } from '../../database/models/promotion.dto';
import { PromotionStatus, PromotionApplicableType } from '../../database/entities/promotion.entity';

@ApiTags('promotions')
@Controller('promotions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class PromotionsController {
  constructor(private readonly promotionsService: PromotionsService) {}

  // Admin endpoints for managing promotions

  @Post()
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Create a new promotion (Admin only)',
    description: 'Creates a new promotion with the specified details.',
  })
  @ApiBody({
    type: CreatePromotionDto,
    description: 'Promotion creation data',
  })
  @ApiOkResponseWithType(PromotionResponseDto, 'Promotion created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(409, 'Promotion with the same code already exists')
  async createPromotion(@Body() createPromotionDto: CreatePromotionDto): Promise<ApiResponse<PromotionResponseDto>> {
    const promotion = await this.promotionsService.createPromotion(createPromotionDto);
    return ApiResponse.success(promotion, 'Promotion created successfully', 201);
  }

  @Get('admin')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Get all promotions (Admin only)',
    description: 'Get a list of all promotions, optionally filtered by status and applicable type.',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: PromotionStatus,
    description: 'Filter promotions by status',
  })
  @ApiQuery({
    name: 'applicableType',
    required: false,
    enum: PromotionApplicableType,
    description: 'Filter promotions by applicable type',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(PromotionResponseDto, 'Promotions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getAllPromotions(
    @Query('status') status?: PromotionStatus,
    @Query('applicableType') applicableType?: PromotionApplicableType,
    @Query() paginationDto?: PaginationDto,
  ): Promise<ApiResponse<PagedListDto<PromotionResponseDto>>> {
    const promotions = await this.promotionsService.getAllPromotions(status, applicableType, paginationDto);
    return ApiResponse.success(promotions, 'Promotions retrieved successfully');
  }

  @Get('admin/:id')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Get promotion by ID (Admin only)',
    description: 'Get details of a specific promotion by ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'Promotion ID',
    type: String,
  })
  @ApiOkResponseWithType(PromotionResponseDto, 'Promotion retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Promotion not found')
  async getPromotionById(@Param('id') id: string): Promise<ApiResponse<PromotionResponseDto>> {
    const promotion = await this.promotionsService.getPromotionById(id);
    return ApiResponse.success(promotion, 'Promotion retrieved successfully');
  }

  @Patch(':id')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Update a promotion (Admin only)',
    description: 'Update an existing promotion with the specified details.',
  })
  @ApiParam({
    name: 'id',
    description: 'Promotion ID',
    type: String,
  })
  @ApiBody({
    type: UpdatePromotionDto,
    description: 'Promotion update data',
  })
  @ApiOkResponseWithType(PromotionResponseDto, 'Promotion updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Promotion not found')
  @ApiErrorResponse(409, 'Promotion with the same code already exists')
  async updatePromotion(@Param('id') id: string, @Body() updatePromotionDto: UpdatePromotionDto): Promise<ApiResponse<PromotionResponseDto>> {
    try {
      const promotion = await this.promotionsService.updatePromotion(id, updatePromotionDto);
      return ApiResponse.success(promotion, 'Promotion updated successfully');
    } catch (error) {
      // Let the global exception filter handle the error
      throw error;
    }
  }

  @Delete(':id')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Delete a promotion (Admin only)',
    description: 'Delete an existing promotion.',
  })
  @ApiParam({
    name: 'id',
    description: 'Promotion ID',
    type: String,
  })
  @ApiOkResponseWithType(Object, 'Promotion deleted successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Promotion not found')
  async deletePromotion(@Param('id') id: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
    const result = await this.promotionsService.deletePromotion(id);
    return ApiResponse.success(result, result.message);
  }

  @Post('generate-code')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Generate a unique promotion code (Admin only)',
    description: 'Generates a unique promotion code that can be used when creating a promotion.',
  })
  @ApiQuery({
    name: 'prefix',
    required: false,
    type: String,
    description: 'Prefix for the promotion code (default: "PROMO")',
  })
  @ApiOkResponseWithType(String, 'Promotion code generated successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async generatePromotionCode(@Query('prefix') prefix?: string): Promise<ApiResponse<string>> {
    const code = await this.promotionsService.generatePromotionCode(prefix);
    return ApiResponse.success(code, 'Promotion code generated successfully');
  }

  // Student endpoints for using promotions

  @Post('apply')
  @UseGuards(StudentGuard)
  @ApiOperation({
    summary: 'Apply a promotion code (Student only)',
    description: 'Applies a promotion code to calculate the discount for an item.',
  })
  @ApiBody({
    type: ApplyPromotionCodeDto,
    description: 'Promotion code application data',
  })
  @ApiOkResponseWithType(PromotionApplicationResponseDto, 'Promotion code applied successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async applyPromotionCode(@Body() applyPromotionCodeDto: ApplyPromotionCodeDto): Promise<ApiResponse<PromotionApplicationResponseDto>> {
    const result = await this.promotionsService.applyPromotionCode(applyPromotionCodeDto);
    return ApiResponse.success(result, result.isApplied ? 'Promotion code applied successfully' : result.message);
  }

  @Post('applicable')
  @UseGuards(StudentGuard)
  @ApiOperation({
    summary: 'Get applicable promotions (Student only)',
    description: 'Get a list of promotions applicable to a specific item type and category/plan.',
  })
  @ApiBody({
    type: GetApplicablePromotionsDto,
    description: 'Item data to get promotions for',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(PromotionResponseDto, 'Applicable promotions retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getApplicablePromotions(@Body() getApplicablePromotionsDto: GetApplicablePromotionsDto, @Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<PromotionResponseDto>>> {
    const promotions = await this.promotionsService.getApplicablePromotions(getApplicablePromotionsDto, paginationDto);
    return ApiResponse.success(promotions, 'Applicable promotions retrieved successfully');
  }
}
