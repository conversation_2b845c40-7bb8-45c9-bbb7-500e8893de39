import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUUID } from 'class-validator';
import { PaginationDto } from '../../../common/models/pagination.dto';

/**
 * DTO for block game participants query parameters
 */
export class GetBlockGameParticipantsQueryDto extends PaginationDto {
  @ApiProperty({
    description: 'Search term for student name or email',
    required: false,
    example: 'john',
  })
  @IsOptional()
  @IsString()
  search?: string;

  // @ApiProperty({
  //   description: 'Filter by block game ID',
  //   required: false,
  //   example: '123e4567-e89b-12d3-a456-************',
  // })
  // @IsOptional()
  // @IsUUID()
  // block_game_id?: string;

  // Inherits sortBy and sortDirection from PaginationDto
}

/**
 * DTO for block game participant
 */
export class BlockGameParticipantDto {
  @ApiProperty({
    description: 'The ID of the student',
    example: '123e4567-e89b-12d3-a456-************',
  })
  student_id: string;

  @ApiProperty({
    description: 'The name of the student',
    example: 'John Doe',
  })
  student_name: string;

  @ApiProperty({
    description: 'The email of the student',
    example: '<EMAIL>',
  })
  student_email: string;

  @ApiPropertyOptional({
    description: 'The profile picture of the student',
    example: 'https://example.com/images/profile.jpg',
  })
  student_profile_picture?: string;

  @ApiProperty({
    description: 'The total number of attempts made by the student across all block games',
    example: 5,
  })
  total_attempts: number;

  @ApiProperty({
    description: 'The sum of all scores received by the student across all block games',
    example: 85,
  })
  total_score: number;

  @ApiProperty({
    description: 'The date of the last attempt',
    example: '2023-01-15T10:30:00.000Z',
  })
  last_attempt_date: Date;
}

/**
 * DTO for block game participants response
 */
export class BlockGameParticipantsResponseDto {
  @ApiProperty({
    description: 'List of participants',
    type: [BlockGameParticipantDto],
  })
  participants: BlockGameParticipantDto[];

  @ApiProperty({
    description: 'Total number of participants',
    example: 25,
  })
  total_items: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  total_pages: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  current_page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  page_size: number;
}

/**
 * DTO for student participation query parameters
 */
export class GetStudentBlockGameParticipationQueryDto extends PaginationDto {
  @ApiProperty({
    description: 'The ID of the student',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  student_id: string;

  // Inherits sortBy and sortDirection from PaginationDto
}

/**
 * DTO for student block game participation
 */
export class StudentBlockGameParticipationDto {
  @ApiProperty({
    description: 'The ID of the attempt record',
    example: '123e4567-e89b-12d3-a456-************',
  })
  attempt_id: string;

  @ApiProperty({
    description: 'The ID of the block game',
    example: '123e4567-e89b-12d3-a456-************',
  })
  block_game_id: string;

  @ApiProperty({
    description: 'The title of the block game',
    example: 'Basic Sentence Building',
  })
  block_game_title: string;

  @ApiProperty({
    description: 'The score achieved',
    example: 18,
  })
  score: number;

  @ApiProperty({
    description: 'The total possible score',
    example: 20,
  })
  total_score: number;

  @ApiProperty({
    description: 'When the attempt was submitted',
    example: '2023-01-15T10:30:00.000Z',
  })
  submitted_at: Date;
}

/**
 * DTO for student block game participation response
 */
export class StudentBlockGameParticipationResponseDto {
  @ApiProperty({
    description: 'List of student participations',
    type: [StudentBlockGameParticipationDto],
  })
  participations: StudentBlockGameParticipationDto[];

  @ApiProperty({
    description: 'Total number of participations',
    example: 15,
  })
  total_items: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 2,
  })
  total_pages: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  current_page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  page_size: number;
}
