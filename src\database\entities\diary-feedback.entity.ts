import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { DiaryEntry } from './diary-entry.entity';
import { User } from './user.entity';

@Entity()
export class DiaryFeedback extends AuditableBaseEntity {
  @Column({ name: 'diary_entry_id' })
  diaryEntryId: string;

  @ManyToOne(() => DiaryEntry, (entry) => entry.feedbacks)
  @JoinColumn({ name: 'diary_entry_id' })
  diaryEntry: DiaryEntry;

  @Column({ name: 'tutor_id' })
  tutorId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'tutor_id' })
  tutor: User;

  @Column({ name: 'feedback', type: 'text' })
  feedback: string;

  @Column({ name: 'rating', default: 0 })
  rating: number;

  @Column({ name: 'award', nullable: true })
  award: string;
}
