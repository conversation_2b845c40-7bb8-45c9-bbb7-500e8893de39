import { WebSocketGateway, WebSocketServer, SubscribeMessage, OnGatewayConnection, OnGatewayDisconnect, WsException, ConnectedSocket, MessageBody } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { NotificationResponseDto } from '../../database/models/notification.dto';
import { NotificationService } from './notification.service';
import { NotificationDelivery, NotificationChannel, DeliveryStatus } from '../../database/entities/notification-delivery.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { getCurrentUTCDate } from '../../common/utils/date-utils';

interface SocketWithUser extends Socket {
  user: {
    id: string;
    email: string;
  };
}

@WebSocketGateway({
  namespace: 'notifications',
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  },
})
@Injectable()
export class NotificationGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(NotificationGateway.name);
  private readonly userConnections = new Map<string, Set<string>>();

  constructor(
    private readonly jwtService: JwtService,
    private readonly notificationService: NotificationService,
    @InjectRepository(NotificationDelivery)
    private readonly notificationDeliveryRepository: Repository<NotificationDelivery>,
  ) {}

  /**
   * Handle new WebSocket connections
   * @param client Socket client
   */
  async handleConnection(client: Socket): Promise<void> {
    try {
      // Verify JWT token
      const token = client.handshake.auth.token;
      if (!token) {
        throw new UnauthorizedException('No token provided');
      }

      const payload = this.jwtService.verify(token);
      const userId = payload.id;

      // Store connection
      if (!this.userConnections.has(userId)) {
        this.userConnections.set(userId, new Set());
      }
      this.userConnections.get(userId).add(client.id);

      // Add user data to socket
      (client as SocketWithUser).user = {
        id: userId,
        email: payload.email,
      };

      // Join user-specific room
      client.join(`user:${userId}`);

      this.logger.log(`Client connected: ${client.id} for user ${userId}`);

      // Send connection confirmation
      client.emit('connected', { userId });

      // Process any pending push notifications
      await this.processPendingPushNotifications(userId);
    } catch (error) {
      this.logger.error(`Connection error: ${error.message}`, error.stack);
      client.disconnect(true);
    }
  }

  /**
   * Handle WebSocket disconnections
   * @param client Socket client
   */
  handleDisconnect(client: Socket): void {
    try {
      const user = (client as SocketWithUser).user;
      if (user) {
        // Remove connection from map
        const connections = this.userConnections.get(user.id);
        if (connections) {
          connections.delete(client.id);
          if (connections.size === 0) {
            this.userConnections.delete(user.id);
          }
        }
        this.logger.log(`Client disconnected: ${client.id} for user ${user.id}`);
      }
    } catch (error) {
      this.logger.error(`Disconnect error: ${error.message}`, error.stack);
    }
  }

  /**
   * Subscribe to notifications
   * @param client Socket client
   * @param data Subscription data
   */
  @SubscribeMessage('subscribe')
  handleSubscribe(@ConnectedSocket() client: SocketWithUser, @MessageBody() data: { userId: string }): void {
    try {
      // Verify user is subscribing to their own notifications
      if (client.user.id !== data.userId) {
        throw new WsException("Cannot subscribe to another user's notifications");
      }

      this.logger.log(`User ${data.userId} subscribed to notifications`);
      client.emit('subscribed', { userId: data.userId });
    } catch (error) {
      this.logger.error(`Subscription error: ${error.message}`, error.stack);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * Send a notification to a user
   * @param userId User ID
   * @param notification Notification data
   */
  async sendNotificationToUser(userId: string, notification: NotificationResponseDto): Promise<boolean> {
    try {
      // Check if user is connected
      if (!this.isUserConnected(userId)) {
        return false;
      }

      // Send notification to user's room
      this.server.to(`user:${userId}`).emit('notification', notification);
      this.logger.log(`Sent notification ${notification.id} to user ${userId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error sending notification to user ${userId}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Check if a user is connected
   * @param userId User ID
   * @returns Whether the user is connected
   */
  isUserConnected(userId: string): boolean {
    const connections = this.userConnections.get(userId);
    return !!connections && connections.size > 0;
  }

  /**
   * Process pending push notifications for a user
   * @param userId User ID
   */
  private async processPendingPushNotifications(userId: string): Promise<void> {
    try {
      // Find pending push notifications
      const pendingDeliveries = await this.notificationDeliveryRepository.find({
        where: {
          notification: {
            userId,
          },
          channel: NotificationChannel.PUSH,
          status: DeliveryStatus.PENDING,
        },
        relations: ['notification'],
      });

      if (pendingDeliveries.length === 0) {
        return;
      }

      this.logger.log(`Processing ${pendingDeliveries.length} pending push notifications for user ${userId}`);

      // Process each delivery
      for (const delivery of pendingDeliveries) {
        try {
          // Get notification with delivery status
          const notifications = await this.notificationService.getUserNotifications(userId);
          const notification = notifications.items.find((n) => n.id === delivery.notification.id);

          if (!notification) {
            continue;
          }

          // Send notification
          const sent = await this.sendNotificationToUser(userId, notification);
          if (sent) {
            // Update delivery status
            delivery.status = DeliveryStatus.SENT;
            delivery.sentAt = getCurrentUTCDate();
            await this.notificationDeliveryRepository.save(delivery);
          }
        } catch (error) {
          this.logger.error(`Error processing pending notification ${delivery.id}: ${error.message}`, error.stack);

          // Update retry count
          delivery.retryCount++;
          delivery.errorMessage = error.message;

          if (delivery.retryCount >= 3) {
            delivery.status = DeliveryStatus.FAILED;
          }

          await this.notificationDeliveryRepository.save(delivery);
        }
      }
    } catch (error) {
      this.logger.error(`Error processing pending notifications for user ${userId}: ${error.message}`, error.stack);
    }
  }
}
