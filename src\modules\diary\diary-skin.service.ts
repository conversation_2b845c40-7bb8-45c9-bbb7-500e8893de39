import { Injectable, NotFoundException, BadRequestException, Logger, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { DiarySkin } from '../../database/entities/diary-skin.entity';
import { StudentDiarySkin } from '../../database/entities/student-diary-skin.entity';
import { DiarySkinRegistry } from '../../database/entities/diary-skin-registry.entity';
import { StudentDiarySkinRegistry } from '../../database/entities/student-diary-skin-registry.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { Diary } from '../../database/entities/diary.entity';
import { User } from '../../database/entities/user.entity';
import { ShopSkinMapping } from '../../database/entities/shop-skin-mapping.entity';
import { ShopItem, ShopItemType } from '../../database/entities/shop-item.entity';
import { StudentOwnedItem } from '../../database/entities/student-owned-item.entity';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';

import { DiarySkinResponseDto, CreateDiarySkinDto, UpdateDiarySkinDto, CreateStudentDiarySkinDto, UpdateStudentDiarySkinDto, ToggleSkinStatusDto } from '../../database/models/diary.dto';

@Injectable()
export class DiarySkinService {
  private readonly logger = new Logger(DiarySkinService.name);

  constructor(
    @InjectRepository(DiarySkin)
    private diarySkinRepository: Repository<DiarySkin>,
    @InjectRepository(StudentDiarySkin)
    private studentDiarySkinRepository: Repository<StudentDiarySkin>,
    @InjectRepository(DiarySkinRegistry)
    private diarySkinRegistryRepository: Repository<DiarySkinRegistry>,
    @InjectRepository(DiaryEntry)
    private diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(Diary)
    private diaryRepository: Repository<Diary>,
    @InjectRepository(ShopSkinMapping)
    private shopSkinMappingRepository: Repository<ShopSkinMapping>,
    @InjectRepository(ShopItem)
    private shopItemRepository: Repository<ShopItem>,
    @InjectRepository(StudentOwnedItem)
    private studentOwnedItemRepository: Repository<StudentOwnedItem>,
    private readonly fileRegistryService: FileRegistryService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Check if a skin is used in any diary entry or as anyone's default skin
   * @param skinId The ID of the skin to check
   * @returns True if the skin is in use, false otherwise
   */
  private async isSkinInUse(skinId: string): Promise<boolean> {
    try {
      // Check if the skin is used in any diary entry
      const entryCount = await this.diaryEntryRepository.count({
        where: { skinId },
      });

      if (entryCount > 0) {
        this.logger.log(`Skin ${skinId} is used in ${entryCount} diary entries`);
        return true;
      }

      // Check if the skin is used as anyone's default skin
      const diaryCount = await this.diaryRepository.count({
        where: { defaultSkinId: skinId },
      });

      if (diaryCount > 0) {
        this.logger.log(`Skin ${skinId} is used as default skin in ${diaryCount} diaries`);
        return true;
      }

      this.logger.log(`Skin ${skinId} is not in use`);
      return false;
    } catch (error) {
      this.logger.error(`Error checking if skin ${skinId} is in use: ${error.message}`, error.stack);
      // Default to false if there's an error
      return false;
    }
  }

  /**
   * Get a diary skin by ID
   * @param id The ID of the skin
   * @returns The diary skin
   */
  async getDiarySkinById(id: string): Promise<DiarySkinResponseDto> {
    const skin = await this.diarySkinRepository.findOne({
      where: { id },
    });

    if (!skin) {
      throw new NotFoundException(`Diary skin with ID ${id} not found`);
    }

    // Generate registry URL for preview image
    const registryUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, skin.id);

    // Check if the skin is in use
    const isUsedIn = await this.isSkinInUse(skin.id);

    return {
      id: skin.id,
      name: skin.name,
      description: skin.description,
      previewImagePath: registryUrl,
      isActive: skin.isActive,
      isGlobal: true,
      createdById: skin.createdById,
      templateContent: skin.templateContent,
      isUsedIn,
    };
  }

  /**
   * Get a student diary skin by ID
   * @param id The ID of the skin
   * @param studentId The ID of the student
   * @returns The student diary skin
   */
  async getStudentDiarySkinById(id: string, studentId: string): Promise<DiarySkinResponseDto> {
    const skin = await this.studentDiarySkinRepository.findOne({
      where: { id, studentId },
    });

    if (!skin) {
      throw new NotFoundException(`Student diary skin with ID ${id} not found or does not belong to student ${studentId}`);
    }

    // Generate registry URL for preview image (same as admin skins)
    const registryUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, skin.id);

    // Check if the skin is in use
    const isUsedIn = await this.isSkinInUse(skin.id);

    return {
      id: skin.id,
      name: skin.name,
      description: skin.description,
      previewImagePath: registryUrl,
      isActive: skin.isActive,
      isGlobal: false,
      studentId: skin.studentId,
      templateContent: skin.templateContent,
      isUsedIn,
    };
  }

  /**
   * Get all diary skins available to a student
   * Business Logic:
   * - All free admin skins (global, no purchase required)
   * - Purchased paid admin skins (global, but purchased by this student)
   * - Student's own private skins (created by them)
   * @param includeInactive Whether to include inactive skins
   * @param studentId Optional student ID to include their custom skins and check purchases
   * @param paginationDto Pagination parameters
   * @returns A paged list of diary skins
   */
  async getDiarySkins(includeInactive: boolean = false, studentId?: string, paginationDto?: PaginationDto): Promise<PagedListDto<DiarySkinResponseDto>> {
    // Set default pagination values
    const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC' } = paginationDto || {};

    // For regular users, only return active skins
    // For admins (includeInactive = true), return all skins
    const where = includeInactive ? {} : { isActive: true };

    // First, get ALL accessible global skins without pagination to calculate total count
    const allGlobalSkins = await this.diarySkinRepository.find({
      where,
      order: { [sortBy]: sortDirection },
    });

    // Filter global skins based on student access (free skins + purchased skins)
    const accessibleGlobalSkins = studentId ? await this.filterAccessibleAdminSkins(allGlobalSkins, studentId) : allGlobalSkins; // If no studentId, return all (admin view)

    // Get user default skin preferences if studentId is provided
    let userDefaultDiarySkinId: string | undefined;
    let userDefaultNovelSkinId: string | undefined;

    if (studentId) {
      const userRepository = this.dataSource.getRepository(User);
      const user = await userRepository.findOne({
        where: { id: studentId },
        select: ['defaultDiarySkinId', 'defaultNovelSkinId'],
      });
      userDefaultDiarySkinId = user?.defaultDiarySkinId;
      userDefaultNovelSkinId = user?.defaultNovelSkinId;
    }

    // Get ALL student skins if studentId is provided (for total count)
    let allStudentSkins: StudentDiarySkin[] = [];
    if (studentId) {
      const studentWhere = includeInactive ? { studentId } : { studentId, isActive: true };
      allStudentSkins = await this.studentDiarySkinRepository.find({
        where: studentWhere,
        order: { [sortBy]: sortDirection },
      });
    }

    // Combine all skins for proper pagination
    const allSkins = [...accessibleGlobalSkins, ...allStudentSkins];
    const totalCount = allSkins.length;

    // Apply pagination to the combined result
    const skip = (page - 1) * limit;
    const paginatedSkins = allSkins.slice(skip, skip + limit);

    // Separate paginated skins back into global and student skins
    const paginatedGlobalSkins = paginatedSkins.filter((skin) => 'createdById' in skin);
    const paginatedStudentSkins = paginatedSkins.filter((skin) => 'studentId' in skin);

    // Map paginated global skins to response DTOs with registry URLs
    const globalSkinDtos = await Promise.all(
      paginatedGlobalSkins.map(async (skin) => {
        const registryUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, skin.id);

        // Check if the skin is in use
        const isUsedIn = await this.isSkinInUse(skin.id);

        // Check if this skin is purchased by the student (for display purposes)
        const isPurchased = studentId ? await this.isAdminSkinPurchasedByStudent(skin.id, studentId) : false;
        const isFree = await this.isAdminSkinFree(skin.id);

        return {
          id: skin.id,
          name: skin.name,
          description: skin.description,
          previewImagePath: registryUrl,
          isActive: skin.isActive,
          isGlobal: true,
          createdById: skin.createdById,
          templateContent: skin.templateContent,
          isUsedIn,
          isFree,
          isPurchased,
          isUserDefaultDiary: skin.id === userDefaultDiarySkinId,
          isUserDefaultNovel: skin.id === userDefaultNovelSkinId,
        };
      }),
    );

    // Map paginated student skins to response DTOs with unified URL generation
    const studentSkinDtos = await Promise.all(
      paginatedStudentSkins.map(async (skin) => {
        // Use the correct entity type for student diary skins
        const previewImageUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STUDENT_DIARY_SKIN, skin.id);

        // Check if the skin is in use
        const isUsedIn = await this.isSkinInUse(skin.id);

        return {
          id: skin.id,
          name: skin.name,
          description: skin.description,
          previewImagePath: previewImageUrl,
          isActive: skin.isActive,
          isGlobal: false,
          studentId: skin.studentId,
          templateContent: skin.templateContent,
          isUsedIn,
          isFree: true, // Student skins are always "free" for the creator
          isPurchased: false, // Student skins are not purchased items
          isUserDefaultDiary: skin.id === userDefaultDiarySkinId,
          isUserDefaultNovel: skin.id === userDefaultNovelSkinId,
        };
      }),
    );

    // Combine paginated results
    const combinedSkins = [...globalSkinDtos, ...studentSkinDtos];

    // Return properly paginated result with correct total count
    return new PagedListDto(combinedSkins, totalCount, page, limit);
  }

  /**
   * Create a new diary skin (admin only)
   * @param adminId The ID of the admin creating the skin
   * @param createDiarySkinDto Data for creating the skin
   * @param previewImage The preview image file
   * @returns The created diary skin
   */
  async createDiarySkin(adminId: string, createDiarySkinDto: CreateDiarySkinDto, previewImage: any): Promise<DiarySkinResponseDto> {
    try {
      // First, verify that the admin user exists
      const adminUser = await this.dataSource.getRepository(User).findOne({
        where: { id: adminId },
      });

      if (!adminUser) {
        this.logger.error(`Admin user with ID ${adminId} not found`);
        throw new NotFoundException(`Admin user with ID ${adminId} not found`);
      }

      this.logger.log(`Creating diary skin with adminId: ${adminId}, admin user found: ${adminUser.name}`);

      // Start a transaction
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Create and save the diary skin first (without preview image path)
        const newSkin = this.diarySkinRepository.create({
          name: createDiarySkinDto.name,
          description: createDiarySkinDto.description,
          templateContent: createDiarySkinDto.templateContent || '', // Ensure it's not null
          previewImagePath: '', // Will be updated after file upload
          isActive: createDiarySkinDto.isActive !== undefined ? createDiarySkinDto.isActive : true,
          isGlobal: true,
          createdById: adminId,
        });

        const savedSkin = await queryRunner.manager.save(newSkin);
        this.logger.log(`Created diary skin: ${savedSkin.id}`);

        // Validate that we have a valid UUID before uploading
        if (!savedSkin.id) {
          throw new Error('Diary skin ID is missing after creation');
        }

        this.logger.log(`Uploading file for diary skin with UUID: ${savedSkin.id}`);

        // Commit the transaction first so the diary skin exists for the foreign key constraint
        await queryRunner.commitTransaction();
        this.logger.log('Transaction committed - diary skin created successfully');

        // Now upload the file with the actual skin ID (outside transaction)
        let uploadResult: { filePath: string; registry?: any };
        try {
          uploadResult = await this.fileRegistryService.uploadFile(
            FileEntityType.DIARY_SKIN,
            previewImage,
            savedSkin.id, // Use the actual skin UUID as referenceId
            {
              isStudentSkin: false,
              userId: adminId,
              entityId: savedSkin.id, // Pass the skin ID for S3 registry
            },
          );

          this.logger.log(`File upload successful - filePath: ${uploadResult.filePath}`);
        } catch (uploadError) {
          this.logger.error(`Error uploading file: ${uploadError.message}`);
          // If file upload fails, we need to clean up the created skin
          try {
            await this.diarySkinRepository.remove(savedSkin);
            this.logger.log(`Cleaned up diary skin after upload failure: ${savedSkin.id}`);
          } catch (cleanupError) {
            this.logger.error(`Error cleaning up diary skin after upload failure: ${cleanupError.message}`);
          }
          throw uploadError;
        }

        // Update the skin with the preview image path (outside transaction)
        savedSkin.previewImagePath = uploadResult.filePath;
        await this.diarySkinRepository.save(savedSkin);
        this.logger.log(`Updated diary skin preview image path: ${savedSkin.previewImagePath}`);

        // Generate registry URL for preview image
        const registryPreviewImageUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, savedSkin.id);

        // Return the DTO
        return {
          id: savedSkin.id,
          name: savedSkin.name,
          description: savedSkin.description,
          previewImagePath: registryPreviewImageUrl,
          isActive: savedSkin.isActive,
          isGlobal: true,
          createdById: savedSkin.createdById,
          templateContent: savedSkin.templateContent,
          isUsedIn: false, // New skin is not in use yet
        };
      } catch (error) {
        // Rollback the transaction in case of error
        await queryRunner.rollbackTransaction();
        this.logger.error(`Error in transaction: ${error.message}`, error.stack);
        throw error;
      } finally {
        // Release the query runner
        await queryRunner.release();
      }
    } catch (error) {
      this.logger.error(`Error creating diary skin: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create diary skin: ${error.message}`);
    }
  }

  /**
   * Update a diary skin (admin only)
   * @param skinId The ID of the skin to update
   * @param updateDiarySkinDto Data for updating the skin
   * @param previewImage Optional preview image file
   * @returns The updated diary skin
   */
  async updateDiarySkin(skinId: string, updateDiarySkinDto: UpdateDiarySkinDto, previewImage?: any): Promise<DiarySkinResponseDto> {
    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the existing skin
      const skin = await queryRunner.manager.findOne(DiarySkin, {
        where: { id: skinId },
      });

      if (!skin) {
        throw new NotFoundException(`Diary skin with ID ${skinId} not found`);
      }

      // Update the skin properties
      if (updateDiarySkinDto.name !== undefined) {
        skin.name = updateDiarySkinDto.name;
      }

      if (updateDiarySkinDto.description !== undefined) {
        skin.description = updateDiarySkinDto.description;
      }

      if (updateDiarySkinDto.templateContent !== undefined) {
        skin.templateContent = updateDiarySkinDto.templateContent;
      }

      if (updateDiarySkinDto.isActive !== undefined) {
        skin.isActive = updateDiarySkinDto.isActive;
      }

      // If a new preview image is provided, upload it and update the skin
      if (previewImage) {
        this.logger.log(`Updating diary skin with new image - skinId: ${skin.id}, skinName: ${skin.name}`);
        this.logger.log(`Preview image details - Name: ${previewImage?.originalname || 'unknown'}, Size: ${previewImage?.size || 'unknown'}, Type: ${previewImage?.mimetype || 'unknown'}`);

        // Delete existing registry entries from database
        await queryRunner.manager.delete(DiarySkinRegistry, { diarySkinId: skin.id });
        this.logger.log(`Deleted existing registry entries for diary skin ID: ${skin.id}`);

        // Upload the file to disk only (don't update registry in FileRegistryService)
        let uploadResult: { filePath: string; registry: any };
        try {
          // Validate that we have a valid UUID before uploading
          if (!skin.id) {
            throw new Error('Diary skin ID is missing for update');
          }

          this.logger.log(`Uploading new file for diary skin ID: ${skin.id}`);

          // Use the FileRegistryService to upload the file, but we'll handle the registry update ourselves
          uploadResult = await this.fileRegistryService.uploadFile(
            FileEntityType.DIARY_SKIN,
            previewImage,
            skin.id, // Use the actual skin UUID as referenceId
            {
              isStudentSkin: false,
              userId: skin.createdById,
              entityId: skin.id, // Pass the skin ID for S3 registry
            },
          );

          this.logger.log(`File upload successful - filePath: ${uploadResult.filePath}`);
        } catch (uploadError) {
          this.logger.error(`Error uploading file: ${uploadError.message}`);
          if (uploadError.stack) {
            this.logger.error(`Stack trace: ${uploadError.stack}`);
          }
          throw uploadError;
        }

        // Update the skin's preview image path
        skin.previewImagePath = uploadResult.filePath;
        this.logger.log(`Updated skin preview image path: ${skin.previewImagePath}`);

        // Create a new registry entry for the updated image
        const registryEntry = queryRunner.manager.create(DiarySkinRegistry, {
          diarySkinId: skin.id,
          filePath: uploadResult.filePath,
          mimeType: previewImage.mimetype,
          fileName: previewImage.originalname,
          fileSize: previewImage.size,
        });

        // Save the registry entry within the transaction
        await queryRunner.manager.save(registryEntry);
        this.logger.log(`Created new registry entry for updated diary skin ID: ${skin.id}`);
      }

      // Save the updated skin
      const updatedSkin = await queryRunner.manager.save(skin);

      // Commit the transaction
      this.logger.log(`Committing transaction for diary skin ID: ${updatedSkin.id}`);
      await queryRunner.commitTransaction();
      this.logger.log(`Transaction committed successfully for diary skin ID: ${updatedSkin.id}`);

      // Generate registry URL for preview image
      const registryPreviewImageUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, updatedSkin.id);

      // Check if the skin is in use
      const isUsedIn = await this.isSkinInUse(updatedSkin.id);

      // Return the DTO
      return {
        id: updatedSkin.id,
        name: updatedSkin.name,
        description: updatedSkin.description,
        previewImagePath: registryPreviewImageUrl,
        isActive: updatedSkin.isActive,
        isGlobal: true,
        createdById: updatedSkin.createdById,
        templateContent: updatedSkin.templateContent,
        isUsedIn,
      };
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();

      // Log detailed error information
      this.logger.error(`Error updating diary skin ID ${skinId}: ${error.message}`);
      if (previewImage) {
        this.logger.error(`Preview image details - Name: ${previewImage?.originalname || 'unknown'}, Size: ${previewImage?.size || 'unknown'}, Type: ${previewImage?.mimetype || 'unknown'}`);
      }
      this.logger.error(`Update parameters: ${JSON.stringify(updateDiarySkinDto)}`);

      if (error.stack) {
        this.logger.error(`Stack trace: ${error.stack}`);
      }

      // Throw a more descriptive error
      throw new BadRequestException(`Failed to update diary skin: ${error.message}. Check server logs for more details.`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Delete a diary skin (admin only)
   * @param skinId The ID of the skin to delete
   */
  async deleteDiarySkin(skinId: string): Promise<void> {
    // Check if the skin is in use
    const isInUse = await this.isSkinInUse(skinId);
    if (isInUse) {
      throw new BadRequestException('Cannot delete skin as it is being used by diaries or diary entries');
    }

    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the skin
      const skin = await queryRunner.manager.findOne(DiarySkin, {
        where: { id: skinId },
      });

      if (!skin) {
        throw new NotFoundException(`Diary skin with ID ${skinId} not found`);
      }

      // Delete registry entries from database
      await queryRunner.manager.delete(DiarySkinRegistry, { diarySkinId: skinId });

      // Delete the skin from the database
      await queryRunner.manager.remove(skin);

      // Commit the transaction
      await queryRunner.commitTransaction();
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(`Failed to delete diary skin: ${error.message}`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Create a student diary skin
   * @param studentId The ID of the student creating the skin
   * @param createStudentDiarySkinDto Data for creating the skin
   * @param previewImage The preview image file
   * @returns The created student diary skin
   */
  async createStudentDiarySkin(studentId: string, createStudentDiarySkinDto: CreateStudentDiarySkinDto, previewImage: any): Promise<DiarySkinResponseDto> {
    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create and save the student diary skin first (without preview image path)
      const newSkin = this.studentDiarySkinRepository.create({
        name: createStudentDiarySkinDto.name,
        description: createStudentDiarySkinDto.description,
        templateContent: createStudentDiarySkinDto.templateContent || '', // Ensure it's not null
        previewImagePath: '', // Will be updated after file upload
        isActive: createStudentDiarySkinDto.isActive !== undefined ? createStudentDiarySkinDto.isActive : true,
        studentId: studentId,
      });

      const savedSkin = await queryRunner.manager.save(newSkin);
      this.logger.log(`Created student diary skin: ${savedSkin.id}`);

      // Validate that we have a valid UUID before uploading
      if (!savedSkin.id) {
        throw new Error('Student diary skin ID is missing after creation');
      }

      this.logger.log(`Uploading file for student diary skin with UUID: ${savedSkin.id}`);

      // Commit the transaction first so the student diary skin exists for the foreign key constraint
      await queryRunner.commitTransaction();
      this.logger.log('Transaction committed - student diary skin created successfully');

      // Now upload the file using the unified file registry system (same as admin skins)
      let uploadResult: { filePath: string; registry?: any };
      try {
        uploadResult = await this.fileRegistryService.uploadFile(
          FileEntityType.STUDENT_DIARY_SKIN,
          previewImage,
          savedSkin.id, // Use the actual skin UUID as referenceId
          {
            userId: studentId,
            entityId: savedSkin.id, // Pass the skin ID for registry
          },
        );

        this.logger.log(`File upload successful - filePath: ${uploadResult.filePath}`);
      } catch (uploadError) {
        this.logger.error(`Error uploading file: ${uploadError.message}`);
        if (uploadError.stack) {
          this.logger.error(`Stack trace: ${uploadError.stack}`);
        }
        throw uploadError;
      }

      // Start a new transaction to update the skin with the file path
      await queryRunner.connect();
      await queryRunner.startTransaction();

      // Update the skin with the preview image path
      savedSkin.previewImagePath = uploadResult.filePath;
      await queryRunner.manager.save(savedSkin);
      this.logger.log(`Updated student diary skin preview image path: ${savedSkin.previewImagePath}`);

      // Commit the transaction
      await queryRunner.commitTransaction();
      this.logger.log('Transaction committed successfully');

      // Generate unified URL for preview image (same as admin skins)
      const previewImageUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STUDENT_DIARY_SKIN, savedSkin.id);

      // Return the DTO
      return {
        id: savedSkin.id,
        name: savedSkin.name,
        description: savedSkin.description,
        previewImagePath: previewImageUrl,
        isActive: savedSkin.isActive,
        isGlobal: false,
        studentId: savedSkin.studentId,
        templateContent: savedSkin.templateContent,
        isUsedIn: false, // New skin is not in use yet
      };
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error creating student diary skin: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create student diary skin: ${error.message}`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Update a student diary skin
   * @param skinId The ID of the skin to update
   * @param studentId The ID of the student updating the skin
   * @param updateStudentDiarySkinDto Data for updating the skin
   * @param previewImage Optional preview image file
   * @returns The updated student diary skin
   */
  async updateStudentDiarySkin(skinId: string, studentId: string, updateStudentDiarySkinDto: UpdateStudentDiarySkinDto, previewImage?: any): Promise<DiarySkinResponseDto> {
    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the existing skin
      const skin = await queryRunner.manager.findOne(StudentDiarySkin, {
        where: { id: skinId, studentId },
      });

      if (!skin) {
        throw new NotFoundException(`Student diary skin with ID ${skinId} not found or does not belong to student ${studentId}`);
      }

      // Update the skin properties
      if (updateStudentDiarySkinDto.name !== undefined) {
        skin.name = updateStudentDiarySkinDto.name;
      }

      if (updateStudentDiarySkinDto.description !== undefined) {
        skin.description = updateStudentDiarySkinDto.description;
      }

      if (updateStudentDiarySkinDto.templateContent !== undefined) {
        skin.templateContent = updateStudentDiarySkinDto.templateContent;
      }

      if (updateStudentDiarySkinDto.isActive !== undefined) {
        skin.isActive = updateStudentDiarySkinDto.isActive;
      }

      // If a new preview image is provided, upload it and update the skin
      if (previewImage) {
        this.logger.log(`Updating student diary skin with new image - skinId: ${skin.id}, skinName: ${skin.name}, studentId: ${studentId}`);
        this.logger.log(`Preview image details - Name: ${previewImage?.originalname || 'unknown'}, Size: ${previewImage?.size || 'unknown'}, Type: ${previewImage?.mimetype || 'unknown'}`);

        // Delete existing registry entries from database (same as admin skins)
        await queryRunner.manager.delete(StudentDiarySkinRegistry, { studentDiarySkinId: skin.id });
        this.logger.log(`Deleted existing registry entries for student diary skin ID: ${skin.id}`);

        // Commit the transaction first so the student diary skin exists for the foreign key constraint
        await queryRunner.commitTransaction();
        this.logger.log('Transaction committed - preparing for file upload');

        // Now upload the file using the unified file registry system (same as admin skins)
        let uploadResult: { filePath: string; registry?: any };
        try {
          // Validate that we have a valid UUID before uploading
          if (!skin.id) {
            throw new Error('Student diary skin ID is missing for update');
          }

          this.logger.log(`Uploading new file for student diary skin ID: ${skin.id}`);

          // Use the unified file registry system (same as admin skins)
          uploadResult = await this.fileRegistryService.uploadFile(
            FileEntityType.STUDENT_DIARY_SKIN,
            previewImage,
            skin.id, // Use the actual skin UUID as referenceId
            {
              userId: studentId,
              entityId: skin.id, // Pass the skin ID for registry
            },
          );

          this.logger.log(`File upload successful - filePath: ${uploadResult.filePath}`);
        } catch (uploadError) {
          this.logger.error(`Error uploading file: ${uploadError.message}`);
          if (uploadError.stack) {
            this.logger.error(`Stack trace: ${uploadError.stack}`);
          }
          throw uploadError;
        }

        // Start a new transaction to update the skin with the file path
        await queryRunner.connect();
        await queryRunner.startTransaction();

        // Update the skin's preview image path
        skin.previewImagePath = uploadResult.filePath;
        this.logger.log(`Updated student skin preview image path: ${skin.previewImagePath}`);
      }

      // Save the updated skin
      const updatedSkin = await queryRunner.manager.save(skin);

      // Commit the transaction
      this.logger.log(`Committing transaction for student diary skin ID: ${updatedSkin.id}`);
      await queryRunner.commitTransaction();
      this.logger.log(`Transaction committed successfully for student diary skin ID: ${updatedSkin.id}`);

      // Generate unified URL for preview image (same as admin skins)
      const previewImageUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STUDENT_DIARY_SKIN, updatedSkin.id);

      // Check if the skin is in use
      const isUsedIn = await this.isSkinInUse(updatedSkin.id);

      // Return the DTO
      return {
        id: updatedSkin.id,
        name: updatedSkin.name,
        description: updatedSkin.description,
        previewImagePath: previewImageUrl,
        isActive: updatedSkin.isActive,
        isGlobal: false,
        studentId: updatedSkin.studentId,
        templateContent: updatedSkin.templateContent,
        isUsedIn,
      };
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();

      // Log detailed error information
      this.logger.error(`Error updating student diary skin ID ${skinId} for student ${studentId}: ${error.message}`);
      if (previewImage) {
        this.logger.error(`Preview image details - Name: ${previewImage?.originalname || 'unknown'}, Size: ${previewImage?.size || 'unknown'}, Type: ${previewImage?.mimetype || 'unknown'}`);
      }
      this.logger.error(`Update parameters: ${JSON.stringify(updateStudentDiarySkinDto)}`);

      if (error.stack) {
        this.logger.error(`Stack trace: ${error.stack}`);
      }

      // Throw a more descriptive error
      throw new BadRequestException(`Failed to update student diary skin: ${error.message}. Check server logs for more details.`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Delete a student diary skin
   * @param skinId The ID of the skin to delete
   * @param studentId The ID of the student deleting the skin
   */
  async deleteStudentDiarySkin(skinId: string, studentId: string): Promise<void> {
    // Check if the skin is in use
    const isInUse = await this.isSkinInUse(skinId);
    if (isInUse) {
      throw new BadRequestException('Cannot delete skin as it is being used by diaries or diary entries');
    }

    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the skin
      const skin = await queryRunner.manager.findOne(StudentDiarySkin, {
        where: { id: skinId, studentId },
      });

      if (!skin) {
        throw new NotFoundException(`Student diary skin with ID ${skinId} not found or does not belong to student ${studentId}`);
      }

      // Delete registry entries from database (student diary skin registry)
      await queryRunner.manager.delete(StudentDiarySkinRegistry, { studentDiarySkinId: skinId });

      // Delete the skin from the database
      await queryRunner.manager.remove(skin);

      // Commit the transaction
      await queryRunner.commitTransaction();
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(`Failed to delete student diary skin: ${error.message}`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Delete a diary skin as an admin
   * @param id The ID of the skin to delete
   * @param adminId The ID of the admin performing the deletion
   */
  async deleteDiarySkinAsAdmin(id: string, adminId: string): Promise<void> {
    // First find the skin to check admin permissions
    const skin = await this.diarySkinRepository.findOne({
      where: { id },
    });

    if (!skin) {
      throw new NotFoundException(`Diary skin with ID ${id} not found`);
    }

    // Check if the admin is the creator of the skin
    if (skin.createdById && skin.createdById !== adminId) {
      throw new ForbiddenException(`You don't have permission to delete this skin`);
    }

    // Perform the actual deletion
    await this.deleteDiarySkin(id);
  }

  /**
   * Filter admin skins to only include those accessible to a student
   * (free skins + purchased skins)
   * @param adminSkins All admin skins
   * @param studentId Student ID
   * @returns Filtered list of accessible admin skins
   */
  private async filterAccessibleAdminSkins(adminSkins: DiarySkin[], studentId: string): Promise<DiarySkin[]> {
    const accessibleSkins: DiarySkin[] = [];

    for (const skin of adminSkins) {
      const isFree = await this.isAdminSkinFree(skin.id);
      const isPurchased = await this.isAdminSkinPurchasedByStudent(skin.id, studentId);

      if (isFree || isPurchased) {
        accessibleSkins.push(skin);
      }
    }

    return accessibleSkins;
  }

  /**
   * Check if an admin skin is free (not linked to any shop item or linked to a free shop item)
   * @param skinId Admin skin ID
   * @returns True if the skin is free
   */
  private async isAdminSkinFree(skinId: string): Promise<boolean> {
    try {
      // Check if the skin is linked to a shop item
      const shopMapping = await this.shopSkinMappingRepository.findOne({
        where: { diarySkinId: skinId },
        relations: ['shopItem'],
      });

      if (!shopMapping) {
        // If not linked to any shop item, it's free
        return true;
      }

      // Check if the linked shop item is free
      return shopMapping.shopItem.type === ShopItemType.FREE;
    } catch (error) {
      this.logger.error(`Error checking if admin skin ${skinId} is free: ${error.message}`);
      // Default to free if there's an error
      return true;
    }
  }

  /**
   * Check if an admin skin is purchased by a student
   * @param skinId Admin skin ID
   * @param studentId Student ID
   * @returns True if the student has purchased this skin
   */
  private async isAdminSkinPurchasedByStudent(skinId: string, studentId: string): Promise<boolean> {
    try {
      // Find the shop item linked to this skin
      const shopMapping = await this.shopSkinMappingRepository.findOne({
        where: { diarySkinId: skinId },
      });

      if (!shopMapping) {
        // If not linked to any shop item, consider it as not purchased (but free)
        return false;
      }

      // Check if the student owns this shop item
      const ownedItem = await this.studentOwnedItemRepository.findOne({
        where: {
          studentId: studentId,
          shopItemId: shopMapping.shopItemId,
        },
      });

      return !!ownedItem;
    } catch (error) {
      this.logger.error(`Error checking if student ${studentId} purchased skin ${skinId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Set default diary skin for a user
   * @param userId User ID
   * @param skinId Diary skin ID
   */
  async setDefaultDiarySkin(userId: string, skinId: string): Promise<void> {
    // Validate that the user has access to this skin
    await this.validateStudentSkinAccess(userId, skinId);

    // Update user's default diary skin
    const userRepository = this.dataSource.getRepository(User);
    await userRepository.update(userId, {
      defaultDiarySkinId: skinId,
    });

    // Also update the diary table's defaultSkinId for compatibility
    const diaryRepository = this.dataSource.getRepository(Diary);
    await diaryRepository.update(
      { userId },
      {
        defaultSkinId: skinId,
      },
    );

    this.logger.log(`Updated default diary skin for user ${userId} to ${skinId} (both user and diary tables)`);
  }

  /**
   * Get default diary skin for a user
   * @param userId User ID
   * @returns Default diary skin or null if not set
   */
  async getDefaultDiarySkin(userId: string): Promise<DiarySkinResponseDto | null> {
    const userRepository = this.dataSource.getRepository(User);
    const user = await userRepository.findOne({
      where: { id: userId },
      relations: ['defaultDiarySkin'],
    });

    if (!user?.defaultDiarySkin) {
      return null;
    }

    // Use existing skin mapping logic
    const registryUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, user.defaultDiarySkin.id);

    // Check if the skin is in use
    const isUsedIn = await this.isSkinInUse(user.defaultDiarySkin.id);

    // Check purchase status
    const isPurchased = await this.isAdminSkinPurchasedByStudent(user.defaultDiarySkin.id, userId);
    const isFree = await this.isAdminSkinFree(user.defaultDiarySkin.id);

    return {
      id: user.defaultDiarySkin.id,
      name: user.defaultDiarySkin.name,
      description: user.defaultDiarySkin.description,
      previewImagePath: registryUrl,
      isActive: user.defaultDiarySkin.isActive,
      isGlobal: true,
      createdById: user.defaultDiarySkin.createdById,
      templateContent: user.defaultDiarySkin.templateContent,
      isUsedIn,
      isFree,
      isPurchased,
      isUserDefaultDiary: true,
      isUserDefaultNovel: false,
    };
  }

  /**
   * Validate that a student has access to a specific skin
   * @param studentId Student ID
   * @param skinId Skin ID
   * @throws ForbiddenException if student doesn't have access
   */
  async validateStudentSkinAccess(studentId: string, skinId: string): Promise<void> {
    // Check if it's an admin skin
    const adminSkin = await this.diarySkinRepository.findOne({ where: { id: skinId } });
    if (adminSkin) {
      const isFree = await this.isAdminSkinFree(skinId);

      // Free skins can always be used
      if (isFree) {
        return;
      }

      // Paid skins require purchase validation
      const isPurchased = await this.isAdminSkinPurchasedByStudent(skinId, studentId);
      if (!isPurchased) {
        throw new ForbiddenException('You do not have access to this skin. Please purchase it first.');
      }
      return;
    }

    // Check if it's a student's own skin
    const studentSkin = await this.studentDiarySkinRepository.findOne({
      where: { id: skinId, studentId },
    });
    if (studentSkin) {
      return;
    }

    throw new NotFoundException('Skin not found or you do not have access to it');
  }

  /**
   * Toggle the active status of an admin diary skin
   * @param skinId The ID of the skin to toggle
   * @param toggleSkinStatusDto The new status data
   * @returns The updated skin
   */
  async toggleAdminSkinStatus(skinId: string, toggleSkinStatusDto: ToggleSkinStatusDto): Promise<DiarySkinResponseDto> {
    try {
      // Find the admin skin
      const skin = await this.diarySkinRepository.findOne({
        where: { id: skinId },
      });

      if (!skin) {
        throw new NotFoundException(`Admin diary skin with ID ${skinId} not found`);
      }

      // Update the active status
      skin.isActive = toggleSkinStatusDto.isActive;
      const updatedSkin = await this.diarySkinRepository.save(skin);

      // Generate registry URL for preview image
      const registryUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, updatedSkin.id);

      // Check if the skin is in use
      const isUsedIn = await this.isSkinInUse(updatedSkin.id);

      this.logger.log(`Admin skin ${skinId} status updated to ${toggleSkinStatusDto.isActive ? 'active' : 'inactive'}`);

      return {
        id: updatedSkin.id,
        name: updatedSkin.name,
        description: updatedSkin.description,
        previewImagePath: registryUrl,
        isActive: updatedSkin.isActive,
        isGlobal: true,
        createdById: updatedSkin.createdById,
        templateContent: updatedSkin.templateContent,
        isUsedIn,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error(`Error toggling admin skin status: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to update skin status: ${error.message}`);
    }
  }

  /**
   * Toggle the active status of a student diary skin
   * @param skinId The ID of the skin to toggle
   * @param studentId The ID of the student who owns the skin
   * @param toggleSkinStatusDto The new status data
   * @returns The updated skin
   */
  async toggleStudentSkinStatus(skinId: string, studentId: string, toggleSkinStatusDto: ToggleSkinStatusDto): Promise<DiarySkinResponseDto> {
    try {
      // Find the student skin
      const skin = await this.studentDiarySkinRepository.findOne({
        where: { id: skinId, studentId },
      });

      if (!skin) {
        throw new NotFoundException(`Student diary skin with ID ${skinId} not found or does not belong to student ${studentId}`);
      }

      // Update the active status
      skin.isActive = toggleSkinStatusDto.isActive;
      const updatedSkin = await this.studentDiarySkinRepository.save(skin);

      // Generate registry URL for preview image
      const registryUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STUDENT_DIARY_SKIN, updatedSkin.id);

      // Check if the skin is in use
      const isUsedIn = await this.isSkinInUse(updatedSkin.id);

      this.logger.log(`Student skin ${skinId} status updated to ${toggleSkinStatusDto.isActive ? 'active' : 'inactive'} for student ${studentId}`);

      return {
        id: updatedSkin.id,
        name: updatedSkin.name,
        description: updatedSkin.description,
        previewImagePath: registryUrl,
        isActive: updatedSkin.isActive,
        isGlobal: false,
        studentId: updatedSkin.studentId,
        templateContent: updatedSkin.templateContent,
        isUsedIn,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error(`Error toggling student skin status: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to update skin status: ${error.message}`);
    }
  }
}
