import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { UserType } from '../../database/entities/user.entity';
import { Messages } from '../../constants/messages';

@Injectable()
export class Tutor<PERSON><PERSON> implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException(Messages.UNAUTHORIZED);
    }

    // Check if user has tutor type
    if (user.type === UserType.TUTOR) {
      return true;
    }

    // Check if user has tutor role
    if (user.roles && Array.isArray(user.roles)) {
      if (user.roles.includes('tutor')) {
        return true;
      }
    }

    // Ad<PERSON> can also access tutor endpoints
    if (user.type === UserType.ADMIN || (user.roles && user.roles.includes('admin'))) {
      return true;
    }

    throw new ForbiddenException(Messages.FORBIDDEN);
  }
}
