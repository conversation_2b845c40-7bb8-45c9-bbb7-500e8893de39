import { Injectable, NotFoundException, ForbiddenException, ConflictException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DiaryEntryFriendShare } from '../../database/entities/diary-entry-friend-share.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { Diary } from '../../database/entities/diary.entity';
import { User } from '../../database/entities/user.entity';
import { StudentFriendship, FriendshipStatus } from '../../database/entities/student-friendship.entity';
import { ChatService } from '../chat/chat.service';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { MessageType } from '../../database/entities/message.entity';
import { v4 as uuidv4 } from 'uuid';

import { ShareDiaryEntryWithFriendDto, DiaryFriendShareResponseDto, DiaryFriendShareDetailsDto } from '../../database/models/diary-friend-share.dto';
import { NotificationType } from '../../database/entities/notification.entity';

@Injectable()
export class DiaryFriendShareService {
  private readonly logger = new Logger(DiaryFriendShareService.name);

  constructor(
    @InjectRepository(DiaryEntryFriendShare)
    private diaryFriendShareRepository: Repository<DiaryEntryFriendShare>,
    @InjectRepository(DiaryEntry)
    private diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(Diary)
    private diaryRepository: Repository<Diary>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(StudentFriendship)
    private studentFriendshipRepository: Repository<StudentFriendship>,
    private chatService: ChatService,
    private deeplinkService: DeeplinkService,
    private notificationHelperService: NotificationHelperService,
  ) {}

  /**
   * Share a diary entry with a friend
   * @param entryId The ID of the diary entry to share
   * @param sharerId The ID of the user sharing the entry
   * @param shareDto Sharing details
   * @returns Share response with chat message and notification details
   */
  async shareDiaryEntryWithFriend(entryId: string, sharerId: string, shareDto: ShareDiaryEntryWithFriendDto): Promise<DiaryFriendShareResponseDto> {
    try {
      this.logger.log(`User ${sharerId} sharing diary entry ${entryId} with user ${shareDto.targetUserId}`);

      // 1. Validate the diary entry and ownership
      const entry = await this.validateEntryOwnership(entryId, sharerId);

      // 2. Validate friendship
      const friendship = await this.validateFriendship(sharerId, shareDto.targetUserId);

      // 3. Get user details
      const [sharer, targetUser] = await Promise.all([this.userRepository.findOne({ where: { id: sharerId } }), this.userRepository.findOne({ where: { id: shareDto.targetUserId } })]);

      if (!sharer || !targetUser) {
        throw new NotFoundException('User not found');
      }

      // 4. Check for existing active share
      await this.checkExistingShare(entryId, sharerId, shareDto.targetUserId);

      // 5. Create share record
      const shareRecord = await this.createShareRecord(entryId, sharerId, shareDto);

      // 6. Generate deep link
      const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.DIARY_FRIEND_SHARE, {
        id: entryId,
      });

      // 7. Generate and send chat message with HTML content
      const chatMessage = await this.sendChatMessage(sharer, targetUser, entry, deepLink);

      // 8. Update share record with chat message ID
      shareRecord.chatMessageId = chatMessage.id;
      await this.diaryFriendShareRepository.save(shareRecord);

      // 9. Send notification
      const notificationSent = await this.sendNotification(sharer, targetUser, entry, deepLink);

      // 10. Prepare response
      const shareDetails: DiaryFriendShareDetailsDto = {
        id: shareRecord.id,
        isActive: shareRecord.isActive,
        createdAt: shareRecord.createdAt,
        chatMessageId: shareRecord.chatMessageId,
      };

      return {
        shareDetails,
        deepLink,
        chatMessage,
        notificationSent,
      };
    } catch (error) {
      this.logger.error(`Error sharing diary entry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Validate diary entry ownership
   */
  private async validateEntryOwnership(entryId: string, sharerId: string): Promise<DiaryEntry> {
    const entry = await this.diaryEntryRepository.findOne({
      where: { id: entryId },
      relations: ['diary'],
    });

    if (!entry) {
      throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
    }

    if (entry.diary.userId !== sharerId) {
      throw new ForbiddenException('You do not have permission to share this diary entry');
    }

    return entry;
  }

  /**
   * Validate friendship - bidirectional sharing for accepted friends
   */
  private async validateFriendship(sharerId: string, targetUserId: string): Promise<StudentFriendship> {
    if (sharerId === targetUserId) {
      throw new ForbiddenException('You cannot share a diary entry with yourself');
    }

    // ENHANCEMENT: Allow sharing between any accepted friends, regardless of canViewDiary setting
    // Once users are friends, they should be able to share diary entries with each other
    const friendship = await this.studentFriendshipRepository.findOne({
      where: [
        { requesterId: sharerId, requestedId: targetUserId, status: FriendshipStatus.ACCEPTED },
        { requesterId: targetUserId, requestedId: sharerId, status: FriendshipStatus.ACCEPTED },
      ],
    });

    if (!friendship) {
      throw new ForbiddenException('You can only share diary entries with accepted friends');
    }

    return friendship;
  }

  /**
   * Check for existing active share
   */
  private async checkExistingShare(entryId: string, sharerId: string, targetUserId: string): Promise<void> {
    const existingShare = await this.diaryFriendShareRepository.findOne({
      where: {
        diaryEntryId: entryId,
        sharedById: sharerId,
        sharedWithId: targetUserId,
        isActive: true,
      },
    });

    if (existingShare) {
      throw new ConflictException('This diary entry has already been shared with this friend');
    }
  }

  /**
   * Create share record
   */
  private async createShareRecord(entryId: string, sharerId: string, shareDto: ShareDiaryEntryWithFriendDto): Promise<DiaryEntryFriendShare> {
    const shareToken = uuidv4();

    const shareRecord = this.diaryFriendShareRepository.create({
      diaryEntryId: entryId,
      sharedById: sharerId,
      sharedWithId: shareDto.targetUserId,
      message: null,
      expiryDate: null,
      isActive: true,
    });

    return await this.diaryFriendShareRepository.save(shareRecord);
  }

  /**
   * Send chat message with HTML content
   */
  private async sendChatMessage(sharer: User, targetUser: User, entry: DiaryEntry, deepLink: string) {
    const htmlContent = this.generateDiaryShareHtmlContent(sharer, entry, deepLink);

    return await this.chatService.sendMessage(sharer.id, {
      recipientId: targetUser.id,
      type: MessageType.TEXT,
      content: htmlContent,
      metadata: {
        type: 'diary_share',
        diaryEntryId: entry.id,
        shareToken: deepLink.split('/').pop(),
        deepLink: deepLink,
      },
    });
  }

  /**
   * Generate rich HTML content for chat message
   */
  private generateDiaryShareHtmlContent(sharer: User, entry: DiaryEntry, deepLink: string): string {
    const entryPreview = entry.content && entry.content.length > 50 ? entry.content.substring(0, 50) + '...' : entry.content || '';

    // Simplified but attractive HTML that should work within content limits
    return `
<div style="border: 1px solid #ccc; border-radius: 8px; padding: 16px; max-width: 420px; background-color: #ffffff; font-family: Arial, sans-serif; color: #222;">
  <div style="background-color: #2f70c9; color: #ffffff; padding: 12px; border-radius: 6px; text-align: center; margin-bottom: 12px;">
    <div style="font-size: 22px;">📖</div>
    <div style="font-weight: 600; font-size: 16px;">${this.escapeHtml(sharer.name)} shared a diary entry</div>
  </div>

  <div style="margin-bottom: 8px; font-size: 16px;">
    <strong>"${this.escapeHtml(entry.title)}"</strong>
  </div>

  <div style="color: #444; font-size: 14px; margin-bottom: 12px;">
    📅 ${this.formatDate(entry.entryDate)}
  </div>

  ${
    entryPreview
      ? `
    <div style="background-color: #f0f2f5; color: #333; padding: 10px; border-radius: 4px; font-size: 14px; margin-bottom: 12px; line-height: 1.4;">
      ${this.escapeHtml(entryPreview)}
    </div>
  `
      : ''
  }

  <div style="text-align: center; margin-top: 8px;">
    <a href="${deepLink}" style="background-color: #2f70c9; color: #ffffff; padding: 10px 20px; border-radius: 20px; text-decoration: none; font-weight: bold; font-size: 14px; display: inline-block;">
      📖 Open Entry
    </a>
  </div>

  <div style="text-align: center; color: #777; font-size: 12px; margin-top: 16px;">
    Shared on ${this.formatDateTime(new Date())}
  </div>
</div>
`.trim();
  }

  /**
   * Send notification to the target user
   */
  private async sendNotification(sharer: User, targetUser: User, entry: DiaryEntry, deepLink: string): Promise<boolean> {
    try {
      await this.notificationHelperService.notify(targetUser.id, NotificationType.DIARY_FRIEND_SHARE, `${sharer.name} shared a diary entry with you`, `"${entry.title}" - Click to view`, {
        relatedEntityId: entry.id,
        relatedEntityType: 'diary_friend_share',
        htmlContent: `
            <p><strong>${sharer.name}</strong> shared a diary entry with you:</p>
            <p><strong>"${entry.title}"</strong></p>
            <p>Date: ${this.formatDate(entry.entryDate)}</p>
            <p><a href="${deepLink}">Click here to view the diary entry</a></p>
          `,
      });
      return true;
    } catch (error) {
      this.logger.error(`Failed to send notification: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Escape HTML to prevent XSS
   */
  private escapeHtml(text: string): string {
    if (!text) return '';
    return text.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#39;');
  }

  /**
   * Format date for display
   */
  private formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(new Date(date));
  }

  /**
   * Format date and time for display
   */
  private formatDateTime(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  }
}
