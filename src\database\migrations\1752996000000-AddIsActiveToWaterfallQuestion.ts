import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddIsActiveToWaterfallQuestion1752996000000 implements MigrationInterface {
  name = 'AddIsActiveToWaterfallQuestion1752996000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add is_active column to waterfall_question table
    await queryRunner.addColumn(
      'waterfall_question',
      new TableColumn({
        name: 'is_active',
        type: 'boolean',
        default: true,
        isNullable: false,
      }),
    );

    // Update existing questions to be active by default
    await queryRunner.query(`UPDATE waterfall_question SET is_active = true WHERE is_active IS NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove is_active column from waterfall_question table
    await queryRunner.dropColumn('waterfall_question', 'is_active');
  }
}
