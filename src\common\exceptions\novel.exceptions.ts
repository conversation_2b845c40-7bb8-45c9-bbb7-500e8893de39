import { HttpException, HttpStatus } from '@nestjs/common';

/**
 * Exception thrown when a student tries to access novel functionality without setting a tutor greeting
 */
export class NovelTutorGreetingRequiredException extends HttpException {
  constructor(message: string = 'Please set a greeting message for your tutor before accessing novel entries. This is required once for the Novel module.') {
    super(
      {
        message,
        error: 'NOVEL_TUTOR_GREETING_REQUIRED',
        statusCode: HttpStatus.PRECONDITION_REQUIRED,
      },
      HttpStatus.PRECONDITION_REQUIRED,
    );
  }
}

/**
 * Exception thrown when a student tries to create novel entries without setting a default skin
 */
export class NovelDefaultSkinRequiredException extends HttpException {
  constructor(message: string = 'Please set a default skin for the Novel module before creating entries.') {
    super(
      {
        message,
        error: 'NOVEL_DEFAULT_SKIN_REQUIRED',
        statusCode: HttpStatus.PRECONDITION_REQUIRED,
      },
      HttpStatus.PRECONDITION_REQUIRED,
    );
  }
}
