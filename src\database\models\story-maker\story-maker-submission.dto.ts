import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID, IsOptional, IsN<PERSON>ber, IsPositive, IsBoolean } from 'class-validator';

/**
 * DTO for student to submit a story
 */
export class CreateStoryMakerSubmissionDto {
  @ApiProperty({
    description: 'The ID of the story maker to submit for',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty({ message: 'Story maker ID is required' })
  story_maker_id: string;

  @ApiProperty({
    description: 'The content of the story in rich text format',
    example: '<p>Once upon a time in a magical forest...</p>',
  })
  @IsString()
  @IsNotEmpty({ message: 'Content is required' })
  content: string;
}

/**
 * DTO for viewing a submission
 */
export class StoryMakerSubmissionResponseDto {
  @ApiProperty({
    description: 'The ID of the submission',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The ID of the participation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  participation_id: string;

  @ApiProperty({
    description: 'The content of the submitted story in rich text format',
    example: '<p>Once upon a time in a magical forest...</p>',
  })
  content: string;

  @ApiProperty({
    description: 'When the story was submitted',
    example: '2023-01-01T00:00:00.000Z',
  })
  submitted_at: Date;

  @ApiProperty({
    description: 'Whether the submission has been evaluated',
    example: false,
  })
  is_evaluated: boolean;

  @ApiPropertyOptional({
    description: 'The evaluation for this submission (if evaluated)',
    type: () => StoryMakerEvaluationResponseDto,
  })
  evaluation?: StoryMakerEvaluationResponseDto;

  @ApiProperty({
    description: 'When the submission was created',
    example: '2023-01-01T00:00:00.000Z',
  })
  created_at: Date;
}

/**
 * DTO for viewing an evaluation
 */
export class StoryMakerEvaluationResponseDto {
  @ApiProperty({
    description: 'The ID of the evaluation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The ID of the submission',
    example: '123e4567-e89b-12d3-a456-************',
  })
  submission_id: string;

  @ApiProperty({
    description: 'The ID of the tutor who evaluated the submission',
    example: '123e4567-e89b-12d3-a456-************',
  })
  tutor_id: string;

  @ApiPropertyOptional({
    description: 'The name of the tutor who evaluated the submission',
    example: 'John Doe',
  })
  tutor_name?: string;

  @ApiPropertyOptional({
    description: 'The profile picture of the tutor who evaluated the submission',
    example: 'https://example.com/images/profile.jpg',
  })
  tutor_profile_picture?: string;

  @ApiPropertyOptional({
    description: 'Corrections to the submission',
    example: '<p>Once upon a time in a magical forest, there lived a wise old owl...</p>',
  })
  corrections?: string;

  @ApiPropertyOptional({
    description: 'Feedback on the submission',
    example: 'Great use of descriptive language and creative storyline.',
  })
  feedback?: string;

  @ApiProperty({
    description: 'When the submission was evaluated',
    example: '2023-01-02T00:00:00.000Z',
  })
  evaluated_at: Date;

  @ApiProperty({
    description: 'When the evaluation was created',
    example: '2023-01-02T00:00:00.000Z',
  })
  created_at: Date;
}

/**
 * DTO for creating an evaluation (for tutors)
 */
export class CreateStoryMakerEvaluationDto {
  @ApiProperty({
    description: 'The ID of the submission to evaluate',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty({ message: 'Submission ID is required' })
  submission_id: string;

  @ApiPropertyOptional({
    description: 'Corrections to the submission',
    example: '<p>Once upon a time in a magical forest, there lived a wise old owl...</p>',
  })
  @IsString()
  @IsOptional()
  corrections?: string;

  @ApiPropertyOptional({
    description: 'Feedback on the submission',
    example: 'Great use of descriptive language and creative storyline.',
  })
  @IsString()
  @IsOptional()
  feedback?: string;

  @ApiPropertyOptional({
    description: 'Score for the submission (only for first evaluation)',
    example: 45,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  score?: number;
}

/**
 * DTO for viewing a student's participation with submissions
 */
export class StoryMakerParticipationWithSubmissionsDto {
  @ApiProperty({
    description: 'The ID of the participation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The ID of the student',
    example: '123e4567-e89b-12d3-a456-************',
  })
  student_id: string;

  @ApiPropertyOptional({
    description: 'The name of the student',
    example: 'Jane Smith',
  })
  student_name?: string;

  @ApiPropertyOptional({
    description: 'The email of the student',
    example: '<EMAIL>',
  })
  student_email?: string;

  @ApiPropertyOptional({
    description: 'The profile picture of the student',
    example: 'https://example.com/images/profile.jpg',
  })
  student_profile_picture?: string;

  @ApiProperty({
    description: 'The ID of the story maker',
    example: '123e4567-e89b-12d3-a456-************',
  })
  story_maker_id: string;

  @ApiProperty({
    description: 'The title of the story maker',
    example: 'Adventure in the Forest',
  })
  story_maker_title: string;

  @ApiProperty({
    description: 'When the student first submitted',
    example: '2023-01-01T00:00:00.000Z',
  })
  first_submitted_at: Date;

  @ApiProperty({
    description: 'Whether the participation has been evaluated',
    example: true,
  })
  is_evaluated: boolean;

  @ApiPropertyOptional({
    description: 'The score assigned to the participation (only available after evaluation)',
    example: 45,
  })
  score?: number;

  @ApiPropertyOptional({
    description: 'When the participation was evaluated (only available after evaluation)',
    example: '2023-01-02T00:00:00.000Z',
  })
  evaluated_at?: Date;

  @ApiPropertyOptional({
    description: 'The ID of the evaluator (only available after evaluation)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  evaluated_by?: string;

  @ApiPropertyOptional({
    description: 'The name of the evaluator (only available after evaluation)',
    example: 'John Doe',
  })
  evaluator_name?: string;

  @ApiPropertyOptional({
    description: 'The profile picture of the evaluator (only available after evaluation)',
    example: 'https://example.com/images/profile.jpg',
  })
  evaluator_profile_picture?: string;

  @ApiProperty({
    description: 'List of submissions for this participation',
    type: [StoryMakerSubmissionResponseDto],
  })
  submissions: StoryMakerSubmissionResponseDto[];

  @ApiProperty({
    description: 'When the participation was created',
    example: '2023-01-01T00:00:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'When the participation was last updated',
    example: '2023-01-02T00:00:00.000Z',
  })
  updated_at: Date;
}
