import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsInt, Min, Max, IsString, IsDateString, IsArray } from 'class-validator';
import { AwardModule, AwardFrequency } from '../entities/award.entity';
import { PaginationDto } from '../../common/models/pagination.dto';

/**
 * DTO for Hall of Fame query parameters
 */
export class HallOfFameQueryDto extends PaginationDto {
  @ApiProperty({
    example: AwardModule.DIARY,
    description: 'Filter by award module',
    enum: AwardModule,
    required: false,
  })
  @IsOptional()
  @IsEnum(AwardModule)
  module?: AwardModule;

  @ApiProperty({
    example: AwardFrequency.MONTHLY,
    description: 'Filter by award frequency',
    enum: AwardFrequency,
    required: false,
  })
  @IsOptional()
  @IsEnum(AwardFrequency)
  frequency?: AwardFrequency;

  @ApiProperty({
    example: 2024,
    description: 'Filter by year',
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(2020)
  @Max(2030)
  year?: number;

  @ApiProperty({
    example: 'Best Writer Award',
    description: 'Filter by award name (partial match)',
    required: false,
  })
  @IsOptional()
  @IsString()
  awardName?: string;

  @ApiProperty({
    example: 'John Doe',
    description: 'Filter by winner name (partial match)',
    required: false,
  })
  @IsOptional()
  @IsString()
  winnerName?: string;
}

/**
 * DTO for ongoing frequency awards query
 */
export class OngoingAwardsQueryDto {
  @ApiProperty({
    example: AwardModule.DIARY,
    description: 'Filter by award module',
    enum: AwardModule,
    required: false,
  })
  @IsOptional()
  @IsEnum(AwardModule)
  module?: AwardModule;

  @ApiProperty({
    example: AwardFrequency.MONTHLY,
    description: 'Filter by award frequency',
    enum: AwardFrequency,
    required: false,
  })
  @IsOptional()
  @IsEnum(AwardFrequency)
  frequency?: AwardFrequency;
}

/**
 * DTO for Hall of Fame winner entry
 */
export class HallOfFameWinnerDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  userName: string;

  @ApiProperty()
  userProfilePicture?: string;

  @ApiProperty()
  awardId: string;

  @ApiProperty()
  awardName: string;

  @ApiProperty({ enum: AwardModule })
  awardModule: AwardModule;

  @ApiProperty({ enum: AwardFrequency })
  awardFrequency: AwardFrequency;

  @ApiProperty()
  awardDate: Date;

  @ApiProperty()
  awardReason?: string;

  @ApiProperty()
  rewardPoints: number;

  @ApiProperty()
  metadata?: any;

  @ApiProperty()
  rank?: number; // Position in the hall of fame

  @ApiProperty()
  totalAwards?: number; // Total awards won by this user

  @ApiProperty()
  createdAt: Date;
}

/**
 * DTO for Hall of Fame response
 */
export class HallOfFameResponseDto {
  @ApiProperty({ type: [HallOfFameWinnerDto] })
  winners: HallOfFameWinnerDto[];

  @ApiProperty()
  totalWinners: number;

  @ApiProperty()
  currentPage: number;

  @ApiProperty()
  totalPages: number;

  @ApiProperty()
  itemsPerPage: number;

  @ApiProperty()
  filters: {
    module?: AwardModule;
    frequency?: AwardFrequency;
    year?: number;
    awardName?: string;
    winnerName?: string;
  };
}

/**
 * DTO for ongoing awards response
 */
export class OngoingAwardsResponseDto {
  @ApiProperty({ type: [HallOfFameWinnerDto] })
  currentWinners: HallOfFameWinnerDto[];

  @ApiProperty()
  periodInfo: {
    type: string; // 'current_month', 'current_week', etc.
    startDate: Date;
    endDate: Date;
    description: string;
  };

  @ApiProperty()
  totalWinners: number;

  @ApiProperty()
  filters: {
    module?: AwardModule;
    frequency?: AwardFrequency;
  };
}

/**
 * DTO for Hall of Fame statistics
 */
export class HallOfFameStatsDto {
  @ApiProperty()
  totalAwards: number;

  @ApiProperty()
  totalWinners: number;

  @ApiProperty()
  awardsByModule: {
    [key in AwardModule]: number;
  };

  @ApiProperty()
  awardsByFrequency: {
    [key in AwardFrequency]: number;
  };

  @ApiProperty()
  topWinners: {
    userId: string;
    userName: string;
    userProfilePicture?: string;
    totalAwards: number;
    modules: AwardModule[];
  }[];

  @ApiProperty()
  recentAwards: HallOfFameWinnerDto[];

  @ApiProperty()
  periodCovered: {
    startDate: Date;
    endDate: Date;
  };
}

/**
 * DTO for award type with frequencies
 */
export class AwardTypeDto {
  @ApiProperty()
  name: string;

  @ApiProperty({ enum: AwardFrequency, isArray: true })
  frequencies: AwardFrequency[];
}

/**
 * DTO for module-specific Hall of Fame
 */
export class ModuleHallOfFameDto {
  @ApiProperty({ enum: AwardModule })
  module: AwardModule;

  @ApiProperty()
  moduleName: string;

  @ApiProperty({ type: [HallOfFameWinnerDto] })
  winners: HallOfFameWinnerDto[];

  @ApiProperty()
  totalWinners: number;

  @ApiProperty({ type: [AwardTypeDto], description: 'List of award types with their configured frequencies for this module' })
  awardTypes: AwardTypeDto[];

  @ApiProperty()
  topPerformer?: {
    userId: string;
    userName: string;
    userProfilePicture?: string;
    totalAwards: number;
    latestAward: HallOfFameWinnerDto;
  };
}
