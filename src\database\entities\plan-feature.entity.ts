import { Entity, Column, ManyToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { Plan } from './plan.entity';

export enum FeatureType {
  HEC_USER_DIARY = 'hec_user_diary',
  HEC_PLAY = 'hec_play',
  ENGLISH_QA_WRITING = 'english_qa_writing',
  ENGLISH_ESSAY = 'english_essay',
  ENGLISH_NOVEL = 'english_novel',
  MODULE = 'module',
}

@Entity()
export class PlanFeature extends AuditableBaseEntity {
  @Column({
    name: 'type',
    type: 'enum',
    enum: FeatureType,
    unique: true,
  })
  type: FeatureType;

  @Column({ name: 'name' })
  name: string;

  @Column({ name: 'description', type: 'text' })
  description: string;

  @ManyToMany(() => Plan, (plan) => plan.planFeatures)
  plans: Plan[];

  // Helper method to get a simple representation of this feature
  toSimpleObject() {
    return {
      id: this.id,
      type: this.type,
      name: this.name,
      description: this.description,
    };
  }
}
