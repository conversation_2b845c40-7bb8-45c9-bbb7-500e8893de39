import { MigrationInterface, QueryRunner } from 'typeorm';

export class EssaySkin1747807452724 implements MigrationInterface {
  name = 'EssaySkin1747807452724';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_fac44bb71949efc3e4ce2eac6f7"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD "title" text`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD "submission_skin" uuid`);
    await queryRunner.query(
      `ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_94a4ca8a8ec4b6919b370024c02" FOREIGN KEY ("submission_skin") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_94a4ca8a8ec4b6919b370024c02"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP COLUMN "submission_skin"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP COLUMN "title"`);
    await queryRunner.query(
      `ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_fac44bb71949efc3e4ce2eac6f7" FOREIGN KEY ("submission_skin_id") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
