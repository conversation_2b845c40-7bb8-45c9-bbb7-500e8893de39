import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminDashboardService } from './admin-dashboard.service';
import { TutorDashboardService } from './tutor-dashboard.service';
import { User, UserType } from '../../database/entities/user.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { Plan } from '../../database/entities/plan.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { DiaryEntryAttendance, AttendanceStatus } from '../../database/entities/diary-entry-attendance.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { MissionDiaryEntry } from '../../database/entities/mission-diary-entry.entity';
import { NovelEntry } from '../../database/entities/novel-entry.entity';
import { QATaskSubmissions } from '../../database/entities/qa-task-submissions.entity';
import { QAMissionTasks } from '../../database/entities/qa-mission-tasks.entity';
import { EssayTaskSubmissions } from '../../database/entities/essay-task-submissions.entity';
import { EssayMissionTasks } from '../../database/entities/essay-mission-tasks.entity';
import { DiaryFeedback } from '../../database/entities/diary-feedback.entity';
import { DiaryCorrection } from '../../database/entities/diary-correction.entity';
import { MissionDiaryEntryFeedback } from '../../database/entities/mission-diary-entry-feedback.entity';
import { NovelFeedback } from '../../database/entities/novel-feedback.entity';
import { NovelCorrection } from '../../database/entities/novel-correction.entity';
import { QATaskSubmissionMarking } from '../../database/entities/qa-task-submission-marking.entity';
import { EssayTaskSubmissionMarking } from '../../database/entities/essay-task-submission-marking.entity';

describe('Dashboard Data Consistency Tests', () => {
  let adminDashboardService: AdminDashboardService;
  let tutorDashboardService: TutorDashboardService;
  let userRepository: Repository<User>;
  let attendanceRepository: Repository<DiaryEntryAttendance>;
  let mappingRepository: Repository<StudentTutorMapping>;

  const mockQueryBuilder = {
    createQueryBuilder: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    addSelect: jest.fn().mockReturnThis(),
    groupBy: jest.fn().mockReturnThis(),
    getCount: jest.fn(),
    getRawMany: jest.fn(),
    getRawOne: jest.fn(),
    getMany: jest.fn(),
  };

  const createMockRepository = () => ({
    createQueryBuilder: jest.fn(() => mockQueryBuilder),
    count: jest.fn(),
    find: jest.fn(),
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminDashboardService,
        TutorDashboardService,
        {
          provide: getRepositoryToken(User),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(UserPlan),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(Plan),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(PlanFeature),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(StudentTutorMapping),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(DiaryEntryAttendance),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(DiaryEntry),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(MissionDiaryEntry),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(NovelEntry),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(QATaskSubmissions),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(QAMissionTasks),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(EssayTaskSubmissions),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(EssayMissionTasks),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(DiaryFeedback),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(DiaryCorrection),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(MissionDiaryEntryFeedback),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(NovelFeedback),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(NovelCorrection),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(QATaskSubmissionMarking),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(EssayTaskSubmissionMarking),
          useValue: createMockRepository(),
        },
      ],
    }).compile();

    adminDashboardService = module.get<AdminDashboardService>(AdminDashboardService);
    tutorDashboardService = module.get<TutorDashboardService>(TutorDashboardService);
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    attendanceRepository = module.get<Repository<DiaryEntryAttendance>>(getRepositoryToken(DiaryEntryAttendance));
    mappingRepository = module.get<Repository<StudentTutorMapping>>(getRepositoryToken(StudentTutorMapping));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Admin Dashboard Consistency', () => {
    it('should ensure total students equals present + absent in attendance stats', async () => {
      // Mock active student count
      const totalActiveStudents = 100;
      mockQueryBuilder.getCount.mockResolvedValueOnce(totalActiveStudents);

      // Mock attendance stats - only 80 students have attendance records
      const attendanceStats = [
        { status: AttendanceStatus.PRESENT, count: '50' },
        { status: AttendanceStatus.ABSENT, count: '30' },
      ];
      mockQueryBuilder.getRawMany.mockResolvedValueOnce(attendanceStats);

      const result = await adminDashboardService.getTodayAttendanceStats();

      expect(result.totalStudents).toBe(totalActiveStudents);
      expect(result.presentCount).toBe(50);
      expect(result.absentCount).toBe(50); // 30 marked absent + 20 without attendance
      expect(result.presentCount + result.absentCount).toBe(result.totalStudents);
      expect(result.attendancePercentage).toBe(50); // 50/100 * 100
    });

    it('should handle case where all students have attendance records', async () => {
      const totalActiveStudents = 100;
      mockQueryBuilder.getCount.mockResolvedValueOnce(totalActiveStudents);

      const attendanceStats = [
        { status: AttendanceStatus.PRESENT, count: '70' },
        { status: AttendanceStatus.ABSENT, count: '30' },
      ];
      mockQueryBuilder.getRawMany.mockResolvedValueOnce(attendanceStats);

      const result = await adminDashboardService.getTodayAttendanceStats();

      expect(result.totalStudents).toBe(totalActiveStudents);
      expect(result.presentCount).toBe(70);
      expect(result.absentCount).toBe(30);
      expect(result.presentCount + result.absentCount).toBe(result.totalStudents);
      expect(result.attendancePercentage).toBe(70);
    });
  });

  describe('Tutor Dashboard Consistency', () => {
    const tutorId = 'tutor-123';

    it('should ensure total students equals present + absent in attendance stats', async () => {
      // Mock assigned student count
      const totalAssignedStudents = 25;
      mockQueryBuilder.getRawOne.mockResolvedValueOnce({ count: totalAssignedStudents.toString() });

      // Mock attendance stats - only 20 students have attendance records
      const attendanceStats = [
        { status: AttendanceStatus.PRESENT, count: '15' },
        { status: AttendanceStatus.ABSENT, count: '5' },
      ];
      mockQueryBuilder.getRawMany.mockResolvedValueOnce(attendanceStats);

      const result = await tutorDashboardService.getTodayAttendanceStats(tutorId);

      expect(result.totalStudents).toBe(totalAssignedStudents);
      expect(result.presentCount).toBe(15);
      expect(result.absentCount).toBe(10); // 5 marked absent + 5 without attendance
      expect(result.presentCount + result.absentCount).toBe(result.totalStudents);
      expect(result.attendancePercentage).toBe(60); // 15/25 * 100
    });

    it('should handle case where all assigned students have attendance records', async () => {
      const totalAssignedStudents = 25;
      mockQueryBuilder.getRawOne.mockResolvedValueOnce({ count: totalAssignedStudents.toString() });

      const attendanceStats = [
        { status: AttendanceStatus.PRESENT, count: '20' },
        { status: AttendanceStatus.ABSENT, count: '5' },
      ];
      mockQueryBuilder.getRawMany.mockResolvedValueOnce(attendanceStats);

      const result = await tutorDashboardService.getTodayAttendanceStats(tutorId);

      expect(result.totalStudents).toBe(totalAssignedStudents);
      expect(result.presentCount).toBe(20);
      expect(result.absentCount).toBe(5);
      expect(result.presentCount + result.absentCount).toBe(result.totalStudents);
      expect(result.attendancePercentage).toBe(80);
    });

    it('should handle case with no assigned students', async () => {
      mockQueryBuilder.getRawOne.mockResolvedValueOnce({ count: '0' });

      const result = await tutorDashboardService.getTodayAttendanceStats(tutorId);

      expect(result.totalStudents).toBe(0);
      expect(result.presentCount).toBe(0);
      expect(result.absentCount).toBe(0);
      expect(result.attendancePercentage).toBe(0);
    });
  });
});
