import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, Unique } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';

@Entity('qa-assignment-sets')
@Unique(['setSequence'])
export class QAAssignmentSets extends AuditableBaseEntity {
  @Column()
  setSequence: number;

  @Column({ nullable: true })
  score: number;

  @Column({ type: 'text', nullable: true })
  instructions: string;
}
