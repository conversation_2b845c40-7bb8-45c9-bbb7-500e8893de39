import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { DiaryEntry } from './diary-entry.entity';
import { User } from './user.entity';

@Entity()
export class DiaryCorrection extends AuditableBaseEntity {
  @Column({ name: 'diary_entry_id' })
  diaryEntryId: string;

  @OneToOne(() => DiaryEntry, (entry) => entry.correction)
  @JoinColumn({ name: 'diary_entry_id' })
  diaryEntry: DiaryEntry;

  @Column({ name: 'tutor_id' })
  tutorId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'tutor_id' })
  tutor: User;

  @Column({ name: 'correction_text', type: 'text' })
  correctionText: string;

  @Column({ name: 'score', type: 'int' })
  score: number;

  @Column({ name: 'comments', type: 'text', nullable: true })
  comments: string;
}
