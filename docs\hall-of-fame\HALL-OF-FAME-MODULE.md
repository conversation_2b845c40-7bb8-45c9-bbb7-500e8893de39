# Hall of Fame Module

## Overview

The Hall of Fame module showcases award winners across all educational modules (Diary, Novel, Essay) with comprehensive filtering, pagination, and statistics features.

## Features

### 1. **Ongoing Awards** (Current Period)
- Shows current month/week award winners based on frequency
- Real-time view of recent achievements
- Filtered by module and frequency

### 2. **Historical Hall of Fame** (Paginated with Filters)
- Complete award history with pagination
- Advanced filtering by:
  - Modu<PERSON> (Diary, Novel, Essay)
  - Award frequency (Weekly, Monthly, Yearly)
  - Year
  - Award name (partial match)
  - Winner name (partial match)
- Sortable by various criteria

### 3. **Statistics Dashboard**
- Total awards and winners count
- Awards distribution by module and frequency
- Top 10 winners across all modules
- Recent awards activity
- Period coverage information

### 4. **Module-Specific Hall of Fame**
- Dedicated views for each module
- Top performer identification
- Award types listing
- Module-specific statistics

## API Endpoints

### Base URL: `/api/hall-of-fame`

#### 1. Get Hall of Fame (Paginated)
```
GET /hall-of-fame
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `module` (optional): Filter by module (diary, novel, essay)
- `frequency` (optional): Filter by frequency (weekly, monthly, yearly)
- `year` (optional): Filter by year
- `awardName` (optional): Filter by award name (partial match)
- `winnerName` (optional): Filter by winner name (partial match)
- `sortBy` (optional): Sort field (default: awardDate)
- `sortDirection` (optional): Sort direction (ASC/DESC, default: DESC)

**Response:**
```json
{
  "winners": [
    {
      "id": "uuid",
      "userId": "uuid",
      "userName": "John Doe",
      "userProfilePicture": "url",
      "awardId": "uuid",
      "awardName": "Best Writer Award",
      "awardModule": "diary",
      "awardFrequency": "monthly",
      "awardDate": "2024-01-31",
      "awardReason": "Achieved 95.5 points in Best Writer Award",
      "rewardPoints": 100,
      "metadata": {...},
      "rank": 1,
      "totalAwards": 5,
      "createdAt": "2024-01-31T10:00:00Z"
    }
  ],
  "totalWinners": 150,
  "currentPage": 1,
  "totalPages": 8,
  "itemsPerPage": 20,
  "filters": {
    "module": "diary",
    "year": 2024
  }
}
```

#### 2. Get Ongoing Awards (Current Period)
```
GET /hall-of-fame/ongoing
```

**Query Parameters:**
- `module` (optional): Filter by module
- `frequency` (optional): Filter by frequency

**Response:**
```json
{
  "currentWinners": [...],
  "periodInfo": {
    "type": "current_month",
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-01-31T23:59:59Z",
    "description": "Current Month (1/1/2024 - 1/31/2024)"
  },
  "totalWinners": 25,
  "filters": {
    "module": "diary"
  }
}
```

#### 3. Get Hall of Fame Statistics
```
GET /hall-of-fame/stats
```

**Response:**
```json
{
  "totalAwards": 500,
  "totalWinners": 150,
  "awardsByModule": {
    "diary": 200,
    "novel": 150,
    "essay": 150
  },
  "awardsByFrequency": {
    "weekly": 100,
    "monthly": 300,
    "yearly": 100
  },
  "topWinners": [
    {
      "userId": "uuid",
      "userName": "Jane Smith",
      "userProfilePicture": "url",
      "totalAwards": 15,
      "modules": ["diary", "novel", "essay"]
    }
  ],
  "recentAwards": [...],
  "periodCovered": {
    "startDate": "2023-01-01T00:00:00Z",
    "endDate": "2024-01-31T23:59:59Z"
  }
}
```

#### 4. Get Module-Specific Hall of Fame
```
GET /hall-of-fame/module/{module}
GET /hall-of-fame/diary
GET /hall-of-fame/novel
GET /hall-of-fame/essay
```

**Query Parameters:**
- `limit` (optional): Number of winners to return (default: 50)

**Response:**
```json
{
  "module": "diary",
  "moduleName": "Diary",
  "winners": [...],
  "totalWinners": 75,
  "awardTypes": [
    "Best Writer Award",
    "Best Perfect Award",
    "Best Designer Award",
    "Best Friendship Award"
  ],
  "topPerformer": {
    "userId": "uuid",
    "userName": "Top Student",
    "userProfilePicture": "url",
    "totalAwards": 8,
    "latestAward": {...}
  }
}
```

## Usage Examples

### Frontend Integration

#### 1. Display Current Month Winners
```typescript
// Get current month's award winners
const ongoingAwards = await fetch('/api/hall-of-fame/ongoing?frequency=monthly');
const data = await ongoingAwards.json();

// Display in a "Current Winners" section
data.currentWinners.forEach(winner => {
  console.log(`${winner.userName} won ${winner.awardName} in ${winner.awardModule}`);
});
```

#### 2. Paginated Hall of Fame with Filters
```typescript
// Get filtered and paginated results
const hallOfFame = await fetch('/api/hall-of-fame?module=diary&year=2024&page=1&limit=10');
const data = await hallOfFame.json();

// Display with pagination controls
console.log(`Showing ${data.winners.length} of ${data.totalWinners} winners`);
console.log(`Page ${data.currentPage} of ${data.totalPages}`);
```

#### 3. Module-Specific Dashboard
```typescript
// Get diary module Hall of Fame
const diaryHallOfFame = await fetch('/api/hall-of-fame/diary?limit=20');
const data = await diaryHallOfFame.json();

// Display top performer and recent winners
console.log(`Top Diary Performer: ${data.topPerformer.userName} with ${data.topPerformer.totalAwards} awards`);
console.log(`Available Award Types: ${data.awardTypes.join(', ')}`);
```

## Database Schema

The Hall of Fame module uses existing entities:
- **AwardWinner**: Main entity storing award winner records
- **Award**: Award definitions with module and frequency
- **User**: User information for winner details

## Performance Considerations

1. **Indexing**: Ensure proper database indexes on:
   - `award_date` for date-based queries
   - `user_id` for user-specific queries
   - `award_id` for award-specific queries

2. **Caching**: Consider caching for:
   - Statistics data (updated less frequently)
   - Top performers (relatively stable)
   - Module-specific data

3. **Pagination**: Always use pagination for large result sets

## Security

- All endpoints require authentication (JWT)
- Role-based access control (Admin, Tutor, Student can all view)
- No sensitive data exposure in public endpoints

## Future Enhancements

1. **Real-time Updates**: WebSocket integration for live updates
2. **Export Features**: PDF/Excel export of Hall of Fame data
3. **Advanced Analytics**: Trend analysis and performance metrics
4. **Social Features**: Comments and reactions on achievements
5. **Leaderboards**: Dynamic leaderboards with real-time rankings
