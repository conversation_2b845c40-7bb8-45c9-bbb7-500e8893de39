import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID, IsOptional, IsBoolean, IsString } from 'class-validator';

export class FreeSubscriptionDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Plan ID to subscribe to',
  })
  @IsNotEmpty({ message: 'Plan ID is required' })
  @IsUUID('4', { message: 'Plan ID must be a valid UUID' })
  planId: string;

  @ApiProperty({
    example: false,
    description: 'Auto-renewal setting',
    default: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Auto-renewal must be a boolean value' })
  autoRenew?: boolean = false;

  @ApiProperty({
    example: 'Development testing',
    description: 'Reason for free subscription (for audit purposes)',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Reason must be a string' })
  reason?: string;
}
