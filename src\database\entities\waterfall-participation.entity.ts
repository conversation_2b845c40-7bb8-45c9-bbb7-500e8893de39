import { Entity, Column, ManyToOne, OneToMany, <PERSON>inColum<PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { WaterfallSet } from './waterfall-set.entity';
import { User } from './user.entity';
import { WaterfallAnswer } from './waterfall-answer.entity';

@Entity()
export class WaterfallParticipation extends AuditableBaseEntity {
  @Column({ name: 'student_id', type: 'uuid' })
  studentId: string;

  @Column({ name: 'set_id', type: 'uuid' })
  setId: string;

  @Column({ name: 'total_correct_answers', type: 'int' })
  totalCorrectAnswers: number;

  @Column({ name: 'total_questions', type: 'int' })
  totalQuestions: number;

  @Column({ type: 'float' })
  score: number;

  // No need for submittedAt as createdAt from AuditableBaseEntity serves the same purpose

  // Relationships
  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  @ManyToOne(() => WaterfallSet)
  @JoinColumn({ name: 'set_id' })
  set: WaterfallSet;

  @OneToMany(() => WaterfallAnswer, (answer) => answer.participation, { cascade: true })
  answers: WaterfallAnswer[];
}
