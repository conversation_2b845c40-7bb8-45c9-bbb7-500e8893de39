import { Injectable } from '@nestjs/common';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { Notification, NotificationType } from '../../database/entities/notification.entity';

/**
 * Example service showing how to use the DeeplinkService in the NotificationService
 * This is not meant to be used directly, but as a reference for refactoring
 * the NotificationService to use the DeeplinkService.
 */
@Injectable()
export class NotificationDeeplinkExample {
  constructor(private readonly deeplinkService: DeeplinkService) {}

  /**
   * Generate a deep link URL for a notification based on its type and related entity
   * @param notification The notification to generate a deep link for
   * @returns A deep link URL that can be used to navigate to the relevant screen in the app
   */
  getDeepLinkForNotification(notification: Notification): string {
    return this.deeplinkService.getDeepLink(DeeplinkType.NOTIFICATION, {
      id: notification.relatedEntityId,
      notificationType: notification.type,
      relatedEntityType: notification.relatedEntityType,
    });
  }

  /**
   * Generate a web URL for a notification based on its type and related entity
   * @param notification The notification to generate a web URL for
   * @returns A web URL that can be used in email notifications
   */
  getWebUrlForNotification(notification: Notification): string {
    return this.deeplinkService.getWebLink(DeeplinkType.NOTIFICATION, {
      id: notification.relatedEntityId,
      notificationType: notification.type,
      relatedEntityType: notification.relatedEntityType,
    });
  }

  /**
   * Generate an HTML link for a notification
   * @param notification The notification
   * @param linkText The text to display in the link
   * @returns An HTML anchor tag
   */
  getNotificationLinkHtml(notification: Notification, linkText: string): string {
    return this.deeplinkService.getLinkHtml(DeeplinkType.NOTIFICATION, {
      id: notification.relatedEntityId,
      notificationType: notification.type,
      relatedEntityType: notification.relatedEntityType,
      linkText,
    });
  }

  /**
   * Generate a button-styled HTML link for a notification
   * @param notification The notification
   * @param buttonText The text to display on the button
   * @returns An HTML anchor tag styled as a button
   */
  getNotificationButtonHtml(notification: Notification, buttonText: string): string {
    return this.deeplinkService.getLinkHtml(DeeplinkType.NOTIFICATION, {
      id: notification.relatedEntityId,
      notificationType: notification.type,
      relatedEntityType: notification.relatedEntityType,
      linkText: buttonText,
      buttonStyle: true,
    });
  }
}
