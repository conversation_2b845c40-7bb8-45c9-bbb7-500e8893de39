import { Controller, Get, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { ShoppingCartService } from './shopping-cart.service';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { StudentGuard } from '../../common/guards/student.guard';
import { StrictStudentOnly } from '../../common/decorators/strict-student-only.decorator';
import { Request } from 'express';

// Define a custom interface to extend the Express Request type
interface RequestWithUser extends Request {
  user: { id: string; [key: string]: any };
}

@ApiTags('Reward Points')
@Controller('reward-points')
@UseGuards(JwtAuthGuard, StudentGuard)
@StrictStudentOnly()
@ApiBearerAuth('JWT-auth')
export class RewardPointsController {
  constructor(private readonly shoppingCartService: ShoppingCartService) {}

  @Get('balance')
  @ApiOperation({
    summary: 'Get reward points balance',
    description: "Get the current user's reward points balance",
  })
  @ApiOkResponseWithType(Number, 'Reward points balance retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getRewardPointsBalance(@Req() req: RequestWithUser): Promise<ApiResponse<number>> {
    const balance = await this.shoppingCartService.getUserRewardPoints(req.user['id']);
    return ApiResponse.success(balance, 'Reward points balance retrieved successfully');
  }
}
