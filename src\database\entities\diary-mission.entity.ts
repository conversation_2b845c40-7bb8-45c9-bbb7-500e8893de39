import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>any } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { MissionDiaryEntry } from './mission-diary-entry.entity';
import { Category } from './category.entity';

@Entity()
export class DiaryMission extends AuditableBaseEntity {
  @Column({ name: 'title' })
  title: string;

  @Column({ name: 'description', type: 'text' })
  description: string;

  @Column({ name: 'category_id', type: 'uuid', nullable: true })
  categoryId: string | null;

  @ManyToOne(() => Category, (category) => category.diaryMissions, { nullable: true })
  @JoinColumn({ name: 'category_id' })
  category: Category | null;

  @Column({ name: 'target_word_count', type: 'integer' })
  targetWordCount: number;

  @Column({ name: 'target_max_word_count', type: 'integer', nullable: true })
  targetMaxWordCount: number;

  @Column({ name: 'publish_date', type: 'timestamp' })
  publishDate: Date;

  @Column({ name: 'expiry_date', type: 'timestamp', nullable: true })
  expiryDate: Date;

  @Column({ name: 'admin_id', type: 'uuid' })
  adminId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'admin_id' })
  tutor: User;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'score', type: 'integer' })
  score: number;

  @OneToMany(() => MissionDiaryEntry, (entry) => entry.mission)
  entries: MissionDiaryEntry[];
}
