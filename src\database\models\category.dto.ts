import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsBoolean, IsNumber, IsHexColor, Min } from 'class-validator';

// Request DTOs

/**
 * DTO for creating a new category
 */
export class CreateCategoryDto {
  @ApiProperty({ example: 'Creative Writing', description: 'Name of the category' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ example: 'Category for creative writing missions', description: 'Description of the category', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ example: '#FF5733', description: 'Hex color code for the category', required: false })
  @IsOptional()
  @IsHexColor()
  color?: string;

  @ApiProperty({ example: 1, description: 'Sort order for displaying categories', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  sortOrder?: number;
}

/**
 * DTO for updating a category
 */
export class UpdateCategoryDto {
  @ApiProperty({ example: 'Academic Writing', description: 'Name of the category', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ example: 'Updated description for the category', description: 'Description of the category', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ example: '#33FF57', description: 'Hex color code for the category', required: false })
  @IsOptional()
  @IsHexColor()
  color?: string;

  @ApiProperty({ example: true, description: 'Whether the category is active', required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ example: 2, description: 'Sort order for displaying categories', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  sortOrder?: number;
}

// Response DTOs

/**
 * DTO for category response
 */
export class CategoryResponseDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000', description: 'Category ID' })
  id: string;

  @ApiProperty({ example: 'Creative Writing', description: 'Name of the category' })
  name: string;

  @ApiProperty({ example: 'Category for creative writing missions', description: 'Description of the category' })
  description?: string;

  @ApiProperty({ example: '#FF5733', description: 'Hex color code for the category' })
  color?: string;

  @ApiProperty({ example: true, description: 'Whether the category is active' })
  isActive: boolean;

  @ApiProperty({ example: 1, description: 'Sort order for displaying categories' })
  sortOrder: number;

  @ApiProperty({ example: '2023-08-10T12:00:00Z', description: 'When the category was created' })
  createdAt: Date;

  @ApiProperty({ example: '2023-08-11T15:30:00Z', description: 'When the category was last updated' })
  updatedAt: Date;
}
