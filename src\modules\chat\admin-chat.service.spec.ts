import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, DataSource, QueryRunner } from 'typeorm';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { AdminChatService } from './admin-chat.service';
import { Conversation, ConversationType, ConversationStatus } from '../../database/entities/conversation.entity';
import { Message, MessageType, MessageStatus } from '../../database/entities/message.entity';
import { AdminConversationParticipant } from '../../database/entities/admin-conversation-participant.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { AsyncNotificationHelperService } from '../notification/async-notification-helper.service';
import { CreateMessageDto } from '../../database/models/chat.dto';

describe('AdminChatService', () => {
  let service: AdminChatService;
  let conversationRepository: jest.Mocked<Repository<Conversation>>;
  let messageRepository: jest.Mocked<Repository<Message>>;
  let adminConversationParticipantRepository: jest.Mocked<Repository<AdminConversationParticipant>>;
  let userRepository: jest.Mocked<Repository<User>>;
  let asyncNotificationHelper: jest.Mocked<AsyncNotificationHelperService>;
  let dataSource: jest.Mocked<DataSource>;
  let queryRunner: jest.Mocked<QueryRunner>;

  const mockAdmin: User = {
    id: 'admin-id',
    userId: 'admin',
    name: 'Admin User',
    email: '<EMAIL>',
    type: UserType.ADMIN,
  } as User;

  const mockStudent: User = {
    id: 'student-id',
    userId: 'student',
    name: 'Student User',
    email: '<EMAIL>',
    type: UserType.STUDENT,
  } as User;

  const mockConversation: Conversation = {
    id: 'conversation-id',
    participant1Id: 'admin-id',
    participant2Id: 'student-id',
    type: ConversationType.DIRECT,
    status: ConversationStatus.ACTIVE,
    isAdminConversation: true,
    adminConversationUserId: 'student-id',
    participant2UnreadCount: 0,
  } as Conversation;

  beforeEach(async () => {
    // Create mocked query runner
    queryRunner = {
      connect: jest.fn(),
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      rollbackTransaction: jest.fn(),
      release: jest.fn(),
      manager: {
        create: jest.fn(),
        save: jest.fn(),
      },
    } as any;

    // Create mocked data source
    dataSource = {
      createQueryRunner: jest.fn().mockReturnValue(queryRunner),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminChatService,
        {
          provide: getRepositoryToken(Conversation),
          useValue: {
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Message),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(AdminConversationParticipant),
          useValue: {
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: AsyncNotificationHelperService,
          useValue: {
            notifyAsync: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: dataSource,
        },
      ],
    }).compile();

    service = module.get<AdminChatService>(AdminChatService);
    conversationRepository = module.get(getRepositoryToken(Conversation));
    messageRepository = module.get(getRepositoryToken(Message));
    adminConversationParticipantRepository = module.get(getRepositoryToken(AdminConversationParticipant));
    userRepository = module.get(getRepositoryToken(User));
    asyncNotificationHelper = module.get(AsyncNotificationHelperService);
  });

  describe('getOrCreateAdminConversation', () => {
    it('should create new admin conversation when none exists', async () => {
      // Arrange
      userRepository.findOne
        .mockResolvedValueOnce(mockAdmin) // Admin user
        .mockResolvedValueOnce(mockStudent); // Target user

      conversationRepository.findOne.mockResolvedValue(null); // No existing conversation

      (queryRunner.manager.create as jest.Mock).mockReturnValueOnce(mockConversation);
      (queryRunner.manager.save as jest.Mock)
        .mockResolvedValueOnce(mockConversation) // Save conversation
        .mockResolvedValueOnce({} as AdminConversationParticipant); // Save participant

      // Act
      const result = await service.getOrCreateAdminConversation(mockAdmin.id, mockStudent.id);

      // Assert
      expect(result).toEqual(mockConversation);
      expect(queryRunner.connect).toHaveBeenCalled();
      expect(queryRunner.startTransaction).toHaveBeenCalled();
      expect(queryRunner.commitTransaction).toHaveBeenCalled();
      expect(queryRunner.release).toHaveBeenCalled();
    });

    it('should return existing admin conversation', async () => {
      // Arrange
      userRepository.findOne.mockResolvedValueOnce(mockAdmin);
      conversationRepository.findOne.mockResolvedValue(mockConversation);
      adminConversationParticipantRepository.findOne.mockResolvedValue(null);
      adminConversationParticipantRepository.save.mockResolvedValue({} as AdminConversationParticipant);

      // Act
      const result = await service.getOrCreateAdminConversation(mockAdmin.id, mockStudent.id);

      // Assert
      expect(result).toEqual(mockConversation);
      expect(conversationRepository.findOne).toHaveBeenCalledWith({
        where: {
          isAdminConversation: true,
          adminConversationUserId: mockStudent.id,
        },
        relations: ['adminConversationUser'],
      });
    });

    it('should throw BadRequestException when user is not admin', async () => {
      // Arrange
      userRepository.findOne.mockResolvedValueOnce(mockStudent); // Non-admin user

      // Act & Assert
      await expect(service.getOrCreateAdminConversation(mockStudent.id, mockAdmin.id))
        .rejects.toThrow(BadRequestException);
    });

    it('should throw NotFoundException when target user not found', async () => {
      // Arrange
      userRepository.findOne
        .mockResolvedValueOnce(mockAdmin) // Admin user
        .mockResolvedValueOnce(null); // Target user not found

      // Act & Assert
      await expect(service.getOrCreateAdminConversation(mockAdmin.id, 'non-existent-id'))
        .rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException when trying to create conversation with another admin', async () => {
      // Arrange
      const anotherAdmin = { ...mockAdmin, id: 'another-admin-id' } as User;
      userRepository.findOne
        .mockResolvedValueOnce(mockAdmin) // Admin user
        .mockResolvedValueOnce(anotherAdmin); // Another admin

      // Act & Assert
      await expect(service.getOrCreateAdminConversation(mockAdmin.id, anotherAdmin.id))
        .rejects.toThrow(BadRequestException);
    });
  });

  describe('sendAdminMessage', () => {
    const createMessageDto: CreateMessageDto = {
      recipientId: mockStudent.id,
      content: 'Test admin message',
      type: MessageType.TEXT,
    };

    const mockMessage: Message = {
      id: 'message-id',
      conversationId: mockConversation.id,
      senderId: mockConversation.participant1Id,
      recipientId: mockConversation.participant2Id,
      actualSenderId: mockAdmin.id,
      content: createMessageDto.content,
      type: MessageType.TEXT,
      status: MessageStatus.SENT,
      createdAt: new Date(),
    } as Message;

    it('should send admin message successfully', async () => {
      // Arrange
      userRepository.findOne.mockResolvedValue(mockAdmin);
      conversationRepository.findOne.mockResolvedValue(mockConversation);
      adminConversationParticipantRepository.findOne.mockResolvedValue({} as AdminConversationParticipant);
      messageRepository.create.mockReturnValue(mockMessage);
      messageRepository.save.mockResolvedValue(mockMessage);
      conversationRepository.update.mockResolvedValue({} as any);
      adminConversationParticipantRepository.update.mockResolvedValue({} as any);
      asyncNotificationHelper.notifyAsync.mockResolvedValue(undefined);

      // Act
      const result = await service.sendAdminMessage(mockAdmin.id, mockConversation.id, createMessageDto);

      // Assert
      expect(result).toBeDefined();
      expect(result.content).toBe(createMessageDto.content);
      expect(result.actualSender).toBeDefined();
      expect(messageRepository.save).toHaveBeenCalledWith(mockMessage);
      expect(asyncNotificationHelper.notifyAsync).toHaveBeenCalled();
    });

    it('should throw BadRequestException when user is not admin', async () => {
      // Arrange
      userRepository.findOne.mockResolvedValue(mockStudent);

      // Act & Assert
      await expect(service.sendAdminMessage(mockStudent.id, mockConversation.id, createMessageDto))
        .rejects.toThrow(BadRequestException);
    });

    it('should throw NotFoundException when conversation not found', async () => {
      // Arrange
      userRepository.findOne.mockResolvedValue(mockAdmin);
      conversationRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(service.sendAdminMessage(mockAdmin.id, 'non-existent-id', createMessageDto))
        .rejects.toThrow(NotFoundException);
    });
  });

  describe('isAdminConversation', () => {
    it('should return true for admin conversation', async () => {
      // Arrange
      conversationRepository.findOne.mockResolvedValue(mockConversation);

      // Act
      const result = await service.isAdminConversation(mockConversation.id);

      // Assert
      expect(result).toBe(true);
    });

    it('should return false for regular conversation', async () => {
      // Arrange
      const regularConversation = { ...mockConversation, isAdminConversation: false };
      conversationRepository.findOne.mockResolvedValue(regularConversation);

      // Act
      const result = await service.isAdminConversation(mockConversation.id);

      // Assert
      expect(result).toBe(false);
    });

    it('should return false when conversation not found', async () => {
      // Arrange
      conversationRepository.findOne.mockResolvedValue(null);

      // Act
      const result = await service.isAdminConversation('non-existent-id');

      // Assert
      expect(result).toBe(false);
    });
  });
});
