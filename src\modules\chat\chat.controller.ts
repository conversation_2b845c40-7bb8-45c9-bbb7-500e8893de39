import { Controller, Get, Post, Body, Param, Query, UseGuards, UseInterceptors, UploadedFile, Res, StreamableFile } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiConsumes, ApiBody, ApiParam, ApiQuery } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { GetUser } from '../../common/decorators/get-user.decorator';
import { User, UserType } from '../../database/entities/user.entity';
import { ChatService } from './chat.service';
import {
  ConversationDto,
  MessageDto,
  CreateMessageDto,
  UpdateMessageDto,
  ConversationFilterDto,
  MessageFilterDto,
  PagedConversationListDto,
  PagedMessageListDto,
  ConversationParticipantDto,
  ChatFileUploadResponseDto,
  ContactFilterDto,
} from '../../database/models/chat.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithArrayType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { ApiOkResponseWithEmptyData } from '../../common/decorators/api-empty-response.decorator';

@ApiTags('chat')
@Controller('chat')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Get('conversations')
  @ApiOperation({ summary: 'Get all conversations for the current user' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term' })
  @ApiQuery({ name: 'status', required: false, description: 'Conversation status' })
  @ApiQuery({ name: 'type', required: false, description: 'Conversation type' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Page size', type: Number })
  @ApiOkResponseWithType(PagedConversationListDto, 'Conversations retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getConversations(@GetUser() user: User, @Query() filter: ConversationFilterDto): Promise<ApiResponse<PagedConversationListDto>> {
    const conversations = await this.chatService.getConversations(user.id, filter);
    return ApiResponse.success(conversations, 'Conversations retrieved successfully');
  }

  @Get('conversations/:id')
  @ApiOperation({ summary: 'Get a conversation by ID' })
  @ApiParam({ name: 'id', description: 'Conversation ID' })
  @ApiOkResponseWithType(ConversationDto, 'Conversation retrieved successfully')
  @ApiErrorResponse(404, 'Conversation not found')
  @ApiErrorResponse(403, 'User is not a participant in this conversation')
  @ApiErrorResponse(500, 'Internal server error')
  async getConversation(@GetUser() user: User, @Param('id') id: string): Promise<ApiResponse<ConversationDto>> {
    const conversation = await this.chatService.getConversation(id, user.id);
    return ApiResponse.success(conversation, 'Conversation retrieved successfully');
  }

  @Get('conversations/:id/messages')
  @ApiOperation({ summary: 'Get messages for a conversation' })
  @ApiParam({ name: 'id', description: 'Conversation ID' })
  @ApiQuery({ name: 'type', required: false, description: 'Message type' })
  @ApiQuery({ name: 'status', required: false, description: 'Message status' })
  @ApiQuery({ name: 'search', required: false, description: 'Search term for partial match on message content' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Page size', type: Number })
  @ApiOkResponseWithType(PagedMessageListDto, 'Messages retrieved successfully')
  @ApiErrorResponse(404, 'Conversation not found')
  @ApiErrorResponse(403, 'User is not a participant in this conversation')
  @ApiErrorResponse(500, 'Internal server error')
  async getMessages(@GetUser() user: User, @Param('id') id: string, @Query() filter: MessageFilterDto): Promise<ApiResponse<PagedMessageListDto>> {
    const messages = await this.chatService.getMessages(id, user.id, filter);
    return ApiResponse.success(messages, 'Messages retrieved successfully');
  }

  @Post('messages')
  @ApiOperation({ summary: 'Send a message' })
  @ApiBody({ type: CreateMessageDto })
  @ApiOkResponseWithType(MessageDto, 'Message sent successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(403, 'Users cannot chat with each other')
  @ApiErrorResponse(500, 'Internal server error')
  async sendMessage(@GetUser() user: User, @Body() createMessageDto: CreateMessageDto): Promise<ApiResponse<MessageDto>> {
    const message = await this.chatService.sendMessage(user.id, createMessageDto);
    return ApiResponse.success(message, 'Message sent successfully');
  }

  @Post('messages/:id/read')
  @ApiOperation({ summary: 'Mark messages as read' })
  @ApiParam({ name: 'id', description: 'Conversation ID' })
  @ApiOkResponseWithEmptyData('Messages marked as read')
  @ApiErrorResponse(404, 'Conversation not found')
  @ApiErrorResponse(403, 'User is not a participant in this conversation')
  @ApiErrorResponse(500, 'Internal server error')
  async markMessagesAsRead(@GetUser() user: User, @Param('id') id: string): Promise<ApiResponse<null>> {
    await this.chatService.markMessagesAsRead(id, user.id);
    return ApiResponse.success(null, 'Messages marked as read');
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload a file for a message',
    description: 'Uploads a file that can be attached to a message.',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'The file to upload',
        },
      },
      required: ['file'],
    },
  })
  @ApiOkResponseWithType(ChatFileUploadResponseDto, 'File uploaded successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(500, 'Internal server error')
  async uploadFile(@GetUser() user: User, @UploadedFile() file: Express.Multer.File): Promise<ApiResponse<ChatFileUploadResponseDto>> {
    const result = await this.chatService.uploadFile(user.id, file);
    return ApiResponse.success(result, 'File uploaded successfully');
  }

  @Get('files/:id')
  @ApiOperation({ summary: 'Get a file by ID' })
  @ApiParam({ name: 'id', description: 'File ID' })
  async getFile(@Param('id') id: string, @Res({ passthrough: true }) res: Response): Promise<StreamableFile> {
    const { buffer, fileName, mimeType } = await this.chatService.getFile(id);

    res.set({
      'Content-Type': mimeType,
      'Content-Disposition': `attachment; filename="${encodeURIComponent(fileName)}"`,
    });

    return new StreamableFile(buffer);
  }

  @Get('contacts')
  @ApiOperation({ summary: 'Get available chat contacts for the current user' })
  @ApiQuery({ name: 'name', required: false, description: 'Filter by name' })
  @ApiQuery({ name: 'email', required: false, description: 'Filter by email' })
  @ApiQuery({ name: 'phone', required: false, description: 'Filter by phone' })
  @ApiOkResponseWithArrayType(ConversationParticipantDto, 'Contacts retrieved successfully')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getChatContacts(@GetUser() user: User, @Query() filter: ContactFilterDto): Promise<ApiResponse<ConversationParticipantDto[]>> {
    const contacts = await this.chatService.getChatContacts(user.id, filter);
    return ApiResponse.success(contacts, 'Contacts retrieved successfully');
  }
}
