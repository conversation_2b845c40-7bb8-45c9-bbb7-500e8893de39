import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

@Entity()
export class StudentDiarySkin extends AuditableBaseEntity {
  @Column({ name: 'name' })
  name: string;

  @Column({ name: 'description', type: 'text' })
  description: string;

  @Column({ name: 'template_content', type: 'text' })
  templateContent: string;

  @Column({ name: 'preview_image_path', nullable: true })
  previewImagePath: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'student_id' })
  studentId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  // This is false by default since it's a student skin
  @Column({ name: 'is_global', default: false })
  isGlobal: boolean;
}
