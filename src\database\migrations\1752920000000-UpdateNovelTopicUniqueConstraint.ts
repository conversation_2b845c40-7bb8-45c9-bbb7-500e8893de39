import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateNovelTopicUniqueConstraint1752920000000 implements MigrationInterface {
  name = 'UpdateNovelTopicUniqueConstraint1752920000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the existing unique constraint on sequence_title
    await queryRunner.query(`
      ALTER TABLE "novel_topic" 
      DROP CONSTRAINT IF EXISTS "UQ_novel_topic_sequence_title"
    `);

    // Drop the existing unique index if it exists
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_novel_topic_sequence_title"
    `);

    // Create a new composite unique constraint on sequence_title and category
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_novel_topic_sequence_title_category" 
      ON "novel_topic" ("sequence_title", "category")
    `);

    // Add the constraint name for better management
    await queryRunner.query(`
      ALTER TABLE "novel_topic" 
      ADD CONSTRAINT "UQ_novel_topic_sequence_title_category" 
      UNIQUE USING INDEX "IDX_novel_topic_sequence_title_category"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the composite unique constraint
    await queryRunner.query(`
      ALTER TABLE "novel_topic" 
      DROP CONSTRAINT IF EXISTS "UQ_novel_topic_sequence_title_category"
    `);

    // Drop the composite unique index
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_novel_topic_sequence_title_category"
    `);

    // Restore the original unique constraint on sequence_title only
    await queryRunner.query(`
      ALTER TABLE "novel_topic" 
      ADD CONSTRAINT "UQ_novel_topic_sequence_title" 
      UNIQUE ("sequence_title")
    `);
  }
}
