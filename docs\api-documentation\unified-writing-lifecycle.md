# Unified Writing Entry Lifecycle

## Overview

All writing modules in the HEC system (Diary Entries, Mission Diary Entries, and Novel Entries) now follow a **unified submission/review/confirmation lifecycle** that ensures consistent behavior, API patterns, and user experience across all writing activities.

## Unified Status System

### Status Enum
All writing entries use the `WritingEntryStatus` enum:

- `DRAFT` - Entry is being drafted by the student
- `SUBMITTED` - Entry has been submitted for review
- `REVIEWED` - Entry has been reviewed by tutor (feedback/correction given)
- `CONFIRMED` - Entry has been confirmed by tutor (review completed)

### Status Flow with Resubmission Tracking
```
DRAFT → SUBMITTED → REVIEWED → CONFIRMED
  ↑                    ↓           ↓
  └── (resubmission) ←─┘           │
  └── (resubmission) ←─────────────┘
```

**Key Principles:**
- **Simple Progression**: No intermediate states like "under review"
- **Unlimited Resubmission**: Students can resubmit after each review or confirmation
- **Resubmission Identification**: All subsequent submissions are flagged with resubmission type
- **Clear Transitions**: Each status change has specific triggers and permissions

### Resubmission Tracking
Every submission after the first one is tracked with:
- **`isResubmission`**: Boolean flag indicating this is not the first submission
- **`resubmissionType`**: Either `'after_review'` or `'after_confirmation'`
- **`previousReviewCount`**: Number of times entry was reviewed before this submission
- **`previousConfirmationCount`**: Number of times entry was confirmed before this submission

## Draft vs Submission Logic

### Update APIs (Save as Draft)
- **No Version Creation**: Updates don't create version history entries
- **No Notifications**: No tutor notifications are sent during updates
- **Draft Mode**: Entries are marked as `isDraft = true` and `status = DRAFT`
- **Free Updates**: Students can update draft content unlimited times

### Submit APIs (Create Submitted Version)
- **Version Creation**: Creates a new version in history marked as `isSubmittedVersion = true`
- **Submission Gating**: Checks `canSubmitNewVersion` before allowing submission
- **Always Notify**: Sends tutor notification for every submission with submission number and resubmission info
- **Status Transition**: Changes `status` to `SUBMITTED` and `isDraft = false`
- **Tracking Updates**: Updates submission count, timestamps, and references
- **Resubmission Tracking**: Identifies and flags resubmissions with type and previous counts

## Unified Review API Requirements

### Review Submission Rules
1. **Score is Required**: All review APIs require a score parameter
2. **Correction is Optional**: Correction/feedback text is optional during review
3. **One Review Per Submission**: Tutor can only submit review (score + correction) once per submission
4. **Unlimited Feedback**: Tutor can add unlimited feedback entries even after review

### Review API Methods

#### Diary Module
```typescript
// Submit review with required score and optional correction
submitDiaryReview(entryId: string, tutorId: string, score: number, correction?: string): Promise<DiaryEntry>

// Add unlimited feedback
addDiaryFeedback(entryId: string, tutorId: string, feedbackText: string): Promise<DiaryFeedback>
```

#### Mission Diary Module
```typescript
// Submit review with required score and optional correction
submitMissionReview(entryId: string, tutorId: string, score: number, correction?: string): Promise<MissionDiaryEntry>

// Add unlimited feedback
addMissionFeedback(entryId: string, tutorId: string, feedbackText: string): Promise<MissionDiaryEntryFeedback>
```

#### Novel Module
```typescript
// Submit review with required score and optional correction
submitReview(entryId: string, tutorId: string, score: number, correction?: string): Promise<NovelEntry>

// Add unlimited feedback
addNovelFeedback(entryId: string, tutorId: string, feedbackText: string): Promise<NovelFeedback>
```

## Database Schema Unification

### Common Fields Added to All Writing Entry Tables
```sql
-- Unified status tracking
unified_status VARCHAR DEFAULT 'draft'

-- Submission/draft tracking
is_draft BOOLEAN DEFAULT true
last_submitted_at TIMESTAMP
last_reviewed_at TIMESTAMP
can_submit_new_version BOOLEAN DEFAULT true
submitted_version_count INTEGER DEFAULT 0
current_submitted_version_id UUID

-- Review tracking
word_count INTEGER DEFAULT 0
submitted_at TIMESTAMP
reviewed_at TIMESTAMP
reviewed_by VARCHAR
evaluated_at TIMESTAMP
evaluated_by VARCHAR
gained_score INTEGER
score INTEGER
```

### Common Fields Added to All History Tables
```sql
-- Version tracking
is_submitted_version BOOLEAN DEFAULT false
submission_number INTEGER
submitted_at TIMESTAMP
```

## API Endpoint Patterns

### Student APIs (Consistent Across All Modules)
```
POST /diary/entries/{id}/submit          # Submit diary entry
PUT  /diary/entries/{id}                 # Update diary entry (draft)

POST /diary/mission-entries/{id}/submit  # Submit mission entry
PUT  /diary/mission-entries/{id}         # Update mission entry (draft)

POST /novel/entries/{id}/submit          # Submit novel entry
PUT  /novel/entries/{id}                 # Update novel entry (draft)
```

### Tutor APIs (Unified Review Pattern)
```
POST /tutor/diary/entries/{id}/review    # Submit diary review
POST /tutor/diary/entries/{id}/feedback  # Add diary feedback

POST /tutor/mission/entries/{id}/review  # Submit mission review
POST /tutor/mission/entries/{id}/feedback # Add mission feedback

POST /tutor/novel/entries/{id}/review    # Submit novel review
POST /tutor/novel/entries/{id}/feedback  # Add novel feedback
```

## Notification System

### Submission Notifications
- **Trigger**: When student submits entry for review
- **Recipients**: Assigned tutor(s)
- **Content**: Includes submission number and resubmission information
- **Frequency**: Every submission (unlimited)
- **Resubmission Context**: Clearly identifies initial submissions vs resubmissions with type

### Review Notifications
- **Trigger**: When tutor submits review (score + optional correction)
- **Recipients**: Student who submitted the entry
- **Content**: Score and correction details
- **Frequency**: Once per submission

### Feedback Notifications
- **Trigger**: When tutor adds feedback
- **Recipients**: Student who submitted the entry
- **Content**: Feedback text
- **Frequency**: Unlimited (every feedback addition)

## Version Tracking

### Submitted Versions
- Marked with `isSubmittedVersion = true`
- Include `submissionNumber` for sequence tracking
- Contain `submittedAt` timestamp
- Preserve complete submission history
- **Resubmission Tracking**: Include `isResubmission`, `resubmissionType`, and `previousStatus` fields

### Draft Updates
- Do not create version entries
- Update main entry content directly
- No historical tracking for draft changes

## Migration Strategy

### Data Migration
1. **Add Unified Fields**: Add new unified fields to all writing entry tables
2. **Map Legacy Statuses**: Convert existing status values to unified system
3. **Create Initial Versions**: Generate submitted versions for existing reviewed entries
4. **Update Tracking Fields**: Set appropriate flags and timestamps
5. **Enable Resubmission**: Allow resubmission for previously reviewed entries

### Backward Compatibility
- Legacy API methods maintained for existing integrations
- Gradual migration path with deprecation notices
- No breaking changes to current functionality

## Benefits of Unification

1. **Consistent User Experience**: Same behavior across all writing modules
2. **Simplified Development**: Shared logic and patterns reduce code duplication
3. **Easier Maintenance**: Single source of truth for writing entry lifecycle
4. **Better Testing**: Unified test patterns and validation
5. **Scalable Architecture**: Easy to add new writing module types
6. **Clear Documentation**: Single comprehensive guide for all writing features
