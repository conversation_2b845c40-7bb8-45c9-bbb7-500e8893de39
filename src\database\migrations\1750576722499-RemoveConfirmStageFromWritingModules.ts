import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveConfirmStageFromWritingModules1750576722499 implements MigrationInterface {
  name = 'RemoveConfirmStageFromWritingModules1750576722499';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('Starting migration: Remove confirm stage from writing modules...');

    // 1. Update all CONFIRMED entries to REVIEWED status
    console.log('Updating CONFIRMED entries to REVIEWED status...');

    // Update diary entries
    await queryRunner.query(`
      UPDATE "diary_entry" 
      SET "status" = 'reviewed', "unified_status" = 'reviewed'
      WHERE "status" = 'confirm'
    `);

    // Update mission diary entries
    await queryRunner.query(`
      UPDATE "mission_diary_entry" 
      SET "status" = 'reviewed', "unified_status" = 'reviewed'
      WHERE "status" IN ('confirm', 'CONFIRMED')
    `);

    // Update novel entries
    await queryRunner.query(`
      UPDATE "novel_entry" 
      SET "status" = 'reviewed', "unified_status" = 'reviewed'
      WHERE "status" = 'confirmed'
    `);

    // 2. Update resubmission types - remove 'after_confirmation' type
    console.log('Updating resubmission types...');

    // Update diary entries resubmission type
    await queryRunner.query(`
      UPDATE "diary_entry" 
      SET "resubmission_type" = 'after_review'
      WHERE "resubmission_type" = 'after_confirmation'
    `);

    // Update mission diary entries resubmission type
    await queryRunner.query(`
      UPDATE "mission_diary_entry" 
      SET "resubmission_type" = 'after_review'
      WHERE "resubmission_type" = 'after_confirmation'
    `);

    // Update novel entries resubmission type
    await queryRunner.query(`
      UPDATE "novel_entry" 
      SET "resubmission_type" = 'after_review'
      WHERE "resubmission_type" = 'after_confirmation'
    `);

    // 3. Ensure canSubmitNewVersion is true for all reviewed entries
    console.log('Updating canSubmitNewVersion flags...');

    await queryRunner.query(`
      UPDATE "diary_entry" 
      SET "can_submit_new_version" = true
      WHERE "status" = 'reviewed'
    `);

    await queryRunner.query(`
      UPDATE "mission_diary_entry" 
      SET "can_submit_new_version" = true
      WHERE "status" = 'reviewed'
    `);

    await queryRunner.query(`
      UPDATE "novel_entry" 
      SET "can_submit_new_version" = true
      WHERE "status" = 'reviewed'
    `);

    console.log('Migration completed: Confirm stage removed from writing modules');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('Rolling back migration: Restore confirm stage to writing modules...');

    // Note: This rollback is not perfect as we lose the original distinction
    // between reviewed and confirmed entries, but it provides basic functionality

    // 1. Restore some entries to confirmed status (those that were likely confirmed)
    // We'll use a heuristic: entries that have scores and are not resubmissions
    console.log('Restoring some entries to confirmed status...');

    // Restore diary entries
    await queryRunner.query(`
      UPDATE "diary_entry" 
      SET "status" = 'confirm', "unified_status" = 'confirmed'
      WHERE "status" = 'reviewed' 
        AND "is_resubmission" = false
        AND EXISTS (
          SELECT 1 FROM "diary_correction" 
          WHERE "diary_correction"."diary_entry_id" = "diary_entry"."id" 
            AND "diary_correction"."score" IS NOT NULL
        )
    `);

    // Restore mission diary entries
    await queryRunner.query(`
      UPDATE "mission_diary_entry" 
      SET "status" = 'confirm', "unified_status" = 'confirmed'
      WHERE "status" = 'reviewed' 
        AND "is_resubmission" = false
        AND "gained_score" IS NOT NULL
    `);

    // Restore novel entries
    await queryRunner.query(`
      UPDATE "novel_entry" 
      SET "status" = 'confirmed', "unified_status" = 'confirmed'
      WHERE "status" = 'reviewed' 
        AND "is_resubmission" = false
        AND EXISTS (
          SELECT 1 FROM "novel_correction" 
          WHERE "novel_correction"."novel_entry_id" = "novel_entry"."id" 
            AND "novel_correction"."score" IS NOT NULL
        )
    `);

    console.log('Migration rollback completed');
  }
}
