import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { TutorGuard } from '../../common/guards/tutor.guard';
import { GetUser } from '../../common/decorators/get-user.decorator';
import { User } from '../../database/entities/user.entity';
import { TutorDashboardService } from './tutor-dashboard.service';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { TutorStudentCountDto, TutorAttendanceStatsDto, TutorReviewStatsDto, TutorSubmissionStatsDto } from '../../database/models/dashboard.dto';

@ApiTags('Tutor Dashboard')
@Controller('tutor/dashboard')
@UseGuards(JwtAuthGuard, TutorGuard)
@ApiBearerAuth('JWT-auth')
export class TutorDashboardController {
  constructor(private readonly tutorDashboardService: TutorDashboardService) {}

  @Get('student-count')
  @ApiOperation({
    summary: 'Get assigned student count',
    description: 'Returns the total number of active students assigned to the authenticated tutor',
  })
  @ApiOkResponseWithType(TutorStudentCountDto, 'Assigned student count retrieved successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getAssignedStudentCount(@GetUser() user: User): Promise<ApiResponse<TutorStudentCountDto>> {
    const result = await this.tutorDashboardService.getAssignedStudentCount(user.id);
    return ApiResponse.success(result, 'Assigned student count retrieved successfully');
  }

  @Get('attendance/today')
  @ApiOperation({
    summary: "Get today's attendance statistics for assigned students",
    description: "Returns present and absent counts for today among the tutor's assigned students",
  })
  @ApiOkResponseWithType(TutorAttendanceStatsDto, "Today's attendance statistics retrieved successfully")
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getTodayAttendanceStats(@GetUser() user: User): Promise<ApiResponse<TutorAttendanceStatsDto>> {
    const result = await this.tutorDashboardService.getTodayAttendanceStats(user.id);
    return ApiResponse.success(result, "Today's attendance statistics retrieved successfully");
  }

  @Get('reviews/total')
  @ApiOperation({
    summary: 'Get total review and feedback statistics',
    description: 'Returns total reviews and feedback given by the tutor across all required modules with breakdown by individual modules',
  })
  @ApiOkResponseWithType(TutorReviewStatsDto, 'Total review statistics retrieved successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getReviewStats(@GetUser() user: User): Promise<ApiResponse<TutorReviewStatsDto>> {
    const result = await this.tutorDashboardService.getReviewStats(user.id);
    return ApiResponse.success(result, 'Total review statistics retrieved successfully');
  }

  @Get('submissions/confirmed')
  @ApiOperation({
    summary: 'Get confirmed submission statistics',
    description: 'Returns total confirmed submissions for all modules except HECplay with breakdown by individual modules',
  })
  @ApiOkResponseWithType(TutorSubmissionStatsDto, 'Confirmed submission statistics retrieved successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getConfirmedSubmissionStats(@GetUser() user: User): Promise<ApiResponse<TutorSubmissionStatsDto>> {
    const result = await this.tutorDashboardService.getSubmissionStats(user.id);
    return ApiResponse.success(result, 'Confirmed submission statistics retrieved successfully');
  }

  @Get('submissions/pending')
  @ApiOperation({
    summary: 'Get pending/ongoing submission statistics',
    description: 'Returns total pending/ongoing submissions for all modules with breakdown by individual modules',
  })
  @ApiOkResponseWithType(TutorSubmissionStatsDto, 'Pending submission statistics retrieved successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getPendingSubmissionStats(@GetUser() user: User): Promise<ApiResponse<TutorSubmissionStatsDto>> {
    const result = await this.tutorDashboardService.getSubmissionStats(user.id);
    return ApiResponse.success(result, 'Pending submission statistics retrieved successfully');
  }
}
