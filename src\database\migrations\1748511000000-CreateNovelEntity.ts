import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateNovelEntity1748511000000 implements MigrationInterface {
  name = 'CreateNovelEntity1748511000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if novel table already exists
    const tableExists = await queryRunner.hasTable('novel');
    if (tableExists) {
      console.log('Novel table already exists, skipping creation');
      return;
    }

    // Create novel table
    await queryRunner.query(`
      CREATE TABLE "novel" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "user_id" uuid NOT NULL,
        "default_skin_id" uuid NOT NULL,
        "tutor_greeting" text,
        CONSTRAINT "PK_novel" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_novel_user" UNIQUE ("user_id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "novel" 
      ADD CONSTRAINT "FK_novel_user" 
      FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "novel" 
      ADD CONSTRAINT "FK_novel_default_skin" 
      FOREIGN KEY ("default_skin_id") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    // Create index on user_id for faster lookups
    await queryRunner.query(`
      CREATE INDEX "IDX_novel_user_id" ON "novel" ("user_id")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "novel" DROP CONSTRAINT "FK_novel_default_skin"`);
    await queryRunner.query(`ALTER TABLE "novel" DROP CONSTRAINT "FK_novel_user"`);

    // Drop index
    await queryRunner.query(`DROP INDEX "IDX_novel_user_id"`);

    // Drop table
    await queryRunner.query(`DROP TABLE "novel"`);
  }
}
