import { MigrationInterface, QueryRunner } from 'typeorm';

export class EnsureRewardPointSettingTable1747000000004 implements MigrationInterface {
  name = 'EnsureRewardPointSettingTable1747000000004';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the table exists
    const tableExists = await queryRunner.hasTable('reward_point_setting');

    if (!tableExists) {
      // Create the reward_point_setting table if it doesn't exist
      await queryRunner.query(`
                CREATE TABLE "reward_point_setting" (
                    "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                    "created_at" TIMESTAMP NOT NULL,
                    "updated_at" TIMESTAMP,
                    "created_by" character varying(36),
                    "updated_by" character varying(36),
                    "name" character varying(100) NOT NULL,
                    "description" text,
                    "conversion_rate" decimal(10,2) NOT NULL,
                    "is_active" boolean NOT NULL DEFAULT false,
                    CONSTRAINT "PK_reward_point_setting" PRIMARY KEY ("id")
                )
            `);

      // Insert a default setting
      const now = new Date().toISOString();
      await queryRunner.query(`
                INSERT INTO "reward_point_setting"
                ("name", "description", "conversion_rate", "is_active", "created_at", "updated_at", "created_by", "updated_by")
                VALUES
                ('Default Conversion Rate', 'Default conversion rate for reward points (100 points = $1)', 100, true, '${now}', '${now}', NULL, NULL)
            `);
    } else {
      // Check if the created_by and updated_by columns exist
      const columnsExist = await queryRunner.query(`
                SELECT EXISTS (
                    SELECT FROM information_schema.columns
                    WHERE table_name = 'reward_point_setting'
                    AND column_name = 'created_by'
                );
            `);

      if (!columnsExist[0].exists) {
        // Add the missing columns
        await queryRunner.query(`
                    ALTER TABLE "reward_point_setting"
                    ADD COLUMN "created_by" character varying(36),
                    ADD COLUMN "updated_by" character varying(36)
                `);
      }
    }
  }

  public async down(_queryRunner: QueryRunner): Promise<void> {
    // We don't want to drop the table in the down migration
    // as it might contain important data
  }
}
