import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixNovelCorrectionEntryIdConstraint1746500000000 implements MigrationInterface {
  name = 'FixNovelCorrectionEntryIdConstraint1746500000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Check if the table exists
    const tableExists = await queryRunner.hasTable('novel_correction');
    if (!tableExists) {
      console.log('novel_correction table does not exist, skipping migration');
      return;
    }

    // Step 2: Check if there are any records with null entry_id
    const nullEntryIdRecords = await queryRunner.query(`SELECT COUNT(*) as count FROM novel_correction WHERE entry_id IS NULL`);

    if (nullEntryIdRecords[0].count > 0) {
      console.log(`Found ${nullEntryIdRecords[0].count} records with null entry_id, deleting them...`);

      // Delete records with null entry_id as they are invalid
      await queryRunner.query(`DELETE FROM novel_correction WHERE entry_id IS NULL`);

      console.log('Deleted records with null entry_id');
    }

    // Step 3: Check if there are any records with null tutor_id
    const nullTutorIdRecords = await queryRunner.query(`SELECT COUNT(*) as count FROM novel_correction WHERE tutor_id IS NULL`);

    if (nullTutorIdRecords[0].count > 0) {
      console.log(`Found ${nullTutorIdRecords[0].count} records with null tutor_id, deleting them...`);

      // Delete records with null tutor_id as they are invalid
      await queryRunner.query(`DELETE FROM novel_correction WHERE tutor_id IS NULL`);

      console.log('Deleted records with null tutor_id');
    }

    // Step 4: Drop existing constraints if they exist
    try {
      // Check if NOT NULL constraint exists on entry_id
      const entryIdConstraint = await queryRunner.query(`
        SELECT column_name, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'novel_correction' 
        AND column_name = 'entry_id'
      `);

      if (entryIdConstraint.length > 0 && entryIdConstraint[0].is_nullable === 'NO') {
        console.log('Temporarily making entry_id nullable to fix constraint issues...');

        // Temporarily make entry_id nullable
        await queryRunner.query(`ALTER TABLE novel_correction ALTER COLUMN entry_id DROP NOT NULL`);
      }

      // Check if NOT NULL constraint exists on tutor_id
      const tutorIdConstraint = await queryRunner.query(`
        SELECT column_name, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'novel_correction' 
        AND column_name = 'tutor_id'
      `);

      if (tutorIdConstraint.length > 0 && tutorIdConstraint[0].is_nullable === 'NO') {
        console.log('Temporarily making tutor_id nullable to fix constraint issues...');

        // Temporarily make tutor_id nullable
        await queryRunner.query(`ALTER TABLE novel_correction ALTER COLUMN tutor_id DROP NOT NULL`);
      }
    } catch (error) {
      console.log('Error modifying constraints (may not exist):', error.message);
    }

    // Step 5: Ensure the columns have the correct type
    await queryRunner.query(`ALTER TABLE novel_correction ALTER COLUMN entry_id TYPE uuid USING entry_id::uuid`);

    await queryRunner.query(`ALTER TABLE novel_correction ALTER COLUMN tutor_id TYPE uuid USING tutor_id::uuid`);

    // Step 6: Re-add NOT NULL constraints
    console.log('Re-adding NOT NULL constraints...');

    await queryRunner.query(`ALTER TABLE novel_correction ALTER COLUMN entry_id SET NOT NULL`);

    await queryRunner.query(`ALTER TABLE novel_correction ALTER COLUMN tutor_id SET NOT NULL`);

    // Step 7: Ensure foreign key constraints exist
    try {
      // Check if foreign key constraint for entry_id exists
      const entryFkExists = await queryRunner.query(`
        SELECT constraint_name 
        FROM information_schema.table_constraints 
        WHERE table_name = 'novel_correction' 
        AND constraint_type = 'FOREIGN KEY'
        AND constraint_name LIKE '%entry_id%'
      `);

      if (entryFkExists.length === 0) {
        console.log('Adding foreign key constraint for entry_id...');
        await queryRunner.query(
          `ALTER TABLE novel_correction 
           ADD CONSTRAINT FK_novel_correction_entry_id 
           FOREIGN KEY (entry_id) REFERENCES novel_entry(id) ON DELETE CASCADE`,
        );
      }

      // Check if foreign key constraint for tutor_id exists
      const tutorFkExists = await queryRunner.query(`
        SELECT constraint_name 
        FROM information_schema.table_constraints 
        WHERE table_name = 'novel_correction' 
        AND constraint_type = 'FOREIGN KEY'
        AND constraint_name LIKE '%tutor_id%'
      `);

      if (tutorFkExists.length === 0) {
        console.log('Adding foreign key constraint for tutor_id...');
        await queryRunner.query(
          `ALTER TABLE novel_correction 
           ADD CONSTRAINT FK_novel_correction_tutor_id 
           FOREIGN KEY (tutor_id) REFERENCES "user"(id) ON DELETE CASCADE`,
        );
      }
    } catch (error) {
      console.log('Error adding foreign key constraints:', error.message);
    }

    console.log('Novel correction entry_id constraint fix completed successfully');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Check if the table exists
    const tableExists = await queryRunner.hasTable('novel_correction');
    if (!tableExists) {
      console.log('novel_correction table does not exist, skipping rollback');
      return;
    }

    // Step 2: Drop foreign key constraints if they exist
    try {
      await queryRunner.query(`ALTER TABLE novel_correction DROP CONSTRAINT IF EXISTS FK_novel_correction_entry_id`);

      await queryRunner.query(`ALTER TABLE novel_correction DROP CONSTRAINT IF EXISTS FK_novel_correction_tutor_id`);
    } catch (error) {
      console.log('Error dropping foreign key constraints during rollback:', error.message);
    }

    console.log('Novel correction constraint rollback completed');
  }
}
