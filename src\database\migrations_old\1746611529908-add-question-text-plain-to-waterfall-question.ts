import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddQuestionTextPlainToWaterfallQuestion1746611529908 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the question_text_plain column
    await queryRunner.query(`
            ALTER TABLE "waterfall_question"
            ADD COLUMN "question_text_plain" text NOT NULL DEFAULT '';
        `);

    // Copy data from question_text to question_text_plain for existing records
    await queryRunner.query(`
            UPDATE "waterfall_question"
            SET "question_text_plain" = "question_text";
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the question_text_plain column
    await queryRunner.query(`
            ALTER TABLE "waterfall_question"
            DROP COLUMN "question_text_plain";
        `);
  }
}
