import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { AdminDashboardService } from './admin-dashboard.service';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { ApiResponse } from '../../common/dto/api-response.dto';
import {
  AdminStudentCountDto,
  AdminAttendanceStatsDto,
  AdminSubmissionStatsDto,
  AdminTutorCountDto,
  AdminSubscriptionStatusDto,
  AdminModuleCompletionRatesDto,
  StudentDailyActivityResponseDto,
} from '../../database/models/dashboard.dto';

@ApiTags('Admin Dashboard')
@Controller('admin/dashboard')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth('JWT-auth')
export class AdminDashboardController {
  constructor(private readonly adminDashboardService: AdminDashboardService) {}

  @Get('student-count')
  @ApiOperation({
    summary: 'Get total active student count',
    description: 'Returns the total number of active students with valid subscriptions',
  })
  @ApiOkResponseWithType(AdminStudentCountDto, 'Active student count retrieved successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getActiveStudentCount(): Promise<ApiResponse<AdminStudentCountDto>> {
    const result = await this.adminDashboardService.getActiveStudentCount();
    return ApiResponse.success(result, 'Active student count retrieved successfully');
  }

  @Get('attendance/today')
  @ApiOperation({
    summary: "Get today's attendance statistics",
    description: 'Returns present and absent counts for today with attendance percentage',
  })
  @ApiOkResponseWithType(AdminAttendanceStatsDto, "Today's attendance statistics retrieved successfully")
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getTodayAttendanceStats(): Promise<ApiResponse<AdminAttendanceStatsDto>> {
    const result = await this.adminDashboardService.getTodayAttendanceStats();
    return ApiResponse.success(result, "Today's attendance statistics retrieved successfully");
  }

  @Get('submissions/total')
  @ApiOperation({
    summary: 'Get total submission statistics',
    description: 'Returns total submissions across all modules with breakdown by individual modules',
  })
  @ApiOkResponseWithType(AdminSubmissionStatsDto, 'Total submission statistics retrieved successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getTotalSubmissionStats(): Promise<ApiResponse<AdminSubmissionStatsDto>> {
    const result = await this.adminDashboardService.getTotalSubmissionStats();
    return ApiResponse.success(result, 'Total submission statistics retrieved successfully');
  }

  @Get('submissions/pending')
  @ApiOperation({
    summary: 'Get pending submission statistics',
    description: 'Returns total pending submissions across all modules with breakdown by individual modules',
  })
  @ApiOkResponseWithType(AdminSubmissionStatsDto, 'Pending submission statistics retrieved successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getPendingSubmissionStats(): Promise<ApiResponse<AdminSubmissionStatsDto>> {
    const result = await this.adminDashboardService.getTotalSubmissionStats();
    return ApiResponse.success(result, 'Pending submission statistics retrieved successfully');
  }

  @Get('tutor-count')
  @ApiOperation({
    summary: 'Get assigned tutor count',
    description: 'Returns total number of assigned tutors and active tutors with current assignments',
  })
  @ApiOkResponseWithType(AdminTutorCountDto, 'Assigned tutor count retrieved successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getAssignedTutorCount(): Promise<ApiResponse<AdminTutorCountDto>> {
    const result = await this.adminDashboardService.getAssignedTutorCount();
    return ApiResponse.success(result, 'Assigned tutor count retrieved successfully');
  }

  @Get('subscription-status')
  @ApiOperation({
    summary: "Get students' subscription status breakdown",
    description: 'Returns breakdown of students by subscription plan type (free, trial, premium, etc.)',
  })
  @ApiOkResponseWithType(AdminSubscriptionStatusDto, 'Subscription status breakdown retrieved successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getSubscriptionStatusBreakdown(): Promise<ApiResponse<AdminSubscriptionStatusDto>> {
    const result = await this.adminDashboardService.getSubscriptionStatusBreakdown();
    return ApiResponse.success(result, 'Subscription status breakdown retrieved successfully');
  }

  @Get('completion-rates')
  @ApiOperation({
    summary: 'Get module completion rates for all users',
    description: 'Returns completion rates for each user-module combination showing progress and completion percentages',
  })
  @ApiOkResponseWithType(AdminModuleCompletionRatesDto, 'Module completion rates retrieved successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getModuleCompletionRates(): Promise<ApiResponse<AdminModuleCompletionRatesDto>> {
    const result = await this.adminDashboardService.getModuleCompletionRates();
    return ApiResponse.success(result, 'Module completion rates retrieved successfully');
  }

  @Get('student-daily-activity')
  @ApiOperation({
    summary: 'Get student daily activity performance by years',
    description: 'Returns comprehensive activity statistics across all modules (diary, QA, essay, novel, games, story maker) grouped by years and months',
  })
  @ApiOkResponseWithType(StudentDailyActivityResponseDto, 'Student daily activity statistics retrieved successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getStudentDailyActivity(): Promise<ApiResponse<StudentDailyActivityResponseDto>> {
    const result = await this.adminDashboardService.getStudentDailyActivity();
    return ApiResponse.success(result, 'Student daily activity statistics retrieved successfully');
  }
}
