import { MigrationInterface, QueryRunner } from 'typeorm';

export class TutorEssayMarkingTable1745859597817 implements MigrationInterface {
  name = 'TutorEssayMarkingTable1745859597817';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" RENAME COLUMN "teacher_remarks" TO "marking"`);
    await queryRunner.query(
      `CREATE TABLE "essay_task_submission_marking" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "points" double precision NOT NULL, "submission_feedback" text, "task_remarks" text, "submission_id" uuid NOT NULL, "submission_history_id" uuid NOT NULL, "submission" uuid, "submission_history" uuid, CONSTRAINT "REL_84039f2dd37abc5e5ea72261b1" UNIQUE ("submission"), CONSTRAINT "REL_0276834b14c1f78c5a81eb61c9" UNIQUE ("submission_history"), CONSTRAINT "PK_16cbd98bc507e01955d80bfd15c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_090783a2cd92909ecf3f68fb2e" ON "essay_task_submission_marking" ("submission_id", "submission_history_id") `);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP COLUMN "reviewed_at"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP COLUMN "feedback"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD "marking" uuid`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD CONSTRAINT "UQ_2c0f158665674b8d3cbbe35e05f" UNIQUE ("marking")`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP COLUMN "marking"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD "marking" uuid`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "UQ_28cca731b1dda53dbe764ee2834" UNIQUE ("marking")`);
    await queryRunner.query(
      `ALTER TABLE "essay_task_submission_history" ADD CONSTRAINT "FK_2c0f158665674b8d3cbbe35e05f" FOREIGN KEY ("marking") REFERENCES "essay_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_28cca731b1dda53dbe764ee2834" FOREIGN KEY ("marking") REFERENCES "essay_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "essay_task_submission_marking" ADD CONSTRAINT "FK_84039f2dd37abc5e5ea72261b1b" FOREIGN KEY ("submission") REFERENCES "essay_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "essay_task_submission_marking" ADD CONSTRAINT "FK_0276834b14c1f78c5a81eb61c9b" FOREIGN KEY ("submission_history") REFERENCES "essay_task_submission_history"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_task_submission_marking" DROP CONSTRAINT "FK_0276834b14c1f78c5a81eb61c9b"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_marking" DROP CONSTRAINT "FK_84039f2dd37abc5e5ea72261b1b"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_28cca731b1dda53dbe764ee2834"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP CONSTRAINT "FK_2c0f158665674b8d3cbbe35e05f"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "UQ_28cca731b1dda53dbe764ee2834"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP COLUMN "marking"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD "marking" text`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP CONSTRAINT "UQ_2c0f158665674b8d3cbbe35e05f"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP COLUMN "marking"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD "feedback" text`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD "reviewed_at" TIMESTAMP`);
    await queryRunner.query(`DROP INDEX "public"."IDX_090783a2cd92909ecf3f68fb2e"`);
    await queryRunner.query(`DROP TABLE "essay_task_submission_marking"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" RENAME COLUMN "marking" TO "teacher_remarks"`);
  }
}
