#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to run the payment method enum migration safely
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Starting Payment Method Enum Migration...\n');

try {
  // Change to project directory
  const projectDir = path.resolve(__dirname, '..');
  process.chdir(projectDir);

  console.log('📋 Current directory:', process.cwd());
  console.log('📋 Running migration...\n');

  // Run the migration
  const result = execSync('npm run typeorm:run-migrations', { 
    encoding: 'utf8',
    stdio: 'inherit'
  });

  console.log('\n✅ Migration completed successfully!');
  console.log('\n📊 Verifying migration results...');

  // Optional: Run a quick verification
  try {
    const verifyResult = execSync('npm run typeorm:show-migrations', { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    console.log('📋 Migration status:', verifyResult);
  } catch (verifyError) {
    console.log('ℹ️ Could not verify migration status (this is normal)');
  }

  console.log('\n🎉 Payment method enum migration completed!');
  console.log('\n📝 Next steps:');
  console.log('   1. Test the APIs with virtual account and mobile payments');
  console.log('   2. Verify payment URLs are returned correctly');
  console.log('   3. Check that KCP integration works with new enum values');

} catch (error) {
  console.error('\n❌ Migration failed:', error.message);
  console.log('\n🔧 Troubleshooting:');
  console.log('   1. Check database connection');
  console.log('   2. Ensure TypeORM is properly configured');
  console.log('   3. Verify database permissions');
  console.log('   4. Check migration file syntax');
  
  process.exit(1);
}
