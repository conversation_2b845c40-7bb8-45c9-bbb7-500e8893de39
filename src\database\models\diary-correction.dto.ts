import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsInt, IsOptional, <PERSON>, Max } from 'class-validator';

// Request DTOs
export class CreateDiaryCorrectionDto {
  @ApiProperty({ example: 'Here are the corrections to your diary entry...', description: 'Correction text for the diary entry' })
  @IsNotEmpty()
  @IsString()
  correctionText: string;

  @ApiProperty({ example: 85, description: 'Score for the diary entry (0-100)' })
  @IsNotEmpty()
  @IsInt()
  @Min(0)
  @Max(100)
  score: number;

  @ApiProperty({ example: 'Good effort, but pay attention to grammar', required: false })
  @IsOptional()
  @IsString()
  comments?: string;
}

// Response DTOs
export class DiaryCorrectionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  diaryEntryId: string;

  @ApiProperty()
  tutorId: string;

  @ApiProperty({ required: false })
  tutorName?: string;

  @ApiProperty()
  correctionText: string;

  @ApiProperty()
  score: number;

  @ApiProperty({ required: false })
  comments?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
