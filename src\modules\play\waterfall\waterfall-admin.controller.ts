import { Controller, Post, Get, Delete, Patch, Body, Param, UseGuards, Query, Logger, ParseU<PERSON><PERSON><PERSON><PERSON>, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery, ApiParam, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../common/guards/jwt.guard';
import { AdminGuard } from '../../../common/guards/admin.guard';
import { WaterfallAdminService } from './waterfall-admin.service';
import {
  CreateWaterfallSetDto,
  UpdateWaterfallSetDto,
  WaterfallFilterDto,
  WaterfallSetResponseDto,
  UpdateWaterfallSetWrapperDto,
  CreateWaterfallSetWrapperDto,
  ToggleWaterfallStatusDto,
} from '../../../database/models/waterfall/waterfall-set.dto';

import {
  CreateWaterfallQuestionsDto,
  UpdateWaterfallQuestionDto,
  WaterfallQuestionResponseDto,
  CreateWaterfallQuestionsWrapperDto,
  CreateWaterfallQuestionWrapperDto,
  UpdateWaterfallQuestionWrapperDto,
  ToggleWaterfallQuestionStatusDto,
} from '../../../database/models/waterfall/waterfall-question.dto';

import { WaterfallSetFullResponseDto } from '../../../database/models/waterfall/waterfall-response.dto';

import {
  WaterfallParticipantsQueryDto,
  WaterfallParticipantsResponseDto,
  WaterfallStudentParticipationQueryDto,
  WaterfallStudentParticipationResponseDto,
} from '../../../database/models/waterfall/waterfall-participants.dto';

import { PagedListDto } from '../../../common/models/paged-list.dto';

import { ApiResponse } from '../../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType } from '../../../common/decorators/api-response.decorator';

@ApiTags('Play-Waterfall')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, AdminGuard)
@Controller('play/waterfall/admin')
export class WaterfallAdminController {
  private readonly logger = new Logger(WaterfallAdminController.name);

  constructor(private readonly waterfallAdminService: WaterfallAdminService) {}

  @Get('sets')
  @ApiOperation({
    summary: 'Get all waterfall sets',
    description: 'Retrieves all waterfall sets with pagination and filtering options',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (1-based)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Field to sort by' })
  @ApiQuery({ name: 'sortDirection', required: false, enum: ['ASC', 'DESC'], description: 'Sort direction' })
  @ApiQuery({ name: 'title', required: false, type: String, description: 'Filter sets by title (partial match)' })
  @ApiOkResponseWithPagedListType(WaterfallSetResponseDto, 'Waterfall sets retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getAllSets(@Query() filterDto: WaterfallFilterDto): Promise<ApiResponse<PagedListDto<WaterfallSetResponseDto>>> {
    const result = await this.waterfallAdminService.getAllSets(filterDto);

    // Check if any sets were found
    const message = result.items.length === 0 ? 'No waterfall sets found' : 'Waterfall sets retrieved successfully';

    return ApiResponse.success(result, message);
  }

  @Post('sets')
  @ApiOperation({
    summary: 'Create a new waterfall set',
    description: 'Creates a new waterfall set with title and total score',
  })
  @ApiBody({
    type: CreateWaterfallSetWrapperDto,
    description: 'Wrapper containing set creation data',
    examples: {
      example1: {
        summary: 'Example set creation',
        description: 'A sample waterfall set creation',
        value: {
          set: {
            title: 'Basic Grammar Set 1',
            total_score: 50,
          },
        },
      },
    },
  })
  @ApiOkResponseWithType(WaterfallSetResponseDto, 'Waterfall set created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async createWaterfallSet(@Body() wrapper: CreateWaterfallSetWrapperDto): Promise<ApiResponse<any>> {
    const { set } = wrapper;
    const result = await this.waterfallAdminService.createSet(set);

    return ApiResponse.success(
      {
        id: result.id,
        title: result.title,
        total_score: result.totalScore,
        total_questions: result.totalQuestions,
        is_active: result.isActive,
      },
      'Waterfall set created successfully',
      201,
    );
  }

  @Post('sets/:setId/question')
  @ApiOperation({
    summary: 'Create a single question for a waterfall set',
    description: 'Creates a single question for an existing waterfall set',
  })
  @ApiParam({
    name: 'setId',
    description: 'The ID of the waterfall set to add the question to',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    type: CreateWaterfallQuestionWrapperDto,
    description: 'Wrapper containing a question to add to the waterfall set',
    examples: {
      example1: {
        summary: 'Example question',
        description: 'A sample waterfall question',
        value: {
          question: {
            question_text: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
            question_text_plain: 'The cat [[gap]] on the mat.',
            correct_answers: ['sat'],
            options: ['sits', 'sat', 'standing', 'lying'],
            time_limit_in_seconds: 30,
            level: 2,
          },
        },
      },
    },
  })
  @ApiOkResponseWithType(WaterfallQuestionResponseDto, 'Question created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Waterfall set not found')
  async createWaterfallQuestion(@Param('setId', ParseUUIDPipe) setId: string, @Body() wrapper: CreateWaterfallQuestionWrapperDto): Promise<ApiResponse<WaterfallQuestionResponseDto>> {
    const { question } = wrapper;
    const result = await this.waterfallAdminService.createQuestion(setId, question);

    return ApiResponse.success(
      {
        id: result.id,
        question_text: result.questionText,
        question_text_plain: result.questionTextPlain,
        correct_answers: result.correctAnswers,
        options: result.options,
        time_limit_in_seconds: result.timeLimitInSeconds,
        level: result.level,
        created_at: result.createdAt,
        updated_at: result.updatedAt,
        is_active: result.isActive,
      },
      'Question created successfully',
      201,
    );
  }

  @Post('sets/:setId/questions')
  @ApiOperation({
    summary: 'Create questions for a waterfall set',
    description:
      'Bulk creates questions for an existing waterfall set. Valid questions are saved, invalid ones are returned with error details. The request body must be a wrapper object with a "questions" property containing an array of questions.',
  })
  @ApiParam({
    name: 'setId',
    description: 'The ID of the waterfall set to add questions to',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    type: CreateWaterfallQuestionsWrapperDto,
    description: 'Wrapper object containing an array of questions to add to the waterfall set.',
    examples: {
      example1: {
        summary: 'Example questions',
        description: 'A wrapper object containing an array of waterfall questions',
        value: {
          questions: [
            {
              question_text: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
              question_text_plain: 'The cat [[gap]] on the mat.',
              correct_answers: ['sat'],
              options: ['sits', 'sat', 'standing', 'lying'],
              time_limit_in_seconds: 30,
              level: 2,
            },
            {
              question_text: '<p>I <span class="blank-highlight">___</span> to <span class="blank-highlight">___</span> to the store.</p>',
              question_text_plain: 'I [[gap]] to [[gap]] to the store.',
              correct_answers: ['want', 'go'],
              options: ['want', 'go', 'fly', 'walk', 'run'],
              time_limit_in_seconds: 45,
              level: 3,
            },
          ],
        },
      },
    },
  })
  @ApiOkResponseWithType(WaterfallSetResponseDto, 'Questions created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Waterfall set not found')
  async createWaterfallQuestions(@Param('setId', ParseUUIDPipe) setId: string, @Body() wrapper: CreateWaterfallQuestionsWrapperDto): Promise<ApiResponse<any>> {
    const { questions } = wrapper;

    if (questions.length === 0) {
      this.logger.warn(`Empty questions array provided for set ID: ${setId}`);
      throw new BadRequestException('No questions provided');
    }

    try {
      await this.waterfallAdminService.createQuestions(setId, questions);

      const updatedSet = await this.waterfallAdminService.getSetById(setId);

      return ApiResponse.success(
        {
          id: updatedSet.id,
          title: updatedSet.title,
          total_score: updatedSet.totalScore,
          total_questions: updatedSet.totalQuestions,
          is_active: updatedSet.isActive,
          questions_added: questions.length,
          invalid_questions: [],
        },
        'All questions created successfully',
        201,
      );
    } catch (error) {
      // Check if this is a partial validation error (some questions were valid, some invalid)
      if (error instanceof BadRequestException && error.getResponse() && typeof error.getResponse() === 'object' && (error.getResponse() as any).error?.type === 'PartialValidationError') {
        const errorResponse = error.getResponse() as any;
        const updatedSet = await this.waterfallAdminService.getSetById(setId);

        // Create a response with both success and error information
        return ApiResponse.success(
          {
            id: updatedSet.id,
            title: updatedSet.title,
            total_score: updatedSet.totalScore,
            total_questions: updatedSet.totalQuestions,
            is_active: updatedSet.isActive,
            questions_added: errorResponse.partialSuccess.successCount,
            invalid_questions: Object.entries(errorResponse.error.details).map(([key, errors]) => ({
              index: parseInt(key.replace('question', ''), 10),
              errors: errors,
            })),
          },
          `Created ${errorResponse.partialSuccess.successCount} questions successfully, but ${errorResponse.partialSuccess.totalCount - errorResponse.partialSuccess.successCount} questions had validation errors`,
          207, // Using 207 Multi-Status to indicate partial success
        );
      }

      // Log the error but don't rethrow - let the global exception filter handle it
      this.logger.error(`Failed to create questions for waterfall set ID ${setId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Get('sets/:id/full')
  @ApiOperation({
    summary: 'Get a waterfall set with all questions',
    description: 'Retrieves a waterfall set by ID with all its questions and options',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the waterfall set to retrieve',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(WaterfallSetFullResponseDto, 'Waterfall set retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Waterfall set not found')
  async getSetWithQuestions(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<WaterfallSetFullResponseDto>> {
    const result = await this.waterfallAdminService.getSetWithQuestions(id);
    return ApiResponse.success(result, 'Waterfall set retrieved successfully');
  }

  @Patch('sets/:id')
  @ApiOperation({
    summary: 'Update a waterfall set',
    description:
      "Updates a waterfall set's title and total score. Fields not included in the request will remain unchanged. Null values and empty strings will be ignored and previous values will be kept.",
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the waterfall set to update',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    type: UpdateWaterfallSetWrapperDto,
    description: 'Wrapper containing set update data',
    examples: {
      example1: {
        summary: 'Example set update',
        description: 'A sample waterfall set update',
        value: {
          set: {
            title: 'Updated Grammar Set 1',
            total_score: 60,
          },
        },
      },
    },
  })
  @ApiOkResponseWithType(WaterfallSetResponseDto, 'Waterfall set updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Waterfall set not found')
  async updateSet(@Param('id', ParseUUIDPipe) id: string, @Body() wrapper: UpdateWaterfallSetWrapperDto): Promise<ApiResponse<WaterfallSetResponseDto>> {
    const { set } = wrapper;
    const result = await this.waterfallAdminService.updateSet(id, set);

    return ApiResponse.success(
      {
        id: result.id,
        title: result.title,
        total_score: result.totalScore,
        total_questions: result.totalQuestions,
        is_active: result.isActive,
        created_at: result.createdAt,
        updated_at: result.updatedAt,
      },
      'Waterfall set updated successfully',
    );
  }

  @Patch('questions/:question_id')
  @ApiOperation({
    summary: 'Update a specific question',
    description:
      'Updates a specific waterfall question by ID. Fields not included in the request will remain unchanged. Null values and empty strings will be ignored and previous values will be kept.',
  })
  @ApiParam({
    name: 'question_id',
    description: 'The ID of the question to update',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    type: UpdateWaterfallQuestionWrapperDto,
    description: 'Wrapper containing question update data',
    examples: {
      example1: {
        summary: 'Example question update',
        description: 'A sample waterfall question update',
        value: {
          question: {
            question_text: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
            options: ['sits', 'sat', 'standing', 'lying'],
            time_limit_in_seconds: 45,
            level: 3,
          },
        },
      },
    },
  })
  @ApiOkResponseWithType(WaterfallQuestionResponseDto, 'Question updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Question not found')
  async updateQuestion(@Param('question_id', ParseUUIDPipe) questionId: string, @Body() wrapper: UpdateWaterfallQuestionWrapperDto): Promise<ApiResponse<WaterfallQuestionResponseDto>> {
    const { question } = wrapper;
    const result = await this.waterfallAdminService.updateQuestion(questionId, question);

    return ApiResponse.success(
      {
        id: result.id,
        question_text: result.questionText,
        question_text_plain: result.questionTextPlain,
        correct_answers: result.correctAnswers,
        options: result.options,
        time_limit_in_seconds: result.timeLimitInSeconds,
        level: result.level,
        created_at: result.createdAt,
        updated_at: result.updatedAt,
        is_active: result.isActive,
      },
      'Question updated successfully',
    );
  }

  @Delete('questions/:question_id')
  @ApiOperation({
    summary: 'Delete a specific question',
    description: "Deletes a specific waterfall question by ID and updates the set's question count",
  })
  @ApiParam({
    name: 'question_id',
    description: 'The ID of the question to delete',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(Object, 'Question deleted successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Question not found')
  async deleteQuestion(@Param('question_id', ParseUUIDPipe) questionId: string): Promise<ApiResponse<any>> {
    await this.waterfallAdminService.deleteQuestion(questionId);
    return ApiResponse.success({ success: true }, 'Question deleted successfully');
  }

  @Delete('sets/:id')
  @ApiOperation({
    summary: 'Delete a waterfall set',
    description: 'Deletes a waterfall set and all its associated questions',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the waterfall set to delete',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(Object, 'Waterfall set deleted successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Waterfall set not found')
  async deleteSet(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<any>> {
    await this.waterfallAdminService.deleteSet(id);
    return ApiResponse.success({ success: true }, 'Waterfall set deleted successfully');
  }

  @Get('questions/:question_id')
  @ApiOperation({
    summary: 'Get a single waterfall question by ID',
    description: 'Retrieves a specific waterfall question with all its details',
  })
  @ApiParam({
    name: 'question_id',
    description: 'The ID of the question to retrieve',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(WaterfallQuestionResponseDto, 'Question retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Question not found')
  async getQuestionById(@Param('question_id', ParseUUIDPipe) questionId: string): Promise<ApiResponse<WaterfallQuestionResponseDto>> {
    const result = await this.waterfallAdminService.getQuestionById(questionId);
    return ApiResponse.success(result, 'Question retrieved successfully');
  }

  @Get('participants')
  @ApiOperation({
    summary: 'List participants for all sets',
    description: 'Retrieves a paginated list of students who have participated in waterfall sets with their aggregated performance data',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (1-based)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for student name or email' })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['latest_participation', 'highest_score'],
    description: 'Field to sort by (default: latest_participation, highest_score sorts by total marks across all sets)',
  })
  @ApiOkResponseWithType(WaterfallParticipantsResponseDto, 'Participants retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getAllParticipants(@Query() queryDto: WaterfallParticipantsQueryDto): Promise<ApiResponse<WaterfallParticipantsResponseDto>> {
    const result = await this.waterfallAdminService.getAllParticipants(queryDto);

    // Check if any participants were found
    const message = result.participants.length === 0 ? 'No participants found' : 'Participants retrieved successfully';

    return ApiResponse.success(result, message);
  }

  @Get('participants/:user_id')
  @ApiOperation({
    summary: "View specific user's participation detail",
    description: 'Retrieves a paginated list of participation records for a specific student',
  })
  @ApiParam({
    name: 'user_id',
    description: 'The ID of the student',
    type: 'string',
    format: 'uuid',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (1-based)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page' })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['participated_at', 'score'],
    description: 'Field to sort by (default: participated_at)',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (default: DESC)',
  })
  @ApiOkResponseWithType(WaterfallStudentParticipationResponseDto, 'Student participation history retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Student not found')
  async getStudentParticipationHistory(
    @Param('user_id', ParseUUIDPipe) userId: string,
    @Query() queryDto: WaterfallStudentParticipationQueryDto,
  ): Promise<ApiResponse<WaterfallStudentParticipationResponseDto>> {
    const result = await this.waterfallAdminService.getStudentParticipationHistory(userId, queryDto);

    // Check if any participation records were found
    const message = result.participation_records.length === 0 ? 'No participation records found for this student' : 'Student participation history retrieved successfully';

    return ApiResponse.success(result, message);
  }

  @Patch('sets/:id/toggle-status')
  @ApiOperation({
    summary: 'Toggle the active status of a waterfall set',
    description: 'Activates or deactivates a waterfall set, making it available or unavailable to students.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the waterfall set to toggle',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        is_active: {
          type: 'boolean',
          example: true,
          description: 'Whether the waterfall set should be active or not',
        },
      },
    },
  })
  @ApiOkResponseWithType(WaterfallSetResponseDto, 'Waterfall set status updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Waterfall set not found')
  async toggleWaterfallSetStatus(@Param('id', ParseUUIDPipe) id: string, @Body() toggleDto: ToggleWaterfallStatusDto): Promise<ApiResponse<WaterfallSetResponseDto>> {
    const result = await this.waterfallAdminService.toggleWaterfallSetStatus(id, toggleDto);
    return ApiResponse.success(result, `Waterfall set ${toggleDto.is_active ? 'activated' : 'deactivated'} successfully`);
  }

  @Patch(':setId/questions/:questionId/toggle-status')
  @ApiOperation({
    summary: 'Toggle waterfall question status',
    description: 'Toggle the active status of a waterfall question',
  })
  @ApiParam({
    name: 'setId',
    description: 'The ID of the waterfall set',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'questionId',
    description: 'The ID of the waterfall question',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        is_active: {
          type: 'boolean',
          example: true,
          description: 'Whether the waterfall question should be active or not',
        },
      },
    },
  })
  @ApiOkResponseWithType(WaterfallQuestionResponseDto, 'Waterfall question status updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Waterfall question not found')
  async toggleWaterfallQuestionStatus(
    @Param('setId', ParseUUIDPipe) setId: string,
    @Param('questionId', ParseUUIDPipe) questionId: string,
    @Body() toggleDto: ToggleWaterfallQuestionStatusDto,
  ): Promise<ApiResponse<WaterfallQuestionResponseDto>> {
    const result = await this.waterfallAdminService.toggleWaterfallQuestionStatus(setId, questionId, toggleDto);
    return ApiResponse.success(result, `Waterfall question ${toggleDto.is_active ? 'activated' : 'deactivated'} successfully`);
  }
}
