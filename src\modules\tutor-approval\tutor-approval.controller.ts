// Define a custom interface that extends Request to include the user property
import { Request } from 'express';

interface RequestWithUser extends Request {
  user: {
    id: string;
    type: string;
    roles: string[];
    [key: string]: any;
  };
}

import { Controller, Get, Post, Body, Param, UseGuards, Request as NestRequest, HttpStatus, Logger, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiParam, ApiBody, ApiQuery } from '@nestjs/swagger';
import { TutorApprovalService } from './tutor-approval.service';
import { TutorApprovalResponseDto, ApproveTutorDto, RejectTutorDto } from '../../database/models/tutor-approval.dto';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import { Roles } from 'src/common/decorators/roles.decorator';
import { UserType } from 'src/database/entities/user.entity';
import { RolesGuard } from 'src/common/guards/roles.guard';
import { ApiResponse } from 'src/common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiErrorResponse } from 'src/common/decorators/api-response.decorator';
import { PagedListDto } from 'src/common/models/paged-list.dto';
import { PaginationDto } from 'src/common/models/pagination.dto';
import { TutorApprovalStatus } from 'src/database/entities/tutor-approval.entity';

@ApiTags('Tutor Approval')
@Controller('tutor-approval')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class TutorApprovalController {
  private readonly logger = new Logger(TutorApprovalController.name);

  constructor(private readonly tutorApprovalService: TutorApprovalService) {}

  @Get()
  @Roles(UserType.ADMIN)
  @ApiOperation({
    summary: 'Get all tutor approval requests',
    description: 'Admin only: Get all tutor approval requests. Only administrators can manage tutor approvals.',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: TutorApprovalStatus,
    description: 'Filter by approval status',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(TutorApprovalResponseDto, 'Returns all tutor approval requests')
  @ApiErrorResponse(HttpStatus.UNAUTHORIZED, 'Unauthorized - Invalid or missing JWT token')
  @ApiErrorResponse(HttpStatus.FORBIDDEN, 'Forbidden - User does not have admin role')
  async findAll(
    @NestRequest() req: RequestWithUser,
    @Query('status') status?: TutorApprovalStatus,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
  ): Promise<ApiResponse<PagedListDto<TutorApprovalResponseDto>>> {
    this.logger.log(`User ${req.user.id} with roles ${req.user.roles} is accessing tutor approval requests${status ? ` with status ${status}` : ''}`);

    // Create pagination DTO manually
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC',
    };

    const data = await this.tutorApprovalService.findAll(status, paginationDto);
    return ApiResponse.success(data, 'Tutor approval requests retrieved successfully');
  }

  @Get('pending')
  @Roles(UserType.ADMIN)
  @ApiOperation({
    summary: 'Get pending tutor approval requests',
    description: 'Admin only: Get pending tutor approval requests. Only administrators can manage tutor approvals.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(TutorApprovalResponseDto, 'Returns pending tutor approval requests')
  @ApiErrorResponse(HttpStatus.UNAUTHORIZED, 'Unauthorized - Invalid or missing JWT token')
  @ApiErrorResponse(HttpStatus.FORBIDDEN, 'Forbidden - User does not have admin role')
  async findPending(
    @NestRequest() req: RequestWithUser,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
  ): Promise<ApiResponse<PagedListDto<TutorApprovalResponseDto>>> {
    this.logger.log(`User ${req.user.id} with roles ${req.user.roles} is accessing pending tutor approval requests`);

    // Create pagination DTO manually
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC',
    };

    const data = await this.tutorApprovalService.findPending(paginationDto);
    return ApiResponse.success(data, 'Pending tutor approval requests retrieved successfully');
  }

  @Get(':id')
  @Roles(UserType.ADMIN)
  @ApiOperation({
    summary: 'Get a tutor approval request by ID',
    description: 'Admin only: Get a tutor approval request by ID. Only administrators can manage tutor approvals.',
  })
  @ApiParam({ name: 'id', description: 'Tutor approval request ID' })
  @ApiOkResponseWithType(TutorApprovalResponseDto, 'Returns the tutor approval request')
  @ApiErrorResponse(HttpStatus.NOT_FOUND, 'Tutor approval request not found')
  @ApiErrorResponse(HttpStatus.UNAUTHORIZED, 'Unauthorized - Invalid or missing JWT token')
  @ApiErrorResponse(HttpStatus.FORBIDDEN, 'Forbidden - User does not have admin role')
  async findOne(@NestRequest() req: RequestWithUser, @Param('id') id: string): Promise<ApiResponse<TutorApprovalResponseDto>> {
    this.logger.log(`User ${req.user.id} with roles ${req.user.roles} is accessing tutor approval request with ID ${id}`);
    const data = await this.tutorApprovalService.findById(id);
    return ApiResponse.success(data, 'Tutor approval request retrieved successfully');
  }

  @Post('approve')
  @Roles(UserType.ADMIN)
  @ApiOperation({
    summary: 'Approve a tutor',
    description: 'Admin only: Approve a tutor registration request. Only administrators can approve tutor registrations.',
  })
  @ApiBody({ type: ApproveTutorDto })
  @ApiOkResponseWithType(TutorApprovalResponseDto, 'Tutor approved successfully')
  @ApiErrorResponse(HttpStatus.NOT_FOUND, 'Tutor approval request not found')
  @ApiErrorResponse(HttpStatus.BAD_REQUEST, 'Invalid request')
  @ApiErrorResponse(HttpStatus.UNAUTHORIZED, 'Unauthorized - Invalid or missing JWT token')
  @ApiErrorResponse(HttpStatus.FORBIDDEN, 'Forbidden - User does not have admin role')
  async approveTutor(@NestRequest() req: RequestWithUser, @Body() dto: ApproveTutorDto): Promise<ApiResponse<TutorApprovalResponseDto>> {
    this.logger.log(`User ${req.user.id} with roles ${req.user.roles} is approving tutor with approval ID ${dto.approvalId}`);
    const data = await this.tutorApprovalService.approveTutor(req.user.id, dto);
    return ApiResponse.success(data, 'Tutor approved successfully');
  }

  @Post('reject')
  @Roles(UserType.ADMIN)
  @ApiOperation({
    summary: 'Reject a tutor',
    description: 'Admin only: Reject a tutor registration request. Only administrators can reject tutor registrations.',
  })
  @ApiBody({ type: RejectTutorDto })
  @ApiOkResponseWithType(TutorApprovalResponseDto, 'Tutor rejected successfully')
  @ApiErrorResponse(HttpStatus.NOT_FOUND, 'Tutor approval request not found')
  @ApiErrorResponse(HttpStatus.BAD_REQUEST, 'Invalid request')
  @ApiErrorResponse(HttpStatus.UNAUTHORIZED, 'Unauthorized - Invalid or missing JWT token')
  @ApiErrorResponse(HttpStatus.FORBIDDEN, 'Forbidden - User does not have admin role')
  async rejectTutor(@NestRequest() req: RequestWithUser, @Body() dto: RejectTutorDto): Promise<ApiResponse<TutorApprovalResponseDto>> {
    this.logger.log(`User ${req.user.id} with roles ${req.user.roles} is rejecting tutor with approval ID ${dto.approvalId}`);
    const data = await this.tutorApprovalService.rejectTutor(req.user.id, dto);
    return ApiResponse.success(data, 'Tutor rejected successfully');
  }
}
