import { Controller, Get, Put, Body, Param, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { GetUser } from '../../common/decorators/get-user.decorator';
import { User } from '../../database/entities/user.entity';
import { NotificationService } from './notification.service';
import { NotificationPreferenceDto, UpdateNotificationPreferenceDto } from '../../database/models/notification.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { NotificationType } from '../../database/entities/notification.entity';
import { NotificationChannel } from '../../database/entities/notification-delivery.entity';

@ApiTags('notification-preferences')
@Controller('notification-preferences')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class NotificationPreferenceController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get()
  @ApiOperation({ summary: 'Get notification preferences for the current user' })
  @ApiOkResponseWithType(Array, 'Notification preferences retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getNotificationPreferences(@GetUser() user: User): Promise<ApiResponse<NotificationPreferenceDto[]>> {
    const preferences = await this.notificationService.getUserNotificationPreferences(user.id);

    // Map to DTOs
    const preferenceDtos = preferences.map((pref) => ({
      notificationType: pref.notificationType,
      channel: pref.channel,
      isEnabled: pref.isEnabled,
    }));

    return ApiResponse.success(preferenceDtos, 'Notification preferences retrieved successfully');
  }

  @Put(':type/:channel')
  @ApiOperation({ summary: 'Update notification preference' })
  @ApiOkResponseWithType(NotificationPreferenceDto, 'Notification preference updated successfully')
  @ApiErrorResponse(400, 'Invalid notification type or channel')
  @ApiErrorResponse(500, 'Internal server error')
  async updateNotificationPreference(
    @GetUser() user: User,
    @Param('type') type: string,
    @Param('channel') channel: string,
    @Body() updateDto: UpdateNotificationPreferenceDto,
  ): Promise<ApiResponse<NotificationPreferenceDto>> {
    // Validate type and channel
    if (!Object.values(NotificationType).includes(type as NotificationType)) {
      return ApiResponse.error(`Invalid notification type: ${type}`, 400, {});
    }

    if (!Object.values(NotificationChannel).includes(channel as NotificationChannel)) {
      return ApiResponse.error(`Invalid notification channel: ${channel}`, 400, {});
    }

    const preference = await this.notificationService.updateNotificationPreference(user.id, type as NotificationType, channel as NotificationChannel, updateDto.isEnabled);

    return ApiResponse.success(
      {
        notificationType: preference.notificationType,
        channel: preference.channel,
        isEnabled: preference.isEnabled,
      },
      'Notification preference updated successfully',
    );
  }
}
