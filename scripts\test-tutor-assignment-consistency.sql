-- Test Script for Tutor Assignment Consistency
-- This script helps verify that the new tutor assignment system works correctly

-- 1. Check current tutor assignments for students
-- This query shows which students have multiple tutors (inconsistent assignments)
SELECT 
    stm.student_id,
    u.name as student_name,
    COUNT(DISTINCT stm.tutor_id) as tutor_count,
    STRING_AGG(DISTINCT t.name, ', ') as tutor_names,
    STRING_AGG(DISTINCT pf.name, ', ') as feature_names
FROM student_tutor_mapping stm
JOIN "user" u ON stm.student_id = u.id
JOIN "user" t ON stm.tutor_id = t.id
JOIN plan_feature pf ON stm.plan_feature_id = pf.id
WHERE stm.status = 'active'
GROUP BY stm.student_id, u.name
HAVING COUNT(DISTINCT stm.tutor_id) > 1
ORDER BY tutor_count DESC, u.name;

-- 2. Check tutor workload distribution
-- This query shows how many students each tutor has
SELECT 
    t.id as tutor_id,
    t.name as tutor_name,
    COUNT(DISTINCT stm.student_id) as student_count,
    COUNT(stm.id) as total_assignments,
    STRING_AGG(DISTINCT pf.name, ', ') as features
FROM "user" t
LEFT JOIN student_tutor_mapping stm ON t.id = stm.tutor_id AND stm.status = 'active'
LEFT JOIN plan_feature pf ON stm.plan_feature_id = pf.id
WHERE t.type = 'TUTOR' AND t.is_active = true
GROUP BY t.id, t.name
ORDER BY student_count DESC, t.name;

-- 3. Find students with preferred tutors (oldest assignment)
-- This query identifies each student's preferred tutor based on oldest assignment
WITH student_preferred_tutors AS (
    SELECT DISTINCT ON (stm.student_id)
        stm.student_id,
        stm.tutor_id as preferred_tutor_id,
        t.name as preferred_tutor_name,
        stm.assigned_date as first_assignment_date
    FROM student_tutor_mapping stm
    JOIN "user" t ON stm.tutor_id = t.id
    WHERE stm.status = 'active'
    ORDER BY stm.student_id, stm.assigned_date ASC
)
SELECT 
    spt.student_id,
    u.name as student_name,
    spt.preferred_tutor_id,
    spt.preferred_tutor_name,
    spt.first_assignment_date,
    COUNT(stm.id) as total_assignments,
    COUNT(CASE WHEN stm.tutor_id = spt.preferred_tutor_id THEN 1 END) as consistent_assignments,
    COUNT(CASE WHEN stm.tutor_id != spt.preferred_tutor_id THEN 1 END) as inconsistent_assignments
FROM student_preferred_tutors spt
JOIN "user" u ON spt.student_id = u.id
LEFT JOIN student_tutor_mapping stm ON spt.student_id = stm.student_id AND stm.status = 'active'
GROUP BY spt.student_id, u.name, spt.preferred_tutor_id, spt.preferred_tutor_name, spt.first_assignment_date
ORDER BY inconsistent_assignments DESC, u.name;

-- 4. Test data setup for consistency testing
-- Create test students and tutors if they don't exist

-- Insert test tutors
INSERT INTO "user" (id, name, email, type, is_active, is_confirmed, created_at, updated_at)
VALUES 
    ('test-tutor-1', 'Test Tutor 1', '<EMAIL>', 'TUTOR', true, true, NOW(), NOW()),
    ('test-tutor-2', 'Test Tutor 2', '<EMAIL>', 'TUTOR', true, true, NOW(), NOW()),
    ('test-tutor-3', 'Test Tutor 3', '<EMAIL>', 'TUTOR', true, true, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert test students
INSERT INTO "user" (id, name, email, type, is_active, is_confirmed, created_at, updated_at)
VALUES 
    ('test-student-1', 'Test Student 1', '<EMAIL>', 'STUDENT', true, true, NOW(), NOW()),
    ('test-student-2', 'Test Student 2', '<EMAIL>', 'STUDENT', true, true, NOW(), NOW()),
    ('test-student-3', 'Test Student 3', '<EMAIL>', 'STUDENT', true, true, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert test plan features if they don't exist
INSERT INTO plan_feature (id, name, type, description, is_active, created_at, updated_at)
VALUES 
    ('test-feature-1', 'Test Writing Module', 'MODULE', 'Test writing feature', true, NOW(), NOW()),
    ('test-feature-2', 'Test Speaking Module', 'MODULE', 'Test speaking feature', true, NOW(), NOW()),
    ('test-feature-3', 'Test Reading Module', 'MODULE', 'Test reading feature', true, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- 5. Cleanup script for test data
-- Use this to clean up test data after testing
/*
DELETE FROM student_tutor_mapping WHERE student_id LIKE 'test-student-%' OR tutor_id LIKE 'test-tutor-%';
DELETE FROM plan_feature WHERE id LIKE 'test-feature-%';
DELETE FROM "user" WHERE id LIKE 'test-student-%' OR id LIKE 'test-tutor-%';
*/

-- 6. Verify consistent assignments after running the new system
-- This query should show that each student has only one tutor across all features
SELECT 
    stm.student_id,
    u.name as student_name,
    COUNT(DISTINCT stm.tutor_id) as unique_tutors,
    COUNT(stm.id) as total_assignments,
    CASE 
        WHEN COUNT(DISTINCT stm.tutor_id) = 1 THEN 'CONSISTENT'
        ELSE 'INCONSISTENT'
    END as assignment_status,
    STRING_AGG(DISTINCT t.name, ', ') as tutor_names,
    STRING_AGG(DISTINCT pf.name, ', ') as feature_names
FROM student_tutor_mapping stm
JOIN "user" u ON stm.student_id = u.id
JOIN "user" t ON stm.tutor_id = t.id
JOIN plan_feature pf ON stm.plan_feature_id = pf.id
WHERE stm.status = 'active'
GROUP BY stm.student_id, u.name
ORDER BY 
    CASE WHEN COUNT(DISTINCT stm.tutor_id) = 1 THEN 1 ELSE 0 END,
    COUNT(DISTINCT stm.tutor_id) DESC,
    u.name;

-- 7. Performance check - ensure queries are efficient
-- Check for missing indexes that might affect performance
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE tablename = 'student_tutor_mapping' 
    AND (attname IN ('student_id', 'tutor_id', 'plan_feature_id', 'status', 'assigned_date'))
ORDER BY tablename, attname;
