import { DataSource, QueryRunner } from 'typeorm';
import { Logger } from '@nestjs/common';

/**
 * Transaction helper utility for optimized database transaction handling
 * Provides best practices for transaction management with proper error handling
 */
export class TransactionHelper {
  private static readonly logger = new Logger(TransactionHelper.name);

  /**
   * Execute a function within a database transaction with optimized settings
   * @param dataSource The TypeORM DataSource
   * @param operation The operation to execute within the transaction
   * @param options Transaction options
   * @returns The result of the operation
   */
  static async executeInTransaction<T>(
    dataSource: DataSource,
    operation: (queryRunner: QueryRunner) => Promise<T>,
    options: {
      isolationLevel?: 'READ UNCOMMITTED' | 'READ COMMITTED' | 'REPEATABLE READ' | 'SERIALIZABLE';
      timeout?: number; // in milliseconds
      retryOnDeadlock?: boolean;
      maxRetries?: number;
    } = {}
  ): Promise<T> {
    const {
      isolationLevel = 'READ COMMITTED', // Default to READ committed for better performance
      timeout = 30000, // 30 seconds default timeout
      retryOnDeadlock = true,
      maxRetries = 3
    } = options;

    let attempt = 0;
    let lastError: Error;

    while (attempt < maxRetries) {
      const queryRunner = dataSource.createQueryRunner();
      const startTime = Date.now();

      try {
        await queryRunner.connect();
        
        // Set transaction isolation level for optimal performance
        await queryRunner.startTransaction(isolationLevel);

        // Set statement timeout to prevent long-running transactions
        await queryRunner.query(`SET statement_timeout = ${timeout}`);

        this.logger.debug(`Transaction started with isolation level: ${isolationLevel}`);

        // Execute the operation
        const result = await operation(queryRunner);

        // Commit the transaction
        await queryRunner.commitTransaction();

        const duration = Date.now() - startTime;
        this.logger.debug(`Transaction completed successfully in ${duration}ms`);

        return result;

      } catch (error) {
        const duration = Date.now() - startTime;
        lastError = error;

        try {
          // Rollback the transaction
          await queryRunner.rollbackTransaction();
          this.logger.warn(`Transaction rolled back after ${duration}ms due to error: ${error.message}`);
        } catch (rollbackError) {
          this.logger.error(`Failed to rollback transaction: ${rollbackError.message}`);
        }

        // Check if this is a deadlock error and we should retry
        if (retryOnDeadlock && this.isDeadlockError(error) && attempt < maxRetries - 1) {
          attempt++;
          const delay = Math.min(1000 * Math.pow(2, attempt), 5000); // Exponential backoff, max 5s
          this.logger.warn(`Deadlock detected, retrying transaction (attempt ${attempt + 1}/${maxRetries}) after ${delay}ms`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }

        throw error;

      } finally {
        // Always release the query runner
        try {
          await queryRunner.release();
        } catch (releaseError) {
          this.logger.error(`Failed to release query runner: ${releaseError.message}`);
        }
      }
    }

    throw lastError;
  }

  /**
   * Execute multiple operations in a single optimized transaction
   * @param dataSource The TypeORM DataSource
   * @param operations Array of operations to execute
   * @param options Transaction options
   * @returns Array of results
   */
  static async executeBatchInTransaction<T>(
    dataSource: DataSource,
    operations: ((queryRunner: QueryRunner) => Promise<T>)[],
    options: {
      isolationLevel?: 'READ UNCOMMITTED' | 'READ COMMITTED' | 'REPEATABLE READ' | 'SERIALIZABLE';
      timeout?: number;
      failFast?: boolean; // Stop on first error
    } = {}
  ): Promise<T[]> {
    const { failFast = true } = options;

    return this.executeInTransaction(
      dataSource,
      async (queryRunner) => {
        const results: T[] = [];
        
        for (let i = 0; i < operations.length; i++) {
          try {
            const result = await operations[i](queryRunner);
            results.push(result);
          } catch (error) {
            if (failFast) {
              throw error;
            }
            this.logger.warn(`Operation ${i} failed: ${error.message}`);
            results.push(null as T);
          }
        }

        return results;
      },
      options
    );
  }

  /**
   * Execute a read-only operation with optimized settings
   * @param dataSource The TypeORM DataSource
   * @param operation The read operation to execute
   * @returns The result of the operation
   */
  static async executeReadOnly<T>(
    dataSource: DataSource,
    operation: (queryRunner: QueryRunner) => Promise<T>
  ): Promise<T> {
    return this.executeInTransaction(
      dataSource,
      async (queryRunner) => {
        // Set transaction to read-only for better performance
        await queryRunner.query('SET TRANSACTION READ ONLY');
        return operation(queryRunner);
      },
      {
        isolationLevel: 'READ COMMITTED',
        timeout: 10000, // Shorter timeout for read operations
        retryOnDeadlock: false // No need to retry read operations
      }
    );
  }

  /**
   * Check if an error is a deadlock error
   * @param error The error to check
   * @returns True if the error is a deadlock
   */
  private static isDeadlockError(error: any): boolean {
    if (!error) return false;
    
    const message = error.message?.toLowerCase() || '';
    const code = error.code;

    // PostgreSQL deadlock detection
    return (
      code === '40P01' || // deadlock_detected
      code === '40001' || // serialization_failure
      message.includes('deadlock') ||
      message.includes('could not serialize access')
    );
  }

  /**
   * Prepare data before transaction to minimize transaction time
   * @param preparationFn Function to prepare data
   * @param transactionFn Function to execute in transaction with prepared data
   * @param dataSource The TypeORM DataSource
   * @returns The result of the transaction
   */
  static async prepareAndExecute<TData, TResult>(
    preparationFn: () => Promise<TData>,
    transactionFn: (data: TData, queryRunner: QueryRunner) => Promise<TResult>,
    dataSource: DataSource,
    options?: {
      isolationLevel?: 'READ UNCOMMITTED' | 'READ COMMITTED' | 'REPEATABLE READ' | 'SERIALIZABLE';
      timeout?: number;
    }
  ): Promise<TResult> {
    // Prepare data outside of transaction to minimize lock time
    const preparedData = await preparationFn();

    // Execute transaction with prepared data
    return this.executeInTransaction(
      dataSource,
      (queryRunner) => transactionFn(preparedData, queryRunner),
      options
    );
  }
}
