import { MigrationInterface, QueryRunner } from 'typeorm';

export class QAAssignmentEnhnaceModelAssign1748491136065 implements MigrationInterface {
  name = 'QAAssignmentEnhnaceModelAssign1748491136065';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_assignment" ALTER COLUMN "points" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_assignment" ALTER COLUMN "points" SET NOT NULL`);
  }
}
