import { MigrationInterface, QueryRunner } from 'typeorm';

export class QASubmissionModelAdd1746423556540 implements MigrationInterface {
  name = 'QASubmissionModelAdd1746423556540';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."qa_task_submissions_status_enum" AS ENUM('draft', 'submitted', 'reviewed')`);
    await queryRunner.query(
      `CREATE TABLE "qa_task_submissions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "task_id" uuid NOT NULL, "status" "public"."qa_task_submissions_status_enum" NOT NULL DEFAULT 'draft', "current_revision" integer NOT NULL DEFAULT '1', "total_revisions" integer NOT NULL DEFAULT '1', "is_first_revision" boolean NOT NULL DEFAULT true, "is_active" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_86c7651ecffd3489db0989bb107" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "qa_task_submissions" ADD CONSTRAINT "FK_dcc87476add377484ba58e60c90" FOREIGN KEY ("task_id") REFERENCES "qa_task_missions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_task_submissions" DROP CONSTRAINT "FK_dcc87476add377484ba58e60c90"`);
    await queryRunner.query(`DROP TABLE "qa_task_submissions"`);
    await queryRunner.query(`DROP TYPE "public"."qa_task_submissions_status_enum"`);
  }
}
