import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, Req, BadRequestException } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiTags, ApiOkResponse } from '@nestjs/swagger';
import { AwardsService } from './awards.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { StudentGuard } from '../../common/guards/student.guard';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType } from '../../common/decorators/api-response.decorator';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { EnumValidationPipe } from '../../common/pipes/enum-validation.pipe';
import {
  CreateAwardDto,
  UpdateAwardDto,
  AwardResponseDto,
  CreateRewardPointDto,
  RewardPointResponseDto,
  UserRewardPointBalanceDto,
  CreateAwardWinnerDto,
  AwardWinnerResponseDto,
  UserAwardsResponseDto,
} from '../../database/models/award.dto';
import { AwardModule } from '../../database/entities/award.entity';
import { AwardFrequency } from '../../database/entities/award.entity';
import { AwardCriteriaConfig, getModuleCriteriaMap, getCriteriaForModule } from '../../constants/award-criteria.constant';

@ApiTags('awards')
@Controller('awards')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class AwardsController {
  constructor(private readonly awardsService: AwardsService) {}

  // Admin endpoints for managing awards

  @Post()
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Create a new award (Admin only)',
    description: 'Creates a new award with the specified details.',
  })
  @ApiBody({
    type: CreateAwardDto,
    description: 'Award creation data',
  })
  @ApiOkResponseWithType(AwardResponseDto, 'Award created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(409, 'Award with the same name already exists')
  async createAward(@Body() createAwardDto: CreateAwardDto): Promise<ApiResponse<AwardResponseDto>> {
    const award = await this.awardsService.createAward(createAwardDto);
    return ApiResponse.success(award, 'Award created successfully', 201);
  }

  @Get('admin')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Get all awards (Admin only)',
    description: 'Get a list of all awards, optionally filtered by module, name, and including inactive awards.',
  })
  @ApiQuery({
    name: 'module',
    required: false,
    enum: AwardModule,
    description: 'Filter awards by module (diary, play, qa, novel, essay)',
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: 'Filter awards by name (partial match if 3+ characters)',
  })
  @ApiQuery({
    name: 'includeInactive',
    required: false,
    type: String,
    description: 'Whether to include inactive awards (pass "true" to include inactive awards)',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiQuery({
    name: 'frequency',
    required: false,
    enum: AwardFrequency,
    description: 'Award frequency (daily, weekly, monthly, yearly, one-time)',
  })
  @ApiOkResponseWithPagedListType(AwardResponseDto, 'Awards retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getAllAwards(
    @Query('module', new EnumValidationPipe(AwardModule)) module?: AwardModule,
    @Query('includeInactive') includeInactive?: string,
    @Query('name') name?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
    @Query('frequency') frequency?: AwardFrequency,
  ): Promise<ApiResponse<PagedListDto<AwardResponseDto>>> {
    // Create pagination DTO manually
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC',
    };

    const awards = await this.awardsService.getAllAwards(module, includeInactive === 'true', name, paginationDto, frequency);
    return ApiResponse.success(awards, 'Awards retrieved successfully');
  }

  @Get('admin/:id')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Get award by ID (Admin only)',
    description: 'Get details of a specific award by ID.',
  })
  @ApiParam({
    name: 'id',
    description: 'Award ID',
    type: String,
  })
  @ApiOkResponseWithType(AwardResponseDto, 'Award retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Award not found')
  async getAwardById(@Param('id') id: string): Promise<ApiResponse<AwardResponseDto>> {
    const award = await this.awardsService.getAwardById(id);
    return ApiResponse.success(award, 'Award retrieved successfully');
  }

  @Patch(':id')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Update an award (Admin only)',
    description: 'Update an existing award with the specified details.',
  })
  @ApiParam({
    name: 'id',
    description: 'Award ID',
    type: String,
  })
  @ApiBody({
    type: UpdateAwardDto,
    description: 'Award update data',
  })
  @ApiOkResponseWithType(AwardResponseDto, 'Award updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Award not found')
  @ApiErrorResponse(409, 'Award with the same name already exists')
  async updateAward(@Param('id') id: string, @Body() updateAwardDto: UpdateAwardDto): Promise<ApiResponse<AwardResponseDto>> {
    const award = await this.awardsService.updateAward(id, updateAwardDto);
    return ApiResponse.success(award, 'Award updated successfully');
  }

  @Delete(':id')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Delete an award (Admin only)',
    description: 'Delete an existing award or mark it as inactive if it has been assigned to users.',
  })
  @ApiParam({
    name: 'id',
    description: 'Award ID',
    type: String,
  })
  @ApiOkResponseWithType(Object, 'Award deleted successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Award not found')
  async deleteAward(@Param('id') id: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
    const result = await this.awardsService.deleteAward(id);
    return ApiResponse.success(result, result.message);
  }

  // Admin endpoints for managing reward points

  @Post('reward-points')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Create a reward point transaction (Admin only)',
    description: 'Creates a new reward point transaction for a user.',
  })
  @ApiBody({
    type: CreateRewardPointDto,
    description: 'Reward point creation data',
  })
  @ApiOkResponseWithType(RewardPointResponseDto, 'Reward point created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'User not found')
  async createRewardPoint(@Body() createRewardPointDto: CreateRewardPointDto): Promise<ApiResponse<RewardPointResponseDto>> {
    const rewardPoint = await this.awardsService.createRewardPoint(createRewardPointDto);
    return ApiResponse.success(rewardPoint, 'Reward point created successfully', 201);
  }

  @Get('reward-points/:userId')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Get user reward point balance (Admin only)',
    description: 'Get the reward point balance and recent transactions for a specific user.',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    type: String,
  })
  @ApiOkResponseWithType(UserRewardPointBalanceDto, 'User reward point balance retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'User not found')
  async getUserRewardPointBalance(@Param('userId') userId: string): Promise<ApiResponse<UserRewardPointBalanceDto>> {
    const balance = await this.awardsService.getUserRewardPointBalance(userId);
    return ApiResponse.success(balance, 'User reward point balance retrieved successfully');
  }

  // Admin endpoints for managing award winners

  @Post('winners')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Create an award winner (Admin only)',
    description: 'Assigns an award to a user and creates the corresponding reward points.',
  })
  @ApiBody({
    type: CreateAwardWinnerDto,
    description: 'Award winner creation data',
  })
  @ApiOkResponseWithType(AwardWinnerResponseDto, 'Award winner created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'User or award not found')
  async createAwardWinner(@Body() createAwardWinnerDto: CreateAwardWinnerDto): Promise<ApiResponse<AwardWinnerResponseDto>> {
    const awardWinner = await this.awardsService.createAwardWinner(createAwardWinnerDto);
    return ApiResponse.success(awardWinner, 'Award winner created successfully', 201);
  }

  @Get('winners')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Get all award winners (Admin only)',
    description: 'Get a list of all award winners, optionally filtered by award ID.',
  })
  @ApiQuery({
    name: 'awardId',
    required: false,
    type: String,
    description: 'Filter winners by award ID',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(AwardWinnerResponseDto, 'Award winners retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getAllAwardWinners(
    @Query('awardId') awardId?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
  ): Promise<ApiResponse<PagedListDto<AwardWinnerResponseDto>>> {
    // Create pagination DTO manually
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC',
    };

    const winners = await this.awardsService.getAllAwardWinners(awardId, paginationDto);
    return ApiResponse.success(winners, 'Award winners retrieved successfully');
  }

  @Get('winners/:userId')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Get user awards (Admin only)',
    description: 'Get all awards earned by a specific user.',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    type: String,
  })
  @ApiOkResponseWithType(UserAwardsResponseDto, 'User awards retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'User not found')
  async getUserAwardsAdmin(@Param('userId') userId: string): Promise<ApiResponse<UserAwardsResponseDto>> {
    const awards = await this.awardsService.getUserAwards(userId);
    return ApiResponse.success(awards, 'User awards retrieved successfully');
  }

  // Public endpoints for getting criteria information

  @Get('criteria')
  @ApiOperation({
    summary: 'Get award criteria information',
    description: 'Get available award criteria, optionally filtered by module. When no module is specified, returns all criteria organized by module.',
  })
  @ApiQuery({
    name: 'module',
    required: false,
    enum: AwardModule,
    description: 'Filter criteria by specific module (diary, novel, essay). If not provided, returns all criteria organized by module.',
  })
  @ApiOkResponse({
    description: 'Award criteria retrieved successfully',
    schema: {
      oneOf: [
        {
          // Response when no module filter is applied
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            message: { type: 'string', example: 'Award criteria retrieved successfully' },
            data: {
              type: 'object',
              properties: {
                diary: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string', example: 'diary_score' },
                      name: { type: 'string', example: 'Diary Score' },
                      description: { type: 'string', example: 'Evaluation based on diary entry scores' },
                      module: { type: 'string', example: 'diary' },
                    },
                  },
                },
                novel: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string', example: 'novel_performance' },
                      name: { type: 'string', example: 'Novel Performance' },
                      description: { type: 'string', example: 'Overall performance in the novel module' },
                      module: { type: 'string', example: 'novel' },
                    },
                  },
                },
              },
            },
          },
        },
        {
          // Response when module filter is applied
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            message: { type: 'string', example: 'Award criteria for diary module retrieved successfully' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string', example: 'diary_score' },
                  name: { type: 'string', example: 'Diary Score' },
                  description: { type: 'string', example: 'Evaluation based on diary entry scores' },
                  module: { type: 'string', example: 'diary' },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiErrorResponse(400, 'Invalid module specified')
  @ApiErrorResponse(401, 'Unauthorized')
  async getAwardCriteria(@Query('module') module?: string): Promise<ApiResponse<Record<AwardModule, AwardCriteriaConfig[]> | AwardCriteriaConfig[]>> {
    // Manual validation for the module parameter
    if (module && !Object.values(AwardModule).includes(module as AwardModule)) {
      const validModules = Object.values(AwardModule).join(', ');
      throw new BadRequestException(`Invalid module '${module}'. Valid modules are: ${validModules}`);
    }
    if (module) {
      // Return criteria for specific module
      const criteriaForModule = getCriteriaForModule(module as AwardModule);
      return ApiResponse.success(criteriaForModule, `Award criteria for ${module} module retrieved successfully`);
    } else {
      // Return all criteria organized by module
      const criteriaMap = getModuleCriteriaMap();
      return ApiResponse.success(criteriaMap, 'Award criteria retrieved successfully');
    }
  }

  // Student endpoints for viewing their own awards and reward points

  @Get('available')
  @UseGuards(StudentGuard)
  @ApiOperation({
    summary: 'Get available awards (Student only)',
    description: 'Get a list of all active awards available to students, optionally filtered by module and name.',
  })
  @ApiQuery({
    name: 'module',
    required: false,
    enum: AwardModule,
    description: 'Filter awards by module (diary, play, qa, novel, essay)',
  })
  @ApiQuery({
    name: 'name',
    required: false,
    type: String,
    description: 'Filter awards by name (partial match if 3+ characters)',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(AwardResponseDto, 'Awards retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getAvailableAwards(
    @Query('module', new EnumValidationPipe(AwardModule)) module?: AwardModule,
    @Query('name') name?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
  ): Promise<ApiResponse<PagedListDto<AwardResponseDto>>> {
    // Create pagination DTO manually
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC',
    };

    const awards = await this.awardsService.getAllAwards(module, false, name, paginationDto);
    return ApiResponse.success(awards, 'Awards retrieved successfully');
  }

  @Get('my-awards')
  @UseGuards(StudentGuard)
  @ApiOperation({
    summary: 'Get my awards (Student only)',
    description: 'Get all awards earned by the current student.',
  })
  @ApiOkResponseWithType(UserAwardsResponseDto, 'User awards retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'User not found')
  async getMyAwards(@Req() req: any): Promise<ApiResponse<UserAwardsResponseDto>> {
    const userId = req.user.sub;
    const awards = await this.awardsService.getUserAwards(userId);
    return ApiResponse.success(awards, 'Your awards retrieved successfully');
  }

  @Get('my-points')
  @UseGuards(StudentGuard)
  @ApiOperation({
    summary: 'Get my reward points (Student only)',
    description: 'Get the reward point balance and recent transactions for the current student.',
  })
  @ApiOkResponseWithType(UserRewardPointBalanceDto, 'User reward point balance retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'User not found')
  async getMyRewardPoints(@Req() req: any): Promise<ApiResponse<UserRewardPointBalanceDto>> {
    const userId = req.user.sub;
    const balance = await this.awardsService.getUserRewardPointBalance(userId);
    return ApiResponse.success(balance, 'Your reward point balance retrieved successfully');
  }
}
