import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNovelEntryStatuses1746500000000 implements MigrationInterface {
  name = 'AddNovelEntryStatuses1746500000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the table exists first
    const tableExists = await queryRunner.hasTable('novel_entry');
    if (!tableExists) {
      console.log('novel_entry table does not exist, skipping migration');
      return;
    }

    // Check if the new constraint already exists
    const existingConstraintQuery = `
      SELECT tc.constraint_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
      WHERE tc.table_name = 'novel_entry'
      AND tc.constraint_type = 'CHECK'
      AND tc.constraint_name = 'novel_entry_status_check'
    `;

    const existingConstraints = await queryRunner.query(existingConstraintQuery);
    if (existingConstraints.length > 0) {
      console.log('novel_entry_status_check constraint already exists, skipping migration');
      return;
    }

    // Find and drop any existing check constraints on the status column
    const constraintQuery = `
      SELECT tc.constraint_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
      WHERE tc.table_name = 'novel_entry'
      AND tc.constraint_type = 'CHECK'
      AND (cc.check_clause LIKE '%status%' OR cc.check_clause LIKE '%"status"%')
    `;

    const constraints = await queryRunner.query(constraintQuery);

    // Check for any invalid status values before proceeding
    const invalidStatusQuery = `
      SELECT DISTINCT status
      FROM "novel_entry"
      WHERE status NOT IN ('new', 'submitted', 'updated', 'correction_given', 'reviewed', 'under_review', 'confirmed')
    `;

    const invalidStatuses = await queryRunner.query(invalidStatusQuery);
    if (invalidStatuses.length > 0) {
      console.log(
        'Found invalid status values:',
        invalidStatuses.map((row: any) => row.status),
      );
      console.log('Updating invalid status values to "new"...');

      // Update any invalid status values to 'new'
      await queryRunner.query(`
        UPDATE "novel_entry"
        SET status = 'new'
        WHERE status NOT IN ('new', 'submitted', 'updated', 'correction_given', 'reviewed', 'under_review', 'confirmed')
      `);
      console.log('Updated invalid status values to "new"');
    }

    // Drop existing status check constraints
    for (const constraint of constraints) {
      const constraintName = constraint.constraint_name;
      console.log(`Dropping existing constraint: ${constraintName}`);
      try {
        await queryRunner.query(`
          ALTER TABLE "novel_entry"
          DROP CONSTRAINT "${constraintName}"
        `);
        console.log(`Successfully dropped constraint: ${constraintName}`);
      } catch (error) {
        console.log(`Error dropping constraint ${constraintName}:`, error.message);
        // Continue with next constraint
      }
    }

    // Add new check constraint with updated statuses
    console.log('Adding new novel_entry_status_check constraint...');
    try {
      await queryRunner.query(`
        ALTER TABLE "novel_entry"
        ADD CONSTRAINT "novel_entry_status_check"
        CHECK ("status" IN ('new', 'submitted', 'updated', 'correction_given', 'reviewed', 'under_review', 'confirmed'))
      `);
      console.log('Successfully added novel_entry_status_check constraint');
    } catch (error) {
      console.log('Error adding novel_entry_status_check constraint:', error.message);
      throw error;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Check if the table exists first
    const tableExists = await queryRunner.hasTable('novel_entry');
    if (!tableExists) {
      console.log('novel_entry table does not exist, skipping rollback');
      return;
    }

    // Drop the new constraint if it exists
    console.log('Dropping novel_entry_status_check constraint...');
    try {
      await queryRunner.query(`
        ALTER TABLE "novel_entry"
        DROP CONSTRAINT IF EXISTS "novel_entry_status_check"
      `);
      console.log('Successfully dropped novel_entry_status_check constraint');
    } catch (error) {
      console.log('Error dropping novel_entry_status_check constraint:', error.message);
    }

    // Check if original constraint already exists before adding it
    const originalConstraintQuery = `
      SELECT tc.constraint_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
      WHERE tc.table_name = 'novel_entry'
      AND tc.constraint_type = 'CHECK'
      AND (cc.check_clause LIKE '%status%' OR cc.check_clause LIKE '%"status"%')
    `;

    const existingConstraints = await queryRunner.query(originalConstraintQuery);
    if (existingConstraints.length === 0) {
      // Restore original constraint only if no status constraint exists
      console.log('Restoring original status constraint...');
      try {
        await queryRunner.query(`
          ALTER TABLE "novel_entry"
          ADD CONSTRAINT "novel_entry_status_check_original"
          CHECK ("status" IN ('new', 'submitted', 'updated', 'reviewed', 'confirmed'))
        `);
        console.log('Successfully restored original status constraint');
      } catch (error) {
        console.log('Error restoring original constraint:', error.message);
      }
    } else {
      console.log('Status constraint already exists, skipping restoration');
    }
  }
}
