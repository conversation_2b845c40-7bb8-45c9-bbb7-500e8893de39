import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { PaymentController } from './payment.controller';
import { PaymentService } from './services/payment.service';
import { KcpService } from './services/kcp.service';
import { KcpConfigService } from './services/kcp-config.service';
import { PaymentResponseService } from './services/payment-response.service';
import { PaymentTransaction } from '../../database/entities/payment-transaction.entity';
import { PaymentWebhook } from '../../database/entities/payment-webhook.entity';
import { ShopItemPurchase } from '../../database/entities/shop-item-purchase.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { User } from '../../database/entities/user.entity';
import { ShopModule } from '../shop/shop.module';
import { PlansModule } from '../plans/plans.module';
import { NotificationModule } from '../notification/notification.module';
import { CommonModule } from '../../common/common.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PaymentTransaction, PaymentWebhook, ShopItemPurchase, UserPlan, User]),
    ConfigModule,
    HttpModule,
    CommonModule,
    NotificationModule,
    forwardRef(() => ShopModule),
    forwardRef(() => PlansModule),
  ],
  controllers: [PaymentController],
  providers: [PaymentService, KcpService, KcpConfigService, PaymentResponseService],
  exports: [PaymentService, KcpService, KcpConfigService, PaymentResponseService],
})
export class PaymentModule {}
