import { MigrationInterface, QueryRunner } from 'typeorm';

export class MakeCategoryIdNullableInDiaryMission1752920000000 implements MigrationInterface {
  name = 'MakeCategoryIdNullableInDiaryMission1752920000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Make category_id nullable in diary_mission table
    await queryRunner.query(`
      ALTER TABLE "diary_mission" 
      ALTER COLUMN "category_id" DROP NOT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Set default category for any missions that have null category_id
    await queryRunner.query(`
      UPDATE "diary_mission" 
      SET "category_id" = (SELECT "id" FROM "category" WHERE "name" = 'General' LIMIT 1)
      WHERE "category_id" IS NULL
    `);

    // Make category_id NOT NULL again
    await queryRunner.query(`
      ALTER TABLE "diary_mission" 
      ALTER COLUMN "category_id" SET NOT NULL
    `);
  }
}
