import { MigrationInterface, QueryRunner } from 'typeorm';

export class QASubmissionEnhancedone1747896513672 implements MigrationInterface {
  name = 'QASubmissionEnhancedone1747896513672';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" DROP CONSTRAINT "FK_5769bb5bb9b640e0d811df4c4f0"`);
    await queryRunner.query(`ALTER TABLE "qa_task_submissions" DROP CONSTRAINT "FK_dcc87476add377484ba58e60c90"`);
    await queryRunner.query(
      `ALTER TABLE "qa_task_submission_marking" ADD CONSTRAINT "FK_5769bb5bb9b640e0d811df4c4f0" FOREIGN KEY ("submission_mark_id") REFERENCES "qa_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "qa_task_submissions" ADD CONSTRAINT "FK_dcc87476add377484ba58e60c90" FOREIGN KEY ("task_id") REFERENCES "qa_mission_tasks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_task_submissions" DROP CONSTRAINT "FK_dcc87476add377484ba58e60c90"`);
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" DROP CONSTRAINT "FK_5769bb5bb9b640e0d811df4c4f0"`);
    await queryRunner.query(
      `ALTER TABLE "qa_task_submissions" ADD CONSTRAINT "FK_dcc87476add377484ba58e60c90" FOREIGN KEY ("task_id") REFERENCES "qa_task_missions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "qa_task_submission_marking" ADD CONSTRAINT "FK_5769bb5bb9b640e0d811df4c4f0" FOREIGN KEY ("submission_mark_id") REFERENCES "essay_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
