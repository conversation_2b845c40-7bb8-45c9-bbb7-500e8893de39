import { Controller, Post, Get, Patch, Delete, Body, Query, UseGuards, UseInterceptors, UploadedFile, BadRequestException, Param, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiBody, ApiConsumes, ApiQuery, ApiParam } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../../../common/guards/jwt.guard';
import { AdminGuard } from '../../../common/guards/admin.guard';
import { StoryMakerAdminService } from './story-maker-admin.service';
import { CreateStoryMakerDto, UpdateStoryMakerDto, StoryMakerResponseDto, GetStoriesQueryDto, ToggleStoryStatusDto } from '../../../database/models/story-maker/story-maker.dto';
import { GetParticipantsQueryDto, ParticipantDetailResponseDto, GroupedParticipantListResponseDto } from '../../../database/models/story-maker/story-maker-admin.dto';
import { GetUser } from '../../../common/decorators/get-user.decorator';
import { User } from '../../../database/entities/user.entity';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType } from '../../../common/decorators/api-response.decorator';
import { PagedListDto } from '../../../common/models/paged-list.dto';

@ApiTags('Play-StoryMaker')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, AdminGuard)
@Controller('play/story-maker/admin')
export class StoryMakerAdminController {
  constructor(private readonly storyMakerAdminService: StoryMakerAdminService) {}

  @Post('stories')
  @UseInterceptors(
    FileInterceptor('picture', {
      limits: {
        fileSize: 2 * 1024 * 1024, // 2MB limit
      },
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new story',
    description: 'Creates a new story with title, instruction, picture, and score',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          example: 'Adventure in the Forest',
          description: 'Title of the story',
        },
        instruction: {
          type: 'string',
          example: '<p>Create a story about a magical forest adventure</p>',
          description: 'Instruction text in rich text format',
        },
        deadline: {
          type: 'number',
          example: 2,
          description: 'Optional deadline in days for completing the story',
        },
        picture: {
          type: 'string',
          format: 'binary',
          description: 'Picture file for the story. Must be JPEG, PNG, or GIF format. Maximum file size: 2MB.',
        },
      },
      required: ['title', 'instruction', 'score', 'picture'],
    },
  })
  @ApiOkResponseWithType(StoryMakerResponseDto, 'Story created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async createStory(@Body() createStoryMakerDto: CreateStoryMakerDto, @UploadedFile() picture: Express.Multer.File): Promise<ApiResponse<StoryMakerResponseDto>> {
    if (!picture) {
      throw new BadRequestException('Picture is required');
    }

    const result = await this.storyMakerAdminService.createStory(createStoryMakerDto, picture);
    return ApiResponse.success(result, 'Story created successfully', 201);
  }

  @Get('stories')
  @ApiOperation({
    summary: 'Get all stories',
    description: 'Returns a paginated list of all stories with optional filtering and sorting',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (1-based)', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiQuery({ name: 'sortBy', required: false, enum: ['title', 'createdAt', 'score'], description: 'Field to sort by' })
  @ApiQuery({ name: 'sortDirection', required: false, enum: ['ASC', 'DESC'], description: 'Sort direction' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for story title' })
  @ApiQuery({ name: 'isActive', required: false, type: Boolean, description: 'Filter by active status' })
  @ApiOkResponseWithPagedListType(StoryMakerResponseDto, 'Stories retrieved successfully or no stories found message')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getStories(@Query() query: GetStoriesQueryDto): Promise<ApiResponse<PagedListDto<StoryMakerResponseDto>>> {
    const result = await this.storyMakerAdminService.getStories(query);

    // Check if any stories were found
    const message = result.totalCount > 0 ? 'Stories retrieved successfully' : 'No stories found. Create some stories to get started.';

    return ApiResponse.success(result, message);
  }

  @Get('stories/:id')
  @ApiOperation({
    summary: 'Get a story by ID (Admin)',
    description: 'Retrieves a specific story by its ID. Admin can view both active and inactive stories.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story to retrieve',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponseWithType(StoryMakerResponseDto, 'Story retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Story not found')
  async getStoryById(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<StoryMakerResponseDto>> {
    const result = await this.storyMakerAdminService.getStoryById(id);
    return ApiResponse.success(result, 'Story retrieved successfully');
  }

  @Patch('stories/:id')
  @UseInterceptors(
    FileInterceptor('picture', {
      limits: {
        fileSize: 2 * 1024 * 1024, // 2MB limit
      },
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update a story',
    description:
      'Updates an existing story with the provided data. Picture is optional. Fields not included in the request will remain unchanged. Null values and empty strings will be ignored and previous values will be kept. Cannot update stories that have student participation records.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story to update',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          example: 'Updated Adventure in the Forest',
          description: 'Title of the story',
        },
        instruction: {
          type: 'string',
          example: '<p>Create a story about a magical forest adventure with dragons</p>',
          description: 'Instruction text in rich text format',
        },
        isActive: {
          type: 'boolean',
          example: true,
          description: 'Whether the story is active and available to students',
        },
        deadline: {
          type: 'number',
          example: 2,
          description: 'Optional deadline in days for completing the story',
        },
        picture: {
          type: 'string',
          format: 'binary',
          description: 'Picture file for the story. Must be JPEG, PNG, or GIF format. Maximum file size: 2MB.',
        },
      },
    },
  })
  @ApiOkResponseWithType(StoryMakerResponseDto, 'Story updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required or story has student participation')
  @ApiErrorResponse(404, 'Story not found')
  async updateStory(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateStoryMakerDto: UpdateStoryMakerDto,
    @UploadedFile() picture?: Express.Multer.File,
  ): Promise<ApiResponse<StoryMakerResponseDto>> {
    const result = await this.storyMakerAdminService.updateStory(id, updateStoryMakerDto, picture);
    return ApiResponse.success(result, 'Story updated successfully');
  }

  @Delete('stories/:id')
  @ApiOperation({
    summary: 'Delete a story',
    description: 'Deletes a story and its associated files. Cannot delete stories that have student participation records.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story to delete',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required or story has student participation')
  @ApiErrorResponse(404, 'Story not found')
  async deleteStory(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<void>> {
    await this.storyMakerAdminService.deleteStory(id);
    return ApiResponse.success(null, 'Story deleted successfully');
  }

  @Get('participants')
  @ApiOperation({
    summary: 'Get participants grouped by student and story',
    description: 'Returns a list of participants grouped by student and story, with attempt counts for each story.',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (1-based)', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for student name or email', example: 'john' })
  @ApiQuery({ name: 'storyMakerId', required: false, type: String, description: 'Filter by story maker ID', example: '123e4567-e89b-12d3-a456-************' })
  @ApiQuery({
    name: 'isEvaluated',
    required: false,
    type: Boolean,
    description: 'Filter by evaluation status (false to find submissions waiting for evaluation)',
    example: false,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: ['studentName'],
    description: 'Field to sort by',
    example: 'studentName',
  })
  @ApiQuery({ name: 'sortDirection', required: false, enum: ['ASC', 'DESC'], description: 'Sort direction', example: 'ASC' })
  @ApiOkResponseWithType(GroupedParticipantListResponseDto, 'Grouped participants retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(400, 'Bad request')
  async getParticipants(@Query() queryDto: GetParticipantsQueryDto): Promise<ApiResponse<GroupedParticipantListResponseDto>> {
    const result = await this.storyMakerAdminService.getGroupedParticipations(queryDto);

    const message = result.students.length === 0 ? 'No participants found' : 'Participants retrieved successfully';

    return ApiResponse.success(result, message);
  }

  @Get('participants/:studentId/:storyId')
  @ApiOperation({
    summary: 'Get all submissions for a specific student and story',
    description: "Returns detailed information about a student's participation in a specific story, including all their submissions and evaluations.",
  })
  @ApiParam({
    name: 'studentId',
    description: 'The ID of the student',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'storyId',
    description: 'The ID of the story maker',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponseWithType(ParticipantDetailResponseDto, 'Student story participation details retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'No participation found for this student and story')
  async getStudentStoryParticipation(@Param('studentId', ParseUUIDPipe) studentId: string, @Param('storyId', ParseUUIDPipe) storyId: string): Promise<ApiResponse<ParticipantDetailResponseDto>> {
    const result = await this.storyMakerAdminService.getStudentStoryParticipation(studentId, storyId);
    return ApiResponse.success(result, 'Student story participation details retrieved successfully');
  }

  @Patch('stories/:id/toggle-status')
  @ApiOperation({
    summary: 'Toggle the active status of a story',
    description: 'Activates or deactivates a story, making it available or unavailable to students.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story to toggle',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        is_active: {
          type: 'boolean',
          example: true,
          description: 'Whether the story should be active or not',
        },
      },
    },
  })
  @ApiOkResponseWithType(StoryMakerResponseDto, 'Story status updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Story not found')
  async toggleStoryStatus(@Param('id', ParseUUIDPipe) id: string, @Body() toggleDto: ToggleStoryStatusDto): Promise<ApiResponse<StoryMakerResponseDto>> {
    const result = await this.storyMakerAdminService.toggleStoryStatus(id, toggleDto);
    return ApiResponse.success(result, `Story ${toggleDto.is_active ? 'activated' : 'deactivated'} successfully`);
  }
}
