#!/usr/bin/env ts-node

/**
 * Comprehensive Test Data Generator and Award Calculator
 * 
 * This script:
 * 1. Generates realistic test data with weighted performance patterns
 * 2. Runs award calculations for multiple periods (weekly, monthly, yearly)
 * 3. Updates student reward balances based on awards won
 * 4. Provides comprehensive data for testing Hall of Fame functionality
 * 
 * Usage:
 *   npm run test:comprehensive
 *   or
 *   npx ts-node scripts/run-comprehensive-test.ts
 */

import { ComprehensiveTestRunner } from '../src/config/seeds/run-comprehensive-test.seed';

async function main() {
  console.log('🧪 === COMPREHENSIVE TEST DATA GENERATOR ===\n');
  
  const runner = new ComprehensiveTestRunner();
  
  try {
    await runner.run();
    
    console.log('\n✅ === SUCCESS ===');
    console.log('Comprehensive test data has been generated successfully!');
    console.log('\nNext steps:');
    console.log('1. Start the application: npm start');
    console.log('2. Test Hall of Fame endpoints in Swagger: http://localhost:3012/api-docs');
    console.log('3. Verify award calculations and reward balances');
    console.log('4. Test award scheduler functionality');
    
    process.exit(0);
  } catch (error) {
    console.error('\n❌ === ERROR ===');
    console.error('Failed to generate comprehensive test data:', error.message);
    console.error('\nPlease check:');
    console.error('1. Database connection is working');
    console.error('2. All required entities are properly configured');
    console.error('3. Award seeds have been run first');
    
    process.exit(1);
  }
}

main();
