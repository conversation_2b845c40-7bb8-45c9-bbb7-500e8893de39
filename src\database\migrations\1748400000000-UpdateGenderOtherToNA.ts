import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateGenderOtherToNA1748400000000 implements MigrationInterface {
  name = 'UpdateGenderOtherToNA1748400000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update all existing 'other' gender values to 'N/A' in the user table
    console.log('Updating gender values from "other" to "N/A"...');

    const result = await queryRunner.query(`
            UPDATE "user" 
            SET gender = 'N/A' 
            WHERE gender = 'other'
        `);

    console.log(`Updated ${result[1]} user records with gender 'other' to 'N/A'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert 'N/A' gender values back to 'other' in case of rollback
    console.log('Reverting gender values from "N/A" to "other"...');

    const result = await queryRunner.query(`
            UPDATE "user" 
            SET gender = 'other' 
            WHERE gender = 'N/A'
        `);

    console.log(`Reverted ${result[1]} user records with gender 'N/A' to 'other'`);
  }
}
