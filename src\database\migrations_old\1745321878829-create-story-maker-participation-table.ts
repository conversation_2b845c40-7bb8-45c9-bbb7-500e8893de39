import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateStoryMakerParticipationTable1745321878829 implements MigrationInterface {
  name = 'CreateStoryMakerParticipationTable1745321878829';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "story_maker_participation" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "student_id" uuid NOT NULL,
        "story_maker_id" uuid NOT NULL,
        "content" text NOT NULL,
        "is_evaluated" boolean NOT NULL DEFAULT false,
        "score" integer CHECK (score > 0),
        "feedback" text,
        "evaluated_at" TIMESTAMP,
        "evaluated_by" character varying(36),
        CONSTRAINT "PK_story_maker_participation" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_student_story_maker" UNIQUE ("student_id", "story_maker_id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "story_maker_participation"
      ADD CONSTRAINT "FK_story_maker_participation_student"
      FOREIGN KEY ("student_id")
      REFERENCES "user"("id")
      ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_participation"
      ADD CONSTRAINT "FK_story_maker_participation_story_maker"
      FOREIGN KEY ("story_maker_id")
      REFERENCES "story_maker"("id")
      ON DELETE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT "FK_story_maker_participation_story_maker"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT "FK_story_maker_participation_student"`);
    await queryRunner.query(`DROP TABLE "story_maker_participation"`);
  }
}
