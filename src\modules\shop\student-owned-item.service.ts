import { Injectable, NotFoundException, Logger, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { StudentOwnedItem, OwnedItemStatus } from '../../database/entities/student-owned-item.entity';
import { ShopItem, ShopItemType } from '../../database/entities/shop-item.entity';
import { ShopCategory } from '../../database/entities/shop-category.entity';
import { ShopSkinMapping } from '../../database/entities/shop-skin-mapping.entity';
import { StudentDiarySkin } from '../../database/entities/student-diary-skin.entity';
import { User } from '../../database/entities/user.entity';
import { StudentOwnedItemResponseDto, UpdateStudentOwnedItemDto, StudentOwnedItemsResponseDto } from '../../database/models/student-owned-item.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { ShopCategoryService } from './shop-category.service';

@Injectable()
export class StudentOwnedItemService {
  private readonly logger = new Logger(StudentOwnedItemService.name);

  constructor(
    @InjectRepository(StudentOwnedItem)
    private studentOwnedItemRepository: Repository<StudentOwnedItem>,
    @InjectRepository(ShopItem)
    private shopItemRepository: Repository<ShopItem>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(ShopCategory)
    private shopCategoryRepository: Repository<ShopCategory>,
    @InjectRepository(ShopSkinMapping)
    private shopSkinMappingRepository: Repository<ShopSkinMapping>,
    @InjectRepository(StudentDiarySkin)
    private studentDiarySkinRepository: Repository<StudentDiarySkin>,
    private fileRegistryService: FileRegistryService,
    private shopCategoryService: ShopCategoryService,
    private dataSource: DataSource,
  ) {}

  /**
   * Add a purchased item to a student's owned items
   * @param studentId Student ID
   * @param shopItemId Shop item ID
   * @param purchaseId Purchase ID
   * @returns The created owned item
   */ async addOwnedItem(studentId: string, shopItemId: string, purchaseId: string): Promise<StudentOwnedItem> {
    try {
      // Check if student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId },
      });

      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Check if shop item exists with its relations
      const shopItem = await this.shopItemRepository.findOne({
        where: { id: shopItemId },
        relations: ['category'],
      });

      if (!shopItem) {
        throw new NotFoundException(`Shop item with ID ${shopItemId} not found`);
      }

      // Check if student already owns this item
      const existingItem = await this.studentOwnedItemRepository.findOne({
        where: { studentId, shopItemId },
        relations: ['shopItem', 'shopItem.category'],
      });

      if (existingItem) {
        // If the item already exists, just update the status and return it
        existingItem.status = OwnedItemStatus.AVAILABLE;
        existingItem.acquiredDate = new Date();
        existingItem.purchaseId = purchaseId;
        return this.studentOwnedItemRepository.save(existingItem);
      }

      // Create a new owned item
      const ownedItem = this.studentOwnedItemRepository.create({
        studentId,
        shopItemId,
        purchaseId,
        status: OwnedItemStatus.AVAILABLE,
        acquiredDate: new Date(),
      });

      return this.studentOwnedItemRepository.save(ownedItem);
    } catch (error) {
      this.logger.error(`Error adding owned item: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all items owned by a student (includes purchased items and all free items)
   * @param studentId Student ID
   * @param categoryId Optional category ID to filter by
   * @param status Optional status to filter by
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of owned items
   */
  async getStudentOwnedItems(studentId: string, categoryId?: string, status?: OwnedItemStatus, paginationDto?: PaginationDto): Promise<StudentOwnedItemsResponseDto> {
    try {
      // Check if student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId },
      });

      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Get purchased items (items in student_owned_item table)
      const purchasedQuery = this.studentOwnedItemRepository
        .createQueryBuilder('ownedItem')
        .leftJoinAndSelect('ownedItem.shopItem', 'shopItem')
        .leftJoinAndSelect('shopItem.category', 'category')
        .where('ownedItem.studentId = :studentId', { studentId });

      // Apply categoryId filter for purchased items
      if (categoryId) {
        purchasedQuery.andWhere('category.id = :filterCategoryId', { filterCategoryId: categoryId });
      }

      if (status) {
        purchasedQuery.andWhere('ownedItem.status = :status', { status });
      }

      const purchasedItems = await purchasedQuery.getMany();

      // Get all free items that are active
      const freeItemsQuery = this.shopItemRepository
        .createQueryBuilder('shopItem')
        .leftJoinAndSelect('shopItem.category', 'category')
        .where('shopItem.type = :type', { type: 'free' })
        .andWhere('shopItem.isActive = :isActive', { isActive: true });

      // Apply categoryId filter for free items
      if (categoryId) {
        freeItemsQuery.andWhere('category.id = :filterCategoryId', { filterCategoryId: categoryId });
      }

      const freeItems = await freeItemsQuery.getMany();

      // Create a set of shop item IDs that are already owned (purchased)
      const ownedShopItemIds = new Set(purchasedItems.map((item) => item.shopItemId));

      // Create virtual owned items for free items that aren't already owned
      const virtualFreeItems = freeItems
        .filter((freeItem) => !ownedShopItemIds.has(freeItem.id))
        .map((freeItem) => ({
          id: `virtual-${freeItem.id}`, // Virtual ID for free items
          studentId,
          shopItemId: freeItem.id,
          shopItem: freeItem,
          purchaseId: null,
          status: OwnedItemStatus.AVAILABLE,
          acquiredDate: freeItem.createdAt, // Use shop item creation date
          expiryDate: null,
          lastUsedDate: null,
          isFavorite: false,
          notes: null,
          createdAt: freeItem.createdAt,
          updatedAt: freeItem.updatedAt,
          createdBy: null,
          updatedBy: null,
        }));

      // Get student's own created diary skins
      const studentSkins = await this.studentDiarySkinRepository.find({
        where: { studentId, isActive: true },
      });

      // Create virtual owned items for student's own created skins
      let virtualStudentSkins = studentSkins.map((studentSkin) => ({
        id: `virtual-student-skin-${studentSkin.id}`, // Virtual ID for student skins
        studentId,
        shopItemId: null, // Student skins don't have shop items
        shopItem: null,
        studentSkin: studentSkin, // Add the student skin data
        purchaseId: null,
        status: OwnedItemStatus.AVAILABLE,
        acquiredDate: studentSkin.createdAt,
        expiryDate: null,
        lastUsedDate: null,
        isFavorite: false,
        notes: null,
        createdAt: studentSkin.createdAt,
        updatedAt: studentSkin.updatedAt,
        createdBy: null,
        updatedBy: null,
      }));

      // Filter student skins by categoryId if specified
      if (categoryId) {
        const skinCategory = await this.shopCategoryService.getOrCreateSkinCategory();
        if (categoryId !== skinCategory.id) {
          // If filtering by a different category, exclude student skins
          virtualStudentSkins = [];
        }
        // If filtering by skin category, keep all student skins
      }

      // Combine purchased items, virtual free items, and student skins
      const allItems = [...purchasedItems, ...virtualFreeItems, ...virtualStudentSkins];

      // Apply status filter to virtual free items if needed
      const filteredItems = status ? allItems.filter((item) => item.status === status) : allItems;

      // Apply pagination and sorting
      const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto || {};

      // Sort items
      filteredItems.sort((a, b) => {
        if (sortBy && sortDirection) {
          let aValue, bValue;

          if (sortBy.startsWith('shopItem.')) {
            const field = sortBy.replace('shopItem.', '');
            aValue = a.shopItem[field];
            bValue = b.shopItem[field];
          } else {
            aValue = a[sortBy];
            bValue = b[sortBy];
          }

          if (sortDirection === 'ASC') {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        } else {
          // Default sort by acquired date DESC
          return new Date(b.acquiredDate).getTime() - new Date(a.acquiredDate).getTime();
        }
      });

      // Calculate pagination
      const totalItems = filteredItems.length;
      const totalPages = Math.ceil(totalItems / limit);
      const skip = (page - 1) * limit;
      const paginatedItems = filteredItems.slice(skip, skip + limit);

      // Map to DTOs
      const itemPromises = paginatedItems.map(async (item: any) => {
        // Handle student's own created skins
        if (item.studentSkin) {
          // Get the skin category ID for consistency
          const skinCategory = await this.shopCategoryService.getOrCreateSkinCategory();

          const dto: StudentOwnedItemResponseDto = {
            id: item.id,
            shopItemId: null, // Student skins don't have shop items
            title: item.studentSkin.name,
            description: item.studentSkin.description,
            categoryId: skinCategory.id, // Use the skin category ID
            categoryName: skinCategory.name, // Use the actual skin category name
            status: item.status,
            acquiredDate: item.acquiredDate,
            expiryDate: item.expiryDate,
            lastUsedDate: item.lastUsedDate,
            isFavorite: item.isFavorite,
            notes: item.notes,
            filePath: null,
          };

          // Get file URL for student skin using student diary skin media URL
          if (item.studentSkin.previewImagePath) {
            try {
              dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STUDENT_DIARY_SKIN, item.studentSkin.id);
            } catch (error) {
              this.logger.error(`Error getting student skin file URL: ${error.message}`, error.stack);
            }
          }

          return dto;
        }

        // Handle regular shop items (purchased and free)
        const dto: StudentOwnedItemResponseDto = {
          id: item.id,
          shopItemId: item.shopItemId,
          title: item.shopItem.title,
          description: item.shopItem.description,
          categoryId: item.shopItem.categoryId,
          categoryName: item.shopItem.category?.name || 'Unknown',
          status: item.status,
          acquiredDate: item.acquiredDate,
          expiryDate: item.expiryDate,
          lastUsedDate: item.lastUsedDate,
          isFavorite: item.isFavorite,
          notes: item.notes,
          filePath: null,
        };

        // Get file URL if available - conditional based on item type
        if (item.shopItem.filePath) {
          try {
            // Check if this is a skin category item
            const isSkinCategory = await this.isSkinCategory(item.shopItem.categoryId);

            if (isSkinCategory) {
              // For skin items, get the diary skin ID and use diary skin media URL
              const skinMapping = await this.shopSkinMappingRepository.findOne({
                where: { shopItemId: item.shopItemId },
              });

              if (skinMapping?.diarySkinId) {
                dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, skinMapping.diarySkinId);
              }
            } else {
              // For regular shop items, use shop item media URL
              dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.SHOP_ITEM, item.shopItemId);
            }
          } catch (error) {
            this.logger.error(`Error getting file URL: ${error.message}`, error.stack);
          }
        }

        return dto;
      });

      const items = await Promise.all(itemPromises);

      return {
        items,
        totalItems,
        itemsPerPage: limit,
        currentPage: page,
        totalPages,
      };
    } catch (error) {
      this.logger.error(`Error getting student owned items: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a specific owned item
   * @param id Owned item ID
   * @param studentId Student ID
   * @returns The owned item
   */
  async getOwnedItemById(id: string, studentId: string): Promise<StudentOwnedItemResponseDto> {
    try {
      const ownedItem = await this.studentOwnedItemRepository.findOne({
        where: { id, studentId },
        relations: ['shopItem'],
      });

      if (!ownedItem) {
        throw new NotFoundException(`Owned item with ID ${id} not found for student ${studentId}`);
      }

      const dto: StudentOwnedItemResponseDto = {
        id: ownedItem.id,
        shopItemId: ownedItem.shopItemId,
        title: ownedItem.shopItem.title,
        description: ownedItem.shopItem.description,
        categoryId: ownedItem.shopItem.categoryId,
        categoryName: ownedItem.shopItem.category?.name || 'Unknown',
        status: ownedItem.status,
        acquiredDate: ownedItem.acquiredDate,
        expiryDate: ownedItem.expiryDate,
        lastUsedDate: ownedItem.lastUsedDate,
        isFavorite: ownedItem.isFavorite,
        notes: ownedItem.notes,
        filePath: null,
      };

      // Get file URL if available - conditional based on item type
      if (ownedItem.shopItem.filePath) {
        try {
          // Check if this is a skin category item
          const isSkinCategory = await this.isSkinCategory(ownedItem.shopItem.categoryId);

          if (isSkinCategory) {
            // For skin items, get the diary skin ID and use diary skin media URL
            const skinMapping = await this.shopSkinMappingRepository.findOne({
              where: { shopItemId: ownedItem.shopItemId },
            });

            if (skinMapping?.diarySkinId) {
              dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, skinMapping.diarySkinId);
            }
          } else {
            // For regular shop items, use shop item media URL
            dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.SHOP_ITEM, ownedItem.shopItemId);
          }
        } catch (error) {
          this.logger.error(`Error getting file URL: ${error.message}`, error.stack);
        }
      }

      return dto;
    } catch (error) {
      this.logger.error(`Error getting owned item: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update an owned item
   * @param id Owned item ID
   * @param studentId Student ID
   * @param updateDto Update data
   * @returns The updated owned item
   */
  async updateOwnedItem(id: string, studentId: string, updateDto: UpdateStudentOwnedItemDto): Promise<StudentOwnedItemResponseDto> {
    try {
      const ownedItem = await this.studentOwnedItemRepository.findOne({
        where: { id, studentId },
        relations: ['shopItem'],
      });

      if (!ownedItem) {
        throw new NotFoundException(`Owned item with ID ${id} not found for student ${studentId}`);
      }

      // Update fields
      if (updateDto.status !== undefined) {
        ownedItem.status = updateDto.status;

        // If status is changed to IN_USE, update lastUsedDate
        if (updateDto.status === OwnedItemStatus.IN_USE) {
          ownedItem.lastUsedDate = new Date();
        }
      }

      if (updateDto.isFavorite !== undefined) {
        ownedItem.isFavorite = updateDto.isFavorite;
      }

      if (updateDto.notes !== undefined) {
        ownedItem.notes = updateDto.notes;
      }

      // Save changes
      await this.studentOwnedItemRepository.save(ownedItem);

      // Return updated item
      return this.getOwnedItemById(id, studentId);
    } catch (error) {
      this.logger.error(`Error updating owned item: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check if a student owns a specific item (includes purchased items and free items)
   * @param studentId Student ID
   * @param shopItemId Shop item ID
   * @returns True if the student owns the item
   */
  async doesStudentOwnItem(studentId: string, shopItemId: string): Promise<boolean> {
    try {
      // First check if the item is explicitly owned (purchased)
      const ownedItem = await this.studentOwnedItemRepository.findOne({
        where: { studentId, shopItemId, status: OwnedItemStatus.AVAILABLE },
      });

      if (ownedItem) {
        return true;
      }

      // If not explicitly owned, check if it's a free item
      const shopItem = await this.shopItemRepository.findOne({
        where: { id: shopItemId, type: ShopItemType.FREE, isActive: true },
      });

      return !!shopItem; // Return true if it's an active free item
    } catch (error) {
      this.logger.error(`Error checking if student owns item: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check if a student can use a specific shop item
   * Free items: Always usable if active
   * Paid items: Must be purchased first
   * @param studentId Student ID
   * @param shopItemId Shop item ID
   * @returns True if the student can use the item
   */
  async canStudentUseItem(studentId: string, shopItemId: string): Promise<boolean> {
    try {
      // Get the shop item to check its type
      const shopItem = await this.shopItemRepository.findOne({
        where: { id: shopItemId, isActive: true },
      });

      if (!shopItem) {
        return false; // Item doesn't exist or is inactive
      }

      // Free items can always be used if they're active
      if (shopItem.type === ShopItemType.FREE) {
        return true;
      }

      // Paid items require purchase validation
      const ownedItem = await this.studentOwnedItemRepository.findOne({
        where: { studentId, shopItemId, status: OwnedItemStatus.AVAILABLE },
      });

      return !!ownedItem;
    } catch (error) {
      this.logger.error(`Error checking if student can use item: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Validate that a student can use a specific shop item, throwing an error if not
   * @param studentId Student ID
   * @param shopItemId Shop item ID
   * @param actionDescription Description of the action being attempted (for error messages)
   * @throws NotFoundException if item doesn't exist
   * @throws ForbiddenException if student cannot use the item
   */
  async validateStudentCanUseItem(studentId: string, shopItemId: string, actionDescription: string = 'use this item'): Promise<void> {
    try {
      // Get the shop item to check its type
      const shopItem = await this.shopItemRepository.findOne({
        where: { id: shopItemId },
      });

      if (!shopItem) {
        throw new NotFoundException(`Shop item with ID ${shopItemId} not found`);
      }

      if (!shopItem.isActive) {
        throw new ForbiddenException(`This item is no longer available`);
      }

      // Free items can always be used if they're active
      if (shopItem.type === ShopItemType.FREE) {
        return; // Access granted
      }

      // Paid items require purchase validation
      const ownedItem = await this.studentOwnedItemRepository.findOne({
        where: { studentId, shopItemId, status: OwnedItemStatus.AVAILABLE },
      });

      if (!ownedItem) {
        throw new ForbiddenException(`You must purchase this item before you can ${actionDescription}`);
      }
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }
      this.logger.error(`Error validating student can use item: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get student owned items grouped by category (includes purchased items and all free items)
   * @param studentId Student ID
   * @param categoryId Optional category ID to filter by
   * @param status Optional status to filter by
   * @returns Owned items grouped by category
   */
  async getStudentOwnedItemsGroupedByCategory(studentId: string, categoryId?: string, status?: OwnedItemStatus): Promise<{ [category: string]: StudentOwnedItemResponseDto[] }> {
    try {
      // Check if student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId },
      });

      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Get purchased items (items in student_owned_item table)
      const purchasedQuery = this.studentOwnedItemRepository
        .createQueryBuilder('ownedItem')
        .leftJoinAndSelect('ownedItem.shopItem', 'shopItem')
        .leftJoinAndSelect('shopItem.category', 'category')
        .where('ownedItem.studentId = :studentId', { studentId });

      // Apply status filter for purchased items
      if (status) {
        purchasedQuery.andWhere('ownedItem.status = :status', { status });
      }

      // Apply categoryId filter for purchased items
      if (categoryId) {
        purchasedQuery.andWhere('category.id = :filterCategoryId', { filterCategoryId: categoryId });
      }

      const purchasedItems = await purchasedQuery.getMany();

      // Get all free items that are active
      const freeItemsQuery = this.shopItemRepository
        .createQueryBuilder('shopItem')
        .leftJoinAndSelect('shopItem.category', 'category')
        .where('shopItem.type = :type', { type: ShopItemType.FREE })
        .andWhere('shopItem.isActive = :isActive', { isActive: true });

      // Apply categoryId filter for free items
      if (categoryId) {
        freeItemsQuery.andWhere('category.id = :filterCategoryId', { filterCategoryId: categoryId });
      }

      const freeItems = await freeItemsQuery.getMany();

      // Create a set of shop item IDs that are already owned (purchased)
      const ownedShopItemIds = new Set(purchasedItems.map((item) => item.shopItemId));

      // Create virtual owned items for free items that aren't already owned
      const virtualFreeItems = freeItems
        .filter((freeItem) => !ownedShopItemIds.has(freeItem.id))
        .map((freeItem) => ({
          id: `virtual-${freeItem.id}`, // Virtual ID for free items
          studentId,
          shopItemId: freeItem.id,
          shopItem: freeItem,
          purchaseId: null,
          status: OwnedItemStatus.AVAILABLE,
          acquiredDate: freeItem.createdAt, // Use shop item creation date
          expiryDate: null,
          lastUsedDate: null,
          isFavorite: false,
          notes: null,
          createdAt: freeItem.createdAt,
          updatedAt: freeItem.updatedAt,
          createdBy: null,
          updatedBy: null,
        }));

      // Get student's own created diary skins
      const studentSkins = await this.studentDiarySkinRepository.find({
        where: { studentId, isActive: true },
      });

      // Create virtual owned items for student's own created skins
      let virtualStudentSkins = studentSkins.map((studentSkin) => ({
        id: `virtual-student-skin-${studentSkin.id}`, // Virtual ID for student skins
        studentId,
        shopItemId: null, // Student skins don't have shop items
        shopItem: null,
        studentSkin: studentSkin, // Add the student skin data
        purchaseId: null,
        status: OwnedItemStatus.AVAILABLE,
        acquiredDate: studentSkin.createdAt,
        expiryDate: null,
        lastUsedDate: null,
        isFavorite: false,
        notes: null,
        createdAt: studentSkin.createdAt,
        updatedAt: studentSkin.updatedAt,
        createdBy: null,
        updatedBy: null,
      }));

      // Filter student skins by categoryId if specified
      if (categoryId) {
        const skinCategory = await this.shopCategoryService.getOrCreateSkinCategory();
        if (categoryId !== skinCategory.id) {
          // If filtering by a different category, exclude student skins
          virtualStudentSkins = [];
        }
        // If filtering by skin category, keep all student skins
      }

      // Combine purchased items, virtual free items, and student skins
      const allItems = [...purchasedItems, ...virtualFreeItems, ...virtualStudentSkins];

      // Apply status filter to virtual free items if needed
      const filteredItems = status ? allItems.filter((item) => item.status === status) : allItems;

      // Sort items by category name and then by acquisition date
      filteredItems.sort((a, b) => {
        const categoryA = a.shopItem.category?.name || 'Unknown';
        const categoryB = b.shopItem.category?.name || 'Unknown';

        if (categoryA !== categoryB) {
          return categoryA.localeCompare(categoryB);
        }

        return new Date(b.acquiredDate).getTime() - new Date(a.acquiredDate).getTime();
      });

      // Map to DTOs
      const itemPromises = filteredItems.map(async (item: any) => {
        // Handle student's own created skins
        if (item.studentSkin) {
          // Get the skin category ID for consistency
          const skinCategory = await this.shopCategoryService.getOrCreateSkinCategory();

          const dto: StudentOwnedItemResponseDto = {
            id: item.id,
            shopItemId: null, // Student skins don't have shop items
            title: item.studentSkin.name,
            description: item.studentSkin.description,
            categoryId: skinCategory.id, // Use the skin category ID
            categoryName: skinCategory.name, // Use the actual skin category name
            shopItemCategory: skinCategory.name,
            status: item.status,
            acquiredDate: item.acquiredDate,
            expiryDate: item.expiryDate,
            lastUsedDate: item.lastUsedDate,
            isFavorite: item.isFavorite,
            notes: item.notes,
            filePath: null,
          };

          // Get file URL for student skin using student diary skin media URL
          if (item.studentSkin.previewImagePath) {
            try {
              dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STUDENT_DIARY_SKIN, item.studentSkin.id);
            } catch (error) {
              this.logger.error(`Error getting student skin file URL: ${error.message}`, error.stack);
            }
          }

          return dto;
        }

        // Handle regular shop items (purchased and free)
        const dto: StudentOwnedItemResponseDto = {
          id: item.id,
          shopItemId: item.shopItemId,
          title: item.shopItem.title,
          description: item.shopItem.description,
          categoryId: item.shopItem.categoryId,
          categoryName: item.shopItem.category?.name || 'Unknown',
          shopItemCategory: item.shopItem.category?.name,
          status: item.status,
          acquiredDate: item.acquiredDate,
          expiryDate: item.expiryDate,
          lastUsedDate: item.lastUsedDate,
          isFavorite: item.isFavorite,
          notes: item.notes,
          filePath: null,
        };

        // Get file URL if available - conditional based on item type
        if (item.shopItem.filePath) {
          try {
            // Check if this is a skin category item
            const isSkinCategory = await this.isSkinCategory(item.shopItem.categoryId);

            if (isSkinCategory) {
              // For skin items, get the diary skin ID and use diary skin media URL
              const skinMapping = await this.shopSkinMappingRepository.findOne({
                where: { shopItemId: item.shopItemId },
              });

              if (skinMapping?.diarySkinId) {
                dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, skinMapping.diarySkinId);
              }
            } else {
              // For regular shop items, use shop item media URL
              dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.SHOP_ITEM, item.shopItemId);
            }
          } catch (error) {
            this.logger.error(`Error getting file URL: ${error.message}`, error.stack);
          }
        }

        return dto;
      });

      const items = await Promise.all(itemPromises);

      // Group by category
      const groupedItems = items.reduce((groups, item) => {
        const category = item.categoryName || 'Unknown';
        if (!groups[category]) {
          groups[category] = [];
        }
        groups[category].push(item);
        return groups;
      }, {});

      return groupedItems;
    } catch (error) {
      this.logger.error(`Error getting student owned items grouped by category: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get student owned items by category (includes purchased items and all free items)
   * @param studentId Student ID
   * @param categoryId Category ID
   * @param status Optional status to filter by
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of owned items in the specified category
   */
  async getStudentOwnedItemsByCategory(studentId: string, categoryId: string, status?: OwnedItemStatus, paginationDto?: PaginationDto): Promise<StudentOwnedItemsResponseDto> {
    try {
      // Check if student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId },
      });

      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Get purchased items (items in student_owned_item table)
      const purchasedQuery = this.studentOwnedItemRepository
        .createQueryBuilder('ownedItem')
        .leftJoinAndSelect('ownedItem.shopItem', 'shopItem')
        .leftJoinAndSelect('shopItem.category', 'category')
        .where('ownedItem.studentId = :studentId', { studentId })
        .andWhere('shopItem.categoryId = :categoryId', { categoryId });

      // Apply status filter for purchased items
      if (status) {
        purchasedQuery.andWhere('ownedItem.status = :status', { status });
      }

      const purchasedItems = await purchasedQuery.getMany();

      // Get all free items that are active in this category
      const freeItemsQuery = this.shopItemRepository
        .createQueryBuilder('shopItem')
        .leftJoinAndSelect('shopItem.category', 'category')
        .where('shopItem.type = :type', { type: ShopItemType.FREE })
        .andWhere('shopItem.isActive = :isActive', { isActive: true })
        .andWhere('shopItem.categoryId = :categoryId', { categoryId });

      const freeItems = await freeItemsQuery.getMany();

      // Create a set of shop item IDs that are already owned (purchased)
      const ownedShopItemIds = new Set(purchasedItems.map((item) => item.shopItemId));

      // Create virtual owned items for free items that aren't already owned
      const virtualFreeItems = freeItems
        .filter((freeItem) => !ownedShopItemIds.has(freeItem.id))
        .map((freeItem) => ({
          id: `virtual-${freeItem.id}`, // Virtual ID for free items
          studentId,
          shopItemId: freeItem.id,
          shopItem: freeItem,
          purchaseId: null,
          status: OwnedItemStatus.AVAILABLE,
          acquiredDate: freeItem.createdAt, // Use shop item creation date
          expiryDate: null,
          lastUsedDate: null,
          isFavorite: false,
          notes: null,
          createdAt: freeItem.createdAt,
          updatedAt: freeItem.updatedAt,
          createdBy: null,
          updatedBy: null,
        }));

      // Combine purchased items and virtual free items
      const allItems = [...purchasedItems, ...virtualFreeItems];

      // Apply status filter to virtual free items if needed
      const filteredItems = status ? allItems.filter((item) => item.status === status) : allItems;

      // Apply pagination and sorting
      const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto || {};

      // Sort items
      filteredItems.sort((a, b) => {
        if (sortBy && sortDirection) {
          let aValue, bValue;

          if (sortBy.startsWith('shopItem.')) {
            const field = sortBy.replace('shopItem.', '');
            aValue = a.shopItem[field];
            bValue = b.shopItem[field];
          } else {
            aValue = a[sortBy];
            bValue = b[sortBy];
          }

          if (sortDirection === 'ASC') {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        } else {
          // Default sort by acquired date DESC
          return new Date(b.acquiredDate).getTime() - new Date(a.acquiredDate).getTime();
        }
      });

      // Calculate pagination
      const totalItems = filteredItems.length;
      const totalPages = Math.ceil(totalItems / limit);
      const skip = (page - 1) * limit;
      const paginatedItems = filteredItems.slice(skip, skip + limit);

      // Map to DTOs
      const itemPromises = paginatedItems.map(async (item) => {
        const dto: StudentOwnedItemResponseDto = {
          id: item.id,
          shopItemId: item.shopItemId,
          title: item.shopItem.title,
          description: item.shopItem.description,
          categoryId: item.shopItem.categoryId,
          categoryName: item.shopItem.category?.name || 'Unknown',
          shopItemCategory: item.shopItem.category?.name,
          status: item.status,
          acquiredDate: item.acquiredDate,
          expiryDate: item.expiryDate,
          lastUsedDate: item.lastUsedDate,
          isFavorite: item.isFavorite,
          notes: item.notes,
          filePath: null,
        };

        // Get file URL if available - conditional based on item type
        if (item.shopItem.filePath) {
          try {
            // Check if this is a skin category item
            const isSkinCategory = await this.isSkinCategory(item.shopItem.categoryId);

            if (isSkinCategory) {
              // For skin items, get the diary skin ID and use diary skin media URL
              const skinMapping = await this.shopSkinMappingRepository.findOne({
                where: { shopItemId: item.shopItemId },
              });

              if (skinMapping?.diarySkinId) {
                dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, skinMapping.diarySkinId);
              }
            } else {
              // For regular shop items, use shop item media URL
              dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.SHOP_ITEM, item.shopItemId);
            }
          } catch (error) {
            this.logger.error(`Error getting file URL: ${error.message}`, error.stack);
          }
        }

        return dto;
      });

      const items = await Promise.all(itemPromises);

      return {
        items,
        totalItems,
        itemsPerPage: limit,
        currentPage: page,
        totalPages,
      };
    } catch (error) {
      this.logger.error(`Error getting student owned items by category: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check if a category is a skin category
   * @param categoryId Category ID to check
   * @returns True if the category is a skin category, false otherwise
   */
  private async isSkinCategory(categoryId: string): Promise<boolean> {
    try {
      const category = await this.shopCategoryRepository.findOne({
        where: { id: categoryId },
      });

      if (!category) {
        return false;
      }

      // Check if the category name contains 'skin' (case-insensitive)
      return category.name.toLowerCase().includes('skin');
    } catch (error) {
      this.logger.error(`Error checking if category is a skin category: ${error.message}`, error.stack);
      return false;
    }
  }
}
