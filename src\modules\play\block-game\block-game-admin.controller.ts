import { Controller, Post, Get, Patch, Delete, Body, Query, UseGuards, Param, Parse<PERSON><PERSON><PERSON>ipe, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../common/guards/jwt.guard';
import { AdminGuard } from '../../../common/guards/admin.guard';
import { BlockGameAdminService } from './block-game-admin.service';
import { CreateBlockGameDto, UpdateBlockGameDto, BlockGameResponseDto, GetBlockGamesQueryDto, ToggleBlockGameStatusDto } from '../../../database/models/block-game/block-game.dto';
import {
  GetBlockGameParticipantsQueryDto,
  BlockGameParticipantsResponseDto,
  GetStudentBlockGameParticipationQueryDto,
  StudentBlockGameParticipationResponseDto,
} from '../../../database/models/block-game/block-game-admin.dto';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType } from '../../../common/decorators/api-response.decorator';
import { PagedListDto } from '../../../common/models/paged-list.dto';

@ApiTags('Play-Block')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, AdminGuard)
@Controller('play/block/admin')
export class BlockGameAdminController {
  constructor(private readonly blockGameAdminService: BlockGameAdminService) {}

  @Get('games')
  @ApiOperation({
    summary: 'Get all block games',
    description: 'Retrieves all block games with pagination and filtering options',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page', example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for title', example: 'sentence' })
  @ApiQuery({ name: 'is_active', required: false, type: Boolean, description: 'Filter by active status', example: true })
  @ApiOkResponseWithPagedListType(BlockGameResponseDto, 'Block games retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getBlockGames(@Query() queryDto: GetBlockGamesQueryDto): Promise<ApiResponse<PagedListDto<BlockGameResponseDto>>> {
    const result = await this.blockGameAdminService.getBlockGames(queryDto);

    const message = result.items.length === 0 ? 'No block games found' : 'Block games retrieved successfully';

    return ApiResponse.success(result, message);
  }

  @Post('games')
  @ApiOperation({
    summary: 'Create a new block game',
    description: 'Creates a new block game with title, score, and sentences',
  })
  @ApiOkResponseWithType(BlockGameResponseDto, 'Block game created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async createBlockGame(@Body() createBlockGameDto: CreateBlockGameDto): Promise<ApiResponse<BlockGameResponseDto>> {
    try {
      const result = await this.blockGameAdminService.createBlockGame(createBlockGameDto);
      return ApiResponse.success(result, 'Block game created successfully', 201);
    } catch (error) {
      // Check if this is a partial validation error (some sentences were valid, some invalid)
      if (error instanceof BadRequestException && error.getResponse() && typeof error.getResponse() === 'object' && (error.getResponse() as any).error?.type === 'PartialValidationError') {
        const errorResponse = error.getResponse() as any;
        const updatedGame = await this.blockGameAdminService.getBlockGameById(errorResponse.partialSuccess.blockGameId);

        // Create a response with both success and error information
        return ApiResponse.success(
          {
            ...updatedGame,
            sentences_added: errorResponse.partialSuccess.successCount,
            invalid_sentences: errorResponse.invalidSentences || [],
          } as any,
          `Created ${errorResponse.partialSuccess.successCount} sentences successfully, but ${errorResponse.partialSuccess.totalCount - errorResponse.partialSuccess.successCount} sentences had validation errors`,
          207, // Using 207 Multi-Status to indicate partial success
        );
      }

      throw error;
    }
  }

  @Get('games/:id')
  @ApiOperation({
    summary: 'Get a block game by ID',
    description: 'Retrieves a specific block game with all its details',
  })
  @ApiParam({ name: 'id', type: 'string', description: 'Block game ID', example: '123e4567-e89b-12d3-a456-************' })
  @ApiOkResponseWithType(BlockGameResponseDto, 'Block game retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Block game not found')
  async getBlockGameById(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<BlockGameResponseDto>> {
    const result = await this.blockGameAdminService.getBlockGameById(id);
    return ApiResponse.success(result, 'Block game retrieved successfully');
  }

  @Patch('games/:id')
  @ApiOperation({
    summary: 'Update a block game',
    description: 'Updates a block game. Content changes are restricted if the game has student participation.',
  })
  @ApiParam({ name: 'id', type: 'string', description: 'Block game ID', example: '123e4567-e89b-12d3-a456-************' })
  @ApiOkResponseWithType(BlockGameResponseDto, 'Block game updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required or game has student participation')
  @ApiErrorResponse(404, 'Block game not found')
  async updateBlockGame(@Param('id', ParseUUIDPipe) id: string, @Body() updateBlockGameDto: UpdateBlockGameDto): Promise<ApiResponse<BlockGameResponseDto>> {
    const result = await this.blockGameAdminService.updateBlockGame(id, updateBlockGameDto);
    return ApiResponse.success(result, 'Block game updated successfully');
  }

  @Delete('games/:id')
  @ApiOperation({
    summary: 'Delete a block game',
    description: 'Deletes a block game. Deletion is restricted if the game has student participation.',
  })
  @ApiParam({ name: 'id', type: 'string', description: 'Block game ID', example: '123e4567-e89b-12d3-a456-************' })
  @ApiOkResponseWithType(Object, 'Block game deleted successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required or game has student participation')
  @ApiErrorResponse(404, 'Block game not found')
  async deleteBlockGame(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<any>> {
    await this.blockGameAdminService.deleteBlockGame(id);
    return ApiResponse.success({ success: true }, 'Block game deleted successfully');
  }

  @Patch('games/:id/toggle-status')
  @ApiOperation({
    summary: 'Toggle block game active status',
    description: 'Activates or deactivates a block game. This action is always allowed regardless of participation records.',
  })
  @ApiParam({ name: 'id', type: 'string', description: 'Block game ID', example: '123e4567-e89b-12d3-a456-************' })
  @ApiOkResponseWithType(BlockGameResponseDto, 'Block game status updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Block game not found')
  async toggleBlockGameStatus(@Param('id', ParseUUIDPipe) id: string, @Body() toggleDto: ToggleBlockGameStatusDto): Promise<ApiResponse<BlockGameResponseDto>> {
    const result = await this.blockGameAdminService.toggleBlockGameStatus(id, toggleDto);
    const statusText = toggleDto.is_active ? 'activated' : 'deactivated';
    return ApiResponse.success(result, `Block game ${statusText} successfully`);
  }

  @Get('participants')
  @ApiOperation({
    summary: 'List participants for all block games',
    description: 'Retrieves a paginated list of students who have participated in block games with their aggregated performance data',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page', example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for student name or email', example: 'john' })
  // @ApiQuery({ name: 'block_game_id', required: false, type: String, description: 'Filter by block game ID' })
  @ApiOkResponseWithType(BlockGameParticipantsResponseDto, 'Participants retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(400, 'Bad request')
  async getParticipants(@Query() queryDto: GetBlockGameParticipantsQueryDto): Promise<ApiResponse<BlockGameParticipantsResponseDto>> {
    const result = await this.blockGameAdminService.getParticipants(queryDto);

    const message = result.participants.length === 0 ? 'No participants found' : 'Participants retrieved successfully';

    return ApiResponse.success(result, message);
  }

  @Get('participants/:student_id')
  @ApiOperation({
    summary: 'Get student participation history',
    description: 'Retrieves detailed participation history for a specific student across all block games',
  })
  @ApiParam({ name: 'student_id', type: 'string', description: 'Student ID', example: '123e4567-e89b-12d3-a456-426614174001' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page', example: 10 })
  @ApiOkResponseWithType(StudentBlockGameParticipationResponseDto, 'Student participation history retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Student not found')
  @ApiErrorResponse(400, 'Bad request')
  async getStudentParticipation(
    @Param('student_id', ParseUUIDPipe) student_id: string,
    @Query() queryDto: Omit<GetStudentBlockGameParticipationQueryDto, 'student_id'>,
  ): Promise<ApiResponse<StudentBlockGameParticipationResponseDto>> {
    const fullQueryDto: GetStudentBlockGameParticipationQueryDto = {
      ...queryDto,
      student_id,
    };

    const result = await this.blockGameAdminService.getStudentParticipation(fullQueryDto);

    const message = result.participations.length === 0 ? 'No participation history found' : 'Student participation history retrieved successfully';

    return ApiResponse.success(result, message);
  }
}
