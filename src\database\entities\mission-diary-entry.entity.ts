import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, OneToOne } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { DiaryMission } from './diary-mission.entity';
import { User } from './user.entity';
import { MissionDiaryEntryFeedback } from './mission-diary-entry-feedback.entity';
import { DiarySkin } from './diary-skin.entity';
import { MissionDiaryEntryHistory } from './mission-diary-entry-history.entity';
import { WritingEntryStatus } from '../../common/enums/writing-entry-status.enum';
import { IWritingEntry } from '../../common/interfaces/writing-entry.interface';

/**
 * Status of a mission diary entry in its lifecycle
 * LIFECYCLE: NEW → SUBMITTED → REVIEWED (Final State)
 * Note: CONFIRMED stage has been removed from the lifecycle
 * @enum {string}
 */
export enum MissionEntryStatus {
  /** Entry is still being edited by the student */
  NEW = 'new',
  /** Entry has been submitted for review */
  SUBMITTED = 'submit', // Use existing enum value 'submit'
  /** Entry has been reviewed by a tutor - FINAL STATE */
  REVIEWED = 'reviewed',
  /** @deprecated CONFIRMED stage removed - use REVIEWED instead */
  CONFIRMED = 'confirm', // Use existing enum value 'confirm'

  // Legacy values for backward compatibility
  /** @deprecated Use NEW instead */
  LEGACY_NEW = 'NEW',
  /** @deprecated Use SUBMITTED instead */
  LEGACY_SUBMIT = 'SUBMITTED',
  /** @deprecated Use REVIEWED instead */
  LEGACY_REVIEWED = 'REVIEWED',
  /** @deprecated CONFIRMED stage removed - use REVIEWED instead */
  LEGACY_CONFIRM = 'CONFIRMED',
}

@Entity()
export class MissionDiaryEntry extends AuditableBaseEntity implements Partial<IWritingEntry> {
  @Column({ name: 'mission_id', type: 'uuid' })
  missionId: string;

  @ManyToOne(() => DiaryMission, (mission) => mission.entries)
  @JoinColumn({ name: 'mission_id' })
  mission: DiaryMission;

  @Column({ name: 'student_id', type: 'uuid' })
  studentId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({ name: 'word_count', type: 'integer' })
  wordCount: number;

  @Column({ name: 'progress', type: 'float' })
  progress: number;

  @Column({
    name: 'status',
    type: 'enum',
    enum: MissionEntryStatus,
    default: MissionEntryStatus.NEW,
  })
  status: MissionEntryStatus;

  @Column({
    name: 'unified_status',
    type: 'enum',
    enum: WritingEntryStatus,
    default: WritingEntryStatus.DRAFT,
  })
  unifiedStatus: WritingEntryStatus;

  @Column({ name: 'gained_score', type: 'integer', nullable: true })
  gainedScore: number;

  @Column({ name: 'submitted_at', type: 'timestamp', nullable: true })
  submittedAt: Date;

  @Column({ name: 'reviewed_at', type: 'timestamp', nullable: true })
  reviewedAt: Date;

  @Column({ name: 'evaluated_at', type: 'timestamp', nullable: true })
  evaluatedAt: Date;

  @Column({ name: 'evaluated_by', nullable: true })
  evaluatedBy: string;

  @Column({ name: 'reviewed_by', type: 'uuid', nullable: true })
  reviewedBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'reviewed_by' })
  reviewer: User;

  @Column({ name: 'correction', type: 'text', nullable: true })
  correction: string;

  @Column({ name: 'correction_provided_at', type: 'timestamp', nullable: true })
  correctionProvidedAt: Date;

  @Column({ name: 'correction_provided_by', type: 'uuid', nullable: true })
  correctionProvidedBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'correction_provided_by' })
  correctionProvider: User;

  @Column({ name: 'skin_id', type: 'uuid', nullable: true })
  skinId: string;

  @ManyToOne(() => DiarySkin)
  @JoinColumn({ name: 'skin_id' })
  skin: DiarySkin;

  @OneToMany(() => MissionDiaryEntryFeedback, (feedback) => feedback.missionEntry)
  feedbacks: MissionDiaryEntryFeedback[];

  // New fields for submission/draft logic
  @Column({ name: 'is_draft', type: 'boolean', default: true })
  isDraft: boolean;

  @Column({ name: 'last_submitted_at', type: 'timestamp', nullable: true })
  lastSubmittedAt: Date;

  @Column({ name: 'last_reviewed_at', type: 'timestamp', nullable: true })
  lastReviewedAt: Date;

  @Column({ name: 'can_submit_new_version', type: 'boolean', default: true })
  canSubmitNewVersion: boolean;

  @Column({ name: 'submitted_version_count', type: 'integer', default: 0 })
  submittedVersionCount: number;

  @Column({ name: 'current_submitted_version_id', type: 'uuid', nullable: true })
  currentSubmittedVersionId: string;

  // Resubmission tracking fields
  @Column({ name: 'is_resubmission', type: 'boolean', default: false })
  isResubmission: boolean;

  @Column({ name: 'resubmission_type', type: 'varchar', nullable: true })
  resubmissionType: 'after_review' | null;

  @Column({ name: 'previous_review_count', type: 'integer', default: 0 })
  previousReviewCount: number;

  @Column({ name: 'previous_confirmation_count', type: 'integer', default: 0 })
  previousConfirmationCount: number;

  @OneToOne(() => MissionDiaryEntryHistory, { nullable: true })
  @JoinColumn({ name: 'current_submitted_version_id' })
  currentSubmittedVersion: MissionDiaryEntryHistory;

  @Column({ name: 'total_edit_history', type: 'integer', default: 0 })
  totalEditHistory: number;

  // Legacy fields for backward compatibility
  @Column({ name: 'first_submission_notified', type: 'boolean', default: false })
  firstSubmissionNotified: boolean;

  @Column({ name: 'original_reviewed_version_id', type: 'uuid', nullable: true })
  originalReviewedVersionId: string;

  @OneToOne(() => MissionDiaryEntryHistory, { nullable: true })
  @JoinColumn({ name: 'original_reviewed_version_id' })
  originalReviewedVersion: MissionDiaryEntryHistory;

  @OneToMany(() => MissionDiaryEntryHistory, (version) => version.missionEntry)
  versions: MissionDiaryEntryHistory[];
}
