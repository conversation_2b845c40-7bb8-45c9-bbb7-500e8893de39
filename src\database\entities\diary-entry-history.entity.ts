import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, Join<PERSON>olumn, Index } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { DiaryEntry } from './diary-entry.entity';

@Entity()
@Index(['diaryEntryId', 'versionNumber'])
@Index(['diaryEntryId', 'isLatest'])
@Index(['createdAt'])
export class DiaryEntryHistory extends AuditableBaseEntity {
  @Column({ name: 'diary_entry_id', type: 'uuid' })
  diaryEntryId: string;

  @ManyToOne(() => DiaryEntry, (entry) => entry.versions)
  @JoinColumn({ name: 'diary_entry_id' })
  diaryEntry: DiaryEntry;

  @Column({ name: 'title' })
  title: string;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({ name: 'version_number', type: 'integer' })
  versionNumber: number;

  @Column({ name: 'is_latest', type: 'boolean', default: false })
  isLatest: boolean;

  @Column({ name: 'is_submitted_version', type: 'boolean', default: false })
  isSubmittedVersion: boolean;

  @Column({ name: 'submission_number', type: 'integer', nullable: true })
  submissionNumber: number;

  @Column({ name: 'submitted_at', type: 'timestamp', nullable: true })
  submittedAt: Date;

  // Resubmission tracking fields
  @Column({ name: 'is_resubmission', type: 'boolean', default: false })
  isResubmission: boolean;

  @Column({ name: 'resubmission_type', type: 'varchar', nullable: true })
  resubmissionType: 'after_review' | null;

  @Column({ name: 'previous_status', type: 'varchar', nullable: true })
  previousStatus: string;

  @Column({ name: 'word_count', type: 'integer' })
  wordCount: number;

  @Column({
    name: 'meta_data',
    type: 'json',
    nullable: true,
  })
  metaData?: {
    ipAddress?: string;
    userAgent?: string;
    contentLength?: number;
    contentLengthDiff?: number;
    significantChange?: boolean;
    timeFromLastUpdate?: number; // minutes since last update
    updateTrigger?: 'update' | 'submit' | 'restore';
    wordCountChange?: number;
    hasNewParagraphs?: boolean;
    editDistance?: number;
  };
}
