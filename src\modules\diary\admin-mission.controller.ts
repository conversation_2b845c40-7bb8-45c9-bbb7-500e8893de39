import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { DiaryMissionService } from './diary-mission.service';
import { CreateDiaryMissionDto, UpdateDiaryMissionDto, DiaryMissionResponseDto, MissionFilterDto } from '../../database/models/diary-mission.dto';

import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { PaginationDto } from '../../common/models/pagination.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { ApiOkResponseWithEmptyData } from 'src/common/decorators/api-empty-response.decorator';
import { AdminGuard } from 'src/common/guards/admin.guard';

@ApiTags('Admin Diary Missions Management')
@Controller('diary/admin/missions')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, AdminGuard)
export class AdminDiaryMissionController {
  constructor(private readonly diaryMissionService: DiaryMissionService) {}

  // ===== Mission Management Endpoints =====

  @Post()
  @ApiOperation({ summary: 'Create a new diary mission' })
  @ApiBody({
    type: CreateDiaryMissionDto,
    description: 'Diary mission creation data',
    examples: {
      example1: {
        value: {
          title: 'Weekly Writing Challenge',
          description: 'Write about your favorite book and why you enjoyed it.',
          categoryId: '123e4567-e89b-12d3-a456-************', // Optional - can be omitted
          targetWordCount: 200,
          targetMaxWordCount: 300,
          publishDate: '2023-08-15T00:00:00Z',
          expiryDate: '2023-08-30T23:59:59Z',
          score: 100,
        },
      },
    },
  })
  @ApiOkResponseWithType(DiaryMissionResponseDto, 'Mission created successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  async createMission(@Req() req: any, @Body() createDto: CreateDiaryMissionDto): Promise<ApiResponse<DiaryMissionResponseDto>> {
    const mission = await this.diaryMissionService.createMission(req.user.id, createDto);
    return ApiResponse.success(mission, 'Mission created successfully');
  }

  @Get()
  @ApiOperation({ summary: 'Get all diary missions' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'isActive', required: false, type: Boolean })
  @ApiQuery({ name: 'publishDateFrom', required: false, type: String })
  @ApiQuery({ name: 'publishDateTo', required: false, type: String })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortDirection', required: false, enum: ['ASC', 'DESC'] })
  @ApiOkResponseWithPagedListType(DiaryMissionResponseDto, 'Missions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  async getAllMissions(@Req() req: any, @Query() params: any): Promise<ApiResponse<PagedListDto<DiaryMissionResponseDto>>> {
    // Extract pagination parameters
    const paginationDto: PaginationDto = {
      page: params.page ? parseInt(params.page) : 1,
      limit: params.limit ? parseInt(params.limit) : 10,
      sortBy: params.sortBy,
      sortDirection: params.sortDirection,
    };

    // Extract filter parameters
    const filterDto: MissionFilterDto = {
      isActive: params.isActive === 'true' ? true : params.isActive === 'false' ? false : undefined,
      publishDateFrom: params.publishDateFrom,
      publishDateTo: params.publishDateTo,
      createdBy: params.createdBy,
    };

    // We still use getTutorMissions but it now returns all missions
    const missions = await this.diaryMissionService.getTutorMissions(req.user.id, filterDto, paginationDto);
    return ApiResponse.success(missions, 'Missions retrieved successfully');
  }

  @Put('/:id')
  @ApiOperation({ summary: 'Update a diary mission' })
  @ApiParam({ name: 'id', description: 'Mission ID' })
  @ApiBody({
    type: UpdateDiaryMissionDto,
    description: 'Diary mission update data',
    examples: {
      example1: {
        value: {
          title: 'Updated Writing Challenge',
          description: 'Updated description for the mission.',
          categoryId: '456e7890-e89b-12d3-a456-426614174001',
          targetWordCount: 250,
          targetMaxWordCount: 350,
          publishDate: '2023-08-20T00:00:00Z',
          expiryDate: '2023-09-05T23:59:59Z',
          isActive: true,
          score: 120,
        },
      },
    },
  })
  @ApiOkResponseWithType(DiaryMissionResponseDto, 'Mission updated successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Mission not found')
  async updateMission(@Req() req: any, @Param('id') id: string, @Body() updateDto: UpdateDiaryMissionDto): Promise<ApiResponse<DiaryMissionResponseDto>> {
    const mission = await this.diaryMissionService.updateMission(id, req.user.id, updateDto);
    return ApiResponse.success(mission, 'Mission updated successfully');
  }

  @Get('/:id')
  @ApiOperation({ summary: 'Get a specific diary mission' })
  @ApiParam({ name: 'id', description: 'Mission ID' })
  @ApiOkResponseWithType(DiaryMissionResponseDto, 'Mission retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Mission not found')
  async getMission(@Param('id') id: string): Promise<ApiResponse<DiaryMissionResponseDto>> {
    const mission = await this.diaryMissionService.getMission(id);
    return ApiResponse.success(mission, 'Mission retrieved successfully');
  }

  @Delete('/:id')
  @ApiOperation({ summary: 'Delete a diary mission' })
  @ApiParam({ name: 'id', description: 'Mission ID' })
  @ApiOkResponseWithEmptyData('Mission deleted successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Mission not found')
  async deleteMission(@Param('id') id: string): Promise<ApiResponse<null>> {
    await this.diaryMissionService.deleteMission(id);
    return ApiResponse.success(null, 'Mission deleted successfully');
  }
}
