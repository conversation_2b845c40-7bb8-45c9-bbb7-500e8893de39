import { MigrationInterface, QueryRunner } from 'typeorm';

export class NotificationAndMatching1745862923313 implements MigrationInterface {
  name = 'NotificationAndMatching1745862923313';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."notification_type_enum" AS ENUM('diary_submission', 'diary_review', 'diary_feedback', 'tutor_assignment', 'system')`);
    await queryRunner.query(
      `CREATE TABLE "notification" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" uuid NOT NULL, "type" "public"."notification_type_enum" NOT NULL, "title" character varying NOT NULL, "message" text NOT NULL, "related_entity_id" character varying, "related_entity_type" character varying, "is_read" boolean NOT NULL DEFAULT false, "read_at" TIMESTAMP, CONSTRAINT "PK_705b6c7cdf9b2c2ff7ac7872cb7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE TYPE "public"."notification_delivery_channel_enum" AS ENUM('in_app', 'email', 'push', 'mobile', 'sms', 'realtime_message')`);
    await queryRunner.query(`CREATE TYPE "public"."notification_delivery_status_enum" AS ENUM('pending', 'sent', 'failed', 'read')`);
    await queryRunner.query(
      `CREATE TABLE "notification_delivery" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "notification_id" uuid NOT NULL, "channel" "public"."notification_delivery_channel_enum" NOT NULL, "status" "public"."notification_delivery_status_enum" NOT NULL DEFAULT 'pending', "sent_at" TIMESTAMP, "error_message" text, "retry_count" integer NOT NULL DEFAULT '0', "next_retry_at" TIMESTAMP, CONSTRAINT "PK_109dea88c25343da49d86de6bfd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE TYPE "public"."user_notification_preference_notification_type_enum" AS ENUM('diary_submission', 'diary_review', 'diary_feedback', 'tutor_assignment', 'system')`);
    await queryRunner.query(`CREATE TYPE "public"."user_notification_preference_channel_enum" AS ENUM('in_app', 'email', 'push', 'mobile', 'sms', 'realtime_message')`);
    await queryRunner.query(
      `CREATE TABLE "user_notification_preference" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" uuid NOT NULL, "notification_type" "public"."user_notification_preference_notification_type_enum" NOT NULL, "channel" "public"."user_notification_preference_channel_enum" NOT NULL, "is_enabled" boolean NOT NULL DEFAULT true, "time_window" character varying, "days_of_week" text, CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2" UNIQUE ("user_id", "notification_type", "channel"), CONSTRAINT "PK_98bedc3257235969f6ff2ec6682" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE TYPE "public"."student_tutor_mapping_status_enum" AS ENUM('active', 'inactive')`);
    await queryRunner.query(
      `CREATE TABLE "student_tutor_mapping" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "student_id" uuid NOT NULL, "tutor_id" uuid NOT NULL, "module_id" uuid NOT NULL, "status" "public"."student_tutor_mapping_status_enum" NOT NULL DEFAULT 'active', "assigned_date" TIMESTAMP NOT NULL, "last_activity_date" TIMESTAMP, "notes" text, CONSTRAINT "UQ_4162d6d63d9bbc95345ea72334d" UNIQUE ("student_id", "module_id"), CONSTRAINT "PK_c7fd7f7a2f25d9230bce48147ab" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TYPE "public"."plan_feature_type_enum" RENAME TO "plan_feature_type_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."plan_feature_type_enum" AS ENUM('hec_user_diary', 'hec_play', 'english_qa_writing', 'english_essay', 'english_novel', 'module')`);
    await queryRunner.query(`ALTER TABLE "plan_feature" ALTER COLUMN "type" TYPE "public"."plan_feature_type_enum" USING "type"::"text"::"public"."plan_feature_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."plan_feature_type_enum_old"`);
    await queryRunner.query(`ALTER TABLE "notification" ADD CONSTRAINT "FK_928b7aa1754e08e1ed7052cb9d8" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(
      `ALTER TABLE "notification_delivery" ADD CONSTRAINT "FK_2e70a64359ce7416b06a4d7a432" FOREIGN KEY ("notification_id") REFERENCES "notification"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_notification_preference" ADD CONSTRAINT "FK_8ef85bc8bf572f525a116f50eac" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "student_tutor_mapping" ADD CONSTRAINT "FK_2c4b0b8b5a845fa92703bd5f9c9" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "student_tutor_mapping" ADD CONSTRAINT "FK_e244b2500b4cd575dde3aca20d6" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "student_tutor_mapping" ADD CONSTRAINT "FK_0661f17222fcd81d4df848b6f38" FOREIGN KEY ("module_id") REFERENCES "diary_settings_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "student_tutor_mapping" DROP CONSTRAINT "FK_0661f17222fcd81d4df848b6f38"`);
    await queryRunner.query(`ALTER TABLE "student_tutor_mapping" DROP CONSTRAINT "FK_e244b2500b4cd575dde3aca20d6"`);
    await queryRunner.query(`ALTER TABLE "student_tutor_mapping" DROP CONSTRAINT "FK_2c4b0b8b5a845fa92703bd5f9c9"`);
    await queryRunner.query(`ALTER TABLE "user_notification_preference" DROP CONSTRAINT "FK_8ef85bc8bf572f525a116f50eac"`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" DROP CONSTRAINT "FK_2e70a64359ce7416b06a4d7a432"`);
    await queryRunner.query(`ALTER TABLE "notification" DROP CONSTRAINT "FK_928b7aa1754e08e1ed7052cb9d8"`);
    await queryRunner.query(`CREATE TYPE "public"."plan_feature_type_enum_old" AS ENUM('hec_user_diary', 'hec_play', 'english_qa_writing', 'english_essay', 'english_novel')`);
    await queryRunner.query(`ALTER TABLE "plan_feature" ALTER COLUMN "type" TYPE "public"."plan_feature_type_enum_old" USING "type"::"text"::"public"."plan_feature_type_enum_old"`);
    await queryRunner.query(`DROP TYPE "public"."plan_feature_type_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."plan_feature_type_enum_old" RENAME TO "plan_feature_type_enum"`);
    await queryRunner.query(`DROP TABLE "student_tutor_mapping"`);
    await queryRunner.query(`DROP TYPE "public"."student_tutor_mapping_status_enum"`);
    await queryRunner.query(`DROP TABLE "user_notification_preference"`);
    await queryRunner.query(`DROP TYPE "public"."user_notification_preference_channel_enum"`);
    await queryRunner.query(`DROP TYPE "public"."user_notification_preference_notification_type_enum"`);
    await queryRunner.query(`DROP TABLE "notification_delivery"`);
    await queryRunner.query(`DROP TYPE "public"."notification_delivery_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."notification_delivery_channel_enum"`);
    await queryRunner.query(`DROP TABLE "notification"`);
    await queryRunner.query(`DROP TYPE "public"."notification_type_enum"`);
  }
}
