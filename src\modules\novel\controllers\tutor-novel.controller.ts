import { <PERSON>, Get, Post, Put, Body, Param, UseGuards, Req, Parse<PERSON><PERSON><PERSON>ip<PERSON>, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiParam, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../common/guards/jwt.guard';
import { TutorGuard } from '../../../common/guards/tutor.guard';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithArrayType, ApiErrorResponse } from '../../../common/decorators/api-response.decorator';
import { NovelReviewService } from '../services/novel-review.service';
import { NovelEntryService } from '../services/novel-entry.service';
import {
  NovelEntryResponseDto,
  CreateNovelFeedbackDto,
  CreateNovelCorrectionDto,
  UpdateNovelCorrectionDto,
  CreateNovelReviewDto,
  NovelFeedbackResponseDto,
  NovelCorrectionResponseDto,
  NovelEntryHistoryResponseDto,
  NovelEntryVersionDto,
} from '../../../database/models/novel.dto';

@ApiTags('tutor-novel')
@ApiBearerAuth('JWT-auth')
@Controller('tutor/novel')
@UseGuards(JwtAuthGuard, TutorGuard)
export class TutorNovelController {
  constructor(
    private readonly novelReviewService: NovelReviewService,
    private readonly novelEntryService: NovelEntryService,
  ) {}

  @Get('entries')
  @ApiOperation({ summary: 'Get novel entries assigned to tutor for review' })
  @ApiOkResponseWithArrayType(NovelEntryResponseDto, 'Retrieved novel entries for review')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  async getTutorEntries(@Req() req: any): Promise<ApiResponse<NovelEntryResponseDto[]>> {
    const result = await this.novelReviewService.getTutorEntries(req.user.id);
    return ApiResponse.success(result, 'Retrieved novel entries for review');
  }

  @Get('entries/:id')
  @ApiOperation({ summary: 'Get a specific novel entry for review' })
  @ApiParam({ name: 'id', description: 'Entry ID', type: String })
  @ApiOkResponseWithType(NovelEntryResponseDto, 'Retrieved novel entry for review')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required or no access to student')
  @ApiErrorResponse(404, 'Entry not found')
  async getEntryForReview(@Req() req: any, @Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<NovelEntryResponseDto>> {
    const result = await this.novelReviewService.getEntryForReview(req.user.id, id);
    return ApiResponse.success(result, 'Retrieved novel entry for review');
  }

  @Post('entries/:id/feedback')
  @ApiOperation({ summary: 'Submit feedback for a novel entry' })
  @ApiParam({ name: 'id', description: 'Entry ID', type: String })
  @ApiBody({
    type: CreateNovelFeedbackDto,
    description: 'Feedback data',
    examples: {
      example1: {
        value: {
          feedback: 'Great character development! The dialogue feels natural and engaging. Consider adding more descriptive details about the setting.',
        },
      },
    },
  })
  @ApiOkResponseWithType(NovelFeedbackResponseDto, 'Feedback submitted successfully')
  @ApiErrorResponse(400, 'Invalid input data or entry not submitted')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required or no access to student')
  @ApiErrorResponse(404, 'Entry not found')
  async submitFeedback(@Req() req: any, @Param('id', ParseUUIDPipe) id: string, @Body() createFeedbackDto: CreateNovelFeedbackDto): Promise<ApiResponse<NovelFeedbackResponseDto>> {
    const result = await this.novelReviewService.submitFeedback(req.user.id, id, createFeedbackDto);
    return ApiResponse.success(result, 'Feedback submitted successfully', 201);
  }

  @Get('entries/:id/feedbacks')
  @ApiOperation({
    summary: 'Get all feedbacks for a novel entry',
    description: 'Retrieve all feedback comments for a specific novel entry. Accessible to tutors who have access to the student.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the novel entry to get feedbacks for',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponseWithArrayType(NovelFeedbackResponseDto, 'Feedbacks retrieved successfully')
  @ApiErrorResponse(400, 'Invalid entry ID')
  @ApiErrorResponse(404, 'Novel entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to view feedbacks for this entry')
  @ApiErrorResponse(500, 'Internal server error')
  async getFeedbacks(@Req() req: any, @Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<NovelFeedbackResponseDto[]>> {
    const feedbacks = await this.novelReviewService.getFeedbacks(req.user.id, id);
    return ApiResponse.success(feedbacks, 'Feedbacks retrieved successfully');
  }

  @Post('entries/:id/correction')
  @ApiOperation({ summary: 'Submit correction text for a novel entry (without score)' })
  @ApiParam({ name: 'id', description: 'Entry ID', type: String })
  @ApiBody({
    type: CreateNovelCorrectionDto,
    description: 'Correction text data',
    examples: {
      example1: {
        value: {
          correction: 'Overall excellent work. The plot structure is well-developed and the characters are compelling. Minor grammar corrections needed in paragraphs 3 and 7.',
        },
      },
    },
  })
  @ApiOkResponseWithType(NovelCorrectionResponseDto, 'Correction submitted successfully')
  @ApiErrorResponse(400, 'Invalid input data, entry not submitted, or correction already exists')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required or no access to student')
  @ApiErrorResponse(404, 'Entry not found')
  async submitCorrection(@Req() req: any, @Param('id', ParseUUIDPipe) id: string, @Body() createCorrectionDto: CreateNovelCorrectionDto): Promise<ApiResponse<NovelCorrectionResponseDto>> {
    const result = await this.novelReviewService.submitCorrection(req.user.id, id, createCorrectionDto);
    return ApiResponse.success(result, 'Correction submitted successfully', 201);
  }

  @Put('entries/:id/correction')
  @ApiOperation({ summary: 'Update correction text for a novel entry (score cannot be modified)' })
  @ApiParam({ name: 'id', description: 'Entry ID', type: String })
  @ApiBody({
    type: UpdateNovelCorrectionDto,
    description: 'Updated correction text data',
    examples: {
      example1: {
        value: {
          correction: 'Updated: Overall excellent work with improved character development. The plot structure is well-developed and engaging.',
        },
      },
    },
  })
  @ApiOkResponseWithType(NovelCorrectionResponseDto, 'Correction updated successfully')
  @ApiErrorResponse(400, 'Invalid input data, no access to correction, or score already set')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required or no access to student')
  @ApiErrorResponse(404, 'Entry or correction not found')
  async updateCorrection(@Req() req: any, @Param('id', ParseUUIDPipe) id: string, @Body() updateCorrectionDto: UpdateNovelCorrectionDto): Promise<ApiResponse<NovelCorrectionResponseDto>> {
    const result = await this.novelReviewService.updateCorrection(req.user.id, id, updateCorrectionDto);
    return ApiResponse.success(result, 'Correction updated successfully');
  }

  @Post('entries/:id/review')
  @ApiOperation({ summary: 'Submit final review with correction and score for a novel entry' })
  @ApiParam({ name: 'id', description: 'Entry ID', type: String })
  @ApiBody({
    type: CreateNovelReviewDto,
    description: 'Final review data with correction and score',
    examples: {
      example1: {
        value: {
          correction: 'Overall excellent work. The plot structure is well-developed and the characters are compelling. Minor grammar corrections needed in paragraphs 3 and 7.',
          score: 85,
        },
      },
    },
  })
  @ApiOkResponseWithType(NovelCorrectionResponseDto, 'Review submitted successfully')
  @ApiErrorResponse(400, 'Invalid input data, entry not submitted, or already reviewed')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required or no access to student')
  @ApiErrorResponse(404, 'Entry not found')
  async submitReview(@Req() req: any, @Param('id', ParseUUIDPipe) id: string, @Body() createReviewDto: CreateNovelReviewDto): Promise<ApiResponse<NovelCorrectionResponseDto>> {
    if (!id || id === 'undefined' || id.trim() === '') {
      throw new BadRequestException('Entry ID is required and cannot be undefined');
    }
    const result = await this.novelReviewService.submitReview(req.user.id, id, createReviewDto);
    return ApiResponse.success(result, 'Review submitted successfully', 201);
  }

  // REMOVED: Confirm endpoint - confirm stage removed from lifecycle
  // Review is now the final state, no confirmation needed

  // ===== Version History Endpoints =====

  @Get('entries/:id/history')
  @ApiOperation({
    summary: 'Get novel entry version history (Tutor)',
    description: "View all previous versions of a student's novel entry. Shows the complete history of changes made to the entry.",
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the novel entry',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponseWithType(NovelEntryHistoryResponseDto, 'Version history retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Novel entry not found')
  @ApiErrorResponse(403, "Forbidden - You do not have access to this student's entry")
  @ApiErrorResponse(500, 'Internal server error')
  async getNovelEntryHistory(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<NovelEntryHistoryResponseDto>> {
    const tutorId = req.user.id;

    // First verify tutor has access to this entry
    await this.novelReviewService.getEntryForReview(tutorId, id);

    // Get the student ID from the entry to pass to the history service
    const entry = await this.novelReviewService.getEntryForReview(tutorId, id);
    const history = await this.novelEntryService.getNovelEntryHistory(id, entry.studentId);
    return ApiResponse.success(history, 'Version history retrieved successfully');
  }

  @Get('entries/:id/versions/:versionId')
  @ApiOperation({
    summary: 'View a specific version (Tutor)',
    description: "View the content of a specific version of a student's novel entry.",
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the novel entry',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiParam({
    name: 'versionId',
    description: 'The ID of the specific version',
    example: '456e7890-e89b-12d3-a456-************',
  })
  @ApiOkResponseWithType(NovelEntryVersionDto, 'Version retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Version not found')
  @ApiErrorResponse(403, "Forbidden - You do not have access to this student's entry")
  @ApiErrorResponse(500, 'Internal server error')
  async getNovelEntryVersion(@Req() req: any, @Param('id') id: string, @Param('versionId') versionId: string): Promise<ApiResponse<NovelEntryVersionDto>> {
    const tutorId = req.user.id;

    // First verify tutor has access to this entry
    const entry = await this.novelReviewService.getEntryForReview(tutorId, id);

    // Get the version using the student ID
    const version = await this.novelEntryService.getNovelEntryVersion(versionId, entry.studentId);
    return ApiResponse.success(version, 'Version retrieved successfully');
  }
}
