import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixAwardScheduleColumns1747300000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, delete any migration records for our problematic migrations
    await queryRunner.query(`DELETE FROM migrations WHERE name IN (
            'AddAwardScheduleProcessingTimes1747000000002',
            'create-award-schedule1747000000003'
        )`);

    // Check if the processing_started_at column exists
    const hasProcessingStartedAt = await queryRunner.hasColumn('award_schedule', 'processing_started_at');
    if (hasProcessingStartedAt) {
      // If the column exists, we don't need to do anything as it was added in the initial migration
      return;
    }

    // If we get here, it means we need to add the columns
    await queryRunner.query(`
            ALTER TABLE "award_schedule" 
            ADD COLUMN IF NOT EXISTS "processing_started_at" timestamp,
            ADD COLUMN IF NOT EXISTS "processing_completed_at" timestamp
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // We don't want to drop these columns in down migration as they are part of the core functionality
  }
}
