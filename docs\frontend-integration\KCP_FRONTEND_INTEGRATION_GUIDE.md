# KCP Payment Gateway Frontend Integration Guide

## Overview

This guide provides complete implementation details for integrating KCP payment gateway in your frontend application. The integration follows KCP's official JavaScript SDK pattern for secure and reliable payment processing.

**🚀 Updated for Complete Backend Implementation (2025-06-18)**

## Architecture Overview

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Frontend  │    │   Backend   │    │     KCP     │    │  Database   │
│             │    │             │    │   Gateway   │    │             │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
        │                   │                   │                   │
        │ 1. Checkout       │                   │                   │
        │ ─────────────────>│ 2. Trade Reg     │                   │
        │                   │ ─────────────────>│                   │
        │                   │ 3. Create Txn     │                   │
        │                   │ ─────────────────────────────────────>│
        │ 4. Payment URL    │                   │                   │
        │ <─────────────────│                   │                   │
        │                   │                   │                   │
        │ 5. Load KCP Page  │                   │                   │
        │ ─────────────────────────────────────>│                   │
        │                   │                   │                   │
        │ 6. Payment Process│                   │                   │
        │ <─────────────────────────────────────│                   │
        │                   │                   │                   │
        │ 7. Process Payment│                   │                   │
        │ ─────────────────>│ 8. Verify & Complete                  │
        │                   │ ─────────────────────────────────────>│
        │ 9. Success/Failure│                   │                   │
        │ <─────────────────│                   │                   │
```

## Complete Payment Flow

### **Step 1: Checkout Initiation** ✅
- User clicks checkout → `POST /shop/cart/checkout`
- Backend creates KCP trade registration
- Returns payment URL with form data

### **Step 2: Payment Page** 🔧 (Frontend Implementation)
- Frontend redirects to KCP payment page
- Displays payment form with KCP SDK
- User selects payment method and confirms

### **Step 3: KCP Payment Processing** ✅
- KCP handles payment authentication
- Calls `m_Completepayment()` callback
- Form submits to `POST /payment/kcp/process`

### **Step 4: Payment Completion** ✅
- Backend verifies payment with KCP
- Completes actual checkout process
- Redirects to success/failure page

## 🚀 **Complete Frontend Implementation Guide**

### **When to Call Checkout API**

The checkout API should be called when:
1. **User clicks "Pay Now" button** in shopping cart
2. **All required information is collected** (shipping, billing, etc.)
3. **User has selected KCP as payment method**
4. **Cart validation is complete** (items available, prices current)

### **Checkout API Call Implementation**

```typescript
// 1. Call checkout API when user initiates payment
const handleCheckout = async () => {
  try {
    setLoading(true);

    const checkoutRequest = {
      paymentMethod: 'kcp_card', // or 'kcp_bank', 'kcp_virtual_account'
      useRewardPoints: false,
      returnUrl: `${window.location.origin}/payment/success`,
      cancelUrl: `${window.location.origin}/payment/cancel`
    };

    const response = await fetch('/shop/cart/checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(checkoutRequest)
    });

    const result = await response.json();

    if (result.success) {
      // Handle successful checkout initiation
      handleCheckoutResponse(result.data);
    } else {
      throw new Error(result.message);
    }

  } catch (error) {
    console.error('Checkout failed:', error);
    setError(error.message);
  } finally {
    setLoading(false);
  }
};
```

### **Checkout Response Handling**

```typescript
// 2. Handle checkout response and redirect to payment
const handleCheckoutResponse = (checkoutData) => {
  console.log('Checkout Response:', checkoutData);

  // Expected response structure:
  // {
  //   "orderId": "ORDER-123",
  //   "paymentTransactionId": "TXN-456",
  //   "paymentUrl": "http://frontend.com/payment/kcp?site_cd=T0000&tno=123&...",
  //   "status": "PAYMENT_PENDING",
  //   "totalAmount": 20,
  //   "message": "Payment initiated successfully"
  // }

  if (checkoutData.status === 'PAYMENT_PENDING' && checkoutData.paymentUrl) {
    // Store order info for later reference
    sessionStorage.setItem('pendingOrder', JSON.stringify({
      orderId: checkoutData.orderId,
      transactionId: checkoutData.paymentTransactionId,
      amount: checkoutData.totalAmount
    }));

    // Redirect to KCP payment page
    window.location.href = checkoutData.paymentUrl;
  } else {
    throw new Error('Invalid checkout response');
  }
};
```

### **KCP Payment Page Implementation**

```typescript
// 3. KCP Payment Page Component (/payment/kcp)
const KcpPaymentPage = () => {
  const [formData, setFormData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Parse URL parameters from checkout response
    const urlParams = new URLSearchParams(window.location.search);
    const paymentData = {
      site_cd: urlParams.get('site_cd'),
      site_name: urlParams.get('site_name'),
      ordr_idxx: urlParams.get('ordr_idxx'),
      good_name: urlParams.get('good_name'),
      good_mny: urlParams.get('good_mny'),
      buyr_name: urlParams.get('buyr_name'),
      buyr_tel2: urlParams.get('buyr_tel2'),
      buyr_mail: urlParams.get('buyr_mail'),
      pay_method: urlParams.get('pay_method'),
      quotaopt: urlParams.get('quotaopt'),
      return_url: urlParams.get('return_url'),
      action_url: urlParams.get('action_url'),
      tno: urlParams.get('tno'),
      ordr_chk: urlParams.get('ordr_chk'),
      kcp_sign_data: urlParams.get('kcp_sign_data')
    };

    if (!paymentData.site_cd || !paymentData.tno) {
      setError('Invalid payment data');
      return;
    }

    setFormData(paymentData);
    setLoading(false);
  }, []);

  // Load KCP SDK
  useEffect(() => {
    if (!formData) return;

    const script = document.createElement('script');
    script.src = 'https://testspay.kcp.co.kr/plugin/kcp_spay_hub.js';
    script.onload = () => {
      console.log('KCP SDK loaded successfully');
    };
    script.onerror = () => {
      setError('Failed to load KCP payment SDK');
    };
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, [formData]);

  if (loading) return <div>Loading payment page...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="kcp-payment-page">
      <h1>Complete Your Payment</h1>
      <KcpPaymentForm formData={formData} />
    </div>
  );
};
```

### **Payment Form and Processing**

```typescript
// 4. KCP Payment Form Component
const KcpPaymentForm = ({ formData }) => {
  const handlePayment = () => {
    try {
      // Call KCP payment function
      window.KCP_Pay_Execute_Web(document.order_info);
    } catch (error) {
      console.error('Payment execution failed:', error);
      alert('Payment failed to start. Please try again.');
    }
  };

  // KCP completion callback (MUST be global)
  useEffect(() => {
    window.m_Completepayment = (FormOrJson, closeEvent) => {
      const form = document.order_info;

      // Set form values from KCP response
      Object.keys(FormOrJson).forEach(key => {
        if (form[key]) {
          form[key].value = FormOrJson[key];
        }
      });

      // Check payment result
      if (form.res_cd.value === '0000') {
        // Payment successful - submit to backend
        form.action = formData.action_url || '/payment/kcp/process';
        form.method = 'POST';
        form.submit();
      } else {
        // Payment failed
        alert(`Payment failed: [${form.res_cd.value}] ${form.res_msg.value}`);
        closeEvent();

        // Redirect to cancel page
        window.location.href = `/payment/cancel?error=${encodeURIComponent(form.res_msg.value)}`;
      }
    };

    return () => {
      delete window.m_Completepayment;
    };
  }, [formData]);

  return (
    <form name="order_info" method="post">
      {/* Order Information Display */}
      <div className="order-summary">
        <h3>Order Summary</h3>
        <p>Order ID: {formData.ordr_idxx}</p>
        <p>Product: {formData.good_name}</p>
        <p>Amount: ₩{parseInt(formData.good_mny).toLocaleString()}</p>
        <p>Buyer: {formData.buyr_name}</p>
      </div>

      {/* Payment Method Selection */}
      <div className="payment-methods">
        <h3>Select Payment Method</h3>
        <label>
          <input type="radio" name="pay_method" value="************" defaultChecked />
          Credit Card
        </label>
        <label>
          <input type="radio" name="pay_method" value="************" />
          Bank Transfer
        </label>
        <label>
          <input type="radio" name="pay_method" value="************" />
          Virtual Account
        </label>
      </div>

      {/* Hidden Form Fields (Required by KCP) */}
      <input type="hidden" name="site_cd" value={formData.site_cd} />
      <input type="hidden" name="site_name" value={formData.site_name} />
      <input type="hidden" name="ordr_idxx" value={formData.ordr_idxx} />
      <input type="hidden" name="good_name" value={formData.good_name} />
      <input type="hidden" name="good_mny" value={formData.good_mny} />
      <input type="hidden" name="buyr_name" value={formData.buyr_name} />
      <input type="hidden" name="buyr_tel2" value={formData.buyr_tel2} />
      <input type="hidden" name="buyr_mail" value={formData.buyr_mail} />
      <input type="hidden" name="quotaopt" value={formData.quotaopt} />

      {/* KCP Response Fields (Populated by KCP) */}
      <input type="hidden" name="res_cd" value="" />
      <input type="hidden" name="res_msg" value="" />
      <input type="hidden" name="enc_info" value="" />
      <input type="hidden" name="enc_data" value="" />
      <input type="hidden" name="tran_cd" value="" />

      {/* Payment Button */}
      <button
        type="button"
        onClick={handlePayment}
        className="payment-button"
      >
        Pay ₩{parseInt(formData.good_mny).toLocaleString()}
      </button>
    </form>
  );
};
```

### **Success and Failure Page Implementation**

```typescript
// 5. Payment Success Page (/payment/success)
const PaymentSuccessPage = () => {
  const [orderData, setOrderData] = useState(null);
  const searchParams = new URLSearchParams(window.location.search);

  useEffect(() => {
    const orderId = searchParams.get('orderId');
    const transactionId = searchParams.get('transactionId');

    // Get stored order data
    const pendingOrder = JSON.parse(sessionStorage.getItem('pendingOrder') || '{}');

    setOrderData({
      orderId: orderId || pendingOrder.orderId,
      transactionId: transactionId || pendingOrder.transactionId,
      amount: pendingOrder.amount
    });

    // Clear pending order
    sessionStorage.removeItem('pendingOrder');
  }, []);

  return (
    <div className="success-page">
      <div className="success-icon">✅</div>
      <h1>Payment Successful!</h1>
      <div className="order-details">
        <p><strong>Order ID:</strong> {orderData?.orderId}</p>
        <p><strong>Transaction ID:</strong> {orderData?.transactionId}</p>
        <p><strong>Amount:</strong> ₩{orderData?.amount?.toLocaleString()}</p>
      </div>
      <div className="actions">
        <button onClick={() => window.location.href = '/orders'}>
          View My Orders
        </button>
        <button onClick={() => window.location.href = '/shop'}>
          Continue Shopping
        </button>
      </div>
    </div>
  );
};

// 6. Payment Cancel/Failure Page (/payment/cancel)
const PaymentCancelPage = () => {
  const searchParams = new URLSearchParams(window.location.search);
  const error = searchParams.get('error');
  const reason = searchParams.get('reason');

  return (
    <div className="cancel-page">
      <div className="error-icon">❌</div>
      <h1>Payment Cancelled</h1>
      {error && <p className="error-message">Error: {error}</p>}
      {reason && <p className="reason">Reason: {reason}</p>}
      <div className="actions">
        <button onClick={() => window.location.href = '/cart'}>
          Return to Cart
        </button>
        <button onClick={() => window.location.href = '/shop'}>
          Continue Shopping
        </button>
      </div>
    </div>
  );
};
```

## 1. Backend API Endpoints

### 1.1 Available Endpoints ✅

The backend provides the following KCP payment endpoints:

#### **Checkout Initiation**
```
POST /shop/cart/checkout
```
**Request:**
```json
{
  "paymentMethod": "kcp_card",
  "useRewardPoints": false,
  "returnUrl": "http://**************:3010/payment/success",
  "cancelUrl": "http://**************:3010/payment/cancel"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment initiated successfully",
  "data": {
    "orderId": "ORDER-123",
    "paymentTransactionId": "TXN-123",
    "paymentUrl": "http://**************:3001/payment/kcp?site_cd=T0000&tno=TXN123&...",
    "status": "PAYMENT_PENDING",
    "totalAmount": 20
  }
}
```

#### **Payment Processing** (Called by KCP form)
```
POST /payment/kcp/process
```
**Request:** (Form data from KCP)
```
tno=TXN123&ordr_idxx=ORDER123&res_cd=0000&res_msg=SUCCESS&enc_info=...&enc_data=...
```

**Response:** (Redirect information)
```json
{
  "success": true,
  "redirect": "http://**************:3010/payment/success?orderId=ORDER-123&transactionId=TXN-456",
  "message": "Payment completed successfully",
  "data": {
    "transactionId": "TXN-456",
    "orderId": "ORDER-123",
    "kcpTransactionId": "TXN123"
  }
}
```

**Note:** This single endpoint now handles all KCP payment completion scenarios, including both form submissions and return handling.
```

## 2. Environment Setup

### 2.1 Environment Variables

```javascript
// .env or environment configuration
REACT_APP_API_URL=http://**************:3010
REACT_APP_FRONTEND_URL=http://**************:3011
REACT_APP_KCP_ENVIRONMENT=staging  // 'staging' or 'production'
```

### 2.2 KCP SDK Configuration

```javascript
// config/kcp.config.js
export const KCP_CONFIG = {
  SDK_URLS: {
    staging: 'https://testspay.kcp.co.kr/plugin/kcp_spay_hub.js',
    production: 'https://spay.kcp.co.kr/plugin/kcp_spay_hub.js'
  },
  PAYMENT_METHODS: {
    CREDIT_CARD: '************',
    BANK_TRANSFER: '************',
    VIRTUAL_ACCOUNT: '************',
    MOBILE_PAYMENT: '************'
  }
};
```

## 3. Core Implementation

### 🚨 **Critical Implementation Notes**

1. **Form Submission**: The KCP payment form MUST submit to the backend endpoint (`/payment/kcp/process`) after successful payment authentication
2. **No Manual Verification**: Do NOT manually call verification APIs - the form submission handles everything
3. **Backend Handles Redirect**: The backend will redirect to success/failure pages after processing
4. **Amount Format**: Ensure amounts are integers (no decimals) for KRW payments
5. **Required Fields**: All hidden form fields must be included for KCP to work properly

### 3.1 KCP SDK Loader Utility

```javascript
// utils/kcpLoader.js
import { KCP_CONFIG } from '../config/kcp.config';

export class KcpSdkLoader {
  static isLoaded = false;
  static loadPromise = null;

  static async loadSdk(environment = 'staging') {
    // Return existing promise if already loading
    if (this.loadPromise) {
      return this.loadPromise;
    }

    // Return immediately if already loaded
    if (this.isLoaded && window.KCP_Pay_Execute_Web) {
      return Promise.resolve();
    }

    this.loadPromise = new Promise((resolve, reject) => {
      // Check if SDK is already loaded
      if (window.KCP_Pay_Execute_Web) {
        this.isLoaded = true;
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = KCP_CONFIG.SDK_URLS[environment];
      script.async = true;

      script.onload = () => {
        this.isLoaded = true;
        console.log('KCP SDK loaded successfully');
        resolve();
      };

      script.onerror = () => {
        this.loadPromise = null;
        reject(new Error('Failed to load KCP SDK'));
      };

      document.head.appendChild(script);
    });

    return this.loadPromise;
  }

  static unload() {
    // Remove SDK script and reset state
    const scripts = document.querySelectorAll('script[src*="kcp_spay_hub.js"]');
    scripts.forEach(script => script.remove());

    delete window.KCP_Pay_Execute_Web;
    delete window.m_Completepayment;

    this.isLoaded = false;
    this.loadPromise = null;
  }
}
```

### 3.2 Payment API Service

```javascript
// services/paymentService.js
import axios from 'axios';

class PaymentService {
  constructor() {
    this.apiClient = axios.create({
      baseURL: process.env.REACT_APP_API_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Add auth token interceptor
    this.apiClient.interceptors.request.use((config) => {
      const token = this.getAuthToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
  }

  getAuthToken() {
    return localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
  }

  async checkout(checkoutData) {
    try {
      const response = await this.apiClient.post('/shop/cart/checkout', checkoutData);
      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  async processPayment(paymentData) {
    try {
      const response = await this.apiClient.post('/payment/kcp/process', paymentData);
      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  async getPaymentStatus(transactionId) {
    try {
      const response = await this.apiClient.get(`/payment/status/${transactionId}`);
      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  handleApiError(error) {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      return new Error(data.error?.message || `HTTP ${status}: ${data.message || 'Unknown error'}`);
    } else if (error.request) {
      // Network error
      return new Error('Network error: Unable to connect to payment service');
    } else {
      // Other error
      return new Error(error.message || 'Unknown payment error');
    }
  }
}

export const paymentService = new PaymentService();
```

## 4. React Components

### 4.1 Main Payment Page Component

```jsx
// components/payment/KcpPaymentPage.jsx
import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { KcpSdkLoader } from '../../utils/kcpLoader';
import { KcpPaymentForm } from './KcpPaymentForm';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { ErrorMessage } from '../common/ErrorMessage';

export const KcpPaymentPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState(null);

  useEffect(() => {
    initializePayment();
  }, []);

  const initializePayment = async () => {
    try {
      // Extract form data from URL parameters
      const extractedFormData = extractFormDataFromParams();

      if (!extractedFormData.site_cd || !extractedFormData.tno) {
        throw new Error('Invalid payment parameters');
      }

      setFormData(extractedFormData);

      // Load KCP SDK
      const environment = process.env.REACT_APP_KCP_ENVIRONMENT || 'staging';
      await KcpSdkLoader.loadSdk(environment);

      setIsLoading(false);
    } catch (err) {
      console.error('Payment initialization error:', err);
      setError(err.message);
      setIsLoading(false);
    }
  };

  const extractFormDataFromParams = () => {
    return {
      site_cd: searchParams.get('site_cd'),
      site_name: searchParams.get('site_name') || 'HEC Payment',
      ordr_idxx: searchParams.get('ordr_idxx'),
      good_name: searchParams.get('good_name'),
      good_mny: searchParams.get('good_mny'),
      buyr_name: searchParams.get('buyr_name'),
      buyr_tel2: searchParams.get('buyr_tel2'),
      buyr_mail: searchParams.get('buyr_mail'),
      pay_method: searchParams.get('pay_method') || '************',
      quotaopt: searchParams.get('quotaopt') || '12',
      return_url: searchParams.get('return_url'),
      action_url: searchParams.get('action_url'),
      tno: searchParams.get('tno'),
      ordr_chk: searchParams.get('ordr_chk'),
      kcp_sign_data: searchParams.get('kcp_sign_data'),
      // Hidden fields that KCP will populate
      res_cd: '',
      res_msg: '',
      enc_info: '',
      enc_data: '',
      tran_cd: ''
    };
  };

  const handlePaymentCancel = () => {
    navigate('/cart');
  };

  if (isLoading) {
    return (
      <div className="payment-loading">
        <LoadingSpinner />
        <p>Loading payment system...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="payment-error">
        <ErrorMessage message={error} />
        <button onClick={handlePaymentCancel} className="btn-secondary">
          Return to Cart
        </button>
      </div>
    );
  }

  return (
    <div className="payment-page">
      <div className="payment-container">
        <h1>Complete Your Payment</h1>
        <KcpPaymentForm
          formData={formData}
          onCancel={handlePaymentCancel}
        />
      </div>
    </div>
  );
};
```

### 3.2 KCP Payment Form Component

```jsx
// components/payment/KcpPaymentForm.jsx
import React, { useRef, useEffect, useState } from 'react';
import { paymentService } from '../../services/paymentService';
import { KCP_CONFIG } from '../../config/kcp.config';

export const KcpPaymentForm = ({ formData, onCancel }) => {
  const formRef = useRef(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(formData.pay_method);

  useEffect(() => {
    // Define the global callback function that KCP will call
    window.m_Completepayment = function(FormOrJson, closeEvent) {
      const form = formRef.current;

      // KCP populates form fields with payment result
      GetField(form, FormOrJson);

      // Check payment result
      if (form.res_cd.value === '0000') {
        // Payment successful - submit to backend for verification
        handlePaymentSuccess(form);
      } else {
        // Payment failed
        handlePaymentFailure(form, closeEvent);
      }
    };

    // Cleanup function
    return () => {
      delete window.m_Completepayment;
    };
  }, []);

  const handlePaymentRequest = async () => {
    if (isProcessing) return;

    try {
      setIsProcessing(true);

      // Update payment method in form
      const form = formRef.current;
      form.pay_method.value = selectedPaymentMethod;

      // Call KCP payment function
      window.KCP_Pay_Execute_Web(form);
    } catch (e) {
      // IE may throw error on normal completion
      console.log('KCP payment execution completed');
      setIsProcessing(false);
    }
  };

  const handlePaymentSuccess = async (form) => {
    try {
      console.log('Payment successful, processing with backend...');

      // Submit form to backend for processing (this completes the actual checkout)
      form.action = formData.action_url || `${process.env.REACT_APP_API_URL}/payment/kcp/process`;
      form.method = 'POST';
      form.submit();

      // Note: The backend will handle the redirect to success/failure page
      // No need for manual redirect here as the form submission handles it
    } catch (error) {
      console.error('Payment processing error:', error);
      window.location.href = `/payment/failure?error=${encodeURIComponent(error.message)}`;
    }
  };

  const handlePaymentFailure = (form, closeEvent) => {
    const errorMessage = `[${form.res_cd.value}] ${form.res_msg.value}`;
    alert(errorMessage);
    closeEvent(); // Close KCP payment window

    // Redirect to failure page
    window.location.href = `/payment/failure?transaction=${form.tno.value}&order=${form.ordr_idxx.value}&error=${encodeURIComponent(form.res_msg.value)}`;
  };

  const handlePaymentMethodChange = (method) => {
    setSelectedPaymentMethod(method);
  };

  return (
    <div className="kcp-payment-container">
      <form ref={formRef} name="order_info" method="post">
        {/* Payment Information Display */}
        <div className="payment-info">
          <h2>Payment Information</h2>

          <div className="info-grid">
            <div className="info-item">
              <label>Order Number:</label>
              <span>{formData.ordr_idxx}</span>
              <input type="hidden" name="ordr_idxx" value={formData.ordr_idxx} />
            </div>

            <div className="info-item">
              <label>Product:</label>
              <span>{formData.good_name}</span>
              <input type="hidden" name="good_name" value={formData.good_name} />
            </div>

            <div className="info-item">
              <label>Amount:</label>
              <span className="amount">{Number(formData.good_mny).toLocaleString()} KRW</span>
              <input type="hidden" name="good_mny" value={formData.good_mny} />
            </div>

            <div className="info-item">
              <label>Buyer:</label>
              <span>{formData.buyr_name}</span>
              <input type="hidden" name="buyr_name" value={formData.buyr_name} />
            </div>

            <div className="info-item">
              <label>Phone:</label>
              <span>{formData.buyr_tel2}</span>
              <input type="hidden" name="buyr_tel2" value={formData.buyr_tel2} />
            </div>

            <div className="info-item">
              <label>Email:</label>
              <span>{formData.buyr_mail}</span>
              <input type="hidden" name="buyr_mail" value={formData.buyr_mail} />
            </div>
          </div>
        </div>

        {/* Payment Method Selection */}
        <div className="payment-methods">
          <h3>Select Payment Method</h3>
          <div className="payment-options">
            <label className={`payment-option ${selectedPaymentMethod === KCP_CONFIG.PAYMENT_METHODS.CREDIT_CARD ? 'selected' : ''}`}>
              <input
                type="radio"
                name="pay_method"
                value={KCP_CONFIG.PAYMENT_METHODS.CREDIT_CARD}
                checked={selectedPaymentMethod === KCP_CONFIG.PAYMENT_METHODS.CREDIT_CARD}
                onChange={() => handlePaymentMethodChange(KCP_CONFIG.PAYMENT_METHODS.CREDIT_CARD)}
              />
              <div className="option-content">
                <span className="option-icon">💳</span>
                <span className="option-text">Credit Card</span>
              </div>
            </label>

            <label className={`payment-option ${selectedPaymentMethod === KCP_CONFIG.PAYMENT_METHODS.BANK_TRANSFER ? 'selected' : ''}`}>
              <input
                type="radio"
                name="pay_method"
                value={KCP_CONFIG.PAYMENT_METHODS.BANK_TRANSFER}
                checked={selectedPaymentMethod === KCP_CONFIG.PAYMENT_METHODS.BANK_TRANSFER}
                onChange={() => handlePaymentMethodChange(KCP_CONFIG.PAYMENT_METHODS.BANK_TRANSFER)}
              />
              <div className="option-content">
                <span className="option-icon">🏦</span>
                <span className="option-text">Bank Transfer</span>
              </div>
            </label>

            <label className={`payment-option ${selectedPaymentMethod === KCP_CONFIG.PAYMENT_METHODS.VIRTUAL_ACCOUNT ? 'selected' : ''}`}>
              <input
                type="radio"
                name="pay_method"
                value={KCP_CONFIG.PAYMENT_METHODS.VIRTUAL_ACCOUNT}
                checked={selectedPaymentMethod === KCP_CONFIG.PAYMENT_METHODS.VIRTUAL_ACCOUNT}
                onChange={() => handlePaymentMethodChange(KCP_CONFIG.PAYMENT_METHODS.VIRTUAL_ACCOUNT)}
              />
              <div className="option-content">
                <span className="option-icon">🏧</span>
                <span className="option-text">Virtual Account</span>
              </div>
            </label>
          </div>
        </div>

        {/* Hidden fields required by KCP */}
        <input type="hidden" name="site_cd" value={formData.site_cd} />
        <input type="hidden" name="site_name" value={formData.site_name} />
        <input type="hidden" name="quotaopt" value={formData.quotaopt} />
        <input type="hidden" name="tno" value={formData.tno} />
        <input type="hidden" name="ordr_chk" value={formData.ordr_chk} />
        <input type="hidden" name="kcp_sign_data" value={formData.kcp_sign_data} />

        {/* Fields that KCP will populate */}
        <input type="hidden" name="res_cd" value="" />
        <input type="hidden" name="res_msg" value="" />
        <input type="hidden" name="enc_info" value="" />
        <input type="hidden" name="enc_data" value="" />
        <input type="hidden" name="tran_cd" value="" />

        {/* Payment Actions */}
        <div className="payment-actions">
          <button
            type="button"
            onClick={handlePaymentRequest}
            className="btn-primary pay-button"
            disabled={isProcessing}
          >
            {isProcessing ? 'Processing...' : `Pay ${Number(formData.good_mny).toLocaleString()} KRW`}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="btn-secondary cancel-button"
            disabled={isProcessing}
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};
```

### 3.3 Required Utility Functions

```javascript
// utils/kcpUtils.js

/**
 * KCP utility function - MUST be included
 * This function is called by KCP to populate form fields with payment results
 */
window.GetField = function(form, FormOrJson) {
  if (typeof FormOrJson === 'string') {
    // Handle JSON string response
    try {
      const data = JSON.parse(FormOrJson);
      Object.keys(data).forEach(key => {
        if (form[key]) {
          form[key].value = data[key];
        }
      });
    } catch (e) {
      console.error('Error parsing KCP response:', e);
    }
  } else {
    // Handle form object response
    for (let i = 0; i < FormOrJson.elements.length; i++) {
      const element = FormOrJson.elements[i];
      if (form[element.name]) {
        form[element.name].value = element.value;
      }
    }
  }
};

/**
 * Format currency for display
 */
export const formatCurrency = (amount, currency = 'KRW') => {
  return new Intl.NumberFormat('ko-KR', {
    style: 'currency',
    currency: currency
  }).format(amount);
};

/**
 * Validate payment form data
 */
export const validatePaymentData = (formData) => {
  const required = ['site_cd', 'tno', 'ordr_idxx', 'good_name', 'good_mny', 'buyr_name', 'buyr_mail'];

  for (const field of required) {
    if (!formData[field]) {
      throw new Error(`Missing required field: ${field}`);
    }
  }

  if (isNaN(formData.good_mny) || Number(formData.good_mny) <= 0) {
    throw new Error('Invalid payment amount');
  }

  return true;
};
```

## 4. Styling (CSS)

```css
/* styles/payment.css */

.payment-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.payment-container {
  max-width: 600px;
  width: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.payment-container h1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin: 0;
  padding: 24px;
  text-align: center;
  font-size: 24px;
  font-weight: 600;
}

.kcp-payment-container {
  padding: 32px;
}

.payment-info {
  margin-bottom: 32px;
}

.payment-info h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.info-item label {
  font-weight: 600;
  color: #555;
}

.info-item span {
  color: #333;
}

.info-item .amount {
  font-size: 18px;
  font-weight: 700;
  color: #667eea;
}

.payment-methods {
  margin-bottom: 32px;
}

.payment-methods h3 {
  color: #333;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
}

.payment-options {
  display: grid;
  gap: 12px;
}

.payment-option {
  display: block;
  padding: 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: white;
}

.payment-option:hover {
  border-color: #667eea;
  background-color: #f8f9ff;
}

.payment-option.selected {
  border-color: #667eea;
  background-color: #f8f9ff;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.payment-option input[type="radio"] {
  display: none;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.option-icon {
  font-size: 24px;
}

.option-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.payment-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 32px;
}

.btn-primary, .btn-secondary {
  padding: 16px 32px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #545b62;
}

.payment-loading, .payment-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.payment-loading p {
  margin-top: 16px;
  color: #666;
  font-size: 16px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .payment-page {
    padding: 10px;
  }

  .payment-container {
    margin: 0;
  }

  .kcp-payment-container {
    padding: 20px;
  }

  .payment-actions {
    flex-direction: column;
  }

  .btn-primary, .btn-secondary {
    width: 100%;
  }
}
```

### **Complete Redirection Flow**

```typescript
// 7. Complete Flow Management
class PaymentFlowManager {

  // Step 1: Initiate checkout from cart
  static async initiateCheckout(cartData) {
    try {
      const response = await this.callCheckoutAPI(cartData);

      if (response.success && response.data.paymentUrl) {
        // Store order info and redirect to payment
        this.storeOrderInfo(response.data);
        this.redirectToPayment(response.data.paymentUrl);
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      this.handleCheckoutError(error);
    }
  }

  // Step 2: Store order information
  static storeOrderInfo(orderData) {
    sessionStorage.setItem('pendingOrder', JSON.stringify({
      orderId: orderData.orderId,
      transactionId: orderData.paymentTransactionId,
      amount: orderData.totalAmount,
      timestamp: Date.now()
    }));
  }

  // Step 3: Redirect to KCP payment page
  static redirectToPayment(paymentUrl) {
    console.log('Redirecting to payment:', paymentUrl);
    window.location.href = paymentUrl;
  }

  // Step 4: Handle payment completion (called by KCP)
  static handlePaymentCompletion(kcpResponse) {
    if (kcpResponse.res_cd === '0000') {
      // Success: Form will submit to backend
      console.log('Payment successful, submitting to backend...');
    } else {
      // Failure: Redirect to cancel page
      const errorMsg = encodeURIComponent(kcpResponse.res_msg);
      window.location.href = `/payment/cancel?error=${errorMsg}`;
    }
  }

  // Step 5: Handle backend redirect (after processing)
  static handleBackendRedirect() {
    // Backend will redirect to:
    // Success: /payment/success?orderId=ORDER-123&transactionId=TXN-456
    // Failure: /payment/cancel?error=Processing+failed

    const currentPath = window.location.pathname;

    if (currentPath === '/payment/success') {
      this.handleSuccessPage();
    } else if (currentPath === '/payment/cancel') {
      this.handleCancelPage();
    }
  }

  // Success page handling
  static handleSuccessPage() {
    // Clear any pending order data
    sessionStorage.removeItem('pendingOrder');

    // Optional: Send analytics event
    this.trackPaymentSuccess();

    // Optional: Show success notification
    this.showSuccessNotification();
  }

  // Cancel page handling
  static handleCancelPage() {
    // Keep pending order for retry
    const pendingOrder = sessionStorage.getItem('pendingOrder');

    if (pendingOrder) {
      // Optional: Offer retry option
      this.showRetryOption();
    }
  }
}
```

### **Environment-Specific URL Configuration**

```typescript
// 8. Environment Configuration
const getEnvironmentConfig = () => {
  const hostname = window.location.hostname;

  if (hostname === 'localhost') {
    return {
      apiUrl: 'http://localhost:3012',
      frontendUrl: 'http://localhost:3010',
      returnUrl: 'http://localhost:3010/payment/success',
      cancelUrl: 'http://localhost:3010/payment/cancel'
    };
  } else if (hostname === '**************') {
    return {
      apiUrl: 'http://**************:3012',
      frontendUrl: 'http://**************:3010',
      returnUrl: 'http://**************:3010/payment/success',
      cancelUrl: 'http://**************:3010/payment/cancel'
    };
  } else {
    // Production
    return {
      apiUrl: 'https://api.your-domain.com',
      frontendUrl: 'https://your-domain.com',
      returnUrl: 'https://your-domain.com/payment/success',
      cancelUrl: 'https://your-domain.com/payment/cancel'
    };
  }
};

// Use in checkout request
const config = getEnvironmentConfig();
const checkoutRequest = {
  paymentMethod: 'kcp_card',
  useRewardPoints: false,
  returnUrl: config.returnUrl,
  cancelUrl: config.cancelUrl
};
```

### **Error Handling and Recovery**

```typescript
// 9. Comprehensive Error Handling
class PaymentErrorHandler {

  static handleCheckoutError(error) {
    console.error('Checkout error:', error);

    // Show user-friendly error message
    this.showErrorMessage('Failed to initiate payment. Please try again.');

    // Optional: Send error to analytics
    this.trackError('checkout_failed', error.message);
  }

  static handlePaymentError(error) {
    console.error('Payment error:', error);

    // Redirect to cancel page with error
    const errorMsg = encodeURIComponent(error.message);
    window.location.href = `/payment/cancel?error=${errorMsg}`;
  }

  static handleKcpSdkError() {
    console.error('KCP SDK failed to load');

    this.showErrorMessage('Payment system unavailable. Please try again later.');

    // Optional: Fallback to alternative payment method
    this.showAlternativePaymentOptions();
  }

  static showRetryOption() {
    const pendingOrder = JSON.parse(sessionStorage.getItem('pendingOrder') || '{}');

    if (pendingOrder.orderId) {
      // Show retry button
      const retryButton = document.createElement('button');
      retryButton.textContent = 'Retry Payment';
      retryButton.onclick = () => {
        // Restart payment flow
        window.location.href = '/cart';
      };

      document.querySelector('.actions').appendChild(retryButton);
    }
  }
}
```

## 5. Routing Setup

### 5.1 React Router Configuration

```jsx
// App.jsx or routes configuration
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { KcpPaymentPage } from './components/payment/KcpPaymentPage';
import { PaymentSuccessPage } from './components/payment/PaymentSuccessPage';
import { PaymentCancelPage } from './components/payment/PaymentCancelPage';
import { CartPage } from './components/shop/CartPage';

function App() {
  return (
    <Router>
      <Routes>
        {/* Existing routes */}
        <Route path="/" element={<HomePage />} />
        <Route path="/shop" element={<ShopPage />} />
        <Route path="/cart" element={<CartPage />} />
        <Route path="/orders" element={<OrdersPage />} />

        {/* KCP Payment routes - REQUIRED FOR PAYMENT FLOW */}
        <Route path="/payment/kcp" element={<KcpPaymentPage />} />
        <Route path="/payment/success" element={<PaymentSuccessPage />} />
        <Route path="/payment/cancel" element={<PaymentCancelPage />} />

        {/* Fallback route */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </Router>
  );
}
```

### 5.2 Success Page Component

```jsx
// components/payment/PaymentSuccessPage.jsx
import React, { useEffect, useState } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { paymentService } from '../../services/paymentService';

export const PaymentSuccessPage = () => {
  const [searchParams] = useSearchParams();
  const [paymentDetails, setPaymentDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  const transactionId = searchParams.get('transaction');
  const orderId = searchParams.get('order');

  useEffect(() => {
    if (transactionId) {
      fetchPaymentDetails();
    }
  }, [transactionId]);

  const fetchPaymentDetails = async () => {
    try {
      const response = await paymentService.getPaymentStatus(transactionId);
      setPaymentDetails(response.data);
    } catch (error) {
      console.error('Failed to fetch payment details:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="payment-result-page success">
      <div className="result-container">
        <div className="result-icon">✅</div>
        <h1>Payment Successful!</h1>
        <p>Your payment has been processed successfully.</p>

        {!isLoading && paymentDetails && (
          <div className="payment-details">
            <h3>Payment Details</h3>
            <div className="detail-item">
              <span>Transaction ID:</span>
              <span>{transactionId}</span>
            </div>
            <div className="detail-item">
              <span>Order ID:</span>
              <span>{orderId}</span>
            </div>
            <div className="detail-item">
              <span>Amount:</span>
              <span>{paymentDetails.amount.toLocaleString()} {paymentDetails.currency}</span>
            </div>
            <div className="detail-item">
              <span>Payment Method:</span>
              <span>{paymentDetails.paymentMethod}</span>
            </div>
          </div>
        )}

        <div className="result-actions">
          <Link to="/dashboard" className="btn-primary">Go to Dashboard</Link>
          <Link to="/shop" className="btn-secondary">Continue Shopping</Link>
        </div>
      </div>
    </div>
  );
};
```

### 5.3 Failure Page Component

```jsx
// components/payment/PaymentFailurePage.jsx
import React from 'react';
import { useSearchParams, Link } from 'react-router-dom';

export const PaymentFailurePage = () => {
  const [searchParams] = useSearchParams();

  const transactionId = searchParams.get('transaction');
  const orderId = searchParams.get('order');
  const error = searchParams.get('error');

  return (
    <div className="payment-result-page failure">
      <div className="result-container">
        <div className="result-icon">❌</div>
        <h1>Payment Failed</h1>
        <p>Unfortunately, your payment could not be processed.</p>

        {error && (
          <div className="error-details">
            <h3>Error Details</h3>
            <p className="error-message">{decodeURIComponent(error)}</p>
          </div>
        )}

        {transactionId && (
          <div className="payment-details">
            <div className="detail-item">
              <span>Transaction ID:</span>
              <span>{transactionId}</span>
            </div>
            {orderId && (
              <div className="detail-item">
                <span>Order ID:</span>
                <span>{orderId}</span>
              </div>
            )}
          </div>
        )}

        <div className="result-actions">
          <Link to="/cart" className="btn-primary">Return to Cart</Link>
          <Link to="/support" className="btn-secondary">Contact Support</Link>
        </div>
      </div>
    </div>
  );
};
```

## 6. Error Handling & Validation

### 6.1 Error Boundary Component

```jsx
// components/common/PaymentErrorBoundary.jsx
import React from 'react';

export class PaymentErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Payment Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="payment-error-boundary">
          <h2>Something went wrong with the payment system</h2>
          <p>Please try again or contact support if the problem persists.</p>
          <button onClick={() => window.location.reload()}>
            Reload Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### 6.2 Form Validation Hook

```javascript
// hooks/usePaymentValidation.js
import { useState, useCallback } from 'react';

export const usePaymentValidation = () => {
  const [errors, setErrors] = useState({});

  const validatePaymentData = useCallback((formData) => {
    const newErrors = {};

    // Required field validation
    const requiredFields = {
      site_cd: 'Site code is required',
      tno: 'Transaction number is required',
      ordr_idxx: 'Order ID is required',
      good_name: 'Product name is required',
      good_mny: 'Amount is required',
      buyr_name: 'Buyer name is required',
      buyr_mail: 'Email is required'
    };

    Object.entries(requiredFields).forEach(([field, message]) => {
      if (!formData[field] || formData[field].trim() === '') {
        newErrors[field] = message;
      }
    });

    // Amount validation
    if (formData.good_mny) {
      const amount = Number(formData.good_mny);
      if (isNaN(amount) || amount <= 0) {
        newErrors.good_mny = 'Amount must be a positive number';
      } else if (amount < 100) {
        newErrors.good_mny = 'Minimum amount is 100 KRW';
      }
    }

    // Email validation
    if (formData.buyr_mail) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.buyr_mail)) {
        newErrors.buyr_mail = 'Invalid email format';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, []);

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  return {
    errors,
    validatePaymentData,
    clearErrors,
    hasErrors: Object.keys(errors).length > 0
  };
};
```

## 7. Testing Guide

### 7.1 Unit Tests

```javascript
// __tests__/KcpPaymentForm.test.js
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { KcpPaymentForm } from '../components/payment/KcpPaymentForm';

// Mock KCP SDK
global.KCP_Pay_Execute_Web = jest.fn();
global.GetField = jest.fn();

const mockFormData = {
  site_cd: 'T0000',
  tno: 'TXN-123',
  ordr_idxx: 'ORDER-123',
  good_name: 'Test Product',
  good_mny: '1000',
  buyr_name: 'John Doe',
  buyr_tel2: '010-1234-5678',
  buyr_mail: '<EMAIL>',
  pay_method: '************'
};

describe('KcpPaymentForm', () => {
  test('renders payment form with correct data', () => {
    render(<KcpPaymentForm formData={mockFormData} onCancel={() => {}} />);

    expect(screen.getByText('Test Product')).toBeInTheDocument();
    expect(screen.getByText('1,000 KRW')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  test('calls KCP payment function when pay button is clicked', async () => {
    render(<KcpPaymentForm formData={mockFormData} onCancel={() => {}} />);

    const payButton = screen.getByText(/Pay/);
    fireEvent.click(payButton);

    await waitFor(() => {
      expect(global.KCP_Pay_Execute_Web).toHaveBeenCalled();
    });
  });

  test('handles payment method selection', () => {
    render(<KcpPaymentForm formData={mockFormData} onCancel={() => {}} />);

    const bankTransferOption = screen.getByLabelText(/Bank Transfer/);
    fireEvent.click(bankTransferOption);

    expect(bankTransferOption).toBeChecked();
  });
});
```

### 7.2 Integration Tests

```javascript
// __tests__/PaymentFlow.integration.test.js
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { KcpPaymentPage } from '../components/payment/KcpPaymentPage';
import { paymentService } from '../services/paymentService';

// Mock payment service
jest.mock('../services/paymentService');

const mockSearchParams = new URLSearchParams({
  site_cd: 'T0000',
  tno: 'TXN-123',
  ordr_idxx: 'ORDER-123',
  good_name: 'Test Product',
  good_mny: '1000',
  buyr_name: 'John Doe',
  buyr_tel2: '010-1234-5678',
  buyr_mail: '<EMAIL>'
});

// Mock useSearchParams
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useSearchParams: () => [mockSearchParams]
}));

describe('Payment Flow Integration', () => {
  test('completes successful payment flow', async () => {
    // Mock successful verification
    paymentService.verifyPayment.mockResolvedValue({
      success: true,
      data: { status: 'completed' }
    });

    render(
      <BrowserRouter>
        <KcpPaymentPage />
      </BrowserRouter>
    );

    await waitFor(() => {
      expect(screen.getByText('Complete Your Payment')).toBeInTheDocument();
    });

    // Simulate successful KCP callback
    const mockForm = {
      tno: { value: 'TXN-123' },
      ordr_idxx: { value: 'ORDER-123' },
      res_cd: { value: '0000' },
      res_msg: { value: 'SUCCESS' },
      enc_info: { value: '' },
      enc_data: { value: '' },
      tran_cd: { value: '' }
    };

    // Trigger the callback
    window.m_Completepayment(mockForm, () => {});

    await waitFor(() => {
      expect(paymentService.verifyPayment).toHaveBeenCalledWith({
        tno: 'TXN-123',
        ordr_idxx: 'ORDER-123',
        res_cd: '0000',
        res_msg: 'SUCCESS',
        enc_info: '',
        enc_data: '',
        tran_cd: ''
      });
    });
  });
});
```

## 8. Implementation Checklist

### 8.1 Backend Requirements ✅
- [x] KCP trade registration with proper authentication
- [x] Payment processing endpoint (`POST /payment/kcp/process`)
- [x] Payment return handling (`POST /payment/kcp/return`)
- [x] Complete checkout completion after payment verification
- [x] Multi-layer payment verification (KCP response + API verification)
- [x] Database transaction management with rollback capability
- [x] Form data generation with action_url for frontend
- [x] Comprehensive error handling and validation
- [x] Amount validation (integer format for KRW)
- [x] Transaction status tracking and updates

### 8.2 Frontend Implementation Checklist

#### Core Setup
- [ ] Install required dependencies (React Router, Axios)
- [ ] Configure environment variables
- [ ] Set up KCP configuration file
- [ ] Create payment service class

#### Components
- [ ] Implement KcpPaymentPage component
- [ ] Implement KcpPaymentForm component
- [ ] Create PaymentSuccessPage component
- [ ] Create PaymentFailurePage component
- [ ] Add LoadingSpinner and ErrorMessage components

#### Utilities
- [ ] Implement KcpSdkLoader utility
- [ ] Add GetField function for KCP integration
- [ ] Create payment validation hooks
- [ ] Add error boundary component

#### Routing
- [ ] Configure React Router routes
- [ ] Set up payment page route (`/payment/kcp`)
- [ ] Set up success page route (`/payment/success`)
- [ ] Set up failure page route (`/payment/failure`)

#### Styling
- [ ] Implement responsive CSS styles
- [ ] Add mobile-friendly design
- [ ] Create loading and error state styles
- [ ] Add payment method selection styling

#### Testing
- [ ] Write unit tests for components
- [ ] Create integration tests for payment flow
- [ ] Test KCP SDK loading and integration
- [ ] Test error handling scenarios

#### Security & Validation
- [ ] Implement form validation
- [ ] Add input sanitization
- [ ] Handle authentication tokens
- [ ] Implement error boundaries

### 8.3 Testing Scenarios

#### Happy Path
- [ ] User completes payment successfully with credit card
- [ ] User completes payment successfully with bank transfer
- [ ] Payment verification works correctly
- [ ] Success page displays correct information

#### Error Scenarios
- [ ] KCP SDK fails to load
- [ ] Payment is cancelled by user
- [ ] Payment fails due to insufficient funds
- [ ] Network error during verification
- [ ] Invalid payment parameters

#### Edge Cases
- [ ] User navigates away during payment
- [ ] Multiple payment attempts
- [ ] Expired payment session
- [ ] Browser compatibility issues

## 9. Deployment Notes

### 9.1 Environment Configuration
- Ensure correct KCP SDK URLs for staging/production
- Configure proper API endpoints
- Set up error monitoring and logging
- Test payment flow in staging environment

### 9.2 Security Considerations
- Use HTTPS for all payment-related pages
- Implement proper CORS configuration
- Validate all payment data on backend
- Log payment events for audit purposes

### 9.3 Performance Optimization
- Lazy load KCP SDK only when needed
- Implement proper loading states
- Optimize bundle size for payment components
- Cache static payment configuration

## 10. Implementation Summary

### ✅ **Backend Status: COMPLETE**
The backend implementation is fully complete with:
- **Multi-layer payment verification** (KCP response + API verification + amount validation)
- **Complete checkout process** (purchase creation, reward points, owned items)
- **Robust error handling** with database transaction rollback
- **Proper KCP integration** following official patterns

### 🔧 **Frontend Status: NEEDS IMPLEMENTATION**
The frontend team needs to implement:
1. **KCP Payment Page** (`/payment/kcp`) - Displays KCP payment form
2. **Form Submission Logic** - Submits to `/payment/kcp/process` after payment
3. **Success/Failure Pages** - Handles redirects from backend
4. **KCP SDK Integration** - Loads and uses KCP JavaScript SDK

### 🎯 **Key Implementation Points**

#### **Critical Success Factors:**
1. **Form Action**: Payment form MUST submit to `POST /payment/kcp/process`
2. **No Manual API Calls**: Let the form submission handle everything
3. **Include All Hidden Fields**: KCP requires all form fields to be present
4. **Integer Amounts**: Use integers for KRW amounts (no decimals)

#### **Payment Flow:**
```
Checkout → Payment URL → KCP Form → Payment Auth → Form Submit → Backend Process → Success/Failure
```

#### **Backend Endpoints:**
- `POST /shop/cart/checkout` - Initiates payment
- `POST /payment/kcp/process` - Processes payment completion (handles all KCP scenarios)

### 🚀 **Ready for Frontend Implementation**
The backend is production-ready. Frontend team can now implement the payment pages using this guide. All KCP integration complexities are handled by the backend.

## 📋 **Complete Implementation Checklist**

### **✅ Required Frontend Pages**

1. **Shopping Cart Page** (`/cart`)
   - Checkout button that calls `POST /shop/cart/checkout`
   - Handles checkout response and redirects to payment

2. **KCP Payment Page** (`/payment/kcp`)
   - Parses URL parameters from checkout response
   - Loads KCP SDK and displays payment form
   - Handles `m_Completepayment` callback
   - Submits form to backend on success

3. **Success Page** (`/payment/success`)
   - Shows order confirmation
   - Displays order details from URL parameters
   - Provides navigation to orders/shop

4. **Cancel Page** (`/payment/cancel`)
   - Shows payment failure/cancellation message
   - Provides retry and navigation options
   - Handles error messages from URL parameters

### **✅ Required API Integration**

```typescript
// 1. Checkout API Call
const checkoutResponse = await fetch('/shop/cart/checkout', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    paymentMethod: 'kcp_card',
    returnUrl: 'http://**************:3010/payment/success',
    cancelUrl: 'http://**************:3010/payment/cancel'
  })
});

// 2. Handle Response and Redirect
if (checkoutResponse.data.paymentUrl) {
  window.location.href = checkoutResponse.data.paymentUrl;
}
```

### **✅ Required KCP Integration**

```html
<!-- 1. Load KCP SDK -->
<script src="https://testspay.kcp.co.kr/plugin/kcp_spay_hub.js"></script>

<!-- 2. Implement m_Completepayment callback -->
<script>
function m_Completepayment(FormOrJson, closeEvent) {
  const form = document.order_info;

  if (form.res_cd.value === '0000') {
    form.action = '/payment/kcp/process';
    form.submit();
  } else {
    alert('Payment failed: ' + form.res_msg.value);
    closeEvent();
  }
}
</script>

<!-- 3. Payment form with all required fields -->
<form name="order_info" method="post">
  <!-- All hidden fields from URL parameters -->
  <!-- Payment method selection -->
  <!-- Payment button that calls KCP_Pay_Execute_Web() -->
</form>
```

### **✅ Complete Flow Verification**

1. **Cart → Checkout**
   ```
   User clicks "Pay Now" → POST /shop/cart/checkout → Returns paymentUrl
   ```

2. **Checkout → Payment**
   ```
   Redirect to paymentUrl → Load KCP payment page → User selects payment method
   ```

3. **Payment → Processing**
   ```
   User clicks pay → KCP authentication → m_Completepayment callback → Form submit to backend
   ```

4. **Processing → Completion**
   ```
   Backend verifies payment → Completes checkout → Redirects to success/cancel page
   ```

### **🎯 Environment URLs**

**Development:**
```
Frontend: http://localhost:3010
Backend: http://localhost:3012
Return URL: http://localhost:3010/payment/success
Cancel URL: http://localhost:3010/payment/cancel
```

**Your Current Setup:**
```
Frontend: http://**************:3010
Backend: http://**************:3012
Return URL: http://**************:3010/payment/success
Cancel URL: http://**************:3010/payment/cancel
```

**Production:**
```
Frontend: https://your-domain.com
Backend: https://api.your-domain.com
Return URL: https://your-domain.com/payment/success
Cancel URL: https://your-domain.com/payment/cancel
```

### **🚨 Critical Implementation Notes**

1. **URL Parameters**: KCP payment page must parse ALL URL parameters from checkout response
2. **Form Submission**: Payment form MUST submit to backend `/payment/kcp/process` endpoint
3. **Global Callback**: `m_Completepayment` MUST be a global function accessible to KCP SDK
4. **Hidden Fields**: ALL hidden form fields are required for KCP to work properly
5. **Error Handling**: Implement proper error handling for SDK loading and payment failures

### **📱 Testing Checklist**

- [ ] Cart checkout button calls API correctly
- [ ] Checkout response contains valid paymentUrl
- [ ] Payment page loads with correct form data
- [ ] KCP SDK loads without errors
- [ ] Payment form submits to backend on success
- [ ] Success page shows correct order information
- [ ] Cancel page handles errors appropriately
- [ ] All URLs work in your environment

This comprehensive guide provides everything needed to implement KCP payment gateway integration in your frontend application. The implementation is now **complete and production-ready** with detailed frontend integration guidance! 🚀