import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString } from 'class-validator';

export class NovelEntryFilterDto {
  @ApiProperty({
    description: 'Filter entries by date (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsDateString()
  date?: string;

  @ApiProperty({
    description: 'Filter entries by topic sequence',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  sequence?: string;

  @ApiProperty({
    description: 'Filter entries by title',
    required: false,
    type: String,
  })
  @IsOptional()
  @IsString()
  title?: string;
}
