import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCategoryEntityAndUpdateDiaryMission1752910000000 implements MigrationInterface {
  name = 'CreateCategoryEntityAndUpdateDiaryMission1752910000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create category table
    await queryRunner.query(`
      CREATE TABLE "category" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "name" character varying(255) NOT NULL,
        "description" text,
        "color" character varying(7),
        "is_active" boolean NOT NULL DEFAULT true,
        "sort_order" integer NOT NULL DEFAULT 0,
        CONSTRAINT "UQ_category_name" UNIQUE ("name"),
        CONSTRAINT "PK_category" PRIMARY KEY ("id")
      )
    `);

    // Insert default categories
    await queryRunner.query(`
      INSERT INTO "category" ("name", "description", "color", "sort_order") VALUES
      ('General', 'General writing missions', '#6B7280', 1),
      ('Creative Writing', 'Creative and imaginative writing tasks', '#F59E0B', 2),
      ('Academic Writing', 'Academic and formal writing exercises', '#3B82F6', 3),
      ('Personal Reflection', 'Personal thoughts and reflection writing', '#10B981', 4),
      ('Storytelling', 'Narrative and story-based writing', '#8B5CF6', 5)
    `);

    // Drop the old category column if it exists
    await queryRunner.query(`
      ALTER TABLE "diary_mission" 
      DROP COLUMN IF EXISTS "category"
    `);

    // Add category_id column to diary_mission table
    await queryRunner.query(`
      ALTER TABLE "diary_mission" 
      ADD COLUMN "category_id" uuid
    `);

    // Set default category for existing missions (General category)
    await queryRunner.query(`
      UPDATE "diary_mission" 
      SET "category_id" = (SELECT "id" FROM "category" WHERE "name" = 'General' LIMIT 1)
      WHERE "category_id" IS NULL
    `);

    // Make category_id NOT NULL after setting defaults
    await queryRunner.query(`
      ALTER TABLE "diary_mission" 
      ALTER COLUMN "category_id" SET NOT NULL
    `);

    // Add foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "diary_mission" 
      ADD CONSTRAINT "FK_diary_mission_category" 
      FOREIGN KEY ("category_id") REFERENCES "category"("id") ON DELETE RESTRICT ON UPDATE CASCADE
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "diary_mission" 
      DROP CONSTRAINT IF EXISTS "FK_diary_mission_category"
    `);

    // Remove category_id column from diary_mission table
    await queryRunner.query(`
      ALTER TABLE "diary_mission" 
      DROP COLUMN IF EXISTS "category_id"
    `);

    // Drop category table
    await queryRunner.query(`DROP TABLE IF EXISTS "category"`);

    // Add back the old category column as string (if needed for rollback)
    await queryRunner.query(`
      ALTER TABLE "diary_mission" 
      ADD COLUMN "category" varchar(255) DEFAULT 'General'
    `);
  }
}
