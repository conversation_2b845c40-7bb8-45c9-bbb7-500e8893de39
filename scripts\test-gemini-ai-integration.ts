import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { GeminiAiService } from '../src/common/services/gemini-ai.service';
import { StoryMakerScoringService } from '../src/modules/play/story-maker/story-maker-scoring.service';
import { Logger } from '@nestjs/common';

const logger = new Logger('GeminiAITest');

async function testGeminiAIIntegration() {
  logger.log('🚀 Starting Gemini AI Integration Test...');

  try {
    // Bootstrap the NestJS application
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get the services
    const geminiService = app.get(GeminiAiService);
    const scoringService = app.get(StoryMakerScoringService);

    logger.log('✅ Services initialized successfully');

    // Test 1: Connection Test
    logger.log('\n📡 Test 1: Testing Gemini AI Connection...');
    const connectionTest = await geminiService.testConnection();
    logger.log(`Connection test result: ${connectionTest ? '✅ SUCCESS' : '❌ FAILED'}`);

    if (!connectionTest) {
      logger.error('❌ Gemini AI connection failed. Please check your API key.');
      await app.close();
      return;
    }

    // Test 2: Sentence Counting
    logger.log('\n📝 Test 2: Testing Sentence Counting...');
    const testTexts = [
      'Hello world.',
      'This is a test. It has two sentences.',
      'One! Two? Three.',
      'Complex sentence with, commas and; semicolons. Another sentence!',
      ''
    ];

    for (const text of testTexts) {
      const result = geminiService.countSentences(text);
      logger.log(`Text: "${text}" → ${result.sentenceCount} sentences`);
      logger.log(`Sentences: [${result.sentences.join(', ')}]`);
    }

    // Test 3: AI Story Evaluation
    logger.log('\n🤖 Test 3: Testing AI Story Evaluation...');
    const testStories = [
      {
        title: 'Simple Story',
        content: 'Once upon a time, there was a cat. The cat was very happy. It played all day.'
      },
      {
        title: 'Creative Story',
        content: 'In the mystical realm of Eldoria, where dragons soared through crystalline skies and magic flowed like rivers of starlight, a young apprentice named Luna discovered an ancient tome that would change her destiny forever. The book whispered secrets of forgotten spells, each page turning itself as if guided by an invisible hand. As she read the incantations aloud, the very air around her shimmered with ethereal energy, and she realized that she was not just reading magic—she was becoming it.'
      },
      {
        title: 'Story with Errors',
        content: 'Their was a dog who like to run. He run very fast and he dont stop. The dog are happy when he running.'
      }
    ];

    for (const story of testStories) {
      logger.log(`\n📖 Evaluating: ${story.title}`);
      logger.log(`Content: ${story.content.substring(0, 100)}...`);
      
      try {
        const evaluation = await geminiService.evaluateStory(story.content);
        
        logger.log('🎯 Evaluation Results:');
        logger.log(`  Creativity: ${evaluation.creativityScore}/5 - ${evaluation.creativityExplanation}`);
        logger.log(`  Sentence Power: ${evaluation.sentencePowerScore}/3 - ${evaluation.sentencePowerExplanation}`);
        logger.log(`  Grammar Errors: ${evaluation.grammarErrorCount}`);
        logger.log(`  Accuracy Score: ${evaluation.accuracyScore}/3`);
        logger.log(`  Processing Time: ${evaluation.processingTime}ms`);
        logger.log(`  AI Feedback: ${evaluation.overallFeedback}`);
        
        if (evaluation.grammarErrors.length > 0) {
          logger.log(`  Grammar Issues: [${evaluation.grammarErrors.join(', ')}]`);
        }
        
      } catch (error) {
        logger.error(`❌ Evaluation failed for ${story.title}: ${error.message}`);
      }
    }

    // Test 4: Complete Scoring System
    logger.log('\n🏆 Test 4: Testing Complete Scoring System...');
    
    // Create a mock submission for testing
    const mockSubmission = {
      id: 'test-submission-id',
      content: 'In a magical forest filled with talking animals and glowing flowers, a brave little rabbit named Whiskers embarked on an incredible adventure to find the legendary Crystal of Harmony. Along the way, he met a wise old owl who taught him that true courage comes from helping others, not from being fearless. Together, they solved riddles, crossed dangerous bridges, and finally discovered that the real treasure was the friendship they had built during their journey.',
      participationId: 'test-participation-id',
      submittedAt: new Date(),
      isEvaluated: false,
      status: 'SUBMITTED' as const,
      lastAutoSavedAt: null,
      autoSaveCount: 0,
      wordCountDraft: 0,
      characterCountDraft: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'test-user',
      updatedBy: 'test-user',
      participation: null,
      evaluations: []
    };

    try {
      logger.log('📊 Running comprehensive scoring...');
      const comprehensiveScore = await scoringService.scoreStorySubmission(mockSubmission);
      
      logger.log('\n🎯 Complete Scoring Results:');
      logger.log(`📝 Sentence Count: ${comprehensiveScore.sentenceCount}`);
      logger.log(`📝 Sentence Score: ${comprehensiveScore.sentenceScore} points`);
      logger.log(`🎨 Creativity: ${comprehensiveScore.creativityScore}/5 points`);
      logger.log(`✍️  Writing Quality: ${comprehensiveScore.sentencePowerScore}/3 points`);
      logger.log(`📊 Participation: ${comprehensiveScore.participationScore}/5 points (${comprehensiveScore.wordCount} words)`);
      logger.log(`✅ Accuracy: ${comprehensiveScore.accuracyScore}/3 points (${comprehensiveScore.grammarErrorCount} errors)`);
      logger.log(`❤️  Popularity: ${comprehensiveScore.popularityScore}/5 points`);
      logger.log(`🏆 TOTAL SCORE: ${comprehensiveScore.totalScore}/${comprehensiveScore.maxPossibleScore} points`);
      logger.log(`⏱️  Processing Time: ${comprehensiveScore.processingTime}ms`);
      logger.log(`💬 AI Feedback: ${comprehensiveScore.aiFeedback}`);
      
    } catch (error) {
      logger.error(`❌ Comprehensive scoring failed: ${error.message}`);
      logger.error(error.stack);
    }

    // Test 5: Edge Cases
    logger.log('\n🔍 Test 5: Testing Edge Cases...');
    
    const edgeCases = [
      { name: 'Empty Content', content: '' },
      { name: 'Single Word', content: 'Hello' },
      { name: 'No Punctuation', content: 'This is a story without any punctuation marks' },
      { name: 'Only Punctuation', content: '!!! ??? ...' },
      { name: 'Very Long Story', content: 'This is a very long story. '.repeat(100) }
    ];

    for (const testCase of edgeCases) {
      logger.log(`\n🧪 Testing: ${testCase.name}`);
      try {
        const sentenceResult = geminiService.countSentences(testCase.content);
        logger.log(`  Sentences: ${sentenceResult.sentenceCount}`);
        
        if (testCase.content.length > 0) {
          const evaluation = await geminiService.evaluateStory(testCase.content);
          logger.log(`  Creativity: ${evaluation.creativityScore}/5`);
          logger.log(`  Processing: ${evaluation.processingTime}ms`);
        }
      } catch (error) {
        logger.error(`  ❌ Failed: ${error.message}`);
      }
    }

    logger.log('\n🎉 All tests completed!');
    logger.log('\n📋 Test Summary:');
    logger.log('✅ Gemini AI Service initialized');
    logger.log('✅ Connection test passed');
    logger.log('✅ Sentence counting working');
    logger.log('✅ AI evaluation working');
    logger.log('✅ Comprehensive scoring working');
    logger.log('✅ Edge cases handled');

    await app.close();

  } catch (error) {
    logger.error('❌ Test failed with error:', error.message);
    logger.error(error.stack);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testGeminiAIIntegration()
    .then(() => {
      logger.log('🏁 Test script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Test script failed:', error);
      process.exit(1);
    });
}

export { testGeminiAIIntegration };
