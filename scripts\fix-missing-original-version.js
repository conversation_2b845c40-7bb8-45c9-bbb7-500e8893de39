const { DataSource } = require('typeorm');
const { config } = require('dotenv');

// Load environment variables
config();

// Database configuration
const dataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'hec_db',
  synchronize: false,
  logging: false,
});

async function fixMissingOriginalVersion() {
  try {
    await dataSource.initialize();
    console.log('Connected to database');

    const entryId = 'e222d818-93fb-4721-8c6b-8042fd74d8b9';
    
    // Get the diary entry
    const entry = await dataSource.query(`
      SELECT de.*, dc.created_at as correction_created_at
      FROM diary_entry de
      LEFT JOIN diary_correction dc ON de.id = dc.diary_entry_id
      WHERE de.id = $1
    `, [entryId]);

    if (entry.length === 0) {
      console.log('Entry not found');
      return;
    }

    const entryData = entry[0];
    console.log('Found entry:', entryData.title);
    console.log('Has correction:', !!entryData.correction_created_at);
    console.log('Original reviewed version ID:', entryData.original_reviewed_version_id);

    if (entryData.original_reviewed_version_id) {
      console.log('Entry already has original reviewed version');
      return;
    }

    if (!entryData.correction_created_at) {
      console.log('Entry has no correction, skipping');
      return;
    }

    // Calculate word count
    const wordCount = entryData.content ? entryData.content.trim().split(/\s+/).length : 0;

    // Create original reviewed version
    const historyResult = await dataSource.query(`
      INSERT INTO diary_entry_history (
        diary_entry_id,
        title,
        content,
        version_number,
        is_latest,
        word_count,
        created_at,
        created_by,
        meta_data
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING id
    `, [
      entryId,
      entryData.title,
      entryData.content,
      1, // version number
      false, // not latest since this is the original
      wordCount,
      entryData.correction_created_at, // Use correction date as the "original" date
      entryData.created_by,
      JSON.stringify({
        updateTrigger: 'backfill',
        significantChange: true,
        isOriginalReviewedVersion: true
      })
    ]);

    const historyId = historyResult[0].id;
    console.log('Created history entry:', historyId);

    // Update the diary entry to reference this as the original reviewed version
    await dataSource.query(`
      UPDATE diary_entry 
      SET original_reviewed_version_id = $1,
          total_versions = COALESCE(total_versions, 0) + 1
      WHERE id = $2
    `, [historyId, entryId]);

    console.log('Updated diary entry with original reviewed version ID');
    console.log('✅ Successfully fixed missing original version for entry:', entryId);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await dataSource.destroy();
  }
}

fixMissingOriginalVersion();
