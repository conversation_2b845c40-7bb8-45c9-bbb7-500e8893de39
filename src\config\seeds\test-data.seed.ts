import { DataSource } from 'typeorm';
import { User, UserType } from '../../database/entities/user.entity';

/**
 * Test Data Seeder
 *
 * Creates test students with different performance profiles for award testing.
 * These students can be used to test the award calculation logic and Hall of Fame functionality.
 */
export class TestDataSeeder {
  constructor(private dataSource: DataSource) {}

  async run(): Promise<void> {
    console.log('🌱 Starting test data seeding...');

    // Create test students with different performance profiles
    const students = await this.createTestStudents();
    console.log(`✅ Created ${students.length} test students`);

    console.log('🎉 Test data seeding completed!');
    console.log('📊 Data includes:');
    console.log('   - Students with different performance profiles for award testing');
    console.log('   - Performance profiles stored in bio field for reference');
    console.log('   - Ready for award scheduler and Hall of Fame testing');
    console.log('');
    console.log('💡 Next steps:');
    console.log('   1. Run award calculations manually or via scheduler');
    console.log('   2. Test Hall of Fame API endpoints');
    console.log('   3. Verify reward balance updates');
  }

  private async createTestStudents(): Promise<User[]> {
    const userRepository = this.dataSource.getRepository(User);
    const students: User[] = [];

    const studentProfiles = [
      // Top performers - should win awards
      {
        email: '<EMAIL>',
        userId: 'alice_writer',
        name: 'Alice Writer',
        profile: 'excellent_writer', // High scores, consistent
      },
      {
        email: '<EMAIL>',
        userId: 'bob_perfect',
        name: 'Bob Perfect',
        profile: 'perfectionist', // High accuracy, perfect attendance
      },
      {
        email: '<EMAIL>',
        userId: 'carol_designer',
        name: 'Carol Designer',
        profile: 'creative_designer', // Great decorations, many likes
      },
      {
        email: '<EMAIL>',
        userId: 'david_performer',
        name: 'David Performer',
        profile: 'top_performer', // Balanced high performance
      },

      // Medium performers
      {
        email: '<EMAIL>',
        userId: 'emma_consistent',
        name: 'Emma Consistent',
        profile: 'consistent_medium', // Consistent but medium scores
      },
      {
        email: '<EMAIL>',
        userId: 'frank_improving',
        name: 'Frank Improving',
        profile: 'improving', // Shows improvement over time
      },

      // Lower performers - should not win awards
      {
        email: '<EMAIL>',
        userId: 'grace_irregular',
        name: 'Grace Irregular',
        profile: 'irregular', // Inconsistent submissions
      },
      {
        email: '<EMAIL>',
        userId: 'henry_beginner',
        name: 'Henry Beginner',
        profile: 'beginner', // Lower scores, learning
      },
    ];

    for (const profile of studentProfiles) {
      // Check if student already exists
      const existingStudent = await userRepository.findOne({
        where: { email: profile.email },
      });

      if (!existingStudent) {
        const student = userRepository.create({
          email: profile.email,
          userId: profile.userId,
          name: profile.name,
          password: 'password123', // Default password for test users
          type: UserType.STUDENT,
          isActive: true,
          phoneNumber: '1234567890', // Required field
          gender: 'other', // Required field
          profilePicture: `https://api.dicebear.com/7.x/avataaars/svg?seed=${profile.userId}`,
          bio: `Performance Profile: ${profile.profile}`, // Store profile for reference
        });

        const savedStudent = await userRepository.save(student);
        students.push(savedStudent);
        console.log(`   ✅ Created student: ${profile.name} (${profile.profile})`);
      } else {
        students.push(existingStudent);
        console.log(`   ⚠️  Student already exists: ${profile.name}`);
      }
    }

    return students;
  }

  // Helper method to extract performance profile from bio
  getPerformanceProfile(student: User): string {
    if (student.bio && student.bio.includes('Performance Profile:')) {
      return student.bio.split('Performance Profile: ')[1] || 'medium';
    }
    return 'medium';
  }
}
