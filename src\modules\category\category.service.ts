import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Category } from '../../database/entities/category.entity';
import { CreateCategoryDto, UpdateCategoryDto, CategoryResponseDto } from '../../database/models/category.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';

@Injectable()
export class CategoryService {
  private readonly logger = new Logger(CategoryService.name);

  constructor(
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
  ) {}

  /**
   * Create a new category
   */
  async createCategory(createDto: CreateCategoryDto): Promise<CategoryResponseDto> {
    try {
      // Check if category with same name already exists
      const existingCategory = await this.categoryRepository.findOne({
        where: { name: createDto.name },
      });

      if (existingCategory) {
        throw new ConflictException(`Category with name '${createDto.name}' already exists`);
      }

      const category = this.categoryRepository.create({
        name: createDto.name,
        description: createDto.description,
        color: createDto.color,
        sortOrder: createDto.sortOrder || 0,
        isActive: true,
      });

      const savedCategory = await this.categoryRepository.save(category);
      this.logger.log(`Created category: ${savedCategory.name} (${savedCategory.id})`);

      return this.mapToResponseDto(savedCategory);
    } catch (error) {
      this.logger.error(`Error creating category: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all categories with pagination
   */
  async getCategories(paginationDto: PaginationDto = { page: 1, limit: 10 }): Promise<PagedListDto<CategoryResponseDto>> {
    try {
      const { page = 1, limit = 10 } = paginationDto;
      const skip = (page - 1) * limit;

      const [categories, total] = await this.categoryRepository.findAndCount({
        where: { isActive: true },
        order: { sortOrder: 'ASC', name: 'ASC' },
        skip,
        take: limit,
      });

      const items = categories.map(category => this.mapToResponseDto(category));

      return new PagedListDto(items, total, page, limit);
    } catch (error) {
      this.logger.error(`Error getting categories: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all active categories (without pagination)
   */
  async getAllActiveCategories(): Promise<CategoryResponseDto[]> {
    try {
      const categories = await this.categoryRepository.find({
        where: { isActive: true },
        order: { sortOrder: 'ASC', name: 'ASC' },
      });

      return categories.map(category => this.mapToResponseDto(category));
    } catch (error) {
      this.logger.error(`Error getting all active categories: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a specific category by ID
   */
  async getCategoryById(id: string): Promise<CategoryResponseDto> {
    try {
      const category = await this.categoryRepository.findOne({
        where: { id },
      });

      if (!category) {
        throw new NotFoundException(`Category with ID ${id} not found`);
      }

      return this.mapToResponseDto(category);
    } catch (error) {
      this.logger.error(`Error getting category ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update a category
   */
  async updateCategory(id: string, updateDto: UpdateCategoryDto): Promise<CategoryResponseDto> {
    try {
      const category = await this.categoryRepository.findOne({
        where: { id },
      });

      if (!category) {
        throw new NotFoundException(`Category with ID ${id} not found`);
      }

      // Check for name conflicts if name is being updated
      if (updateDto.name && updateDto.name !== category.name) {
        const existingCategory = await this.categoryRepository.findOne({
          where: { name: updateDto.name },
        });

        if (existingCategory) {
          throw new ConflictException(`Category with name '${updateDto.name}' already exists`);
        }
      }

      // Update fields
      if (updateDto.name !== undefined) category.name = updateDto.name;
      if (updateDto.description !== undefined) category.description = updateDto.description;
      if (updateDto.color !== undefined) category.color = updateDto.color;
      if (updateDto.isActive !== undefined) category.isActive = updateDto.isActive;
      if (updateDto.sortOrder !== undefined) category.sortOrder = updateDto.sortOrder;

      const updatedCategory = await this.categoryRepository.save(category);
      this.logger.log(`Updated category: ${updatedCategory.name} (${updatedCategory.id})`);

      return this.mapToResponseDto(updatedCategory);
    } catch (error) {
      this.logger.error(`Error updating category ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete a category (soft delete by setting isActive to false)
   */
  async deleteCategory(id: string): Promise<void> {
    try {
      const category = await this.categoryRepository.findOne({
        where: { id },
      });

      if (!category) {
        throw new NotFoundException(`Category with ID ${id} not found`);
      }

      // Soft delete by setting isActive to false
      category.isActive = false;
      await this.categoryRepository.save(category);

      this.logger.log(`Deleted category: ${category.name} (${category.id})`);
    } catch (error) {
      this.logger.error(`Error deleting category ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Map Category entity to response DTO
   */
  private mapToResponseDto(category: Category): CategoryResponseDto {
    return {
      id: category.id,
      name: category.name,
      description: category.description,
      color: category.color,
      isActive: category.isActive,
      sortOrder: category.sortOrder,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    };
  }
}
