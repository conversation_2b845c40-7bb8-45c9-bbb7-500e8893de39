import { Injectable, NotFoundException, ForbiddenException, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { DiaryEntryHistory } from '../../database/entities/diary-entry-history.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { DiaryEntryHistoryResponseDto, DiaryEntryVersionDto, DiaryEntryVersionMetaDataDto } from '../../database/models/diary.dto';
import LoggerService from '../../common/services/logger.service';

@Injectable()
export class DiaryEntryHistoryService {
  constructor(
    @InjectRepository(DiaryEntryHistory)
    private diaryEntryHistoryRepository: Repository<DiaryEntryHistory>,
    @InjectRepository(DiaryEntry)
    private diaryEntryRepository: Repository<DiaryEntry>,
    private dataSource: DataSource,
    @Inject(LoggerService) private logger: LoggerService,
  ) {}

  /**
   * Create a new version during diary entry update
   */
  async createVersionFromUpdate(
    diaryEntryId: string,
    oldData: { title: string; content: string },
    newData: { title?: string; content?: string },
    userId: string,
    request?: any,
  ): Promise<DiaryEntryHistory> {
    try {
      // Get current entry to determine next version number
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: diaryEntryId },
        relations: ['diary'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${diaryEntryId} not found`);
      }

      // Check ownership
      if (entry.diary.userId !== userId) {
        throw new ForbiddenException('You do not have permission to create versions for this diary entry');
      }

      // Mark previous version as not latest if exists
      if (entry.currentVersionId) {
        await this.diaryEntryHistoryRepository.update(entry.currentVersionId, { isLatest: false });
      }

      // Prepare new version data
      const newTitle = newData.title !== undefined ? newData.title : oldData.title;
      const newContent = newData.content !== undefined ? newData.content : oldData.content;

      // Calculate next version number based on existing history records
      const existingVersionsCount = await this.diaryEntryHistoryRepository.count({
        where: { diaryEntryId: diaryEntryId },
      });
      const nextVersionNumber = existingVersionsCount + 1;

      // Create new version
      const newVersion = this.diaryEntryHistoryRepository.create({
        diaryEntryId: diaryEntryId,
        title: newTitle,
        content: newContent,
        versionNumber: nextVersionNumber,
        isLatest: true,
        wordCount: this.calculateWordCount(newContent),
        metaData: this.generateImplicitMetadata(request, oldData, { title: newTitle, content: newContent }),
        createdBy: userId,
        updatedBy: userId,
      });

      const savedVersion = await this.diaryEntryHistoryRepository.save(newVersion);

      this.logger.log(`Created version ${nextVersionNumber} for diary entry ${diaryEntryId}`);
      return savedVersion;
    } catch (error) {
      this.logger.error(`Error creating version for diary entry ${diaryEntryId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get version history for a diary entry
   */
  async getVersionHistory(diaryEntryId: string, userId: string): Promise<DiaryEntryHistoryResponseDto> {
    try {
      // Verify ownership
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: diaryEntryId },
        relations: ['diary'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${diaryEntryId} not found`);
      }

      if (entry.diary.userId !== userId) {
        throw new ForbiddenException('You do not have permission to view history for this diary entry');
      }

      // Get all versions
      const versions = await this.diaryEntryHistoryRepository.find({
        where: { diaryEntryId },
        order: { versionNumber: 'DESC' },
      });

      return {
        diaryEntryId,
        totalEditHistory: versions.length,
        currentVersionId: entry.currentVersionId,
        versions: versions.map((version) => this.mapVersionToDto(version)),
      };
    } catch (error) {
      this.logger.error(`Error getting version history for diary entry ${diaryEntryId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a specific version
   */
  async getVersion(versionId: string, userId: string): Promise<DiaryEntryVersionDto> {
    try {
      const version = await this.diaryEntryHistoryRepository.findOne({
        where: { id: versionId },
        relations: ['diaryEntry', 'diaryEntry.diary'],
      });

      if (!version) {
        throw new NotFoundException(`Version with ID ${versionId} not found`);
      }

      if (version.diaryEntry.diary.userId !== userId) {
        throw new ForbiddenException('You do not have permission to view this version');
      }

      return this.mapVersionToDto(version);
    } catch (error) {
      this.logger.error(`Error getting version ${versionId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Set a version as the latest (restore functionality)
   */
  async setLatestVersion(diaryEntryId: string, versionId: string, userId: string): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Verify ownership
      const entry = await this.diaryEntryRepository.findOne({
        where: { id: diaryEntryId },
        relations: ['diary'],
      });

      if (!entry) {
        throw new NotFoundException(`Diary entry with ID ${diaryEntryId} not found`);
      }

      if (entry.diary.userId !== userId) {
        throw new ForbiddenException('You do not have permission to modify this diary entry');
      }

      // Get the version to restore
      const versionToRestore = await this.diaryEntryHistoryRepository.findOne({
        where: { id: versionId, diaryEntryId },
      });

      if (!versionToRestore) {
        throw new NotFoundException(`Version with ID ${versionId} not found for this diary entry`);
      }

      // Mark all versions as not latest
      await queryRunner.manager.update(DiaryEntryHistory, { diaryEntryId }, { isLatest: false });

      // Mark the selected version as latest
      await queryRunner.manager.update(DiaryEntryHistory, { id: versionId }, { isLatest: true });

      // Update the main entry with the restored content
      await queryRunner.manager.update(
        DiaryEntry,
        { id: diaryEntryId },
        {
          title: versionToRestore.title,
          content: versionToRestore.content,
          currentVersionId: versionId,
          updatedBy: userId,
        },
      );

      await queryRunner.commitTransaction();
      this.logger.log(`Restored version ${versionToRestore.versionNumber} for diary entry ${diaryEntryId}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error restoring version ${versionId} for diary entry ${diaryEntryId}: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Calculate word count
   */
  private calculateWordCount(content: string): number {
    if (!content || content.trim().length === 0) {
      return 0;
    }
    return content.trim().split(/\s+/).length;
  }

  /**
   * Generate implicit metadata
   */
  private generateImplicitMetadata(request: any, oldData: any, newData: any): any {
    const oldContent = oldData.content || '';
    const newContent = newData.content || '';

    return {
      ipAddress: request?.ip || request?.connection?.remoteAddress,
      userAgent: request?.headers?.['user-agent'],
      contentLength: newContent.length,
      contentLengthDiff: newContent.length - oldContent.length,
      significantChange: this.detectSignificantChange(oldContent, newContent),
      timeFromLastUpdate: this.calculateTimeSinceLastUpdate(oldData.updatedAt),
      updateTrigger: this.detectUpdateTrigger(request),
      wordCountChange: this.calculateWordCount(newContent) - this.calculateWordCount(oldContent),
      hasNewParagraphs: this.detectNewParagraphs(oldContent, newContent),
      editDistance: this.calculateEditDistance(oldContent, newContent),
    };
  }

  /**
   * Detect significant change
   */
  private detectSignificantChange(oldContent: string, newContent: string): boolean {
    const editDistance = this.calculateEditDistance(oldContent, newContent);
    const maxLength = Math.max(oldContent.length, newContent.length);
    const changePercentage = maxLength > 0 ? editDistance / maxLength : 0;

    return changePercentage > 0.1 || Math.abs(oldContent.length - newContent.length) > 50;
  }

  /**
   * Calculate time since last update
   */
  private calculateTimeSinceLastUpdate(lastUpdated?: Date): number {
    if (!lastUpdated) return 0;
    return Math.floor((Date.now() - new Date(lastUpdated).getTime()) / (1000 * 60)); // minutes
  }

  /**
   * Detect update trigger
   */
  private detectUpdateTrigger(request?: any): string {
    // This could be enhanced based on endpoint or headers
    return 'update';
  }

  /**
   * Detect new paragraphs
   */
  private detectNewParagraphs(oldContent: string, newContent: string): boolean {
    const oldParagraphs = oldContent.split('\n').filter((p) => p.trim().length > 0);
    const newParagraphs = newContent.split('\n').filter((p) => p.trim().length > 0);
    return newParagraphs.length > oldParagraphs.length;
  }

  /**
   * Calculate edit distance (Levenshtein distance)
   */
  private calculateEditDistance(str1: string, str2: string): number {
    const matrix = [];
    const len1 = str1.length;
    const len2 = str2.length;

    for (let i = 0; i <= len2; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= len1; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= len2; i++) {
      for (let j = 1; j <= len1; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(matrix[i - 1][j - 1] + 1, matrix[i][j - 1] + 1, matrix[i - 1][j] + 1);
        }
      }
    }

    return matrix[len2][len1];
  }

  /**
   * Map version entity to DTO
   */
  private mapVersionToDto(version: DiaryEntryHistory): DiaryEntryVersionDto {
    return {
      id: version.id,
      diaryEntryId: version.diaryEntryId,
      title: version.title,
      content: version.content,
      versionNumber: version.versionNumber,
      isLatest: version.isLatest,
      wordCount: version.wordCount,
      metaData: version.metaData as DiaryEntryVersionMetaDataDto,
      createdAt: version.createdAt,
      updatedAt: version.updatedAt,
      createdBy: version.createdBy,
      updatedBy: version.updatedBy,
    };
  }
}
