import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO for retrying a pending payment
 */
export class RetryPaymentDto {
  @ApiProperty({
    description: 'Payment method to use for retry',
    enum: ['kcp_card', 'kcp_bank', 'kcp_virtual_account', 'kcp_mobile', 'free'],
    example: 'kcp_card',
  })
  @IsNotEmpty()
  @IsString()
  @IsEnum(['kcp_card', 'kcp_bank', 'kcp_virtual_account', 'kcp_mobile', 'free'], {
    message: 'Payment method must be one of: kcp_card, kcp_bank, kcp_virtual_account, kcp_mobile, free',
  })
  paymentMethod: string;
}

/**
 * Response DTO for retry payment
 */
export class RetryPaymentResponseDto {
  @ApiProperty({
    description: 'Payment URL for KCP payments (only for KCP payment methods)',
    example: 'https://payment.kcp.co.kr/...',
    required: false,
  })
  paymentUrl?: string;

  @ApiProperty({
    description: 'Transaction ID for tracking the payment',
    example: 'TXN123456789',
    required: false,
  })
  transactionId?: string;

  @ApiProperty({
    description: 'User plan information',
    type: 'object',
    additionalProperties: true,
  })
  userPlan: any; // This would be UserPlanResponseDto but avoiding circular imports

  @ApiProperty({
    description: 'Success message',
    example: 'Payment retry initiated successfully. Please complete the payment.',
  })
  message: string;
}
