import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { BadRequestException } from '@nestjs/common';
import { of } from 'rxjs';
import { KcpService } from './kcp.service';
import { KcpConfigService } from './kcp-config.service';
import { PaymentInitiationRequest, KcpTradeRegResponse, KcpPaymentResponse, KcpPaymentMethod, PurchaseType } from '../interfaces/kcp.interface';
import { mockKcpResponses } from '../../../../test/utils/test-helpers';

describe('KcpService', () => {
  let service: KcpService;
  let kcpConfigService: KcpConfigService;
  let httpService: HttpService;

  const mockKcpConfigService = {
    getSiteCd: jest.fn(() => 'TEST_SITE_CD'),
    getKcpCertInfo: jest.fn(() => 'TEST_CERT_INFO'),
    getPaymentUrl: jest.fn(() => 'https://stg-spl.kcp.co.kr/gw/enc/v1/payment'),
    getTradeRegUrl: jest.fn(() => 'https://stg-spl.kcp.co.kr/std/tradeReg/register'),
    getApiUrl: jest.fn(() => 'https://stg-spl.kcp.co.kr'),
    getTimeout: jest.fn(() => 30000),
    getRetryAttempts: jest.fn(() => 3),
    generateOrderCheck: jest.fn(() => 'test-order-check'),
    validateWebhookSignature: jest.fn(() => true),
  };

  const mockConfigService = {
    get: jest.fn((key: string, defaultValue?: any) => {
      const config = {
        API_URL: 'http://localhost:3012',
        NODE_ENV: 'test',
      };
      return config[key] || defaultValue;
    }),
  };

  const mockHttpService = {
    post: jest.fn(() =>
      of({
        status: 200,
        data: {
          res_cd: '0000',
          res_msg: 'SUCCESS',
          tno: 'TXN-123',
          amount: '10000',
          pnt_issue: '0',
          trace_no: 'TRACE-123',
          PayUrl: 'https://stg-spl.kcp.co.kr/gw/enc/v1/payment',
          // Don't include ordr_chk so generateOrderCheck gets called
          kcp_sign_data: 'test-signature-data',
          app_time: '20250622122600',
          app_no: 'APP-123456',
        },
      }),
    ),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KcpService,
        {
          provide: KcpConfigService,
          useValue: mockKcpConfigService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
      ],
    }).compile();

    service = module.get<KcpService>(KcpService);
    kcpConfigService = module.get<KcpConfigService>(KcpConfigService);
    httpService = module.get<HttpService>(HttpService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('registerTrade', () => {
    const mockRequest: PaymentInitiationRequest = {
      orderId: 'TEST-ORDER-123',
      amount: 10000,
      currency: 'KRW',
      productName: 'Test Product',
      buyerName: 'Test User',
      buyerEmail: '<EMAIL>',
      buyerPhone: '010-1234-5678',
      paymentMethod: KcpPaymentMethod.CARD,
      returnUrl: 'http://localhost:3011/payment/success',
      cancelUrl: 'http://localhost:3011/payment/cancel',
      userId: 'test-user-id',
      purchaseType: PurchaseType.SHOP_ITEM,
      referenceId: 'test-reference',
    };

    it('should register trade successfully', async () => {
      const result = await service.registerTrade(mockRequest);

      expect(result).toBeDefined();
      expect(result.res_cd).toBe('0000');
      expect(result.res_msg).toBe('SUCCESS');
      expect(result.tno).toBeDefined();
      expect(result.amount).toBe(mockRequest.amount.toString());
      expect(result.PayUrl).toBeDefined();
      expect(result.ordr_chk).toBeDefined();
      expect(result.kcp_sign_data).toBeDefined();
    });

    it('should call KCP config service methods', async () => {
      await service.registerTrade(mockRequest);

      expect(kcpConfigService.getSiteCd).toHaveBeenCalled();
      expect(kcpConfigService.getKcpCertInfo).toHaveBeenCalled();
      expect(kcpConfigService.getTradeRegUrl).toHaveBeenCalled();
      expect(kcpConfigService.getTimeout).toHaveBeenCalled();
      expect(kcpConfigService.generateOrderCheck).toHaveBeenCalledWith(mockRequest.orderId, mockRequest.amount);
    });

    it('should make HTTP request to KCP trade registration API', async () => {
      await service.registerTrade(mockRequest);

      expect(httpService.post).toHaveBeenCalledWith(
        'https://stg-spl.kcp.co.kr/std/tradeReg/register',
        expect.any(Object), // JSON data
        expect.objectContaining({
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'HEC-Backend/1.0',
          },
          timeout: 30000,
        }),
      );
    });

    it('should handle different payment methods', async () => {
      const cardRequest = { ...mockRequest, paymentMethod: KcpPaymentMethod.CARD };
      const bankRequest = { ...mockRequest, paymentMethod: KcpPaymentMethod.BANK };
      const mobileRequest = { ...mockRequest, paymentMethod: KcpPaymentMethod.MOBILE };

      const cardResult = await service.registerTrade(cardRequest);
      const bankResult = await service.registerTrade(bankRequest);
      const mobileResult = await service.registerTrade(mobileRequest);

      expect(cardResult.res_cd).toBe('0000');
      expect(bankResult.res_cd).toBe('0000');
      expect(mobileResult.res_cd).toBe('0000');
    });

    it('should use default currency when not provided', async () => {
      const requestWithoutCurrency = { ...mockRequest };
      delete requestWithoutCurrency.currency;

      const result = await service.registerTrade(requestWithoutCurrency);
      expect(result.res_cd).toBe('0000');
    });
  });

  describe('processPayment', () => {
    const mockRequest: PaymentInitiationRequest = {
      orderId: 'TEST-ORDER-123',
      amount: 10000,
      currency: 'KRW',
      productName: 'Test Product',
      buyerName: 'Test User',
      buyerEmail: '<EMAIL>',
      buyerPhone: '010-1234-5678',
      paymentMethod: KcpPaymentMethod.CARD,
      returnUrl: 'http://localhost:3011/payment/success',
      cancelUrl: 'http://localhost:3011/payment/cancel',
      userId: 'test-user-id',
      purchaseType: PurchaseType.SHOP_ITEM,
      referenceId: 'test-reference',
    };

    const mockTradeRegResponse: KcpTradeRegResponse = {
      res_cd: '0000',
      res_msg: 'SUCCESS',
      tno: 'TXN-123',
      amount: '10000',
      pnt_issue: '0',
      trace_no: 'TRACE-123',
      PayUrl: 'https://stg-spl.kcp.co.kr/gw/payment.jsp',
      ordr_chk: 'test-order-check',
      kcp_sign_data: 'test-sign-data',
    };

    it('should process payment successfully', async () => {
      const result = await service.processPayment(mockTradeRegResponse, mockRequest);

      expect(result).toBeDefined();
      expect(result.res_cd).toBe('0000');
      expect(result.res_msg).toBe('SUCCESS');
      // The result should use the tno from the HTTP response, not the input
      expect(result.tno).toBe('TXN-123'); // From mockHttpService response
      expect(result.amount).toBe(mockRequest.amount.toString());
      expect(result.app_time).toBeDefined();
      expect(result.app_no).toBeDefined();
    });

    it('should include trade registration data in payment request', async () => {
      const result = await service.processPayment(mockTradeRegResponse, mockRequest);

      // The result should use the tno from the HTTP response
      expect(result.tno).toBe('TXN-123'); // From mockHttpService response
    });
  });

  describe('initiatePayment', () => {
    const mockRequest: PaymentInitiationRequest = {
      orderId: 'TEST-ORDER-123',
      amount: 10000,
      currency: 'KRW',
      productName: 'Test Product',
      buyerName: 'Test User',
      buyerEmail: '<EMAIL>',
      buyerPhone: '010-1234-5678',
      paymentMethod: KcpPaymentMethod.CARD,
      returnUrl: 'http://localhost:3011/payment/success',
      cancelUrl: 'http://localhost:3011/payment/cancel',
      userId: 'test-user-id',
      purchaseType: PurchaseType.SHOP_ITEM,
      referenceId: 'test-reference',
    };

    it('should initiate payment successfully', async () => {
      const result = await service.initiatePayment(mockRequest);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.transactionId).toBeDefined();
      expect(result.paymentUrl).toBeDefined();
      expect(result.redirectUrl).toBeDefined();
      expect(result.message).toBe('Payment initiated successfully');
      expect(result.expiresAt).toBeDefined();
    });

    it('should generate correct payment URL', async () => {
      const result = await service.initiatePayment(mockRequest);

      // New implementation returns frontend payment page URL with form data
      expect(result.paymentUrl).toContain('http://localhost:3011/payment/kcp');
      expect(result.paymentUrl).toContain('site_cd=');
      expect(result.paymentUrl).toContain('tno=');
      expect(result.paymentUrl).toContain('ordr_idxx=');
    });

    it('should set expiration time to 30 minutes from now', async () => {
      const beforeTime = new Date(Date.now() + 29 * 60 * 1000); // 29 minutes
      const afterTime = new Date(Date.now() + 31 * 60 * 1000); // 31 minutes

      const result = await service.initiatePayment(mockRequest);

      expect(result.expiresAt.getTime()).toBeGreaterThan(beforeTime.getTime());
      expect(result.expiresAt.getTime()).toBeLessThan(afterTime.getTime());
    });
  });

  describe('getPayMethodCode', () => {
    it('should return correct payment method codes', () => {
      expect(service['getPayMethodCode'](KcpPaymentMethod.CARD)).toBe('************');
      expect(service['getPayMethodCode'](KcpPaymentMethod.BANK)).toBe('************');
      expect(service['getPayMethodCode'](KcpPaymentMethod.MOBILE)).toBe('************');
      expect(service['getPayMethodCode'](KcpPaymentMethod.VACCT)).toBe('************');
      expect(service['getPayMethodCode']('unknown' as any)).toBe('************'); // default to card
    });
  });

  describe('validateWebhookSignature', () => {
    it('should validate webhook signature', () => {
      const payload = 'test-payload';
      const signature = 'test-signature';

      const result = service.validateWebhookSignature(payload, signature);

      expect(result).toBe(true);
      expect(kcpConfigService.validateWebhookSignature).toHaveBeenCalledWith(payload, signature);
    });
  });

  describe('generatePaymentData', () => {
    it('should generate payment form data with correct parameters', async () => {
      const tradeRegResponse: KcpTradeRegResponse = {
        res_cd: '0000',
        res_msg: 'SUCCESS',
        tno: 'TXN-123',
        amount: '10000',
        pnt_issue: '0',
        trace_no: 'TRACE-123',
        PayUrl: 'https://stg-spl.kcp.co.kr/gw/enc/v1/payment',
        ordr_chk: 'test-order-check',
        kcp_sign_data: 'test-sign-data',
      };

      const request: PaymentInitiationRequest = {
        orderId: 'TEST-ORDER-123',
        amount: 10000,
        currency: 'KRW',
        productName: 'Test Product',
        buyerName: 'Test User',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: KcpPaymentMethod.CARD,
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
        userId: 'test-user-id',
        purchaseType: PurchaseType.SHOP_ITEM,
        referenceId: 'test-reference',
      };

      const result = await service['generatePaymentData'](tradeRegResponse, request);

      // Should return frontend payment URL with form data
      expect(result.paymentUrl).toContain('http://localhost:3011/payment/kcp');
      expect(result.paymentUrl).toContain('site_cd=');
      expect(result.paymentUrl).toContain('tno=TXN-123');
      expect(result.paymentUrl).toContain('ordr_idxx=TEST-ORDER-123');

      // Check form data structure
      expect(result.formData).toBeDefined();
      expect(result.formData.site_cd).toBe('TEST_SITE_CD');
      expect(result.formData.tno).toBe('TXN-123');
      expect(result.formData.ordr_idxx).toBe('TEST-ORDER-123');
      expect(result.formData.good_name).toBe('Test Product');
      expect(result.formData.good_mny).toBe('10000');
    });
  });
});
