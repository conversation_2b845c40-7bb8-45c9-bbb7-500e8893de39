import { MigrationInterface, QueryRunner } from 'typeorm';

export class EssayTitle1747801708410 implements MigrationInterface {
  name = 'EssayTitle1747801708410';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD "title" text`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP COLUMN "title"`);
  }
}
