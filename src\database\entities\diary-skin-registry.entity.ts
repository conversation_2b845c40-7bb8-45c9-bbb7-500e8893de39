import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseFileRegistry } from './base-file-registry.entity';
import { DiarySkin } from './diary-skin.entity';

@Entity()
export class DiarySkinRegistry extends BaseFileRegistry {
  @Column({ name: 'diary_skin_id' })
  diarySkinId: string;

  @ManyToOne(() => DiarySkin, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'diary_skin_id' })
  diarySkin: DiarySkin;

  @Column({ name: 'user_id', nullable: true })
  userId: string;

  /**
   * Convert to DTO for API responses
   */
  toDto(): any {
    return {
      ...this.toSimpleObject(),
      diarySkinId: this.diarySkinId,
      userId: this.userId,
      diarySkin: this.diarySkin
        ? {
            id: this.diarySkin.id,
            name: this.diarySkin.name,
            description: this.diarySkin.description,
            isActive: this.diarySkin.isActive,
            isGlobal: this.diarySkin.isGlobal,
          }
        : null,
    };
  }
}
