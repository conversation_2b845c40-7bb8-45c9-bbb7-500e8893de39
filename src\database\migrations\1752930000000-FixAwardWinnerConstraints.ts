import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixAward<PERSON>innerConstraints1752930000000 implements MigrationInterface {
  name = 'FixAwardWinnerConstraints1752930000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Ensure user_id and award_id columns are not null
    await queryRunner.query(`
      ALTER TABLE "award_winner" 
      ALTER COLUMN "user_id" SET NOT NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "award_winner" 
      ALTER COLUMN "award_id" SET NOT NULL
    `);

    // Add foreign key constraints if they don't exist
    await queryRunner.query(`
      DO $$ 
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints 
          WHERE constraint_name = 'FK_award_winner_user_id' 
          AND table_name = 'award_winner'
        ) THEN
          ALTER TABLE "award_winner" 
          ADD CONSTRAINT "FK_award_winner_user_id" 
          FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE;
        END IF;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ 
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints 
          WHERE constraint_name = 'FK_award_winner_award_id' 
          AND table_name = 'award_winner'
        ) THEN
          ALTER TABLE "award_winner" 
          ADD CONSTRAINT "FK_award_winner_award_id" 
          FOREIGN KEY ("award_id") REFERENCES "award"("id") ON DELETE CASCADE;
        END IF;
      END $$;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "award_winner" 
      DROP CONSTRAINT IF EXISTS "FK_award_winner_user_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "award_winner" 
      DROP CONSTRAINT IF EXISTS "FK_award_winner_award_id"
    `);

    // Allow null values again (reverting the change)
    await queryRunner.query(`
      ALTER TABLE "award_winner" 
      ALTER COLUMN "user_id" DROP NOT NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "award_winner" 
      ALTER COLUMN "award_id" DROP NOT NULL
    `);
  }
}
