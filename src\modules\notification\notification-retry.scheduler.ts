import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { NotificationRetryService } from './notification-retry.service';
import { NotificationService } from './notification.service';

/**
 * Scheduler for retrying failed notifications
 */
@Injectable()
export class NotificationRetryScheduler {
  private readonly logger = new Logger(NotificationRetryScheduler.name);
  private isProcessing = false;

  constructor(
    private readonly notificationRetryService: NotificationRetryService,
    private readonly notificationService: NotificationService,
  ) {}

  /**
   * Retry failed deliveries every hour
   */
  @Cron(CronExpression.EVERY_HOUR)
  async retryFailedDeliveries() {
    if (this.isProcessing) {
      this.logger.log('Already processing failed deliveries, skipping this run');
      return;
    }

    try {
      this.isProcessing = true;
      this.logger.log('Starting scheduled retry of failed deliveries');

      // Find failed deliveries due for retry
      const failedDeliveries = await this.notificationRetryService.findFailedDeliveriesDueForRetry(100);

      if (failedDeliveries.length === 0) {
        this.logger.log('No failed deliveries due for retry');
        return;
      }

      this.logger.log(`Found ${failedDeliveries.length} failed deliveries due for retry`);

      // Process each delivery
      let successCount = 0;
      let failureCount = 0;

      for (const delivery of failedDeliveries) {
        try {
          // Process the delivery with error handling
          const success = await this.notificationService.processDeliveryWithErrorHandling(delivery, true);

          if (success) {
            successCount++;
          } else {
            failureCount++;
          }
        } catch (error) {
          failureCount++;
          this.logger.error(`Unexpected error retrying delivery ${delivery.id}: ${error.message}`, error.stack);
        }
      }

      this.logger.log(`Completed retry of failed deliveries. Success: ${successCount}, Failure: ${failureCount}`);
    } catch (error) {
      this.logger.error(`Error in scheduled retry of failed deliveries: ${error.message}`, error.stack);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Clean up old permanently failed deliveries (older than 30 days)
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async cleanupOldFailedDeliveries() {
    try {
      this.logger.log('Starting cleanup of old permanently failed deliveries');

      // Calculate date 30 days ago
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Delete old permanently failed deliveries
      const result = await this.notificationService.deleteOldFailedDeliveries(thirtyDaysAgo);

      this.logger.log(`Deleted ${result.affected} old permanently failed deliveries`);
    } catch (error) {
      this.logger.error(`Error cleaning up old permanently failed deliveries: ${error.message}`, error.stack);
    }
  }
}
