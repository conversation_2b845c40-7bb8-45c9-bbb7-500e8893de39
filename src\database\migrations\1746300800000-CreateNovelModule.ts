import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateNovelModule1746300800000 implements MigrationInterface {
  name = 'CreateNovelModule1746300800000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create novel_topic table
    await queryRunner.query(`
      CREATE TABLE "novel_topic" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "sequence_title" character varying NOT NULL,
        "category" character varying NOT NULL CHECK ("category" IN ('monthly', 'quarterly')),
        "instruction" text NOT NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        CONSTRAINT "PK_novel_topic" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_novel_topic_sequence_title" UNIQUE ("sequence_title")
      )
    `);

    // Create novel_entry table
    await queryRunner.query(`
      CREATE TABLE "novel_entry" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "topic_id" uuid NOT NULL,
        "student_id" uuid NOT NULL,
        "content" text NOT NULL,
        "word_count" integer NOT NULL DEFAULT 0,
        "status" character varying NOT NULL CHECK ("status" IN ('new', 'submitted', 'updated', 'reviewed', 'confirmed')) DEFAULT 'new',
        "skin_id" uuid,
        "submitted_at" TIMESTAMP,
        "reviewed_at" TIMESTAMP,
        CONSTRAINT "PK_novel_entry" PRIMARY KEY ("id")
      )
    `);

    // Create novel_feedback table
    await queryRunner.query(`
      CREATE TABLE "novel_feedback" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "entry_id" uuid NOT NULL,
        "tutor_id" uuid NOT NULL,
        "feedback" text NOT NULL,
        CONSTRAINT "PK_novel_feedback" PRIMARY KEY ("id")
      )
    `);

    // Create novel_correction table
    await queryRunner.query(`
      CREATE TABLE "novel_correction" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "entry_id" uuid NOT NULL,
        "tutor_id" uuid NOT NULL,
        "correction" text NOT NULL,
        "score" integer NOT NULL,
        CONSTRAINT "PK_novel_correction" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_novel_correction_entry" UNIQUE ("entry_id")
      )
    `);

    // Create novel_suggestion table
    await queryRunner.query(`
      CREATE TABLE "novel_suggestion" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "title" character varying NOT NULL,
        "description" text NOT NULL,
        "student_id" uuid NOT NULL,
        "status" character varying NOT NULL CHECK ("status" IN ('pending', 'approved', 'rejected')) DEFAULT 'pending',
        CONSTRAINT "PK_novel_suggestion" PRIMARY KEY ("id")
      )
    `);

    // Create novel_module_skin_preference table
    await queryRunner.query(`
      CREATE TABLE "novel_module_skin_preference" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "student_id" uuid NOT NULL,
        "default_skin_id" uuid NOT NULL,
        CONSTRAINT "PK_novel_module_skin_preference" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_novel_module_skin_preference_student" UNIQUE ("student_id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "novel_entry" 
      ADD CONSTRAINT "FK_novel_entry_topic" 
      FOREIGN KEY ("topic_id") REFERENCES "novel_topic"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_entry" 
      ADD CONSTRAINT "FK_novel_entry_student" 
      FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_entry" 
      ADD CONSTRAINT "FK_novel_entry_skin" 
      FOREIGN KEY ("skin_id") REFERENCES "diary_skin"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_feedback" 
      ADD CONSTRAINT "FK_novel_feedback_entry" 
      FOREIGN KEY ("entry_id") REFERENCES "novel_entry"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_feedback" 
      ADD CONSTRAINT "FK_novel_feedback_tutor" 
      FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_correction" 
      ADD CONSTRAINT "FK_novel_correction_entry" 
      FOREIGN KEY ("entry_id") REFERENCES "novel_entry"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_correction" 
      ADD CONSTRAINT "FK_novel_correction_tutor" 
      FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_suggestion" 
      ADD CONSTRAINT "FK_novel_suggestion_student" 
      FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_module_skin_preference" 
      ADD CONSTRAINT "FK_novel_module_skin_preference_student" 
      FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_module_skin_preference" 
      ADD CONSTRAINT "FK_novel_module_skin_preference_skin" 
      FOREIGN KEY ("default_skin_id") REFERENCES "diary_skin"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    // Add novel notification types to enum
    await queryRunner.query(`
      ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'novel_submission';
      ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'novel_update';
      ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'novel_review';
      ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'novel_feedback';
    `);

    // Create indexes for better performance
    await queryRunner.query(`CREATE INDEX "IDX_novel_entry_topic_student" ON "novel_entry" ("topic_id", "student_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_novel_entry_status" ON "novel_entry" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_novel_entry_submitted_at" ON "novel_entry" ("submitted_at")`);
    await queryRunner.query(`CREATE INDEX "IDX_novel_feedback_entry" ON "novel_feedback" ("entry_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_novel_suggestion_student" ON "novel_suggestion" ("student_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_novel_topic_category" ON "novel_topic" ("category")`);
    await queryRunner.query(`CREATE INDEX "IDX_novel_topic_active" ON "novel_topic" ("is_active")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_novel_topic_active"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_novel_topic_category"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_novel_suggestion_student"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_novel_feedback_entry"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_novel_entry_submitted_at"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_novel_entry_status"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_novel_entry_topic_student"`);

    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "novel_module_skin_preference" DROP CONSTRAINT "FK_novel_module_skin_preference_skin"`);
    await queryRunner.query(`ALTER TABLE "novel_module_skin_preference" DROP CONSTRAINT "FK_novel_module_skin_preference_student"`);
    await queryRunner.query(`ALTER TABLE "novel_suggestion" DROP CONSTRAINT "FK_novel_suggestion_student"`);
    await queryRunner.query(`ALTER TABLE "novel_correction" DROP CONSTRAINT "FK_novel_correction_tutor"`);
    await queryRunner.query(`ALTER TABLE "novel_correction" DROP CONSTRAINT "FK_novel_correction_entry"`);
    await queryRunner.query(`ALTER TABLE "novel_feedback" DROP CONSTRAINT "FK_novel_feedback_tutor"`);
    await queryRunner.query(`ALTER TABLE "novel_feedback" DROP CONSTRAINT "FK_novel_feedback_entry"`);
    await queryRunner.query(`ALTER TABLE "novel_entry" DROP CONSTRAINT "FK_novel_entry_skin"`);
    await queryRunner.query(`ALTER TABLE "novel_entry" DROP CONSTRAINT "FK_novel_entry_student"`);
    await queryRunner.query(`ALTER TABLE "novel_entry" DROP CONSTRAINT "FK_novel_entry_topic"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "novel_module_skin_preference"`);
    await queryRunner.query(`DROP TABLE "novel_suggestion"`);
    await queryRunner.query(`DROP TABLE "novel_correction"`);
    await queryRunner.query(`DROP TABLE "novel_feedback"`);
    await queryRunner.query(`DROP TABLE "novel_entry"`);
    await queryRunner.query(`DROP TABLE "novel_topic"`);
  }
}
