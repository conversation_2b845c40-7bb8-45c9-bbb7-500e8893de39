import { Controller, Get, Post, Body, Param, UseGuards, Req, Query, BadRequestException } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiTags, ApiQuery } from '@nestjs/swagger';
import { DiaryService } from './diary.service';
import { DiaryReviewService } from './diary-review.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { TutorGuard } from '../../common/guards/tutor.guard';
import {
  DiaryEntryResponseDto,
  TutorDiaryEntryResponseDto,
  DiaryFeedbackResponseDto,
  PendingReviewEntryDto,
  DiaryEntryHistoryResponseDto,
  DiaryEntryVersionDto,
} from '../../database/models/diary.dto';
import { FeedbackOnlyDto } from '../../database/models/feedback-only.dto';
import { CreateDiaryCorrectionDto, DiaryCorrectionResponseDto } from '../../database/models/diary-correction.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiOkResponseWithArrayType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';

@ApiTags('tutor-diary')
@Controller('tutor/diary')
@UseGuards(JwtAuthGuard, TutorGuard)
@ApiBearerAuth('JWT-auth')
export class TutorDiaryController {
  constructor(
    private readonly diaryService: DiaryService,
    private readonly diaryReviewService: DiaryReviewService,
  ) {}

  @Get('pending-reviews')
  @ApiOperation({
    summary: 'Get pending review entries',
    description:
      'Get a list of diary entries that require review by tutors. Only includes entries with SUBMIT status. Entries that are already being reviewed by other tutors are marked as such. Supports filtering by module, student name, and date range.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by (entryDate, submittedAt, studentName, moduleTitle, moduleLevel, status)',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiQuery({
    name: 'moduleTitle',
    required: false,
    type: String,
    description: 'Filter by module title (partial match)',
  })
  @ApiQuery({
    name: 'studentName',
    required: false,
    type: String,
    description: 'Filter by student name (partial match)',
  })
  // Status filter removed - only SUBMIT status entries are included
  @ApiQuery({
    name: 'dateFrom',
    required: false,
    type: String,
    description: 'Filter entries with date on or after this date (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'dateTo',
    required: false,
    type: String,
    description: 'Filter entries with date on or before this date (YYYY-MM-DD)',
  })
  @ApiOkResponseWithPagedListType(PendingReviewEntryDto, 'List of pending review entries')
  @ApiErrorResponse(500, 'Internal server error')
  async getPendingReviewEntries(@Req() req: any, @Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<PendingReviewEntryDto>>> {
    const tutorId = req.user.sub;
    const entries = await this.diaryService.getPendingReviewEntries(tutorId, paginationDto);
    return ApiResponse.success(entries, 'Pending review entries retrieved successfully');
  }

  @Get('entries/:id')
  @ApiOperation({
    summary: 'Get a specific diary entry',
    description: 'Retrieve a specific diary entry by its ID for review.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to retrieve',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(DiaryEntryResponseDto, 'Diary entry retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to view this diary entry')
  @ApiErrorResponse(500, 'Internal server error')
  async getDiaryEntry(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<DiaryEntryResponseDto>> {
    const tutorId = req.user.sub;
    const entry = await this.diaryService.getDiaryEntry(id, tutorId);
    return ApiResponse.success(entry, 'Diary entry retrieved successfully');
  }

  @Post('entries/:id/feedback')
  @ApiOperation({
    summary: 'Add feedback to a diary entry',
    description: 'Add feedback to a specific diary entry. Only accepts feedback text.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to add feedback to',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({
    type: FeedbackOnlyDto,
    description: 'Feedback text only',
  })
  @ApiOkResponseWithType(DiaryFeedbackResponseDto, 'Feedback added successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to add feedback to this diary entry')
  @ApiErrorResponse(409, 'Conflict - This diary entry has already been evaluated')
  @ApiErrorResponse(500, 'Internal server error')
  async addFeedback(@Req() req: any, @Param('id') id: string, @Body() feedbackOnlyDto: FeedbackOnlyDto): Promise<ApiResponse<DiaryFeedbackResponseDto>> {
    if (!id || id === 'undefined' || id.trim() === '') {
      throw new BadRequestException('Entry ID is required and cannot be undefined');
    }
    const tutorId = req.user.sub;
    const feedback = await this.diaryService.submitFeedbackOnly(id, tutorId, feedbackOnlyDto);
    return ApiResponse.success(feedback, 'Feedback added successfully', 201);
  }

  @Get('entries/:id/feedbacks')
  @ApiOperation({
    summary: 'Get all feedbacks for a diary entry',
    description: 'Retrieve all feedback comments for a specific diary entry. Accessible to tutors who have access to the student.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to get feedbacks for',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithArrayType(DiaryFeedbackResponseDto, 'Feedbacks retrieved successfully')
  @ApiErrorResponse(400, 'Invalid entry ID')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to view feedbacks for this diary entry')
  @ApiErrorResponse(500, 'Internal server error')
  async getFeedbacks(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<DiaryFeedbackResponseDto[]>> {
    if (!id || id === 'undefined' || id.trim() === '') {
      throw new BadRequestException('Entry ID is required and cannot be undefined');
    }
    const tutorId = req.user.sub;
    const feedbacks = await this.diaryService.getFeedbacks(id, tutorId);
    return ApiResponse.success(feedbacks, 'Feedbacks retrieved successfully');
  }

  @Get('student/:studentId/entries')
  @ApiOperation({
    summary: 'Get diary entries for a specific student',
    description: 'Retrieve all diary entries for a specific student that are shared with the tutor.',
  })
  @ApiParam({
    name: 'studentId',
    description: 'The ID of the student',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(DiaryEntryResponseDto, 'Student diary entries retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Student not found')
  @ApiErrorResponse(403, "Forbidden - You do not have permission to view this student's diary entries")
  @ApiErrorResponse(500, 'Internal server error')
  async getStudentDiaryEntries(@Param('studentId') studentId: string, @Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<DiaryEntryResponseDto>>> {
    // const tutorId = req.user.sub; // Not needed for this method
    const entries = await this.diaryService.getStudentDiaryEntries(studentId, paginationDto);
    return ApiResponse.success(entries, 'Student diary entries retrieved successfully');
  }

  @Post('entries/:id/start-review')
  @ApiOperation({
    summary: 'Start review of a diary entry',
    description:
      'Start the review process for a diary entry. This locks the entry for 2 hours to prevent other tutors from reviewing it simultaneously. Response includes the name of the user who submitted the entry.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to start reviewing',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(TutorDiaryEntryResponseDto, 'Review started successfully')
  @ApiErrorResponse(400, 'Invalid input or entry not available for review')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - Only tutors can review diary entries')
  @ApiErrorResponse(409, 'Conflict - This entry is already being reviewed by another tutor')
  @ApiErrorResponse(500, 'Internal server error')
  async startReview(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<TutorDiaryEntryResponseDto>> {
    const tutorId = req.user.sub;
    const entry = await this.diaryService.pickEntryForReview(id, tutorId);
    return ApiResponse.success(entry, 'Review started successfully');
  }

  @Post('entries/:id/correction')
  @ApiOperation({
    summary: 'Submit correction for a diary entry',
    description: 'Submit a correction with score for a diary entry. This should be done after starting a review.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry to submit correction for',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiBody({
    type: CreateDiaryCorrectionDto,
    description: 'Correction data including text, score, and optional comments',
  })
  @ApiOkResponseWithType(DiaryCorrectionResponseDto, 'Correction submitted successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - Only tutors can submit corrections')
  @ApiErrorResponse(409, 'Conflict - This entry already has a correction')
  @ApiErrorResponse(500, 'Internal server error')
  async submitCorrection(@Req() req: any, @Param('id') id: string, @Body() createDiaryCorrectionDto: CreateDiaryCorrectionDto): Promise<ApiResponse<DiaryCorrectionResponseDto>> {
    const tutorId = req.user.sub;
    const correction = await this.diaryService.submitCorrection(id, tutorId, createDiaryCorrectionDto);
    return ApiResponse.success(correction, 'Correction submitted successfully', 201);
  }

  // REMOVED: Complete review endpoint - confirm stage removed from lifecycle
  // Review submission is now the final state, no additional completion needed

  @Get('entries/:id/history')
  @ApiOperation({
    summary: 'Get diary entry version history (Tutor)',
    description: "View all previous versions of a student's diary entry. Shows the complete history of changes made to the entry.",
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(DiaryEntryHistoryResponseDto, 'Version history retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary entry not found')
  @ApiErrorResponse(403, "Forbidden - You do not have access to this student's entry")
  @ApiErrorResponse(500, 'Internal server error')
  async getDiaryEntryHistory(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<DiaryEntryHistoryResponseDto>> {
    const tutorId = req.user.sub;

    // Verify tutor has access to this entry (without review status check)
    const entry = await this.diaryReviewService.verifyTutorAccess(id, tutorId);

    // Get the history using the student ID
    const history = await this.diaryService.getDiaryEntryHistory(id, entry.diary.userId);
    return ApiResponse.success(history, 'Version history retrieved successfully');
  }

  @Get('entries/:id/versions/:versionId')
  @ApiOperation({
    summary: 'View a specific version (Tutor)',
    description: "View the content of a specific version of a student's diary entry.",
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the diary entry',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiParam({
    name: 'versionId',
    description: 'The ID of the specific version',
    example: '456e7890-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(DiaryEntryVersionDto, 'Version retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Version not found')
  @ApiErrorResponse(403, "Forbidden - You do not have access to this student's entry")
  @ApiErrorResponse(500, 'Internal server error')
  async getDiaryEntryVersion(@Req() req: any, @Param('id') id: string, @Param('versionId') versionId: string): Promise<ApiResponse<DiaryEntryVersionDto>> {
    const tutorId = req.user.sub;

    // Verify tutor has access to this entry (without review status check)
    const entry = await this.diaryReviewService.verifyTutorAccess(id, tutorId);

    // Get the version using the student ID
    const version = await this.diaryService.getDiaryEntryVersion(versionId, entry.diary.userId);
    return ApiResponse.success(version, 'Version retrieved successfully');
  }
}
