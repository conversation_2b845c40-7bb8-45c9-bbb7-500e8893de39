import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual, In } from 'typeorm';
import { NotificationDelivery, DeliveryStatus } from '../../database/entities/notification-delivery.entity';
import { Notification } from '../../database/entities/notification.entity';
import { getCurrentUTCDate, addMinutesUTC, addHoursUTC } from '../../common/utils/date-utils';

/**
 * Retry strategy types
 */
export enum RetryStrategy {
  /** Fixed interval between retries */
  FIXED = 'fixed',
  /** Exponential backoff between retries */
  EXPONENTIAL = 'exponential',
  /** Linear backoff between retries */
  LINEAR = 'linear',
}

/**
 * Service for handling notification retry logic
 */
@Injectable()
export class NotificationRetryService {
  private readonly logger = new Logger(NotificationRetryService.name);

  constructor(
    @InjectRepository(NotificationDelivery)
    private readonly notificationDeliveryRepository: Repository<NotificationDelivery>,
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
  ) {}

  /**
   * Find all failed deliveries that are due for retry
   * @param limit Maximum number of deliveries to return
   * @returns List of failed deliveries due for retry
   */
  async findFailedDeliveriesDueForRetry(limit: number = 100): Promise<NotificationDelivery[]> {
    const now = getCurrentUTCDate();

    return this.notificationDeliveryRepository.find({
      where: [
        {
          status: DeliveryStatus.FAILED,
          nextRetryAt: LessThanOrEqual(now),
          retryCount: LessThanOrEqual(3), // Max 3 retries by default
        },
        {
          status: DeliveryStatus.RETRY_SCHEDULED,
          nextRetryAt: LessThanOrEqual(now),
        },
      ],
      relations: ['notification'],
      order: {
        priority: 'DESC', // Process high priority deliveries first
        nextRetryAt: 'ASC', // Process oldest retries first
      },
      take: limit,
    });
  }

  /**
   * Find all permanently failed deliveries
   * @param limit Maximum number of deliveries to return
   * @param offset Offset for pagination
   * @returns List of permanently failed deliveries
   */
  async findPermanentlyFailedDeliveries(limit: number = 100, offset: number = 0): Promise<NotificationDelivery[]> {
    return this.notificationDeliveryRepository.find({
      where: {
        status: DeliveryStatus.FAILED_PERMANENT,
      },
      relations: ['notification'],
      order: {
        updatedAt: 'DESC', // Most recently failed first
      },
      take: limit,
      skip: offset,
    });
  }

  /**
   * Calculate next retry time based on retry strategy
   * @param delivery The delivery record
   * @returns Date when the next retry should occur
   */
  calculateNextRetryTime(delivery: NotificationDelivery): Date {
    const now = getCurrentUTCDate();
    const retryCount = delivery.retryCount;
    const strategy = delivery.retryStrategy || RetryStrategy.EXPONENTIAL;

    switch (strategy) {
      case RetryStrategy.FIXED:
        // Fixed interval: 15 minutes
        return addMinutesUTC(now, 15);

      case RetryStrategy.LINEAR:
        // Linear backoff: 5, 10, 15, 20 minutes...
        const linearMinutes = 5 * (retryCount + 1);
        return addMinutesUTC(now, linearMinutes);

      case RetryStrategy.EXPONENTIAL:
      default:
        // Exponential backoff: 5, 25, 125 minutes...
        const exponentialMinutes = Math.pow(5, retryCount + 1);
        // Cap at 24 hours
        const cappedMinutes = Math.min(exponentialMinutes, 24 * 60);
        return addMinutesUTC(now, cappedMinutes);
    }
  }

  /**
   * Mark a delivery for retry
   * @param delivery The delivery to retry
   * @param error The error that occurred
   * @returns Updated delivery record
   */
  async markForRetry(delivery: NotificationDelivery, error: Error | { message: string; code?: string; details?: string }): Promise<NotificationDelivery> {
    try {
      const errorMessage = error.message;
      const errorCode = 'code' in error ? error.code : undefined;
      const errorDetails = 'details' in error ? error.details : undefined;

      // Check if we've reached the maximum retry count
      if (delivery.retryCount >= (delivery.maxRetries || 3)) {
        this.logger.warn(`Delivery ${delivery.id} for notification ${delivery.notificationId} has reached maximum retries and is marked as permanently failed`);

        // Use the entity method to mark as permanently failed
        delivery.markAsPermanentlyFailed(errorMessage, errorCode, errorDetails);
      } else {
        // Calculate next retry time
        const nextRetryAt = this.calculateNextRetryTime(delivery);

        this.logger.log(`Delivery ${delivery.id} for notification ${delivery.notificationId} scheduled for retry at ${nextRetryAt.toISOString()}`);

        // Use the entity method to schedule for retry
        delivery.scheduleForRetry(nextRetryAt, errorMessage, errorCode, errorDetails);
      }

      // Save the updated delivery
      return this.notificationDeliveryRepository.save(delivery);
    } catch (error) {
      this.logger.error(`Error marking delivery ${delivery.id} for retry: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Mark a delivery as successfully retried and delete it
   * @param delivery The delivery that was successfully retried
   */
  async markAsSuccessfulAndDelete(delivery: NotificationDelivery): Promise<void> {
    try {
      this.logger.log(`Delivery ${delivery.id} for notification ${delivery.notificationId} was successfully retried, deleting record`);

      // Delete the delivery record
      await this.notificationDeliveryRepository.delete(delivery.id);
    } catch (error) {
      this.logger.error(`Error deleting successful delivery ${delivery.id}: ${error.message}`, error.stack);

      // If deletion fails, at least mark it as sent
      try {
        delivery.status = DeliveryStatus.SENT;
        delivery.sentAt = getCurrentUTCDate();
        delivery.errorMessage = null;
        delivery.errorCode = null;
        delivery.errorDetails = null;
        delivery.nextRetryAt = null;

        await this.notificationDeliveryRepository.save(delivery);
        this.logger.log(`Marked delivery ${delivery.id} as sent instead of deleting`);
      } catch (saveError) {
        this.logger.error(`Error marking delivery ${delivery.id} as sent: ${saveError.message}`, saveError.stack);
      }
    }
  }

  /**
   * Manually retry a specific delivery
   * @param deliveryId ID of the delivery to retry
   * @returns Updated delivery record
   */
  async manuallyRetryDelivery(deliveryId: string): Promise<NotificationDelivery> {
    try {
      // Find the delivery
      const delivery = await this.notificationDeliveryRepository.findOne({
        where: { id: deliveryId },
        relations: ['notification'],
      });

      if (!delivery) {
        throw new Error(`Delivery with ID ${deliveryId} not found`);
      }

      // Reset retry count and status
      delivery.retryCount = 0;
      delivery.status = DeliveryStatus.PENDING;
      delivery.nextRetryAt = null;
      delivery.lastRetryAt = getCurrentUTCDate();
      delivery.errorMessage = null;
      delivery.errorCode = null;
      delivery.errorDetails = null;

      // Save the updated delivery
      return this.notificationDeliveryRepository.save(delivery);
    } catch (error) {
      this.logger.error(`Error manually retrying delivery ${deliveryId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get statistics about failed deliveries
   * @returns Statistics about failed deliveries
   */
  async getFailedDeliveryStats(): Promise<{
    totalFailed: number;
    totalPermanentlyFailed: number;
    totalScheduledForRetry: number;
    failedByChannel: Record<string, number>;
  }> {
    try {
      // Get total failed
      const totalFailed = await this.notificationDeliveryRepository.count({
        where: { status: DeliveryStatus.FAILED },
      });

      // Get total permanently failed
      const totalPermanentlyFailed = await this.notificationDeliveryRepository.count({
        where: { status: DeliveryStatus.FAILED_PERMANENT },
      });

      // Get total scheduled for retry
      const totalScheduledForRetry = await this.notificationDeliveryRepository.count({
        where: { status: DeliveryStatus.RETRY_SCHEDULED },
      });

      // Get failed by channel
      const failedByChannelResults = await this.notificationDeliveryRepository
        .createQueryBuilder('delivery')
        .select('delivery.channel', 'channel')
        .addSelect('COUNT(*)', 'count')
        .where('delivery.status IN (:...statuses)', {
          statuses: [DeliveryStatus.FAILED, DeliveryStatus.FAILED_PERMANENT, DeliveryStatus.RETRY_SCHEDULED],
        })
        .groupBy('delivery.channel')
        .getRawMany();

      const failedByChannel = failedByChannelResults.reduce((acc, curr) => {
        acc[curr.channel] = parseInt(curr.count, 10);
        return acc;
      }, {});

      return {
        totalFailed,
        totalPermanentlyFailed,
        totalScheduledForRetry,
        failedByChannel,
      };
    } catch (error) {
      this.logger.error(`Error getting failed delivery stats: ${error.message}`, error.stack);
      throw error;
    }
  }
}
