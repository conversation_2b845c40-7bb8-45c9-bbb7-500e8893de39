import { MigrationInterface, QueryRunner } from 'typeorm';

export class QASubmissionPropertyModels1748326724389 implements MigrationInterface {
  name = 'QASubmissionPropertyModels1748326724389';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" DROP CONSTRAINT "FK_5769bb5bb9b640e0d811df4c4f0"`);
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" DROP CONSTRAINT "FK_c642437d858571413d52240de5f"`);
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" DROP COLUMN "submission"`);
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" DROP CONSTRAINT "REL_5769bb5bb9b640e0d811df4c4f"`);
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" DROP COLUMN "submission_mark_id"`);
    await queryRunner.query(
      `ALTER TABLE "qa_task_submission_marking" ADD CONSTRAINT "FK_d301a66c4a2da63b20ca9861bea" FOREIGN KEY ("submission_id") REFERENCES "qa_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "qa_task_submission_marking" ADD CONSTRAINT "FK_54969194ab3ee8aa8a294d879d2" FOREIGN KEY ("submission_history_id") REFERENCES "qa_task_submission_history"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" DROP CONSTRAINT "FK_54969194ab3ee8aa8a294d879d2"`);
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" DROP CONSTRAINT "FK_d301a66c4a2da63b20ca9861bea"`);
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" ADD "submission_mark_id" uuid`);
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" ADD CONSTRAINT "REL_5769bb5bb9b640e0d811df4c4f" UNIQUE ("submission_mark_id")`);
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" ADD "submission" uuid`);
    await queryRunner.query(
      `ALTER TABLE "qa_task_submission_marking" ADD CONSTRAINT "FK_c642437d858571413d52240de5f" FOREIGN KEY ("submission") REFERENCES "qa_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "qa_task_submission_marking" ADD CONSTRAINT "FK_5769bb5bb9b640e0d811df4c4f0" FOREIGN KEY ("submission_mark_id") REFERENCES "qa_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
