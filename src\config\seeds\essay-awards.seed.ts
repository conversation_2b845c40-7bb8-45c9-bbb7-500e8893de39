import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Award, AwardModule, AwardCriteria, AwardFrequency } from '../../database/entities/award.entity';

@Injectable()
export class EssayAwardsSeed {
  private readonly logger = new Logger(EssayAwardsSeed.name);

  constructor(
    @InjectRepository(Award)
    private awardRepository: Repository<Award>,
  ) {}

  async seed(): Promise<void> {
    this.logger.log('Seeding Essay Awards...');

    // Define the essay awards as requested by client
    const essayAwards = [
      // Monthly Awards
      {
        name: 'Best Writer Award',
        description: 'Awarded monthly to the student with exceptional writing quality and consistency in essay submissions',
        module: AwardModule.ESSAY,
        criteria: [AwardCriteria.ESSAY_PERFORMANCE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 150,
        isActive: true,
        criteriaConfig: {
          minScore: 1, // Very low for testing
          minEssays: 1, // Just 1 essay for testing
          maxWinners: 5, // More winners for testing
        },
      },
      {
        name: 'Best Perfect Award',
        description: 'Awarded monthly to the student who excels in all aspects of essay writing - quality, completion, and consistency',
        module: AwardModule.ESSAY,
        criteria: [AwardCriteria.ESSAY_PERFORMANCE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 200,
        isActive: true,
        criteriaConfig: {
          minScore: 7,
          minEssays: 5,
          minCompletionRate: 90,
          maxWinners: 1,
        },
      },

      // Annual Awards
      {
        name: 'Best Writer Award',
        description: 'Awarded annually to the student with exceptional writing quality and consistency throughout the year',
        module: AwardModule.ESSAY,
        criteria: [AwardCriteria.ESSAY_PERFORMANCE],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 500,
        isActive: true,
        criteriaConfig: {
          minScore: 8,
          minEssays: 30,
          maxWinners: 1,
        },
      },
      {
        name: 'Best Perfect Award',
        description: 'Awarded annually to the student who excels in all aspects of essay writing throughout the year',
        module: AwardModule.ESSAY,
        criteria: [AwardCriteria.ESSAY_PERFORMANCE],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 750,
        isActive: true,
        criteriaConfig: {
          minScore: 8,
          minEssays: 40,
          minCompletionRate: 85,
          maxWinners: 1,
        },
      },
    ];

    // Check and create awards
    for (const awardData of essayAwards) {
      try {
        // Check if award already exists (by name, module, and frequency)
        const existingAward = await this.awardRepository.findOne({
          where: {
            name: awardData.name,
            module: awardData.module,
            frequency: awardData.frequency,
          },
        });

        if (existingAward) {
          this.logger.log(`Award "${awardData.name}" (${awardData.frequency}) already exists, skipping...`);
          continue;
        }

        // Create new award
        const award = this.awardRepository.create(awardData);
        await this.awardRepository.save(award);

        this.logger.log(`Created award: "${awardData.name}" (${awardData.frequency})`);
      } catch (error) {
        this.logger.error(`Error creating award "${awardData.name}": ${error.message}`);
      }
    }

    this.logger.log('Essay Awards seeding completed');
  }
}
