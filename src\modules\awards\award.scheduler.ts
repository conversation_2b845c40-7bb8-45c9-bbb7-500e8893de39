import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThanOrEqual } from 'typeorm';
import { DiaryAwardService } from '../diary/diary-award.service';
import { MissionDiaryAwardService } from '../diary/mission-diary-award.service';
import { EssayAwardService } from '../essay/essay-award.service';
import { NovelAwardService } from '../novel/novel-award.service';
import { AwardNotificationService } from './award-notification.service';
import { Award } from '../../database/entities/award.entity';
import { AwardWinner } from '../../database/entities/award-winner.entity';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import { NovelEntry, NovelEntryStatus } from '../../database/entities/novel-entry.entity';
import { getCurrentUTCDate, addDaysUTC, addMonthsUTC, getStartOfMonthUTC, getEndOfMonthUTC } from '../../common/utils/date-utils';

/**
 * Award Scheduler
 *
 * This is the award scheduling system that handles automated award generation.
 *
 * Features:
 * - Weekly awards: Generated every Sunday at 00:30 UTC for the previous week (Diary only)
 * - Monthly awards: Generated on the 1st of each month at 02:00 UTC for the previous month
 * - Quarterly awards: Generated on the 1st of each quarter at 01:00 UTC for the previous quarter
 * - Annual awards: Generated on January 1st at 03:00 UTC for the previous year
 * - Direct cron-based scheduling (no database scheduling overhead)
 * - Comprehensive error handling and processing flags
 * - Manual trigger support for testing and recovery
 * - Covers all modules: Diary, Essay, Novel
 *
 * Schedule:
 * - Weekly: Every Sunday at 00:30 UTC (Diary module only)
 * - Monthly: 1st day of month at 02:00 UTC (All modules)
 * - Quarterly: 1st day of quarter at 01:00 UTC (All modules)
 * - Annual: January 1st at 03:00 UTC (All modules)
 */
@Injectable()
export class AwardScheduler {
  private readonly logger = new Logger(AwardScheduler.name);
  private isProcessingWeekly = false;
  private isProcessingMonthly = false;
  private isProcessingQuarterly = false;
  private isProcessingAnnual = false;

  // Track last run times for status reporting
  private lastWeeklyRun?: Date;
  private lastMonthlyRun?: Date;
  private lastQuarterlyRun?: Date;
  private lastAnnualRun?: Date;

  constructor(
    private readonly diaryAwardService: DiaryAwardService,
    private readonly essayAwardService: EssayAwardService,
    private readonly novelAwardService: NovelAwardService,
    private readonly awardNotificationService: AwardNotificationService,
    @InjectRepository(Award)
    private readonly awardRepository: Repository<Award>,
    @InjectRepository(AwardWinner)
    private readonly awardWinnerRepository: Repository<AwardWinner>,
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(NovelEntry)
    private readonly novelEntryRepository: Repository<NovelEntry>,
  ) {}



  /**
   * Generate weekly awards for diary module every Sunday at 00:30 UTC
   * Processes the previous week's data (Monday to Sunday)
   */
  @Cron('30 0 * * 0') // At 00:30 on Sunday
  async generateWeeklyAwards() {
    if (this.isProcessingWeekly) {
      this.logger.log('Weekly award generation already in progress, skipping');
      return;
    }

    try {
      this.isProcessingWeekly = true;
      this.lastWeeklyRun = getCurrentUTCDate();
      this.logger.log('Starting weekly award generation for diary module');

      const today = getCurrentUTCDate();
      // Calculate previous week (Monday to Sunday)
      const endDate = addDaysUTC(today, -1); // Yesterday (Saturday)
      const startDate = addDaysUTC(endDate, -6); // Monday of previous week

      this.logger.log(`Generating weekly awards for period: ${startDate.toISOString()} to ${endDate.toISOString()}`);

      // Generate awards only for diary module (weekly awards are diary-specific)
      await this.diaryAwardService.generateAwardsForRange(startDate, endDate);

      this.logger.log('Weekly awards generated successfully for diary module');

      // Notify award winners
      await this.awardNotificationService.notifyAwardWinners('WEEKLY', startDate, endDate);
      this.logger.log('Weekly award winner notifications sent successfully');
    } catch (error) {
      this.logger.error(`Error generating weekly awards: ${error.message}`, error.stack);
    } finally {
      this.isProcessingWeekly = false;
    }
  }

  /**
   * Generate monthly awards for all modules on the 1st of each month at 2:00 AM UTC
   * Processes the previous month's data
   */
  @Cron('0 2 1 * *') // At 02:00 on day-of-month 1
  async generateMonthlyAwards() {
    if (this.isProcessingMonthly) {
      this.logger.log('Monthly award generation already in progress, skipping');
      return;
    }

    try {
      this.isProcessingMonthly = true;
      this.lastMonthlyRun = getCurrentUTCDate();
      this.logger.log('Starting monthly award generation for all modules');

      const today = getCurrentUTCDate();
      const lastMonth = addMonthsUTC(today, -1);
      const startDate = getStartOfMonthUTC(lastMonth);
      const endDate = getEndOfMonthUTC(lastMonth);

      this.logger.log(`Generating monthly awards for period: ${startDate.toISOString()} to ${endDate.toISOString()}`);

      // Generate awards for all modules
      await Promise.all([
        this.diaryAwardService.generateAwardsForRange(startDate, endDate),
        this.essayAwardService.generateAwardsForRange(startDate, endDate),
        this.novelAwardService.generateAwardsForRange(startDate, endDate),
      ]);

      this.logger.log('Monthly awards generated successfully for all modules');

      // Notify award winners
      await this.awardNotificationService.notifyAwardWinners('MONTHLY', startDate, endDate);
      this.logger.log('Monthly award winner notifications sent successfully');
    } catch (error) {
      this.logger.error(`Error generating monthly awards: ${error.message}`, error.stack);
    } finally {
      this.isProcessingMonthly = false;
    }
  }

  /**
   * Generate quarterly awards for all modules on the 1st of each quarter at 1:00 AM UTC
   * Processes the previous quarter's data
   */
  @Cron('0 1 1 1,4,7,10 *') // At 01:00 on day-of-month 1 in January, April, July, and October
  async generateQuarterlyAwards() {
    if (this.isProcessingQuarterly) {
      this.logger.log('Quarterly award generation already in progress, skipping');
      return;
    }

    try {
      this.isProcessingQuarterly = true;
      this.lastQuarterlyRun = getCurrentUTCDate();
      this.logger.log('Starting quarterly award generation for all modules');

      const today = getCurrentUTCDate();
      const currentQuarter = Math.floor(today.getUTCMonth() / 3);
      const previousQuarter = currentQuarter === 0 ? 3 : currentQuarter - 1;
      const quarterYear = currentQuarter === 0 ? today.getUTCFullYear() - 1 : today.getUTCFullYear();

      const startDate = new Date(Date.UTC(quarterYear, previousQuarter * 3, 1));
      const endDate = new Date(Date.UTC(quarterYear, previousQuarter * 3 + 3, 0, 23, 59, 59, 999)); // Last day of quarter

      this.logger.log(`Generating quarterly awards for period: ${startDate.toISOString()} to ${endDate.toISOString()}`);

      // Generate awards for all modules
      await Promise.all([
        this.diaryAwardService.generateAwardsForRange(startDate, endDate),
        this.essayAwardService.generateAwardsForRange(startDate, endDate),
        this.novelAwardService.generateAwardsForRange(startDate, endDate),
      ]);

      this.logger.log('Quarterly awards generated successfully for all modules');

      // Notify award winners
      await this.awardNotificationService.notifyAwardWinners('QUARTERLY', startDate, endDate);
      this.logger.log('Quarterly award winner notifications sent successfully');
    } catch (error) {
      this.logger.error(`Error generating quarterly awards: ${error.message}`, error.stack);
    } finally {
      this.isProcessingQuarterly = false;
    }
  }

  /**
   * Generate annual awards for all modules on January 1st at 3:00 AM UTC
   * Processes the previous year's data
   */
  @Cron('0 3 1 1 *') // At 03:00 on January 1st
  async generateAnnualAwards() {
    if (this.isProcessingAnnual) {
      this.logger.log('Annual award generation already in progress, skipping');
      return;
    }

    try {
      this.isProcessingAnnual = true;
      this.lastAnnualRun = getCurrentUTCDate();
      this.logger.log('Starting annual award generation for all modules');

      const today = getCurrentUTCDate();
      const lastYear = today.getUTCFullYear() - 1;
      const startDate = new Date(Date.UTC(lastYear, 0, 1)); // January 1st of last year
      const endDate = new Date(Date.UTC(lastYear, 11, 31, 23, 59, 59, 999)); // December 31st of last year

      this.logger.log(`Generating annual awards for period: ${startDate.toISOString()} to ${endDate.toISOString()}`);

      // Generate awards for all modules
      await Promise.all([
        this.diaryAwardService.generateAwardsForRange(startDate, endDate),
        this.essayAwardService.generateAwardsForRange(startDate, endDate),
        this.novelAwardService.generateAwardsForRange(startDate, endDate),
      ]);

      this.logger.log('Annual awards generated successfully for all modules');

      // Notify award winners
      await this.awardNotificationService.notifyAwardWinners('YEARLY', startDate, endDate);
      this.logger.log('Annual award winner notifications sent successfully');
    } catch (error) {
      this.logger.error(`Error generating annual awards: ${error.message}`, error.stack);
    } finally {
      this.isProcessingAnnual = false;
    }
  }

  /**
   * Manual trigger for weekly awards (for testing or manual execution)
   * @param year Optional year (defaults to current year)
   * @param month Optional month (defaults to current month)
   * @param day Optional day (defaults to current day - should be Sunday for week ending)
   */
  async triggerWeeklyAwards(year?: number, month?: number, day?: number): Promise<void> {
    try {
      const today = getCurrentUTCDate();

      // If no parameters provided, use the previous week (Monday to Sunday)
      if (!year && !month && !day) {
        const endDate = addDaysUTC(today, -1); // Yesterday (assuming today is Monday)
        const startDate = addDaysUTC(endDate, -6); // Monday of previous week

        this.logger.log(`Manually triggering weekly awards for previous week: ${startDate.toISOString().slice(0, 10)} to ${endDate.toISOString().slice(0, 10)}`);

        await this.diaryAwardService.generateAwardsForRange(startDate, endDate);
        await this.awardNotificationService.notifyAwardWinners('WEEKLY', startDate, endDate);

        this.logger.log(`Manual weekly award generation completed for week ending ${endDate.toISOString().slice(0, 10)}`);
        return;
      }

      // If parameters provided, use them to construct the target week ending date
      const targetYear = year || today.getUTCFullYear();
      const targetMonth = month ? month - 1 : today.getUTCMonth(); // Convert to 0-based
      const targetDay = day || today.getUTCDate();

      const endDate = new Date(Date.UTC(targetYear, targetMonth, targetDay));
      const startDate = addDaysUTC(endDate, -6); // Monday of that week

      this.logger.log(`Manually triggering weekly awards for week: ${startDate.toISOString().slice(0, 10)} to ${endDate.toISOString().slice(0, 10)}`);

      // Generate awards only for diary module (weekly awards are diary-specific)
      await this.diaryAwardService.generateAwardsForRange(startDate, endDate);

      // Notify award winners
      await this.awardNotificationService.notifyAwardWinners('WEEKLY', startDate, endDate);
      this.logger.log('Manual weekly award winner notifications sent successfully');
    } catch (error) {
      this.logger.error(`Error in manual weekly award generation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Manual trigger for quarterly awards (for testing or manual execution)
   * @param year Optional year (defaults to current year for current quarter, or previous year for Q4 if current is Q1)
   * @param quarter Optional quarter (1-4, defaults to previous quarter)
   */
  async triggerQuarterlyAwards(year?: number, quarter?: number): Promise<void> {
    try {
      const today = getCurrentUTCDate();
      const currentQuarter = Math.floor(today.getUTCMonth() / 3) + 1;

      // Determine target quarter and year
      let targetQuarter: number;
      let targetYear: number;

      if (quarter && year) {
        // Both provided - use as-is
        targetQuarter = quarter;
        targetYear = year;
      } else if (quarter && !year) {
        // Only quarter provided - use current year unless it's a future quarter
        targetQuarter = quarter;
        targetYear = quarter > currentQuarter ? today.getUTCFullYear() - 1 : today.getUTCFullYear();
      } else if (!quarter && year) {
        // Only year provided - use previous quarter of that year
        targetQuarter = currentQuarter === 1 ? 4 : currentQuarter - 1;
        targetYear = year;
      } else {
        // Neither provided - use previous quarter
        if (currentQuarter === 1) {
          targetQuarter = 4;
          targetYear = today.getUTCFullYear() - 1;
        } else {
          targetQuarter = currentQuarter - 1;
          targetYear = today.getUTCFullYear();
        }
      }

      // Calculate quarter date range
      const startDate = new Date(Date.UTC(targetYear, (targetQuarter - 1) * 3, 1));
      const endDate = new Date(Date.UTC(targetYear, targetQuarter * 3, 0, 23, 59, 59, 999)); // Last day of quarter

      this.logger.log(`Manually triggering quarterly awards for Q${targetQuarter}/${targetYear} (${startDate.toISOString().slice(0, 10)} to ${endDate.toISOString().slice(0, 10)})`);

      await Promise.all([
        this.diaryAwardService.generateAwardsForRange(startDate, endDate),
        this.essayAwardService.generateAwardsForRange(startDate, endDate),
        this.novelAwardService.generateAwardsForRange(startDate, endDate),
      ]);

      this.logger.log(`Manual quarterly award generation completed for Q${targetQuarter}/${targetYear}`);

      // Notify award winners
      await this.awardNotificationService.notifyAwardWinners('QUARTERLY', startDate, endDate);
      this.logger.log('Manual quarterly award winner notifications sent successfully');
    } catch (error) {
      this.logger.error(`Error in manual quarterly award generation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Manual trigger for monthly awards (for testing or manual execution)
   * @param year Optional year (defaults to current year for current month, or previous year for December if current is January)
   * @param month Optional month (1-12, defaults to previous month)
   */
  async triggerMonthlyAwards(year?: number, month?: number): Promise<void> {
    try {
      const today = getCurrentUTCDate();
      const currentMonth = today.getUTCMonth() + 1; // Convert to 1-based

      // Determine target month and year
      let targetMonth: number;
      let targetYear: number;

      if (month && year) {
        // Both provided - use as-is
        targetMonth = month;
        targetYear = year;
      } else if (month && !year) {
        // Only month provided - use current year unless it's a future month
        targetMonth = month;
        targetYear = month > currentMonth ? today.getUTCFullYear() - 1 : today.getUTCFullYear();
      } else if (!month && year) {
        // Only year provided - use previous month of that year
        targetMonth = currentMonth === 1 ? 12 : currentMonth - 1;
        targetYear = year;
      } else {
        // Neither provided - use previous month
        if (currentMonth === 1) {
          targetMonth = 12;
          targetYear = today.getUTCFullYear() - 1;
        } else {
          targetMonth = currentMonth - 1;
          targetYear = today.getUTCFullYear();
        }
      }

      // Calculate month date range
      const startDate = new Date(Date.UTC(targetYear, targetMonth - 1, 1));
      const endDate = getEndOfMonthUTC(startDate);

      this.logger.log(`Manually triggering monthly awards for ${targetMonth}/${targetYear} (${startDate.toISOString().slice(0, 10)} to ${endDate.toISOString().slice(0, 10)})`);

      await Promise.all([
        this.diaryAwardService.generateAwardsForRange(startDate, endDate),
        this.essayAwardService.generateAwardsForRange(startDate, endDate),
        this.novelAwardService.generateAwardsForRange(startDate, endDate),
      ]);

      this.logger.log(`Manual monthly award generation completed for ${targetMonth}/${targetYear}`);

      // Notify award winners
      await this.awardNotificationService.notifyAwardWinners('MONTHLY', startDate, endDate);
      this.logger.log('Manual monthly award winner notifications sent successfully');
    } catch (error) {
      this.logger.error(`Error in manual monthly award generation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Manual trigger for annual awards (for testing or manual execution)
   * @param year Optional year (defaults to previous year)
   */
  async triggerAnnualAwards(year?: number): Promise<void> {
    try {
      const today = getCurrentUTCDate();
      const targetYear = year || today.getUTCFullYear() - 1;

      const startDate = new Date(Date.UTC(targetYear, 0, 1)); // January 1st
      const endDate = new Date(Date.UTC(targetYear, 11, 31, 23, 59, 59, 999)); // December 31st

      this.logger.log(`Manually triggering annual awards for ${targetYear} (${startDate.toISOString().slice(0, 10)} to ${endDate.toISOString().slice(0, 10)})`);

      await Promise.all([
        this.diaryAwardService.generateAwardsForRange(startDate, endDate),
        this.essayAwardService.generateAwardsForRange(startDate, endDate),
        this.novelAwardService.generateAwardsForRange(startDate, endDate),
      ]);

      this.logger.log(`Manual annual award generation completed for ${targetYear}`);

      // Notify award winners
      await this.awardNotificationService.notifyAwardWinners('YEARLY', startDate, endDate);
      this.logger.log('Manual annual award winner notifications sent successfully');
    } catch (error) {
      this.logger.error(`Error in manual annual award generation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get scheduler status for monitoring
   */
  getStatus() {
    return {
      weeklyProcessing: this.isProcessingWeekly,
      monthlyProcessing: this.isProcessingMonthly,
      quarterlyProcessing: this.isProcessingQuarterly,
      annualProcessing: this.isProcessingAnnual,
      nextWeeklyRun: 'Every Sunday at 00:30 UTC (Diary module only)',
      nextMonthlyRun: 'Every 1st of month at 02:00 UTC (All modules)',
      nextQuarterlyRun: 'Every 1st of quarter at 01:00 UTC (All modules)',
      nextAnnualRun: 'January 1st at 03:00 UTC (All modules)',
      lastWeeklyRun: this.lastWeeklyRun?.toISOString(),
      lastMonthlyRun: this.lastMonthlyRun?.toISOString(),
      lastQuarterlyRun: this.lastQuarterlyRun?.toISOString(),
      lastAnnualRun: this.lastAnnualRun?.toISOString(),
      lastWeeklyPeriod: this.getLastWeeklyPeriod(),
      lastMonthlyPeriod: this.getLastMonthlyPeriod(),
      lastQuarterlyPeriod: this.getLastQuarterlyPeriod(),
      lastAnnualPeriod: this.getLastAnnualPeriod(),
    };
  }

  /**
   * Get comprehensive diagnostic information about the award system
   */
  async getDiagnosticInfo() {
    try {
      const now = getCurrentUTCDate();
      const lastWeek = addDaysUTC(now, -7);
      const lastMonth = addMonthsUTC(now, -1);

      // Check award configuration
      const totalAwards = await this.awardRepository.count();
      const activeAwards = await this.awardRepository.count({ where: { isActive: true } });
      const awardsByModule = await this.awardRepository
        .createQueryBuilder('award')
        .select('award.module', 'module')
        .addSelect('COUNT(*)', 'count')
        .where('award.isActive = true')
        .groupBy('award.module')
        .getRawMany();

      // Check recent activity
      const recentDiaryEntries = await this.diaryEntryRepository.count({
        where: { createdAt: MoreThanOrEqual(lastWeek) }
      });

      const recentNovelEntries = await this.novelEntryRepository.count({
        where: { createdAt: MoreThanOrEqual(lastWeek) }
      });

      // Check award winners
      const totalAwardWinners = await this.awardWinnerRepository.count();
      const recentAwardWinners = await this.awardWinnerRepository.count({
        where: { createdAt: MoreThanOrEqual(lastMonth) }
      });

      // Check confirmed diary entries (eligible for awards)
      const confirmedDiaryEntries = await this.diaryEntryRepository.count({
        where: {
          status: DiaryEntryStatus.REVIEWED,
          createdAt: MoreThanOrEqual(lastMonth)
        }
      });

      // Check reviewed novel entries (eligible for awards)
      const reviewedNovelEntries = await this.novelEntryRepository.count({
        where: {
          status: NovelEntryStatus.REVIEWED,
          createdAt: MoreThanOrEqual(lastMonth)
        }
      });

      return {
        timestamp: now.toISOString(),
        awardConfiguration: {
          totalAwards,
          activeAwards,
          awardsByModule: awardsByModule.reduce((acc, item) => {
            acc[item.module] = parseInt(item.count);
            return acc;
          }, {}),
        },
        recentActivity: {
          lastWeekDiaryEntries: recentDiaryEntries,
          lastWeekNovelEntries: recentNovelEntries,
          lastMonthConfirmedDiaryEntries: confirmedDiaryEntries,
          lastMonthReviewedNovelEntries: reviewedNovelEntries,
        },
        awardWinners: {
          totalWinners: totalAwardWinners,
          recentWinners: recentAwardWinners,
        },
        schedulerStatus: this.getStatus(),
        recommendations: this.generateRecommendations({
          totalAwards,
          activeAwards,
          recentDiaryEntries,
          recentNovelEntries,
          totalAwardWinners,
          recentAwardWinners,
          confirmedDiaryEntries,
          reviewedNovelEntries,
        }),
      };
    } catch (error) {
      this.logger.error(`Error getting diagnostic info: ${error.message}`, error.stack);
      return {
        error: 'Failed to get diagnostic information',
        message: error.message,
        timestamp: getCurrentUTCDate().toISOString(),
      };
    }
  }

  /**
   * Generate recommendations based on diagnostic data
   */
  private generateRecommendations(data: any): string[] {
    const recommendations = [];

    if (data.totalAwards === 0) {
      recommendations.push('❌ No awards configured - Run seed data to create awards');
    }

    if (data.activeAwards === 0) {
      recommendations.push('❌ No active awards - Enable awards in the database');
    }

    if (data.recentDiaryEntries === 0 && data.recentNovelEntries === 0) {
      recommendations.push('⚠️ No recent user activity - Check if users are actively using the system');
    }

    if (data.confirmedDiaryEntries === 0) {
      recommendations.push('⚠️ No confirmed diary entries - Check if tutors are reviewing and confirming entries');
    }

    if (data.reviewedNovelEntries === 0) {
      recommendations.push('⚠️ No reviewed novel entries - Check if tutors are reviewing novel submissions');
    }

    if (data.totalAwardWinners === 0) {
      recommendations.push('❌ No award winners ever - Awards may not be generating properly');
    }

    if (data.recentAwardWinners === 0 && data.totalAwardWinners > 0) {
      recommendations.push('⚠️ No recent award winners - Check if cron jobs are running');
    }

    if (recommendations.length === 0) {
      recommendations.push('✅ Award system appears to be functioning normally');
    }

    return recommendations;
  }

  /**
   * Get the period for last week's awards
   */
  private getLastWeeklyPeriod() {
    const today = getCurrentUTCDate();
    // Calculate previous week (Monday to Sunday)
    const endDate = addDaysUTC(today, -1); // Yesterday
    const startDate = addDaysUTC(endDate, -6); // Monday of previous week

    return {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      weekStarting: startDate.toISOString().slice(0, 10),
      weekEnding: endDate.toISOString().slice(0, 10),
    };
  }

  /**
   * Get the period for last month's awards
   */
  private getLastMonthlyPeriod() {
    const today = getCurrentUTCDate();
    const lastMonth = addMonthsUTC(today, -1);
    const startDate = getStartOfMonthUTC(lastMonth);
    const endDate = getEndOfMonthUTC(lastMonth);

    return {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      month: lastMonth.getUTCMonth() + 1,
      year: lastMonth.getUTCFullYear(),
    };
  }

  /**
   * Get the period for last quarter's awards
   */
  private getLastQuarterlyPeriod() {
    const today = getCurrentUTCDate();
    const currentQuarter = Math.floor(today.getUTCMonth() / 3);
    const previousQuarter = currentQuarter === 0 ? 3 : currentQuarter - 1;
    const quarterYear = currentQuarter === 0 ? today.getUTCFullYear() - 1 : today.getUTCFullYear();

    const startDate = new Date(Date.UTC(quarterYear, previousQuarter * 3, 1));
    const endDate = new Date(Date.UTC(quarterYear, previousQuarter * 3 + 3, 0, 23, 59, 59, 999));

    return {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      quarter: previousQuarter + 1,
      year: quarterYear,
    };
  }

  /**
   * Get the period for last year's awards
   */
  private getLastAnnualPeriod() {
    const today = getCurrentUTCDate();
    const lastYear = today.getUTCFullYear() - 1;

    return {
      startDate: new Date(Date.UTC(lastYear, 0, 1)).toISOString(),
      endDate: new Date(Date.UTC(lastYear, 11, 31, 23, 59, 59, 999)).toISOString(),
      year: lastYear,
    };
  }

  /**
   * Trigger test awards for immediate testing
   * Generates awards for the last completed week and month
   */
  async triggerTestAwards(): Promise<void> {
    try {
      this.logger.log('Starting test award generation...');

      const now = getCurrentUTCDate();

      // Test weekly awards (last week)
      const lastWeekEnd = addDaysUTC(now, -1); // Yesterday
      const lastWeekStart = addDaysUTC(lastWeekEnd, -6); // 7 days ago

      this.logger.log(`Generating test weekly awards for period: ${lastWeekStart.toISOString()} to ${lastWeekEnd.toISOString()}`);
      await this.diaryAwardService.generateAwardsForRange(lastWeekStart, lastWeekEnd);

      // Test monthly awards (last month)
      const lastMonth = addMonthsUTC(now, -1);
      const lastMonthStart = getStartOfMonthUTC(lastMonth);
      const lastMonthEnd = getEndOfMonthUTC(lastMonth);

      this.logger.log(`Generating test monthly awards for period: ${lastMonthStart.toISOString()} to ${lastMonthEnd.toISOString()}`);

      await Promise.all([
        this.diaryAwardService.generateAwardsForRange(lastMonthStart, lastMonthEnd),
        this.essayAwardService.generateAwardsForRange(lastMonthStart, lastMonthEnd),
        this.novelAwardService.generateAwardsForRange(lastMonthStart, lastMonthEnd),
      ]);

      this.logger.log('Test award generation completed successfully');

      // Send notifications
      await this.awardNotificationService.notifyAwardWinners('TEST', lastMonthStart, lastMonthEnd);

    } catch (error) {
      this.logger.error(`Error in test award generation: ${error.message}`, error.stack);
      throw error;
    }
  }
}
