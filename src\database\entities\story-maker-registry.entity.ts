import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseFileRegistry } from './base-file-registry.entity';
import { StoryMaker } from './story-maker.entity';

@Entity()
export class StoryMakerRegistry extends BaseFileRegistry {
  @Column({ name: 'story_maker_id' })
  storyMakerId: string;

  @ManyToOne(() => StoryMaker, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'story_maker_id' })
  storyMaker: StoryMaker;

  /**
   * Convert to DTO for API responses
   */
  toDto(): any {
    return {
      ...this.toSimpleObject(),
      storyMakerId: this.storyMakerId,
      storyMaker: this.storyMaker
        ? {
            id: this.storyMaker.id,
            title: this.storyMaker.title,
            instruction: this.storyMaker.instruction,

            isActive: this.storyMaker.isActive,
          }
        : null,
    };
  }
}
