import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEnum, IsOptional, IsBoolean, IsUUID, IsDate } from 'class-validator';
import { Type } from 'class-transformer';
import { OwnedItemStatus } from '../entities/student-owned-item.entity';
import { PaginationDto } from '../../common/models/pagination.dto';

/**
 * DTO for student owned item response
 */
export class StudentOwnedItemResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  shopItemId: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  description: string;

  @ApiProperty({ description: 'Category ID of the item' })
  categoryId: string;

  @ApiProperty({ description: 'Category name of the item' })
  categoryName: string;

  @ApiProperty({ required: false })
  shopItemCategory?: string;

  @ApiProperty({ enum: OwnedItemStatus })
  status: OwnedItemStatus;

  @ApiProperty()
  acquiredDate: Date;

  @ApiProperty({ required: false })
  expiryDate?: Date;

  @ApiProperty({ required: false })
  lastUsedDate?: Date;

  @ApiProperty()
  isFavorite: boolean;

  @ApiProperty({ required: false })
  filePath?: string;

  @ApiProperty({ required: false })
  notes?: string;
}

/**
 * DTO for updating a student owned item
 */
export class UpdateStudentOwnedItemDto {
  @ApiProperty({ enum: OwnedItemStatus, required: false })
  @IsOptional()
  @IsEnum(OwnedItemStatus)
  status?: OwnedItemStatus;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isFavorite?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  notes?: string;
}

/**
 * DTO for paged list of student owned items
 */
export class StudentOwnedItemsResponseDto {
  @ApiProperty({ type: [StudentOwnedItemResponseDto] })
  items: StudentOwnedItemResponseDto[];

  @ApiProperty()
  totalItems: number;

  @ApiProperty()
  itemsPerPage: number;

  @ApiProperty()
  currentPage: number;

  @ApiProperty()
  totalPages: number;
}

/**
 * DTO for a category with its items
 */
export class CategoryWithItemsDto {
  @ApiProperty()
  categoryId: string;

  @ApiProperty()
  categoryName: string;

  @ApiProperty({ type: [StudentOwnedItemResponseDto] })
  items: StudentOwnedItemResponseDto[];
}

/**
 * DTO for student owned items grouped by category
 */
export class GroupedOwnedItemsResponseDto {
  @ApiProperty({ type: [CategoryWithItemsDto] })
  categories: CategoryWithItemsDto[];
}

/**
 * DTO for student owned items query parameters
 */
export class StudentOwnedItemsQueryDto extends PaginationDto {
  @ApiProperty({
    description: 'Filter by category ID',
    required: false,
  })
  @IsOptional()
  @IsString()
  categoryId?: string;

  @ApiProperty({
    description: 'Filter by item status',
    enum: OwnedItemStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(OwnedItemStatus)
  status?: OwnedItemStatus;
}

/**
 * DTO for student owned items by category query parameters
 */
export class StudentOwnedItemsByCategoryQueryDto extends PaginationDto {
  @ApiProperty({
    description: 'Filter by item status',
    enum: OwnedItemStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(OwnedItemStatus)
  status?: OwnedItemStatus;
}

/**
 * DTO for student owned items grouped query parameters
 */
export class StudentOwnedItemsGroupedQueryDto {
  @ApiProperty({
    description: 'Filter by category ID',
    required: false,
  })
  @IsOptional()
  @IsString()
  categoryId?: string;

  @ApiProperty({
    description: 'Filter by item status',
    enum: OwnedItemStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(OwnedItemStatus)
  status?: OwnedItemStatus;
}
