import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDiaryFriendShareAndFollowRequestNotificationTypes1753000000000 implements MigrationInterface {
  name = 'AddDiaryFriendShareAndFollowRequestNotificationTypes1753000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new notification types to the notification_type_enum
    await queryRunner.query(`
      ALTER TYPE "public"."notification_type_enum" 
      ADD VALUE 'diary_friend_share'
    `);
    
    await queryRunner.query(`
      ALTER TYPE "public"."notification_type_enum" 
      ADD VALUE 'diary_follow_request'
    `);

    // Add new notification types to the user_notification_preference_notification_type_enum
    await queryRunner.query(`
      ALTER TYPE "public"."user_notification_preference_notification_type_enum" 
      ADD VALUE 'diary_friend_share'
    `);
    
    await queryRunner.query(`
      ALTER TYPE "public"."user_notification_preference_notification_type_enum" 
      ADD VALUE 'diary_follow_request'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: PostgreSQL doesn't support removing enum values directly
    // This would require recreating the enum types, which is complex
    // For now, we'll leave the enum values in place
    console.log('Warning: Cannot remove enum values in PostgreSQL. The added notification types will remain.');
  }
}
