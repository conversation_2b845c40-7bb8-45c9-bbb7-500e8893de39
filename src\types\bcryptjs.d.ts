declare module 'bcryptjs' {
  /**
   * Generate a hash for the plaintext password
   * @param s The string to hash
   * @param salt The salt to use, or the number of rounds to generate a salt
   * @returns A promise that resolves to the hash
   */
  export function hash(s: string, salt: string | number): Promise<string>;

  /**
   * Generate a hash for the plaintext password (synchronous)
   * @param s The string to hash
   * @param salt The salt to use, or the number of rounds to generate a salt
   * @returns The hash
   */
  export function hashSync(s: string, salt: string | number): string;

  /**
   * Compare the plaintext password to a hash
   * @param s The plaintext password to compare
   * @param hash The hash to compare against
   * @returns A promise that resolves to true if the password matches, false otherwise
   */
  export function compare(s: string, hash: string): Promise<boolean>;

  /**
   * Compare the plaintext password to a hash (synchronous)
   * @param s The plaintext password to compare
   * @param hash The hash to compare against
   * @returns True if the password matches, false otherwise
   */
  export function compareSync(s: string, hash: string): boolean;

  /**
   * Generate a salt
   * @param rounds The number of rounds to use (default: 10)
   * @returns A promise that resolves to the salt
   */
  export function genSalt(rounds?: number): Promise<string>;

  /**
   * Generate a salt (synchronous)
   * @param rounds The number of rounds to use (default: 10)
   * @returns The salt
   */
  export function genSaltSync(rounds?: number): string;

  /**
   * Gets the number of rounds used to generate a hash
   * @param hash The hash to check
   * @returns The number of rounds
   */
  export function getRounds(hash: string): number;
}
