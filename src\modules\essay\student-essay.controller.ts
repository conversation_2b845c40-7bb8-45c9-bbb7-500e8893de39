import { Controller, Get, Post, Body, Query, UseGuards, Req, Param, ParseUUIDPipe } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import { SubscriptionFeatureGuard } from 'src/common/guards/subscription-feature.guard';
import { RequireFeature } from 'src/common/decorators/require-feature.decorator';
import { FeatureType } from 'src/database/entities/plan-feature.entity';
import { ApiResponse } from 'src/common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiErrorResponse } from 'src/common/decorators/api-response.decorator';
import { PagedListDto } from 'src/common/models/paged-list.dto';
import { EssayMissionService } from './admin-essay.service';
import { EssaySubmissionService } from './student-essay.service';
import {
  MissionResponseDto,
  MissionPaginationDto,
  CreateMissionTaskSubmissionDto,
  StartMissionTaskDto,
  EssayTaskSubmissionUpdate,
  EssayTaskActiveDto,
  EssayModuleSkinPreferenceDto,
  TaskSkinInfoResponseDto,
  SetDefaultSkinDto,
} from 'src/database/models/mission.dto';
import { StudentGuard } from 'src/common/guards/student.guard';
import { EssayTaskSubmissionDto } from 'src/database/models/mission.dto';
import { DiarySkinResponseDto } from 'src/database/models/diary.dto';
import { DiaryService } from '../diary/diary.service';
import { PaginationDto } from 'src/common/models/pagination.dto';

@ApiTags('student-essay')
@ApiBearerAuth('JWT-auth')
@Controller('student-essay')
@UseGuards(JwtAuthGuard, StudentGuard, SubscriptionFeatureGuard)
@RequireFeature(FeatureType.ENGLISH_ESSAY)
export class StudentEssayController {
  constructor(
    private readonly missionService: EssayMissionService,
    private readonly essaySubmissionService: EssaySubmissionService,
    private readonly diaryService: DiaryService,
  ) {}

  @Get('getMission')
  @ApiOperation({ summary: 'Get all missions' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    type: String,
    enum: ['ASC', 'DESC'],
    description: 'Sort order (asc or desc)',
  })
  @ApiOkResponseWithPagedListType(MissionResponseDto, 'Retrieved all missions')
  async findAll(@Req() req: any, @Query() paginationDto?: MissionPaginationDto): Promise<ApiResponse<PagedListDto<MissionResponseDto>>> {
    4;
    const userId = req.user.sub;
    const result = await this.missionService.findAll(paginationDto, userId);
    return ApiResponse.success(result, 'Retrieved all missions');
  }

  @Post('start/task')
  @ApiOperation({ summary: 'Start an essay task' })
  @ApiBody({
    type: StartMissionTaskDto,
    description: 'Essay task start data',
    examples: {
      example1: {
        value: {
          taskId: '123e4567-e89b-12d3-a456-426614174000',
        },
      },
    },
  })
  @ApiOkResponseWithType(EssayTaskSubmissionDto, 'Task started successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Task not found')
  @ApiErrorResponse(409, 'Task already started')
  @ApiErrorResponse(500, 'Internal server error')
  async startTask(@Body() startMissionTask: StartMissionTaskDto): Promise<ApiResponse<EssayTaskSubmissionDto>> {
    const result = await this.essaySubmissionService.startTask(startMissionTask.taskId);
    return ApiResponse.success(result, 'Task started successfully');
  }

  @Post('start/essay')
  @ApiOperation({ summary: 'Start an essay task' })
  @ApiBody({
    type: StartMissionTaskDto,
    description: 'Essay task start data',
    examples: {
      example1: {
        value: {
          taskId: null,
        },
      },
    },
  })
  @ApiOkResponseWithType(EssayTaskSubmissionDto, 'Essay started successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Essay not found')
  @ApiErrorResponse(409, 'Essay already started')
  @ApiErrorResponse(500, 'Internal server error')
  async startEssay(@Body() startMissionTask: StartMissionTaskDto): Promise<ApiResponse<EssayTaskSubmissionDto>> {
    const result = await this.essaySubmissionService.startEssay();
    return ApiResponse.success(result, 'Essay started successfully');
  }

  @Post('submit/essay')
  @ApiOperation({ summary: 'Submit an essay task' })
  @ApiBody({
    type: CreateMissionTaskSubmissionDto,
    description: 'Essay task submission data',
    examples: {
      example1: {
        value: {
          taskId: '123e4567-e89b-12d3-a456-426614174000',
          content: 'This is my essay submission',
          title: 'My Essay Title',
          skinId: '123e4567-e89b-12d3-a456-426614174000',
          metaData: {
            timeSpent: 120,
          },
        },
      },
    },
  })
  @ApiOkResponseWithType(EssayTaskSubmissionDto, 'Essay task submitted successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async submitTask(@Body() createMissionTaskSubmissionDto: CreateMissionTaskSubmissionDto): Promise<ApiResponse<EssayTaskSubmissionDto>> {
    const result = await this.essaySubmissionService.submitEssay(createMissionTaskSubmissionDto);
    return ApiResponse.success(result, 'Task submitted successfully');
  }

  @Post('submit/essay/update')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({ summary: 'Update an essay task submission' })
  @ApiBody({
    type: EssayTaskSubmissionUpdate,
    description: 'Essay task submission update data',
    examples: {
      example1: {
        value: {
          submissionId: '123e4567-e89b-12d3-a456-426614174000',
          content: 'This is my updated essay submission',
          title: 'My Updated Essay Title',
          skinId: '123e4567-e89b-12d3-a456-426614174000',
          metaData: {
            timeSpent: 150,
            lastDraftSavedAt: new Date().toISOString(),
          },
        },
      },
    },
  })
  @ApiOkResponseWithType(EssayTaskSubmissionDto, 'Essay task submission updated successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Task submission not found')
  async updateTaskSubmission(@Body() updateTaskSubmissionDto: EssayTaskSubmissionUpdate): Promise<ApiResponse<EssayTaskSubmissionDto>> {
    const result = await this.essaySubmissionService.autoSaveContent(updateTaskSubmissionDto);
    return ApiResponse.success(result, 'Task submission updated successfully');
  }

  @Get('activeTask')
  @ApiOperation({ summary: 'Get all active tasks for the logged-in user' })
  @ApiOkResponseWithPagedListType(EssayTaskSubmissionDto, 'Retrieved all active tasks')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getActiveTask(): Promise<ApiResponse<EssayTaskSubmissionDto>> {
    const result = await this.essaySubmissionService.getActiveTask();
    return ApiResponse.success(result, 'Retrieved all active tasks');
  }

  @Get('activeEssay')
  @ApiOperation({ summary: 'Get active diary for the logged-in user' })
  @ApiOkResponseWithType(EssayTaskSubmissionDto, 'Active diary retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getActiveDiary(@Req() req: any): Promise<ApiResponse<EssayTaskSubmissionDto>> {
    const result = await this.essaySubmissionService.getActiveEssay();
    return ApiResponse.success(result, 'Active essay retrieved successfully');
  }

  @Get('submissions/:taskId')
  @ApiOperation({ summary: 'Get active task by ID' })
  @ApiParam({
    name: 'taskId',
    type: String,
    description: 'The ID of the task to retrieve',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(EssayTaskSubmissionDto, 'Active task retrieved successfully')
  @ApiErrorResponse(400, 'Invalid task ID')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Task not found')
  async getActiveTaskById(@Param('taskId') taskId: string, @Req() req?: any): Promise<ApiResponse<EssayTaskSubmissionDto>> {
    const result = await this.essaySubmissionService.getSubmissionsTaskById(taskId);
    return ApiResponse.success(result, 'Active task retrieved successfully');
  }

  @Get('skins')
  @ApiOperation({
    summary: 'Get all diary skins',
    description: 'Get a list of all available diary skins/templates.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)',
  })
  @ApiOkResponseWithPagedListType(DiarySkinResponseDto, 'List of diary skins retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(500, 'Internal server error')
  async getDiarySkins(@Req() req: any, @Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<DiarySkinResponseDto>>> {
    const studentId = req.user.id;
    const skins = await this.diaryService.getDiarySkins(false, studentId, paginationDto); // Include student's own skins
    return ApiResponse.success(skins, 'Diary skins retrieved successfully');
  }

  @Get('skins/:taskId')
  @ApiOperation({
    summary: 'Get skin information for a specific task',
    description: 'Retrieves the skin configuration for a task, including whether it uses default or task-specific skin.',
  })
  @ApiParam({
    name: 'taskId',
    type: String,
    description: 'The ID of the task to get skin information for',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(TaskSkinInfoResponseDto, 'Task skin information retrieved successfully')
  @ApiErrorResponse(400, 'Invalid task ID')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Task not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getTaskSkinInfo(@Param('taskId') taskId: string, @Req() req?: any): Promise<ApiResponse<TaskSkinInfoResponseDto>> {
    const userId = req.user.id;
    const result = await this.essaySubmissionService.getTaskSkinInfo(taskId, userId);
    return ApiResponse.success(result, 'Task skin information retrieved successfully');
  }

  @Post('skin/default')
  @ApiOperation({ summary: 'Set default skin for essay module' })
  @ApiBody({
    type: SetDefaultSkinDto,
    description: 'Essay module skin preference data',
    examples: {
      example1: {
        value: {
          skinId: '123e4567-e89b-12d3-a456-426614174000',
        },
      },
    },
  })
  @ApiOkResponseWithType(EssayModuleSkinPreferenceDto, 'Default skin set successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Skin not found')
  @ApiErrorResponse(409, 'Skin already set as default')
  @ApiErrorResponse(500, 'Internal server error')
  async setDefaultSkin(@Body() essayModuleSkinPreferenceDto: SetDefaultSkinDto): Promise<ApiResponse<EssayModuleSkinPreferenceDto>> {
    const result = await this.essaySubmissionService.setDefaultSkin(essayModuleSkinPreferenceDto.skinId);
    return ApiResponse.success(result, 'Default skin set successfully');
  }

  @Get('myEssays')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({ summary: 'My Essay Submissions' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    type: String,
    enum: ['ASC', 'DESC'],
    description: 'Sort order (asc or desc)',
  })
  @ApiOkResponseWithPagedListType(EssayTaskSubmissionDto, 'Retrieved all submitted first submitted essays list')
  async findAllSubmittedEssaysByUserId(@Req() req: any, @Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<EssayTaskSubmissionDto>>> {
    const userId = req.user.sub;
    const result = await this.missionService.findAllSubmittedEssays(paginationDto, userId);
    return ApiResponse.success(result, 'Retrieved all submitted first essays list');
  }

  @Get('myEssays/:id')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({ summary: 'Get a specific submitted essay by ID' })
  @ApiParam({ name: 'id', description: 'ID of the submitted essay to retrieve', type: String })
  @ApiOkResponseWithType(EssayTaskSubmissionDto, 'Retrieved submitted essay successfully')
  @ApiErrorResponse(404, 'Submitted essay not found')
  async findOneSubmittedEssay(@Req() req: any, @Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<EssayTaskSubmissionDto>> {
    const userId = req.user.sub;
    const result = await this.missionService.findSubmittedEssayById(id, userId);
    return ApiResponse.success(result, 'Retrieved submitted essay successfully');
  }
}
