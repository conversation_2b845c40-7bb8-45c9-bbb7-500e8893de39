import { MigrationInterface, QueryRunner } from 'typeorm';

export class QAMissionSeedData1746339927175 implements MigrationInterface {
  name = 'QAMissionSeedData1746339927175';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Seed months for 2025
    const months = [
      { title: 'January', sequence: 1, display: 'Jan', year: 2025 },
      { title: 'February', sequence: 2, display: 'Feb', year: 2025 },
      { title: 'March', sequence: 3, display: 'Mar', year: 2025 },
      { title: 'April', sequence: 4, display: 'Apr', year: 2025 },
      { title: 'May', sequence: 5, display: 'May', year: 2025 },
      { title: 'June', sequence: 6, display: 'Jun', year: 2025 },
      { title: 'July', sequence: 7, display: 'Jul', year: 2025 },
      { title: 'August', sequence: 8, display: 'Aug', year: 2025 },
      { title: 'September', sequence: 9, display: 'Sep', year: 2025 },
      { title: 'October', sequence: 10, display: 'Oct', year: 2025 },
      { title: 'November', sequence: 11, display: 'Nov', year: 2025 },
      { title: 'December', sequence: 12, display: 'Dec', year: 2025 },
    ];

    // Insert months
    for (const month of months) {
      await queryRunner.query(`
                INSERT INTO qa_mission_month (id, title, display, sequence, year)
                VALUES (uuid_generate_v4(), '${month.title}', '${month.display}', ${month.sequence}, ${month.year})
            `);
    }

    // Seed weeks for 2025
    const weeks = [
      // January
      { title: 'Week-01', sequence: 1, month: 'January', startDate: '2025-01-01', endDate: '2025-01-07', year: 2025 },
      { title: 'Week-02', sequence: 2, month: 'January', startDate: '2025-01-08', endDate: '2025-01-14', year: 2025 },
      { title: 'Week-03', sequence: 3, month: 'January', startDate: '2025-01-15', endDate: '2025-01-21', year: 2025 },
      { title: 'Week-04', sequence: 4, month: 'January', startDate: '2025-01-22', endDate: '2025-01-28', year: 2025 },
      { title: 'Week-05', sequence: 5, month: 'January', startDate: '2025-01-29', endDate: '2025-02-04', year: 2025 },

      // February
      { title: 'Week-06', sequence: 6, month: 'February', startDate: '2025-02-05', endDate: '2025-02-11', year: 2025 },
      { title: 'Week-07', sequence: 7, month: 'February', startDate: '2025-02-12', endDate: '2025-02-18', year: 2025 },
      { title: 'Week-08', sequence: 8, month: 'February', startDate: '2025-02-19', endDate: '2025-02-25', year: 2025 },
      { title: 'Week-09', sequence: 9, month: 'February', startDate: '2025-02-26', endDate: '2025-03-04', year: 2025 },

      // March
      { title: 'Week-10', sequence: 10, month: 'March', startDate: '2025-03-05', endDate: '2025-03-11', year: 2025 },
      { title: 'Week-11', sequence: 11, month: 'March', startDate: '2025-03-12', endDate: '2025-03-18', year: 2025 },
      { title: 'Week-12', sequence: 12, month: 'March', startDate: '2025-03-19', endDate: '2025-03-25', year: 2025 },
      { title: 'Week-13', sequence: 13, month: 'March', startDate: '2025-03-26', endDate: '2025-04-01', year: 2025 },

      // April
      { title: 'Week-14', sequence: 14, month: 'April', startDate: '2025-04-02', endDate: '2025-04-08', year: 2025 },
      { title: 'Week-15', sequence: 15, month: 'April', startDate: '2025-04-09', endDate: '2025-04-15', year: 2025 },
      { title: 'Week-16', sequence: 16, month: 'April', startDate: '2025-04-16', endDate: '2025-04-22', year: 2025 },
      { title: 'Week-17', sequence: 17, month: 'April', startDate: '2025-04-23', endDate: '2025-04-29', year: 2025 },
      { title: 'Week-18', sequence: 18, month: 'April', startDate: '2025-04-30', endDate: '2025-05-06', year: 2025 },

      // May
      { title: 'Week-19', sequence: 19, month: 'May', startDate: '2025-05-07', endDate: '2025-05-13', year: 2025 },
      { title: 'Week-20', sequence: 20, month: 'May', startDate: '2025-05-14', endDate: '2025-05-20', year: 2025 },
      { title: 'Week-21', sequence: 21, month: 'May', startDate: '2025-05-21', endDate: '2025-05-27', year: 2025 },
      { title: 'Week-22', sequence: 22, month: 'May', startDate: '2025-05-28', endDate: '2025-06-03', year: 2025 },

      // June
      { title: 'Week-23', sequence: 23, month: 'June', startDate: '2025-06-04', endDate: '2025-06-10', year: 2025 },
      { title: 'Week-24', sequence: 24, month: 'June', startDate: '2025-06-11', endDate: '2025-06-17', year: 2025 },
      { title: 'Week-25', sequence: 25, month: 'June', startDate: '2025-06-18', endDate: '2025-06-24', year: 2025 },
      { title: 'Week-26', sequence: 26, month: 'June', startDate: '2025-06-25', endDate: '2025-07-01', year: 2025 },

      // July
      { title: 'Week-27', sequence: 27, month: 'July', startDate: '2025-07-02', endDate: '2025-07-08', year: 2025 },
      { title: 'Week-28', sequence: 28, month: 'July', startDate: '2025-07-09', endDate: '2025-07-15', year: 2025 },
      { title: 'Week-29', sequence: 29, month: 'July', startDate: '2025-07-16', endDate: '2025-07-22', year: 2025 },
      { title: 'Week-30', sequence: 30, month: 'July', startDate: '2025-07-23', endDate: '2025-07-29', year: 2025 },
      { title: 'Week-31', sequence: 31, month: 'July', startDate: '2025-07-30', endDate: '2025-08-05', year: 2025 },

      // August
      { title: 'Week-32', sequence: 32, month: 'August', startDate: '2025-08-06', endDate: '2025-08-12', year: 2025 },
      { title: 'Week-33', sequence: 33, month: 'August', startDate: '2025-08-13', endDate: '2025-08-19', year: 2025 },
      { title: 'Week-34', sequence: 34, month: 'August', startDate: '2025-08-20', endDate: '2025-08-26', year: 2025 },
      { title: 'Week-35', sequence: 35, month: 'August', startDate: '2025-08-27', endDate: '2025-09-02', year: 2025 },

      // September
      { title: 'Week-36', sequence: 36, month: 'September', startDate: '2025-09-03', endDate: '2025-09-09', year: 2025 },
      { title: 'Week-37', sequence: 37, month: 'September', startDate: '2025-09-10', endDate: '2025-09-16', year: 2025 },
      { title: 'Week-38', sequence: 38, month: 'September', startDate: '2025-09-17', endDate: '2025-09-23', year: 2025 },
      { title: 'Week-39', sequence: 39, month: 'September', startDate: '2025-09-24', endDate: '2025-09-30', year: 2025 },

      // October
      { title: 'Week-40', sequence: 40, month: 'October', startDate: '2025-10-01', endDate: '2025-10-07', year: 2025 },
      { title: 'Week-41', sequence: 41, month: 'October', startDate: '2025-10-08', endDate: '2025-10-14', year: 2025 },
      { title: 'Week-42', sequence: 42, month: 'October', startDate: '2025-10-15', endDate: '2025-10-21', year: 2025 },
      { title: 'Week-43', sequence: 43, month: 'October', startDate: '2025-10-22', endDate: '2025-10-28', year: 2025 },
      { title: 'Week-44', sequence: 44, month: 'October', startDate: '2025-10-29', endDate: '2025-11-04', year: 2025 },

      // November
      { title: 'Week-45', sequence: 45, month: 'November', startDate: '2025-11-05', endDate: '2025-11-11', year: 2025 },
      { title: 'Week-46', sequence: 46, month: 'November', startDate: '2025-11-12', endDate: '2025-11-18', year: 2025 },
      { title: 'Week-47', sequence: 47, month: 'November', startDate: '2025-11-19', endDate: '2025-11-25', year: 2025 },
      { title: 'Week-48', sequence: 48, month: 'November', startDate: '2025-11-26', endDate: '2025-12-02', year: 2025 },

      // December
      { title: 'Week-49', sequence: 49, month: 'December', startDate: '2025-12-03', endDate: '2025-12-09', year: 2025 },
      { title: 'Week-50', sequence: 50, month: 'December', startDate: '2025-12-10', endDate: '2025-12-16', year: 2025 },
      { title: 'Week-51', sequence: 51, month: 'December', startDate: '2025-12-17', endDate: '2025-12-23', year: 2025 },
      { title: 'Week-52', sequence: 52, month: 'December', startDate: '2025-12-24', endDate: '2025-12-30', year: 2025 },
    ];

    // Insert weeks
    for (const week of weeks) {
      await queryRunner.query(`
                INSERT INTO qa_mission_week (id, title, sequence, month, start_date, end_date, year)
                VALUES (uuid_generate_v4(), '${week.title}', ${week.sequence}, '${week.month}', '${week.startDate}', '${week.endDate}', ${week.year})
            `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove all weeks for 2025
    await queryRunner.query(`DELETE FROM qa_mission_week WHERE year = 2025`);

    // Remove all months for 2025
    await queryRunner.query(`DELETE FROM qa_mission_month WHERE year = 2025`);
  }
}
