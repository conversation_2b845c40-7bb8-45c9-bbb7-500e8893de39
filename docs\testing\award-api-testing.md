# Award Management System - Testing Flow

## Prerequisites

Before testing the Award Management System API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for different user roles (admin, operator)
3. Set up your API testing tool (<PERSON><PERSON> recommended)
4. Ensure test database is properly seeded with sample data

## Overview

The Award Management System allows operators to create and manage awards with different frequencies (WEEKLY, MONTHLY, QUARTERLY, YEARLY). The system uses the **AwardScheduler** for automated award generation with cron-based scheduling.

## Base URLs

- Award Management: `/api/awards`
- Award Scheduling: `/api/award-scheduler`

## Testing Environment Setup

1. **Database Setup**
   - Ensure the database has the latest migrations applied
   - Verify the following tables exist:
     - `award`
     - `award_schedule`
     - `award_criteria`
     - `award_distribution`

2. **Test Users**
   - Create test admin accounts
   - Create test operator accounts
   - Create test student accounts for award distribution

3. **Test Data**
   - Create sample awards with different frequencies
   - Create schedule entries in various states
   - Set up test criteria data

## API Testing

### Award Management

#### Create Award Test
- Endpoint: `POST /api/awards`
- Auth: Admin token required
- Request body example:
```json
{
  "name": "Quarterly Excellence Award",
  "description": "Award for consistent high performance",
  "module": "DIARY",
  "criteria": ["DIARY_SCORE", "ATTENDANCE"],
  "frequency": "QUARTERLY",
  "rewardPoints": 500,
  "criteriaConfig": {
    "minScore": 90,
    "attendanceRate": 95
  }
}
```
- Expected response: 201 Created with award object

### Frequency-Based Schedule Management

#### Create Schedule Test
- Endpoint: `POST /api/award-schedules`
- Auth: Admin token required
- Request body examples:

1. Quarterly Schedule:
```json
{
  "module": "DIARY",
  "frequency": "QUARTERLY",
  "isActive": true
}
```

2. Monthly Schedule:
```json
{
  "module": "DIARY",
  "frequency": "MONTHLY",
  "isActive": true
}
```

- Expected response: 201 Created with schedule objects

#### List Awards Test
- Endpoint: `GET /api/awards`
- Auth: Admin token required
- Query parameters:
  - module (optional)
  - includeInactive (optional)
  - name (optional)
  - frequency (optional)
  - page (optional)
  - limit (optional)
- Expected response: 200 OK with paged list of awards

#### List Schedule Test
- Endpoint: `GET /api/award-schedules`
- Auth: Admin token required
- Query parameters:
  - module (optional) - Filter by award module
  - status (optional) - Filter by schedule status
  - startDate (optional) - Filter by date range start
  - endDate (optional) - Filter by date range end
- Expected response: 200 OK with list of schedules

#### Update Schedule Status Test
- Endpoint: `POST /api/award-schedules/:id/active-status`
- Auth: Admin token required
- Request body:
```json
{
  "isActive": true
}
```
- Expected response: 200 OK with updated schedule

## Integration Testing

### Frequency-Based Schedule Flow

1. **Create Quarterly Schedule**
   ```typescript
   describe('Quarterly Schedule Creation', () => {
     it('should create correct number of schedule entries', async () => {
       const schedule = await createSchedule({
         module: 'DIARY',
         frequency: 'QUARTERLY',
         isActive: true
       });

       // Check remaining schedules in current year
       const currentYear = new Date().getFullYear();
       const schedules = await listSchedules({
         frequency: 'QUARTERLY',
         year: currentYear
       });

       // Should create one schedule per remaining quarter
       expect(schedules.length).toBeLessThanOrEqual(4);
       expect(schedules.every(s =>
         s.frequency === 'QUARTERLY' &&
         new Date(s.scheduleDate).getFullYear() === currentYear
       )).toBe(true);
     });
   });
   ```

2. **Validate Period Calculations**
   ```typescript
   describe('Period Date Calculations', () => {
     it('should calculate correct period dates for quarterly schedule', async () => {
       const schedule = await createSchedule({
         module: 'DIARY',
         frequency: 'QUARTERLY'
       });

       const { periodStartDate, periodEndDate } = schedule;

       // Verify quarter boundaries
       expect(new Date(periodStartDate).getDate()).toBe(1);
       expect(new Date(periodEndDate).getDate()).toBeGreaterThan(28);
       expect(periodEndDate).toBeDefined();
     });
   });
   ```

## Edge Case Testing

### Frequency Edge Cases

1. **Quarter Boundary Tests**
   - Create schedule at start of quarter
   - Create schedule at end of quarter
   - Verify correct period calculations
   - Verify proper overlap detection

2. **Year Boundary Tests**
   - Create schedule spanning year boundary
   - Verify correct number of schedules created
   - Verify proper period calculations

3. **Frequency Transition Tests**
   - Change frequency from MONTHLY to QUARTERLY
   - Verify existing schedules are properly handled
   - Verify new schedules are created correctly

### Schedule Overlap Tests

1. **Same Frequency Overlap**
   - Create two schedules with same frequency
   - Verify overlap detection works
   - Verify error messages are correct

2. **Different Frequency Overlap**
   - Create schedules with different frequencies
   - Verify overlap detection across frequencies
   - Verify error handling

## Performance Testing

1. **Multiple Schedule Creation**
   - Create multiple schedules simultaneously
   - Verify system handles concurrent schedule creation
   - Test performance with large number of schedules

2. **Schedule Processing**
   - Process multiple schedules concurrently
   - Verify system stability under load
   - Monitor processing time and resource usage

## Error Handling Tests

1. **Invalid Frequency Tests**
   - Attempt to create schedule with invalid frequency
   - Verify proper error responses
   - Check error message clarity

2. **Invalid Date Tests**
   - Test with invalid period dates
   - Test with dates in past
   - Verify proper validation messages

## Conclusion

This testing guide ensures comprehensive coverage of the Award Management System's frequency-based scheduling feature. Follow these procedures to verify proper functionality, edge case handling, and system stability.

For any issues or questions, contact the development team.
