import { Controller, Get, Post, Put, Param, Body, UseGuards, Request } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { QAService } from './qa.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { CreateQASubmissionDto, QAAssignmentResponseDto, QAAssignmentSetResponseDto, QASubmissionResponseDto } from '../../database/models/qa.dto';
import { User, UserType } from '../../database/entities/user.entity';
import { ApiOkResponseWithType } from '../../common/decorators/api-response.decorator';
import { StudentGuard } from '../../common/guards/student.guard';
import { GetUser } from '../../common/decorators/get-user.decorator';
import { StrictStudentOnly } from '../../common/decorators/strict-student-only.decorator';

@ApiTags('Student Q&A')
@ApiBearerAuth('JWT-auth')
@Controller('qa')
export class QAController {
  constructor(private readonly qaService: QAService) {}

  // @Get('latest-assignments/:id')
  // @UseGuards(JwtAuthGuard, StudentGuard)
  // @ApiOperation({ summary: 'Get student\'s latest assignment' })
  // @ApiOkResponseWithType(QAAssignmentResponseDto, 'Latest assignment retrieved successfully')
  // @Roles(UserType.STUDENT)
  // async getLatestAssignment(@Request() req, @GetUser() user: User): Promise<QAAssignmentResponseDto> {
  //   return this.qaService.getLatestAssignment(req.user.id);
  // }

  @Get('latest-assignments/:id')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @StrictStudentOnly()
  @ApiOperation({ summary: "Get student's latest assignment" })
  @ApiOkResponseWithType(QAAssignmentSetResponseDto, 'Latest assignment retrieved successfully')
  async getLatestAssignment(@Request() req, @GetUser() user: User): Promise<QAAssignmentSetResponseDto> {
    return this.qaService.getLatestAssignmentItems(req.user.id);
  }

  //   @Get('assignments/:id')
  //   @ApiOperation({ summary: 'Get specific assignment details' })
  //   @ApiResponse({ status: 200, type: QAAssignmentResponseDto })
  //   @Roles(UserType.STUDENT)
  //   async getAssignment(@Param('id') id: string): Promise<QAAssignmentResponseDto> {
  //     return this.qaService.getAssignment(id);
  //   }

  //   @Get('my-assignments')
  //   @ApiOperation({ summary: 'Get student\'s assignments' })
  //   @ApiResponse({ status: 200, type: [QAAssignmentResponseDto] })
  //   @Roles(UserType.STUDENT)
  //   async getMyAssignments(@Request() req): Promise<QAAssignmentResponseDto[]> {
  //     return this.qaService.getStudentAssignments(req.user.id);
  //   }

  @Post('submissions')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @StrictStudentOnly()
  @ApiOperation({ summary: 'Create or update submission draft' })
  @ApiOkResponseWithType(QASubmissionResponseDto, 'Submission created successfully')
  async createSubmission(@Body() dto: CreateQASubmissionDto, @Request() req): Promise<QASubmissionResponseDto> {
    return this.qaService.createSubmission(dto, req.user.id);
  }

  @Put('submissions/:id/submit')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @StrictStudentOnly()
  @ApiOperation({ summary: 'Submit final answer for review' })
  @ApiOkResponseWithType(QASubmissionResponseDto, 'Submission submitted successfully')
  async submitFinalSubmission(@Param('id') id: string, @Request() req): Promise<QASubmissionResponseDto> {
    return this.qaService.submitFinalSubmission(id, req.user.id);
  }
}
