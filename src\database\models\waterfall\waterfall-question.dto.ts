import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, IsNotEmpty, ArrayMinSize, IsOptional, ValidateNested, IsNumber, IsPositive, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
/**
 * DTO for creating waterfall questions
 */

export class CreateWaterfallQuestionsDto {
  @ApiProperty({
    description: 'The question text in rich HTML format',
    example: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
  })
  @IsString()
  @IsNotEmpty()
  question_text: string;

  @ApiProperty({
    description: 'The plain text version of the question with [[gap]] markers for evaluation',
    example: 'The cat [[gap]] on the mat.',
  })
  @IsString()
  @IsNotEmpty()
  question_text_plain: string;

  @ApiProperty({
    description: 'Array of correct answers for each gap',
    example: ['sat'],
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true }) // ✅ validate each answer is a non-empty string
  correct_answers: string[];

  @ApiProperty({
    description: 'Array of options for the question (should include all correct answers)',
    example: ['sits', 'sat', 'standing', 'lying'],
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true }) // ✅ validate each option is a string
  options: string[];

  @ApiProperty({
    description: 'Time limit for answering this question in seconds (optional)',
    example: 30,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  time_limit_in_seconds?: number;

  @ApiProperty({
    description: 'Difficulty level of the question as a number (optional)',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  level?: number;
}

/**
 * DTO for updating a waterfall question
 */
export class UpdateWaterfallQuestionDto {
  @ApiProperty({
    description: 'The question text in rich HTML format',
    example: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
    required: false,
  })
  @IsString()
  @IsOptional()
  question_text?: string;

  @ApiProperty({
    description: 'The plain text version of the question with [[gap]] markers for evaluation',
    example: 'The cat [[gap]] on the mat.',
    required: false,
  })
  @IsString()
  @IsOptional()
  question_text_plain?: string;

  @ApiProperty({
    description: 'Array of correct answers for each gap',
    example: ['sits', 'sat'],
    required: false,
  })
  @IsArray()
  @IsOptional()
  correct_answers?: string[];

  @ApiProperty({
    description: 'Array of options for the question (should include all correct answers)',
    example: ['sits', 'sat', 'standing', 'lying'],
    required: false,
  })
  @IsArray()
  @IsOptional()
  @ArrayMinSize(1, { message: 'Options array must contain at least one option' })
  options?: string[];

  @ApiProperty({
    description: 'Time limit for answering this question in seconds (optional)',
    example: 30,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  time_limit_in_seconds?: number;

  @ApiProperty({
    description: 'Difficulty level of the question as a number (optional)',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  level?: number;
}

/**
 * DTO for waterfall question response
 */
export class WaterfallQuestionResponseDto {
  @ApiProperty({
    description: 'The ID of the question',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The question text in rich HTML format',
    example: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
  })
  question_text: string;

  @ApiProperty({
    description: 'The plain text version of the question with [[gap]] markers',
    example: 'The cat [[gap]] on the mat.',
  })
  question_text_plain: string;

  @ApiProperty({
    description: 'Array of correct answers for each gap',
    example: ['sat'],
  })
  correct_answers: string[];

  @ApiProperty({
    description: 'Array of options for the question',
    example: ['sits', 'sat', 'standing', 'lying'],
  })
  options: string[];

  @ApiProperty({
    description: 'The date the question was created',
    example: '2023-07-25T12:34:56.789Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'The date the question was last updated',
    example: '2023-07-25T12:34:56.789Z',
  })
  updated_at: Date;

  @ApiProperty({
    description: 'Time limit for answering this question in seconds',
    example: 30,
    required: false,
  })
  time_limit_in_seconds?: number;

  @ApiProperty({
    description: 'Difficulty level of the question as a number',
    example: 2,
    required: false,
  })
  level?: number;

  @ApiProperty({
    description: 'Whether the question is active and available for games',
    example: true,
  })
  is_active: boolean;
}

/**
 * Wrapper DTO for validating arrays of waterfall questions
 * This ensures proper validation of each question in the array
 */

export class CreateWaterfallQuestionsWrapperDto {
  @ApiProperty({
    description: 'Array of questions to add to the waterfall set',
    type: [CreateWaterfallQuestionsDto],
    isArray: true,
    example: [
      {
        question_text: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
        question_text_plain: 'The cat [[gap]] on the mat.',
        correct_answers: ['sat'],
        options: ['sits', 'sat', 'standing', 'lying'],
        time_limit_in_seconds: 30,
        level: 2,
      },
    ],
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one question must be provided' })
  @ValidateNested({ each: true }) // ✅ Required for nested validation
  @Type(() => CreateWaterfallQuestionsDto) // ✅ Required for transformation
  questions: CreateWaterfallQuestionsDto[];
}

/**
 * Wrapper DTO for validating a single waterfall question
 * This ensures proper validation of the question object
 */
export class CreateWaterfallQuestionWrapperDto {
  @ApiProperty({
    description: 'Question to add to the waterfall set',
    type: CreateWaterfallQuestionsDto,
    example: {
      question_text: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
      question_text_plain: 'The cat [[gap]] on the mat.',
      correct_answers: ['sat'],
      options: ['sits', 'sat', 'standing', 'lying'],
      time_limit_in_seconds: 30,
      level: 2,
    },
  })
  @ValidateNested()
  @Type(() => CreateWaterfallQuestionsDto)
  question: CreateWaterfallQuestionsDto;
}

/**
 * Wrapper DTO for validating a waterfall question update
 * This ensures proper validation of the question update object
 */
export class UpdateWaterfallQuestionWrapperDto {
  @ApiProperty({
    description: 'Question update data',
    type: UpdateWaterfallQuestionDto,
    example: {
      question_text: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
      options: ['sits', 'sat', 'standing', 'lying'],
    },
  })
  @ValidateNested()
  @Type(() => UpdateWaterfallQuestionDto)
  question: UpdateWaterfallQuestionDto;
}

/**
 * DTO for toggling waterfall question status
 */
export class ToggleWaterfallQuestionStatusDto {
  @ApiProperty({
    description: 'Whether the question should be active',
    example: true,
  })
  @IsBoolean()
  is_active: boolean;
}
