import { Entity, Column, <PERSON>ToOne, Join<PERSON>olumn, Index } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { ShopItem } from './shop-item.entity';
import { ShopItemPurchase } from './shop-item-purchase.entity';

/**
 * Status of the owned item
 * @enum {string}
 */
export enum OwnedItemStatus {
  /** Item is available for use */
  AVAILABLE = 'available',
  /** Item is currently in use */
  IN_USE = 'in_use',
  /** Item is expired or no longer available */
  EXPIRED = 'expired',
}

@Entity()
@Index(['studentId', 'shopItemId'], { unique: true })
export class StudentOwnedItem extends AuditableBaseEntity {
  @Column({ name: 'student_id' })
  studentId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  @Column({ name: 'shop_item_id' })
  shopItemId: string;

  @ManyToOne(() => ShopItem)
  @JoinColumn({ name: 'shop_item_id' })
  shopItem: ShopItem;

  @Column({ name: 'purchase_id', nullable: true })
  purchaseId: string;

  @ManyToOne(() => ShopItemPurchase, { nullable: true })
  @JoinColumn({ name: 'purchase_id' })
  purchase: ShopItemPurchase;

  @Column({
    name: 'status',
    type: 'enum',
    enum: OwnedItemStatus,
    default: OwnedItemStatus.AVAILABLE,
  })
  status: OwnedItemStatus;

  @Column({ name: 'acquired_date', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  acquiredDate: Date;

  @Column({ name: 'expiry_date', type: 'timestamp', nullable: true })
  expiryDate: Date;

  @Column({ name: 'last_used_date', type: 'timestamp', nullable: true })
  lastUsedDate: Date;

  @Column({ name: 'is_favorite', default: false })
  isFavorite: boolean;

  @Column({ name: 'notes', nullable: true })
  notes: string;
}
