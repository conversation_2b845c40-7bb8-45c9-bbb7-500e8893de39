import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsEnum, IsOptional, IsBoolean } from 'class-validator';
import { AwardModule, AwardFrequency } from '../entities/award.entity';
import { ScheduleStatus } from '../entities/award-schedule.entity';

/**
 * DTO for creating a new award schedule
 */
export class CreateAwardScheduleDto {
  @ApiProperty({
    example: AwardModule.DIARY,
    description: 'Module that the award schedule belongs to',
    enum: AwardModule,
  })
  @IsNotEmpty()
  @IsEnum(AwardModule)
  module: AwardModule;

  @ApiProperty({
    example: AwardFrequency.MONTHLY,
    description: 'Frequency of the award schedule',
    enum: AwardFrequency,
  })
  @IsNotEmpty()
  @IsEnum(AwardFrequency)
  frequency: AwardFrequency;

  @ApiProperty({
    example: true,
    description: 'Whether the schedule is active',
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

/**
 * DTO for updating award schedule active status
 */
export class UpdateAwardScheduleStatusDto {
  @ApiProperty({
    example: true,
    description: 'Whether the schedule should be active',
  })
  @IsNotEmpty()
  @IsBoolean()
  isActive: boolean;
}

/**
 * DTO for award schedule response
 */
export class AwardScheduleResponseDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Unique identifier',
  })
  id: string;

  @ApiProperty({
    example: AwardModule.DIARY,
    description: 'Module that the award schedule belongs to',
    enum: AwardModule,
  })
  module: AwardModule;

  @ApiProperty({
    example: '2024-01-01',
    description: 'Date when the award should be scheduled',
  })
  scheduleDate: Date;

  @ApiProperty({
    example: '2024-01-01',
    description: 'Start date of the period for which awards will be generated',
  })
  periodStartDate: Date;

  @ApiProperty({
    example: '2024-01-31',
    description: 'End date of the period for which awards will be generated',
  })
  periodEndDate: Date;

  @ApiProperty({
    example: ScheduleStatus.PENDING,
    description: 'Current status of the schedule',
    enum: ScheduleStatus,
  })
  status: ScheduleStatus;

  @ApiProperty({
    example: 'Error processing awards: invalid date range',
    description: 'Error message if the schedule failed',
    required: false,
  })
  errorMessage?: string;

  @ApiProperty({
    example: 0,
    description: 'Number of retry attempts made',
  })
  retryCount: number;

  @ApiProperty({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Last retry date',
    required: false,
  })
  lastRetryDate?: Date;

  @ApiProperty({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Creation date',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Last update date',
  })
  updatedAt: Date;
}
