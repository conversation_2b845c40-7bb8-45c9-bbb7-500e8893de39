import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateStoryMakerParticipationForeignKeyTypes1746700000000 implements MigrationInterface {
  name = 'UpdateStoryMakerParticipationForeignKeyTypes1746700000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, drop the foreign key constraints
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT IF EXISTS "FK_story_maker_participation_student_id"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT IF EXISTS "FK_story_maker_participation_story_maker_id"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT IF EXISTS "FK_story_maker_participation_evaluated_by"`);

    // Alter the column types to UUID
    // For student_id
    await queryRunner.query(`
            ALTER TABLE "story_maker_participation" 
            ALTER COLUMN "student_id" TYPE uuid USING "student_id"::uuid
        `);

    // For story_maker_id
    await queryRunner.query(`
            ALTER TABLE "story_maker_participation" 
            ALTER COLUMN "story_maker_id" TYPE uuid USING "story_maker_id"::uuid
        `);

    // For evaluated_by (if not null)
    await queryRunner.query(`
            ALTER TABLE "story_maker_participation" 
            ALTER COLUMN "evaluated_by" TYPE uuid USING 
            CASE WHEN "evaluated_by" IS NULL THEN NULL ELSE "evaluated_by"::uuid END
        `);

    // Recreate the foreign key constraints
    await queryRunner.query(`
            ALTER TABLE "story_maker_participation"
            ADD CONSTRAINT "FK_story_maker_participation_student_id"
            FOREIGN KEY ("student_id") REFERENCES "user"("id")
        `);

    await queryRunner.query(`
            ALTER TABLE "story_maker_participation"
            ADD CONSTRAINT "FK_story_maker_participation_story_maker_id"
            FOREIGN KEY ("story_maker_id") REFERENCES "story_maker"("id")
        `);

    // Optional: Add foreign key for evaluated_by if it exists
    await queryRunner.query(`
            ALTER TABLE "story_maker_participation"
            ADD CONSTRAINT "FK_story_maker_participation_evaluated_by"
            FOREIGN KEY ("evaluated_by") REFERENCES "user"("id")
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the foreign key constraints
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT IF EXISTS "FK_story_maker_participation_student_id"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT IF EXISTS "FK_story_maker_participation_story_maker_id"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT IF EXISTS "FK_story_maker_participation_evaluated_by"`);

    // Revert the column types back to varchar
    await queryRunner.query(`
            ALTER TABLE "story_maker_participation" 
            ALTER COLUMN "student_id" TYPE character varying
        `);

    await queryRunner.query(`
            ALTER TABLE "story_maker_participation" 
            ALTER COLUMN "story_maker_id" TYPE character varying
        `);

    await queryRunner.query(`
            ALTER TABLE "story_maker_participation" 
            ALTER COLUMN "evaluated_by" TYPE character varying
        `);

    // Recreate the original foreign key constraints
    await queryRunner.query(`
            ALTER TABLE "story_maker_participation"
            ADD CONSTRAINT "FK_story_maker_participation_student_id"
            FOREIGN KEY ("student_id") REFERENCES "user"("id")
        `);

    await queryRunner.query(`
            ALTER TABLE "story_maker_participation"
            ADD CONSTRAINT "FK_story_maker_participation_story_maker_id"
            FOREIGN KEY ("story_maker_id") REFERENCES "story_maker"("id")
        `);

    // Optional: Add foreign key for evaluated_by if it exists
    await queryRunner.query(`
            ALTER TABLE "story_maker_participation"
            ADD CONSTRAINT "FK_story_maker_participation_evaluated_by"
            FOREIGN KEY ("evaluated_by") REFERENCES "user"("id")
        `);
  }
}
