import { MigrationInterface, QueryRunner } from 'typeorm';

export class AuditUpdated1745343751787 implements MigrationInterface {
  name = 'AuditUpdated1745343751787';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_role" DROP CONSTRAINT "PK_ed4ec68b176b2c2e86a0a72eab3"`);
    await queryRunner.query(`ALTER TABLE "user_role" ADD CONSTRAINT "PK_f634684acb47c1a158b83af5150" PRIMARY KEY ("user_id", "role_id")`);
    await queryRunner.query(`ALTER TABLE "user_role" DROP COLUMN "id"`);
    await queryRunner.query(`ALTER TABLE "role" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "role" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "plan_feature" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "plan_feature" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "plan" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "plan" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "user_plan" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "user_plan" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "profile_pictures" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "profile_pictures" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "user" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "user" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "student_diary_skin" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "student_diary_skin" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "tutor_approval" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "tutor_approval" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "shop_category" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "shop_category" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "shop_item" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "shop_item" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary_skin" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary_skin" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "shop_skin_mapping" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "shop_skin_mapping" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "shop_item_registry" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "shop_item_registry" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "reward_point" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "reward_point" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "profile_picture_registry" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "profile_picture_registry" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "promotion" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "promotion" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "email_verifications" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "email_verifications" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary_feedback" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary_feedback" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary_share" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary_share" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "password_resets" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "password_resets" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary_award" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary_award" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary_skin_registry" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "diary_skin_registry" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "award" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "award" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "award_winner" ADD "created_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "award_winner" ADD "updated_by" character varying(36)`);
    await queryRunner.query(`ALTER TABLE "role" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "role" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "role" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "user_role" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "user_role" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "plan_feature" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "plan_feature" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "plan_feature" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "plan" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "plan" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "plan" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "user_plan" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "user_plan" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user_plan" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "profile_pictures" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "student_diary_skin" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "student_diary_skin" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "student_diary_skin" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "tutor_approval" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "shop_category" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "shop_category" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "shop_category" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "shop_item" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "shop_item" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "shop_item" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "diary_skin" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "diary_skin" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_skin" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "shop_skin_mapping" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "shop_skin_mapping" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "shop_skin_mapping" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "shop_item_registry" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "shop_item_registry" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "shop_item_registry" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "reward_point" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "reward_point" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reward_point" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "profile_picture_registry" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "profile_picture_registry" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "profile_picture_registry" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "promotion" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "promotion" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "promotion" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "email_verifications" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_feedback" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "diary_feedback" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_feedback" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "diary_share" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "diary_share" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_share" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "diary" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "diary" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "password_resets" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_award" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "diary_award" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_award" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "diary_skin_registry" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "diary_skin_registry" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_skin_registry" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "award" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "award" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "award" ALTER COLUMN "updated_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "award_winner" ALTER COLUMN "created_at" SET DEFAULT now()`);
    await queryRunner.query(`ALTER TABLE "award_winner" ALTER COLUMN "updated_at" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "award_winner" ALTER COLUMN "updated_at" SET DEFAULT now()`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "award_winner" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "award_winner" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "award_winner" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "award" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "award" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "award" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_skin_registry" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_skin_registry" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_skin_registry" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_award" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_award" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_award" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "password_resets" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_share" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_share" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_share" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_feedback" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_feedback" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_feedback" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "email_verifications" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "promotion" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "promotion" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "promotion" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "profile_picture_registry" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "profile_picture_registry" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "profile_picture_registry" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "reward_point" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "reward_point" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reward_point" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "shop_item_registry" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "shop_item_registry" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "shop_item_registry" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "shop_skin_mapping" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "shop_skin_mapping" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "shop_skin_mapping" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_skin" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_skin" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "diary_skin" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "shop_item" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "shop_item" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "shop_item" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "shop_category" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "shop_category" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "shop_category" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "tutor_approval" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "student_diary_skin" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "student_diary_skin" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "student_diary_skin" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "profile_pictures" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user_plan" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "user_plan" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "user_plan" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "plan" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "plan" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "plan" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "plan_feature" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "plan_feature" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "plan_feature" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "user_role" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "user_role" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "role" ALTER COLUMN "updated_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "role" ALTER COLUMN "updated_at" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "role" ALTER COLUMN "created_at" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "award_winner" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "award_winner" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "award" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "award" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "diary_skin_registry" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "diary_skin_registry" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "diary_award" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "diary_award" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "password_resets" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "password_resets" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "diary" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "diary" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "diary_entry" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "diary_entry" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "diary_share" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "diary_share" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "diary_feedback" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "diary_feedback" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "email_verifications" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "email_verifications" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "promotion" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "promotion" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "profile_picture_registry" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "profile_picture_registry" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "reward_point" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "reward_point" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "shop_item_registry" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "shop_item_registry" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "shop_skin_mapping" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "shop_skin_mapping" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "diary_skin" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "diary_skin" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "shop_item" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "shop_item" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "shop_category" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "shop_category" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "tutor_approval" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "tutor_approval" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "student_diary_skin" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "student_diary_skin" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "profile_pictures" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "profile_pictures" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "user_plan" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "user_plan" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "plan" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "plan" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "plan_feature" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "plan_feature" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "role" DROP COLUMN "updated_by"`);
    await queryRunner.query(`ALTER TABLE "role" DROP COLUMN "created_by"`);
    await queryRunner.query(`ALTER TABLE "user_role" ADD "id" uuid NOT NULL DEFAULT uuid_generate_v4()`);
    await queryRunner.query(`ALTER TABLE "user_role" DROP CONSTRAINT "PK_f634684acb47c1a158b83af5150"`);
    await queryRunner.query(`ALTER TABLE "user_role" ADD CONSTRAINT "PK_ed4ec68b176b2c2e86a0a72eab3" PRIMARY KEY ("id", "user_id", "role_id")`);
  }
}
