import { Entity, Column } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';

/**
 * Type of promotion discount
 * @enum {string}
 */
export enum DiscountType {
  /** Percentage discount (e.g., 10% off) */
  PERCENTAGE = 'percentage',
  /** Fixed amount discount (e.g., $10 off) */
  FIXED_AMOUNT = 'fixed_amount',
}

/**
 * Type of promotion
 * @enum {string}
 */
export enum PromotionType {
  /** Percentage discount (e.g., 10% off) */
  PERCENTAGE = 'percentage',
  /** Fixed amount discount (e.g., $10 off) */
  FIXED_AMOUNT = 'fixed_amount',
}

/**
 * Type of items the promotion applies to
 * @enum {string}
 */
export enum PromotionApplicableType {
  /** Applies to subscription plans */
  PLAN = 'plan',
  /** Applies to shop items */
  SHOP_ITEM = 'shop_item',
  /** Applies to both plans and shop items */
  ALL = 'all',
}

/**
 * Status of the promotion
 * @enum {string}
 */
export enum PromotionStatus {
  /** Active promotion */
  ACTIVE = 'active',
  /** Inactive promotion */
  INACTIVE = 'inactive',
  /** Expired promotion */
  EXPIRED = 'expired',
  /** Scheduled promotion (not yet started) */
  SCHEDULED = 'scheduled',
}

@Entity()
export class Promotion extends AuditableBaseEntity {
  @Column({ name: 'name' })
  name: string;

  @Column({ name: 'description', type: 'text' })
  description: string;

  @Column({
    name: 'promotion_type',
    type: 'enum',
    enum: PromotionType,
    default: PromotionType.PERCENTAGE,
  })
  promotionType: PromotionType;

  @Column({
    name: 'discount_type',
    type: 'enum',
    enum: DiscountType,
  })
  discountType: DiscountType;

  @Column({ name: 'discount_value', type: 'decimal', precision: 10, scale: 2 })
  discountValue: number;

  @Column({
    name: 'applicable_type',
    type: 'enum',
    enum: PromotionApplicableType,
  })
  applicableType: PromotionApplicableType;

  @Column({ name: 'applicable_category_ids', nullable: true, type: 'json' })
  applicableCategoryIds: string[];

  @Column({ name: 'applicable_plan_ids', nullable: true, type: 'json' })
  applicablePlanIds: string[];

  @Column({ name: 'applied_to_plan', nullable: true, type: 'boolean', default: false })
  appliedToPlan: boolean;

  @Column({ name: 'promotion_code', nullable: true })
  promotionCode: string;

  @Column({ name: 'start_date', nullable: true })
  startDate: Date;

  @Column({ name: 'end_date', nullable: true })
  endDate: Date;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'usage_limit', nullable: true, type: 'int' })
  usageLimit: number;

  @Column({ name: 'usage_count', default: 0, type: 'int' })
  usageCount: number;

  @Column({ name: 'minimum_purchase_amount', nullable: true, type: 'decimal', precision: 10, scale: 2 })
  minimumPurchaseAmount: number;

  @Column({ name: 'maximum_purchase_amount', nullable: true, type: 'decimal', precision: 10, scale: 2 })
  maximumPurchaseAmount: number;

  @Column({ name: 'maximum_discount_amount', nullable: true, type: 'decimal', precision: 10, scale: 2 })
  maximumDiscountAmount: number;

  /**
   * Get the current status of the promotion
   * @returns The current status
   */
  getStatus(): PromotionStatus {
    const now = new Date();

    if (!this.isActive) {
      return PromotionStatus.INACTIVE;
    }

    if (this.startDate && now < this.startDate) {
      return PromotionStatus.SCHEDULED;
    }

    if (this.endDate && now > this.endDate) {
      return PromotionStatus.EXPIRED;
    }

    if (this.usageLimit && this.usageCount >= this.usageLimit) {
      return PromotionStatus.EXPIRED;
    }

    return PromotionStatus.ACTIVE;
  }

  /**
   * Check if the promotion is valid for use
   * @returns Whether the promotion is valid
   */
  isValid(): boolean {
    const now = new Date();

    // Check if promotion is active
    if (!this.isActive) {
      return false;
    }

    // Check if promotion is within date range (if dates are specified)
    if (this.startDate && this.endDate) {
      if (now < this.startDate || now > this.endDate) {
        return false;
      }
    } else if (this.startDate && now < this.startDate) {
      return false;
    } else if (this.endDate && now > this.endDate) {
      return false;
    }

    // Check if promotion has reached usage limit
    if (this.usageLimit && this.usageCount >= this.usageLimit) {
      return false;
    }

    return true;
  }

  /**
   * Calculate the discount amount for a given price
   * @param price The original price
   * @returns The discount amount
   */
  calculateDiscountAmount(price: number): number {
    if (!this.isValid()) {
      return 0;
    }

    let discountAmount = 0;

    // Calculate based on promotionType and discountValue
    if (this.promotionType === PromotionType.PERCENTAGE) {
      discountAmount = price * (this.discountValue / 100);
    } else {
      discountAmount = this.discountValue;
    }

    // Apply maximum discount limit if set
    if (this.maximumDiscountAmount && discountAmount > this.maximumDiscountAmount) {
      discountAmount = this.maximumDiscountAmount;
    }

    // Ensure discount doesn't exceed the price
    if (discountAmount > price) {
      discountAmount = price;
    }

    return discountAmount;
  }

  /**
   * Calculate the final price after applying the promotion
   * @param price The original price
   * @returns The final price after discount
   */
  calculateFinalPrice(price: number): number {
    // Check if promotion is valid
    if (!this.isValid()) {
      return price;
    }

    // Check minimum purchase amount
    if (this.minimumPurchaseAmount && price < this.minimumPurchaseAmount) {
      return price;
    }

    // Check maximum purchase amount
    if (this.maximumPurchaseAmount && price > this.maximumPurchaseAmount) {
      return price;
    }

    const discountAmount = this.calculateDiscountAmount(price);
    return price - discountAmount;
  }
}
