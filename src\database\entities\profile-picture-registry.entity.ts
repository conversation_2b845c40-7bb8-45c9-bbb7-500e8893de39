import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseFileRegistry } from './base-file-registry.entity';
import { ProfilePicture } from './profile-picture.entity';

@Entity()
export class ProfilePictureRegistry extends BaseFileRegistry {
  @Column({ name: 'profile_picture_id' })
  profilePictureId: string;

  @ManyToOne(() => ProfilePicture, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'profile_picture_id' })
  profilePicture: ProfilePicture;

  @Column({ name: 'user_id' })
  userId: string;

  /**
   * Convert to DTO for API responses
   */
  toDto(): any {
    return {
      ...this.toSimpleObject(),
      profilePictureId: this.profilePictureId,
      userId: this.userId,
      profilePicture: this.profilePicture
        ? {
            id: this.profilePicture.id,
            userId: this.profilePicture.userId,
          }
        : null,
    };
  }
}
