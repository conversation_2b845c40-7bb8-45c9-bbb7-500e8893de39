import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEnum, IsNumber, IsOptional, IsBoolean, IsUUID, Min, Max, IsArray, ValidateIf } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ShopItemType } from '../entities/shop-item.entity';
import { PaymentMethod, PurchaseStatus } from '../entities/shop-item-purchase.entity';

/**
 * DTO for publishing a diary skin to the shop
 */
export class PublishDiarySkinToShopDto {
  /**
   * Item number for the shop item
   * @example "SK-001"
   */
  @ApiProperty({
    example: 'SK-001',
    description: 'Item number (unique identifier for the item). If not provided, one will be generated automatically.',
    required: false,
  })
  @IsOptional()
  @IsString()
  itemNumber?: string;

  /**
   * Title for the shop item
   * @example "Modern Blue Diary Skin"
   */
  @ApiProperty({
    example: 'Modern Blue Diary Skin',
    description: 'Title of the item',
  })
  @IsNotEmpty()
  @IsString()
  title: string;

  /**
   * Description for the shop item
   * @example "A modern blue theme with clean typography for your diary"
   */
  @ApiProperty({
    example: 'A modern blue theme with clean typography for your diary',
    description: 'Description of the item',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  /**
   * Category ID for the shop item (optional, will use Skin category if not provided)
   * @example "123e4567-e89b-12d3-a456-************"
   */
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Category ID for the shop item (optional, will use Skin category if not provided)',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  /**
   * Type of shop item
   * @example "IN_APP_PURCHASE"
   */
  @ApiProperty({
    example: ShopItemType.IN_APP_PURCHASE,
    description: 'Type of shop item (FREE items do not require purchase and will have price automatically set to 0)',
    enum: ShopItemType,
    default: ShopItemType.IN_APP_PURCHASE,
  })
  @IsOptional()
  @IsEnum(ShopItemType)
  type?: ShopItemType;

  /**
   * Price of the shop item (0 for free items)
   * @example 9.99
   */
  @ApiProperty({
    example: 9.99,
    description: 'Price of the item',
    default: 0,
  })
  @IsOptional()
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Price must be a valid number' })
  @Min(0, { message: 'Price must be at least 0' })
  @Type(() => Number)
  price?: number;

  /**
   * Whether the item is purchasable with reward points
   * @example true
   */
  @ApiProperty({
    example: true,
    description: 'Whether the item is purchasable with reward points. For FREE items, this will be automatically set to false.',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isPurchasableInRewardpoint?: boolean;

  /**
   * Whether the item is active in the shop
   * @example true
   */
  @ApiProperty({
    example: true,
    description: 'Whether the item is active',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  /**
   * Whether the item is featured in the shop
   * @example false
   */
  @ApiProperty({
    example: false,
    description: 'Whether the item is featured',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isFeatured?: boolean;

  /**
   * Promotion ID (if the item has a promotion applied)
   * @example "123e4567-e89b-12d3-a456-************"
   */
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Promotion ID (if the item has a promotion applied), null for no promotion',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @ValidateIf((o) => o.promotionId !== null && o.promotionId !== undefined && o.promotionId !== '')
  @IsUUID()
  @Transform(({ value }) => {
    // Convert empty strings to null
    if (value === '' || value === undefined) {
      return null;
    }
    return value;
  })
  promotionId?: string | null;

  /**
   * Additional metadata for the shop item
   * @example "modern,blue,clean"
   */
  @ApiProperty({
    example: 'modern,blue,clean',
    description: 'Additional metadata for the shop item. This is passed through exactly as provided without any modifications.',
    required: false,
  })
  @IsOptional()
  @IsString()
  metadata?: string;
}

/**
 * DTO for creating a new shop category
 */
export class CreateShopCategoryDto {
  @ApiProperty({
    example: 'Graphics',
    description: 'Name of the category',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    example: 'Beautiful graphics for your diary',
    description: 'Description of the category',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: true,
    description: 'Whether the category is active',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Parent category ID (for subcategories), null for top-level categories',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    // Convert empty strings to null
    if (value === '' || value === undefined) {
      return null;
    }
    return value;
  })
  @ValidateIf((o) => o.parentId !== null && o.parentId !== undefined && o.parentId !== '')
  @IsUUID()
  parentId?: string | null;

  @ApiProperty({
    example: 1,
    description: 'Display order of the category',
    default: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  displayOrder?: number;

  @ApiProperty({
    example: 'https://example.com/category-image.png',
    description: 'URL for the category image',
    required: false,
  })
  @IsOptional()
  @IsString()
  imageUrl?: string;
}

/**
 * DTO for updating a shop category
 */
export class UpdateShopCategoryDto {
  @ApiProperty({
    example: 'Graphics',
    description: 'Name of the category',
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    example: 'Beautiful graphics for your diary',
    description: 'Description of the category',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: true,
    description: 'Whether the category is active',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Parent category ID (for subcategories), null for top-level categories',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    // Convert empty strings to null
    if (value === '' || value === undefined) {
      return null;
    }
    return value;
  })
  @ValidateIf((o) => o.parentId !== null && o.parentId !== undefined && o.parentId !== '')
  @IsUUID()
  parentId?: string | null;

  @ApiProperty({
    example: 1,
    description: 'Display order of the category',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  displayOrder?: number;

  @ApiProperty({
    example: 'https://example.com/category-image.png',
    description: 'URL for the category image',
    required: false,
  })
  @IsOptional()
  @IsString()
  imageUrl?: string;
}

/**
 * DTO for shop category response
 */
export class ShopCategoryResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ required: false })
  description?: string;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty({ required: false, nullable: true })
  parentId?: string | null;

  @ApiProperty({ required: false })
  parentName?: string;

  @ApiProperty()
  displayOrder: number;

  @ApiProperty({ required: false })
  imageUrl?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ type: [ShopCategoryResponseDto], required: false })
  subcategories?: ShopCategoryResponseDto[];
}

/**
 * DTO for creating a new shop item
 */
export class CreateShopItemDto {
  @ApiProperty({
    example: 'GR-001',
    description: 'Item number (unique identifier for the item). If not provided, one will be generated automatically.',
    required: false,
  })
  @IsOptional()
  @IsString()
  itemNumber?: string;

  @ApiProperty({
    example: 'Beautiful Flower Graphics',
    description: 'Title of the item',
  })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({
    example: 'A set of beautiful flower graphics for your diary',
    description: 'Description of the item',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Category ID',
  })
  @IsNotEmpty()
  @IsUUID()
  categoryId: string;

  @ApiProperty({
    example: ShopItemType.IN_APP_PURCHASE,
    description: 'Type of shop item (FREE items do not require purchase)',
    enum: ShopItemType,
    default: ShopItemType.IN_APP_PURCHASE,
  })
  @IsOptional()
  @IsEnum(ShopItemType)
  type?: ShopItemType;

  @ApiProperty({
    example: 9.99,
    description: 'Price of the item',
    default: 0,
  })
  @IsOptional()
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Price must be a valid number' })
  @Min(0, { message: 'Price must be at least 0' })
  @Type(() => Number)
  price?: number;

  @ApiProperty({
    example: true,
    description: 'Whether the item is purchasable with reward points',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isPurchasableInRewardpoint?: boolean;

  @ApiProperty({
    example: 'uploads/shop-items/flower-graphics.zip',
    description: 'Path to the item file',
    required: false,
  })
  @IsOptional()
  @IsString()
  filePath?: string;

  @ApiProperty({
    example: true,
    description: 'Whether the item is active',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether the item is featured',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isFeatured?: boolean;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Promotion ID (if the item has a promotion applied), null for no promotion',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @ValidateIf((o) => o.promotionId !== null && o.promotionId !== undefined && o.promotionId !== '')
  @IsUUID()
  @Transform(({ value }) => {
    // Convert empty strings to null
    if (value === '' || value === undefined) {
      return null;
    }
    return value;
  })
  promotionId?: string | null;

  @ApiProperty({
    example: 7.99,
    description: 'Discounted price of the item',
    required: false,
  })
  @IsOptional()
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Discounted price must be a valid number' })
  @Min(0, { message: 'Discounted price must be at least 0' })
  @Type(() => Number)
  discountedPrice?: number;

  @ApiProperty({
    example: 'flower,spring,nature',
    description: 'Product tags as comma-separated string',
    required: false,
  })
  @IsOptional()
  @IsString()
  metadata?: string;
}

/**
 * DTO for updating a shop item
 */
export class UpdateShopItemDto {
  @ApiProperty({
    example: 'GR-001',
    description: 'Item number (unique identifier for the item)',
    required: false,
  })
  @IsOptional()
  @IsString()
  itemNumber?: string;

  @ApiProperty({
    example: 'Beautiful Flower Graphics',
    description: 'Title of the item',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    example: 'A set of beautiful flower graphics for your diary',
    description: 'Description of the item',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Category ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiProperty({
    example: ShopItemType.IN_APP_PURCHASE,
    description: 'Type of shop item',
    enum: ShopItemType,
    required: false,
  })
  @IsOptional()
  @IsEnum(ShopItemType, { message: 'Type must be a valid shop item type' })
  type?: ShopItemType;

  @ApiProperty({
    example: 9.99,
    description: 'Price of the item',
    required: false,
  })
  @IsOptional()
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Price must be a valid number' })
  @Min(0, { message: 'Price must be at least 0' })
  @Type(() => Number)
  price?: number;

  @ApiProperty({
    example: true,
    description: 'Whether the item is purchasable with reward points',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isPurchasableInRewardpoint?: boolean;

  @ApiProperty({
    example: 'uploads/shop-items/flower-graphics.zip',
    description: 'Path to the item file',
    required: false,
  })
  @IsOptional()
  @IsString()
  filePath?: string;

  @ApiProperty({
    example: true,
    description: 'Whether the item is active',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether the item is featured',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isFeatured?: boolean;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Promotion ID (if the item has a promotion applied), null for no promotion',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @ValidateIf((o) => o.promotionId !== null && o.promotionId !== undefined && o.promotionId !== '')
  @IsUUID()
  @Transform(({ value }) => {
    // Convert empty strings to null
    if (value === '' || value === undefined) {
      return null;
    }
    return value;
  })
  promotionId?: string | null;

  @ApiProperty({
    example: 7.99,
    description: 'Discounted price of the item',
    required: false,
  })
  @IsOptional()
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Discounted price must be a valid number' })
  @Min(0, { message: 'Discounted price must be at least 0' })
  @Type(() => Number)
  discountedPrice?: number;

  @ApiProperty({
    example: 'flower,spring,nature',
    description: 'Product tags as comma-separated string',
    required: false,
  })
  @IsOptional()
  @IsString()
  metadata?: string;
}

/**
 * DTO for shop item response
 */
export class ShopItemResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  itemNumber: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  categoryId: string;

  @ApiProperty({ required: false })
  categoryName?: string;

  @ApiProperty({ enum: ShopItemType })
  type: ShopItemType;

  @ApiProperty()
  price: number;

  @ApiProperty()
  isPurchasableInRewardpoint: boolean;

  @ApiProperty({ required: false })
  filePath?: string;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  isFeatured: boolean;

  @ApiProperty({ required: false })
  shopItemCategory?: string;

  @ApiProperty({ required: false, nullable: true })
  promotionId?: string | null;

  @ApiProperty()
  isPromotionActive: boolean;
  @ApiProperty({ required: false })
  discountedPrice?: number;

  @ApiProperty({ required: false })
  finalPrice?: number;

  @ApiProperty()
  isOnSale: boolean;

  @ApiProperty({ required: false })
  discountPercentage?: number;

  @ApiProperty({ required: false })
  metadata?: string;

  @ApiProperty({ required: false })
  purchaseCount?: number;

  @ApiProperty({ required: false })
  viewCount?: number;

  @ApiProperty({ required: false })
  createdAt?: Date;

  @ApiProperty({ required: false })
  updatedAt?: Date;
}

/**
 * DTO for shop item with promotion details response
 */
export class ShopItemWithPromotionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  itemNumber: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  categoryId: string;

  @ApiProperty({ required: false })
  categoryName?: string;

  @ApiProperty()
  price: number;

  @ApiProperty({ required: false })
  isPromotionActive: boolean;

  @ApiProperty({ required: false })
  promotionName?: string;

  @ApiProperty({
    required: false,
    description: 'Human-readable discount display (e.g., "20%" or "$40.00")',
  })
  discountDisplay?: string;
}

/**
 * DTO for bulk price update
 */
export class BulkPriceUpdateDto {
  @ApiProperty({
    example: [
      { id: '123e4567-e89b-12d3-a456-************', price: 9.99 },
      { id: '123e4567-e89b-12d3-a456-************', price: 19.99 },
    ],
    description: 'List of items to update prices for',
  })
  @IsNotEmpty()
  @IsArray()
  items: {
    id: string;
    price: number;
  }[];
}

/**
 * DTO for bulk discount update
 */
export class BulkDiscountUpdateDto {
  @ApiProperty({
    example: [
      { id: '123e4567-e89b-12d3-a456-************', discountedPrice: 7.99 },
      { id: '123e4567-e89b-12d3-a456-************', discountedPrice: 15.99 },
    ],
    description: 'List of items to update discounted prices for',
  })
  @IsNotEmpty()
  @IsArray()
  items: {
    id: string;
    discountedPrice: number;
  }[];
}

/**
 * DTO for applying a promotion to shop items
 */
export class ApplyPromotionToItemsDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Promotion ID to apply',
  })
  @IsNotEmpty()
  @IsUUID()
  promotionId: string;

  @ApiProperty({
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    description: 'List of item IDs to apply the promotion to',
  })
  @IsNotEmpty()
  @IsArray()
  itemIds: string[];
}

/**
 * DTO for applying a promotion to items, categories, or plans
 */
export class ApplyPromotionDto {
  @IsString()
  @IsUUID()
  promotionId: string;

  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  itemIds?: string[];

  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  categoryIds?: string[];

  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  planIds?: string[];

  @ValidateIf((o) => !o.itemIds && !o.categoryIds && !o.planIds)
  @IsNotEmpty({ message: 'At least one of itemIds, categoryIds, or planIds must be provided' })
  atLeastOneTarget?: boolean;
}

/**
 * DTO for purchasing a shop item
 */
export class PurchaseShopItemDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Shop item ID to purchase',
  })
  @IsNotEmpty()
  @IsUUID()
  shopItemId: string;

  @ApiProperty({
    example: PaymentMethod.REWARD_POINTS,
    description: 'Payment method',
    enum: PaymentMethod,
  })
  @IsNotEmpty()
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Promotion ID to apply, null for no promotion',
    required: false,
    nullable: true,
  })
  @IsOptional()
  @ValidateIf((o) => o.promotionId !== null && o.promotionId !== undefined && o.promotionId !== '')
  @IsUUID()
  @Transform(({ value }) => {
    // Convert empty strings to null
    if (value === '' || value === undefined) {
      return null;
    }
    return value;
  })
  promotionId?: string | null;

  @ApiProperty({
    example: { cardToken: 'tok_visa' },
    description: 'Payment details (required for credit card payments)',
    required: false,
  })
  @IsOptional()
  @ValidateIf((o) => o.paymentMethod === PaymentMethod.CREDIT_CARD)
  paymentDetails?: any;

  @ApiProperty({
    example: 'Purchase via mobile app',
    description: 'Additional notes for the purchase',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

/**
 * DTO for shop item purchase response
 */
export class ShopItemPurchaseResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  shopItemId: string;

  @ApiProperty({ required: false })
  shopItemTitle?: string;

  @ApiProperty({ required: false })
  userName?: string;

  @ApiProperty({ required: false })
  secureFileUrl?: string;

  @ApiProperty()
  originalPrice: number;

  @ApiProperty()
  finalPrice: number;

  @ApiProperty({ required: false, nullable: true })
  promotionId?: string | null;

  @ApiProperty({ required: false })
  discountAmount?: number;

  @ApiProperty({ enum: PaymentMethod })
  paymentMethod: PaymentMethod;

  @ApiProperty({ enum: PurchaseStatus })
  status: PurchaseStatus;

  @ApiProperty({ required: false })
  paymentDetails?: any;

  @ApiProperty({ required: false })
  notes?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

/**
 * DTO for generating a new item number
 */
export class GenerateItemNumberDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Category ID (required to generate category-specific prefix)',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  categoryId: string;
}

/**
 * DTO for file upload response
 */
export class FileUploadResponseDto {
  @ApiProperty({
    example: 'shop-items/graphics/GR-001-1234567890.png',
    description: 'Path to the uploaded file',
  })
  filePath: string;

  @ApiProperty({
    example: 'GR-001',
    description: 'Generated or provided item number',
  })
  itemNumber: string;
}

/**
 * DTO for shop items grouped by category
 */
export class ShopItemsByCategoryDto {
  @ApiProperty()
  categoryId: string;

  @ApiProperty()
  categoryName: string;

  @ApiProperty({ required: false })
  description?: string;

  @ApiProperty({ required: false })
  imageUrl?: string;

  @ApiProperty({ type: [ShopItemResponseDto] })
  items: ShopItemResponseDto[];
}

/**
 * DTO for shop items grouped by category response
 */
export class GroupedShopItemsResponseDto {
  @ApiProperty({ type: [ShopItemsByCategoryDto] })
  categories: ShopItemsByCategoryDto[];

  @ApiProperty()
  totalCount: number;
}

/**
 * DTO for a draft shop item created from a diary skin
 */
export class DiarySkinDraftShopItemResponseDto {
  @ApiProperty({
    description: 'The ID of the diary skin',
    example: '123e4567-e89b-12d3-a456-************',
  })
  diarySkinId: string;

  @ApiProperty({
    description: 'The name of the diary skin',
    example: 'Modern Blue Theme',
  })
  diarySkinName: string;

  @ApiProperty({
    description: 'The description of the diary skin',
    example: 'A modern blue theme with clean typography',
  })
  diarySkinDescription: string;

  @ApiProperty({
    description: 'The preview image path of the diary skin',
    example: 'uploads/diary-skins/modern-blue.png',
  })
  previewImagePath: string;

  @ApiProperty({
    description: 'The ID of the skin category',
    example: '123e4567-e89b-12d3-a456-************',
  })
  skinCategoryId: string;

  @ApiProperty({
    description: 'The name of the skin category',
    example: 'Skin',
  })
  skinCategoryName: string;

  @ApiProperty({
    description: 'Suggested item number (can be changed)',
    example: 'SK-001',
  })
  suggestedItemNumber: string;

  @ApiProperty({
    description: 'Suggested title (can be changed)',
    example: 'Modern Blue Theme',
  })
  suggestedTitle: string;

  @ApiProperty({
    description: 'Suggested description (can be changed)',
    example: 'A modern blue theme with clean typography',
  })
  suggestedDescription: string;

  @ApiProperty({
    description: 'Suggested price (can be changed)',
    example: 9.99,
  })
  suggestedPrice: number;

  @ApiProperty({
    description: 'Suggested whether the item is purchasable with reward points (can be changed)',
    example: true,
  })
  suggestedIsPurchasableInRewardpoint: boolean;

  @ApiProperty({
    description: 'Suggested shop item type (can be changed)',
    example: ShopItemType.IN_APP_PURCHASE,
    enum: ShopItemType,
  })
  suggestedType: ShopItemType;

  @ApiProperty({
    description: 'Suggested active status (can be changed)',
    example: true,
  })
  suggestedIsActive: boolean;

  @ApiProperty({
    description: 'Suggested featured status (can be changed)',
    example: false,
  })
  suggestedIsFeatured: boolean;

  @ApiProperty({
    description: 'Suggested promotion ID (can be changed)',
    example: null,
    nullable: true,
  })
  suggestedPromotionId: string | null;

  @ApiProperty({
    description: 'Suggested metadata (can be changed)',
    example: 'skinType:diary,isSkinItem:true,originalSkinId:123e4567-e89b-12d3-a456-************',
  })
  suggestedMetadata: string;

  @ApiProperty({
    description: 'Whether the skin is created by a student',
    example: false,
  })
  isStudentCreated: boolean;

  @ApiProperty({
    description: 'The ID of the student who created the skin (if applicable)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  studentId?: string;
}
