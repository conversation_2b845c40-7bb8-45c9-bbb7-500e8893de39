# Migration Guide: Unified Writing Entry Lifecycle

## Overview

This guide helps developers migrate from the legacy writing entry system to the new **unified writing entry lifecycle** that standardizes behavior across all writing modules (diary entries, mission diary entries, and novel entries).

## What Changed

### Status System Unification
**Before (Legacy):**
- Diary: `new`, `submit`, `reviewed`, `confirm`
- Mission: `NEW`, `SUBMITTED`, `REVIEWED`, `CONFIRMED`
- Novel: `new`, `submitted`, `updated`, `correction_given`, `reviewed`, `under_review`, `confirmed`

**After (Unified):**
- All modules: `DRAFT`, `SUBMITTED`, `REVIEWED`, `CONFIRMED`

### API Behavior Changes

#### Update APIs (Save as Draft)
**Before:**
- Created version history entries
- Sent notifications to tutors
- Inconsistent behavior across modules

**After:**
- No version creation (save as draft)
- No notifications sent
- Consistent behavior across all modules
- Entries marked as `isDraft = true`

#### Submit APIs (Create Submitted Version)
**Before:**
- Limited or single submission allowed
- Inconsistent notification patterns
- Different version tracking approaches

**After:**
- Unlimited submissions after each review
- Consistent notification patterns with submission numbers
- Unified version tracking with `isSubmittedVersion` flag
- Submission gating (`canSubmitNewVersion` flag)

#### Review APIs (Unified Requirements)
**Before:**
- Different review patterns per module
- Inconsistent score/correction requirements
- Multiple review attempts allowed

**After:**
- Score required, correction optional (all modules)
- One review per submission (all modules)
- Unlimited feedback allowed (all modules)
- Consistent API signatures across modules

## Database Migration

### Automatic Migration
The system includes automatic database migrations that:

1. **Add Unified Fields**: Adds new unified fields to all writing entry tables
2. **Map Legacy Statuses**: Converts existing status values to unified system
3. **Create Initial Versions**: Generates submitted versions for existing reviewed entries
4. **Update Tracking Fields**: Sets appropriate flags and timestamps
5. **Enable Resubmission**: Allows resubmission for previously reviewed entries

### Migration Files
- `1750576722495-SubmissionDraftLogicRevision.ts` - Adds new unified fields
- `1750576722496-MigrateExistingSubmissionData.ts` - Migrates existing data
- `1750576722497-UnifyWritingEntryLifecycle.ts` - Adds unified status system

## API Migration Guide

### Frontend Changes Required

#### 1. Status Value Updates
**Update status checks in your frontend code:**

```javascript
// Before (Legacy)
if (entry.status === 'new') { /* draft logic */ }
if (entry.status === 'submit') { /* submitted logic */ }
if (entry.status === 'reviewed') { /* reviewed logic */ }

// After (Unified)
if (entry.unifiedStatus === 'DRAFT') { /* draft logic */ }
if (entry.unifiedStatus === 'SUBMITTED') { /* submitted logic */ }
if (entry.unifiedStatus === 'REVIEWED') { /* reviewed logic */ }
```

#### 2. Submission Gating
**Add submission gating logic:**

```javascript
// Before (Legacy)
const canSubmit = entry.status === 'new' || entry.status === 'submit';

// After (Unified)
const canSubmit = entry.canSubmitNewVersion;
```

#### 3. Draft vs Submission Distinction
**Update UI to distinguish between draft updates and submissions:**

```javascript
// Draft update (no notifications, no versions)
const updateDraft = async (entryId, content) => {
  await fetch(`/api/diary/entries/${entryId}`, {
    method: 'PUT',
    body: JSON.stringify({ content })
  });
};

// Submit for review (creates version, sends notifications)
const submitForReview = async (entryId, content) => {
  await fetch(`/api/diary/entries/${entryId}/submit`, {
    method: 'POST',
    body: JSON.stringify({ content })
  });
};
```

#### 4. Review API Updates
**Update tutor review interfaces:**

```javascript
// Before (Legacy) - Different APIs per module
await fetch(`/api/tutor/diary/entries/${entryId}/correction`, {
  method: 'POST',
  body: JSON.stringify({ correction, score })
});

// After (Unified) - Same pattern for all modules
await fetch(`/api/tutor/diary/entries/${entryId}/review`, {
  method: 'POST',
  body: JSON.stringify({ 
    score: 85,           // Required
    correction: "..."    // Optional
  })
});

// Add unlimited feedback
await fetch(`/api/tutor/diary/entries/${entryId}/feedback`, {
  method: 'POST',
  body: JSON.stringify({ feedback: "Great work!" })
});
```

### Backend Changes Required

#### 1. Service Method Updates
**Update service method calls:**

```typescript
// Before (Legacy)
await diaryService.updateEntry(entryId, updateDto); // Created versions

// After (Unified)
await diaryService.updateEntry(entryId, updateDto); // Save as draft only
await diaryService.submitEntry(entryId, submitDto); // Create submitted version
```

#### 2. Status Handling
**Update status handling logic:**

```typescript
// Before (Legacy)
if (entry.status === DiaryEntryStatus.NEW) {
  // Handle draft
}

// After (Unified)
if (entry.unifiedStatus === WritingEntryStatus.DRAFT) {
  // Handle draft
}
```

#### 3. Review Logic
**Update review implementation:**

```typescript
// Before (Legacy) - Module-specific logic
await diaryReviewService.createCorrection(entryId, tutorId, correctionDto);

// After (Unified) - Standardized across modules
await diaryReviewService.submitReview(entryId, tutorId, score, correction);
await diaryReviewService.addFeedback(entryId, tutorId, feedbackText);
```

## Testing Migration

### 1. Status Transition Tests
```javascript
describe('Unified Status Transitions', () => {
  test('should transition from DRAFT to SUBMITTED', async () => {
    const entry = await createDraftEntry();
    expect(entry.unifiedStatus).toBe('DRAFT');
    
    const submitted = await submitEntry(entry.id);
    expect(submitted.unifiedStatus).toBe('SUBMITTED');
    expect(submitted.canSubmitNewVersion).toBe(false);
  });
  
  test('should enable resubmission after review', async () => {
    const reviewed = await submitReview(entryId, tutorId, 85);
    expect(reviewed.unifiedStatus).toBe('REVIEWED');
    expect(reviewed.canSubmitNewVersion).toBe(true);
  });
});
```

### 2. Draft vs Submission Tests
```javascript
describe('Draft vs Submission Logic', () => {
  test('should not create versions for draft updates', async () => {
    const entry = await updateEntry(entryId, { content: 'updated' });
    const versions = await getEntryVersions(entryId);
    expect(versions.filter(v => v.isSubmittedVersion)).toHaveLength(0);
  });
  
  test('should create versions for submissions', async () => {
    await submitEntry(entryId, { content: 'submitted' });
    const versions = await getEntryVersions(entryId);
    expect(versions.filter(v => v.isSubmittedVersion)).toHaveLength(1);
  });
});
```

### 3. Review API Tests
```javascript
describe('Unified Review APIs', () => {
  test('should require score for review', async () => {
    await expect(
      submitReview(entryId, tutorId, { correction: 'good' })
    ).rejects.toThrow('Score is required');
  });
  
  test('should allow unlimited feedback', async () => {
    await addFeedback(entryId, tutorId, 'First feedback');
    await addFeedback(entryId, tutorId, 'Second feedback');
    
    const feedbacks = await getFeedbacks(entryId);
    expect(feedbacks).toHaveLength(2);
  });
});
```

## Rollback Plan

If issues arise, the migration can be rolled back:

1. **Database Rollback**: Run migration rollback commands
2. **Code Rollback**: Revert to legacy API implementations
3. **Status Mapping**: Temporary mapping layer for status compatibility

```bash
# Rollback database migrations
npm run migration:revert -- --to=1750576722494
```

## Support and Resources

- **Unified Lifecycle Documentation**: `docs/api-documentation/unified-writing-lifecycle.md`
- **API Documentation**: Updated module-specific documentation files
- **Migration Support**: Contact development team for migration assistance
- **Testing Guidelines**: Comprehensive test suites for validation

## Timeline

1. **Phase 1**: Database migration (automatic)
2. **Phase 2**: Backend API updates (backward compatible)
3. **Phase 3**: Frontend updates (gradual migration)
4. **Phase 4**: Legacy API deprecation (6 months notice)
5. **Phase 5**: Legacy API removal (after full migration)
