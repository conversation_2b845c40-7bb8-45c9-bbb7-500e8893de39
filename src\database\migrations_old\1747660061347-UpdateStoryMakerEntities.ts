import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateStoryMakerEntities1747660061347 implements MigrationInterface {
  name = 'UpdateStoryMakerEntities1747660061347';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop existing constraints
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT IF EXISTS "FK_story_maker_participation_evaluated_by"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT IF EXISTS "FK_story_maker_participation_story_maker_id"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT IF EXISTS "FK_story_maker_participation_student_id"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT IF EXISTS "FK_story_maker_participation_story_maker"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT IF EXISTS "FK_story_maker_participation_student"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT IF EXISTS "story_maker_participation_score_check"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT IF EXISTS "UQ_student_story_maker"`);

    // Create new tables
    await queryRunner.query(`CREATE TABLE "story_maker_evaluation" (
            "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP DEFAULT now(),
            "created_by" character varying(36),
            "updated_by" character varying(36),
            "submission_id" uuid NOT NULL,
            "tutor_id" uuid NOT NULL,
            "corrections" text,
            "feedback" text,
            "evaluated_at" TIMESTAMP NOT NULL,
            CONSTRAINT "PK_57c5df88c32d6d49d6184fc0b0a" PRIMARY KEY ("id")
        )`);

    await queryRunner.query(`CREATE TABLE "story_maker_submission" (
            "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP DEFAULT now(),
            "created_by" character varying(36),
            "updated_by" character varying(36),
            "participation_id" uuid NOT NULL,
            "content" text NOT NULL,
            "submitted_at" TIMESTAMP NOT NULL,
            "is_evaluated" boolean NOT NULL DEFAULT false,
            CONSTRAINT "PK_e3465d5bb19f3b5d456e079d213" PRIMARY KEY ("id")
        )`);

    // Update story_maker_participation table
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP COLUMN IF EXISTS "feedback"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP COLUMN IF EXISTS "content"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD "first_submitted_at" TIMESTAMP`);

    // Add check constraint
    await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD CONSTRAINT "CHK_0ab49d83154ff371d4c37ab161" CHECK (score > 0)`);

    // Add foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "story_maker_evaluation" ADD CONSTRAINT "FK_6d8de60704ae69ea2f72b263312" FOREIGN KEY ("submission_id") REFERENCES "story_maker_submission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_maker_evaluation" ADD CONSTRAINT "FK_a2dc43a0b4818a2b3b179793546" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_maker_submission" ADD CONSTRAINT "FK_a424e6e7f9d1dc1058373fb6ca0" FOREIGN KEY ("participation_id") REFERENCES "story_maker_participation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_maker_participation" ADD CONSTRAINT "FK_b3f833ff339c99ff633ec723d2f" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_maker_participation" ADD CONSTRAINT "FK_331c686de7c16855ee6d3749781" FOREIGN KEY ("story_maker_id") REFERENCES "story_maker"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_maker_participation" ADD CONSTRAINT "FK_750c3fd7052bd0ed7a2e520af45" FOREIGN KEY ("evaluated_by") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT "FK_750c3fd7052bd0ed7a2e520af45"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT "FK_331c686de7c16855ee6d3749781"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT "FK_b3f833ff339c99ff633ec723d2f"`);
    await queryRunner.query(`ALTER TABLE "story_maker_submission" DROP CONSTRAINT "FK_a424e6e7f9d1dc1058373fb6ca0"`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP CONSTRAINT "FK_a2dc43a0b4818a2b3b179793546"`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP CONSTRAINT "FK_6d8de60704ae69ea2f72b263312"`);

    // Drop check constraint
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT "CHK_0ab49d83154ff371d4c37ab161"`);

    // Revert story_maker_participation table changes
    await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP COLUMN "first_submitted_at"`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD "content" text NOT NULL`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD "feedback" text`);

    // Drop new tables
    await queryRunner.query(`DROP TABLE "story_maker_submission"`);
    await queryRunner.query(`DROP TABLE "story_maker_evaluation"`);

    // Restore original constraints
    await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD CONSTRAINT "UQ_student_story_maker" UNIQUE ("student_id", "story_maker_id")`);
    await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD CONSTRAINT "story_maker_participation_score_check" CHECK ((score > 0))`);
    await queryRunner.query(
      `ALTER TABLE "story_maker_participation" ADD CONSTRAINT "FK_story_maker_participation_student" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_maker_participation" ADD CONSTRAINT "FK_story_maker_participation_story_maker" FOREIGN KEY ("story_maker_id") REFERENCES "story_maker"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_maker_participation" ADD CONSTRAINT "FK_story_maker_participation_student_id" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_maker_participation" ADD CONSTRAINT "FK_story_maker_participation_story_maker_id" FOREIGN KEY ("story_maker_id") REFERENCES "story_maker"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_maker_participation" ADD CONSTRAINT "FK_story_maker_participation_evaluated_by" FOREIGN KEY ("evaluated_by") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
