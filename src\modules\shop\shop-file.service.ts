import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ShopItemRegistry } from '../../database/entities/shop-item-registry.entity';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';

@Injectable()
export class ShopFileService {
  private readonly logger = new Logger(ShopFileService.name);

  constructor(
    @InjectRepository(ShopItemRegistry)
    private readonly shopItemRegistryRepository: Repository<ShopItemRegistry>,
    private readonly fileRegistryService: FileRegistryService,
  ) {}

  /**
   * Upload a file for a shop item
   * @param file File to upload
   * @param itemNumber Item number
   * @param metadata Additional metadata
   * @returns Upload result
   */
  async uploadShopItemFile(file: Express.Multer.File, itemNumber: string, metadata: any): Promise<{ filePath: string; fileName: string }> {
    try {
      if (!file) {
        throw new BadRequestException('No file provided');
      }

      this.logger.log(`Uploading file for shop item ${itemNumber}`);

      // Upload the file using FileRegistryService
      const result = await this.fileRegistryService.uploadFile(FileEntityType.SHOP_ITEM, file, itemNumber, metadata);

      this.logger.log(`File uploaded successfully: ${result.filePath}`);
      return {
        filePath: result.filePath,
        fileName: file.originalname || `${itemNumber}-file`,
      };
    } catch (error) {
      this.logger.error(`Error uploading shop item file: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Get a file URL for a shop item
   * @param shopItemId Shop item ID
   * @returns File URL
   */
  async getShopItemFileUrl(shopItemId: string): Promise<string> {
    try {
      if (!shopItemId) return null;

      // First try to find the registry entry using QueryBuilder
      const registryEntry = await this.shopItemRegistryRepository
        .createQueryBuilder('registry')
        .where('registry.shopItemId = :shopItemId', { shopItemId })
        .orderBy('registry.updatedAt', 'DESC')
        .getOne();

      if (registryEntry) {
        this.logger.log(`Found registry entry for shop item ID: ${shopItemId}`);
        // Generate URL using the registry ID
        return this.fileRegistryService.getFileUrl(FileEntityType.SHOP_ITEM, registryEntry.id);
      }

      // If no registry entry found, fall back to the FileRegistryService's method
      this.logger.log(`No registry entry found, using fallback method for shop item ID: ${shopItemId}`);
      return await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.SHOP_ITEM, shopItemId);
    } catch (error) {
      this.logger.error(`Error generating file URL for shop item ${shopItemId}: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Delete a file for a shop item
   * @param filePath File path to delete
   * @returns Success status
   */
  async deleteShopItemFile(filePath: string): Promise<boolean> {
    try {
      if (!filePath) {
        this.logger.warn('No file path provided for deletion');
        return false;
      }

      // Find any registry entries that use this file path
      const registryEntries = await this.shopItemRegistryRepository.createQueryBuilder('registry').where('registry.filePath = :filePath', { filePath }).getMany();

      if (registryEntries.length > 0) {
        this.logger.log(`Found ${registryEntries.length} registry entries using file path: ${filePath}`);
        // Consider updating registry entries or logging them
      }

      // Delete the file (this method doesn't return a Promise, so no await needed)
      this.fileRegistryService.deleteFile(filePath);
      this.logger.log(`Deleted file: ${filePath}`);
      return true;
    } catch (error) {
      this.logger.error(`Error deleting shop item file: ${error.message}`, error.stack);
      return false;
    }
  }
}
