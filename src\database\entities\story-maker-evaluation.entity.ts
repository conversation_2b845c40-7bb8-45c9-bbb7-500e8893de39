import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { StoryMakerSubmission } from './story-maker-submission.entity';

@Entity()
export class StoryMakerEvaluation extends AuditableBaseEntity {
  @Column({ name: 'submission_id', type: 'uuid' })
  submissionId: string;

  // AI-generated feedback instead of tutor corrections
  @Column({ name: 'ai_feedback', type: 'text', nullable: true })
  aiFeedback: string;

  // Detailed scoring breakdown
  @Column({ name: 'sentence_count', nullable: false })
  sentenceCount: number;

  @Column({ name: 'sentence_score', nullable: false })
  sentenceScore: number; // = sentence_count

  @Column({ name: 'creativity_score', nullable: false })
  creativityScore: number; // 1-5 (AI evaluated)

  @Column({ name: 'sentence_power_score', nullable: false })
  sentencePowerScore: number; // 1-3 (AI evaluated)

  @Column({ name: 'participation_score', nullable: false })
  participationScore: number; // 1-5 (word count based)

  @Column({ name: 'accuracy_score', nullable: false })
  accuracyScore: number; // 1-3 (AI grammar check)

  @Column({ name: 'popularity_score', nullable: false, default: 0 })
  popularityScore: number; // 1-5 (updated by scheduled job)

  @Column({ name: 'relevance_score', nullable: true })
  relevanceScore: number; // 1-5 (AI evaluated relevance to image, only when image context available)

  @Column({ name: 'total_score', nullable: false })
  totalScore: number;

  // Supporting data
  @Column({ name: 'word_count', nullable: false })
  wordCount: number;

  @Column({ name: 'grammar_error_count', nullable: false })
  grammarErrorCount: number;

  @Column({ name: 'ai_evaluation_data', type: 'jsonb', nullable: true })
  aiEvaluationData: any; // Full Gemini API response

  @Column({ name: 'evaluated_at', nullable: false })
  evaluatedAt: Date; // Immediate upon submission

  // Relationships
  @ManyToOne(() => StoryMakerSubmission, (submission) => submission.evaluations)
  @JoinColumn({ name: 'submission_id' })
  submission: StoryMakerSubmission;
}
