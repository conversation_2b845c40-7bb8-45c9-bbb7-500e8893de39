import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThanOrEqual, MoreThanOrEqual, DataSource } from 'typeorm';
import { DiaryMission } from '../../database/entities/diary-mission.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { MissionEntryStatus } from '../../database/entities/mission-diary-entry.entity';
import { Category } from '../../database/entities/category.entity';
import { MissionFilterDto, CreateDiaryMissionDto, UpdateDiaryMissionDto, DiaryMissionResponseDto } from '../../database/models/diary-mission.dto';
import { CategoryResponseDto } from '../../database/models/category.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { TutorMatchingService } from '../tutor-matching/tutor-matching.service';
import { getCurrentUTCDate, parseYYYYMMDDToUTC } from '../../common/utils/date-utils';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';

@Injectable()
export class DiaryMissionService {
  private readonly logger = new Logger(DiaryMissionService.name);

  constructor(
    @InjectRepository(DiaryMission)
    private readonly diaryMissionRepository: Repository<DiaryMission>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Category)
    private readonly categoryRepository: Repository<Category>,
    private readonly dataSource: DataSource,
    private readonly notificationHelper: NotificationHelperService,
    private readonly tutorMatchingService: TutorMatchingService,
    private readonly deeplinkService: DeeplinkService,
  ) {}

  /**
   * Create a new diary mission
   * @param adminId ID of the admin creating the mission
   * @param createDto Mission creation data
   * @returns The created mission
   */
  async createMission(adminId: string, createDto: CreateDiaryMissionDto): Promise<DiaryMissionResponseDto> {
    try {
      // Verify the admin exists
      const admin = await this.userRepository.findOne({ where: { id: adminId } });
      if (!admin) {
        throw new NotFoundException(`Admin with ID ${adminId} not found`);
      }

      // Verify the category exists and is active (if provided)
      if (createDto.categoryId) {
        const category = await this.categoryRepository.findOne({
          where: { id: createDto.categoryId, isActive: true }
        });
        if (!category) {
          throw new NotFoundException(`Category with ID ${createDto.categoryId} not found or inactive`);
        }
      }

      // Create the mission
      const mission = this.diaryMissionRepository.create({
        title: createDto.title,
        description: createDto.description,
        categoryId: createDto.categoryId,
        targetWordCount: createDto.targetWordCount,
        targetMaxWordCount: createDto.targetMaxWordCount,
        publishDate: new Date(createDto.publishDate),
        expiryDate: createDto.expiryDate ? new Date(createDto.expiryDate) : null,
        adminId: adminId, // We're keeping the field name but using it to store the admin ID
        isActive: true,
        score: createDto.score,
      });

      // Save the mission
      const savedMission = await this.diaryMissionRepository.save(mission);
      this.logger.log(`Created mission with ID ${savedMission.id} by admin ${adminId}`);

      return this.mapToResponseDto(savedMission);
    } catch (error) {
      this.logger.error(`Error creating mission: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update an existing diary mission
   * @param id Mission ID
   * @param adminId ID of the admin updating the mission
   * @param updateDto Mission update data
   * @returns The updated mission
   */
  async updateMission(id: string, adminId: string, updateDto: UpdateDiaryMissionDto): Promise<DiaryMissionResponseDto> {
    try {
      // Find the mission
      const mission = await this.diaryMissionRepository.findOne({
        where: { id },
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${id} not found`);
      }

      // Verify category if being updated (and not null)
      if (updateDto.categoryId !== undefined && updateDto.categoryId !== null) {
        const category = await this.categoryRepository.findOne({
          where: { id: updateDto.categoryId, isActive: true }
        });
        if (!category) {
          throw new NotFoundException(`Category with ID ${updateDto.categoryId} not found or inactive`);
        }
      }

      // Update the mission
      if (updateDto.title !== undefined) mission.title = updateDto.title;
      if (updateDto.description !== undefined) mission.description = updateDto.description;
      if (updateDto.categoryId !== undefined) mission.categoryId = updateDto.categoryId;
      if (updateDto.targetWordCount !== undefined) mission.targetWordCount = updateDto.targetWordCount;
      if (updateDto.targetMaxWordCount !== undefined) mission.targetMaxWordCount = updateDto.targetMaxWordCount;
      if (updateDto.publishDate !== undefined) mission.publishDate = new Date(updateDto.publishDate);
      if (updateDto.expiryDate !== undefined) mission.expiryDate = updateDto.expiryDate ? new Date(updateDto.expiryDate) : null;
      if (updateDto.isActive !== undefined) mission.isActive = updateDto.isActive;
      if (updateDto.score !== undefined) mission.score = updateDto.score;

      // Save the updated mission
      const updatedMission = await this.diaryMissionRepository.save(mission);
      this.logger.log(`Updated mission with ID ${updatedMission.id} by admin ${adminId}`);

      return this.mapToResponseDto(updatedMission);
    } catch (error) {
      this.logger.error(`Error updating mission: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a specific diary mission by ID
   * @param id Mission ID
   * @returns The mission
   */
  async getMission(id: string): Promise<DiaryMissionResponseDto> {
    try {
      const mission = await this.diaryMissionRepository.findOne({
        where: { id },
        relations: ['category'],
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${id} not found`);
      }

      // Handle missing category by loading it separately or assigning default
      if (!mission.category && mission.categoryId) {
        try {
          const categoryRepository = this.dataSource.getRepository('category');
          const category = await categoryRepository.findOne({
            where: { id: mission.categoryId }
          });
          if (category) {
            mission.category = category as any;
            this.logger.log(`Loaded missing category ${category.name} for mission ${mission.id}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to load category ${mission.categoryId} for mission ${mission.id}: ${error.message}`);
        }
      }

      // If still no category, assign a default one
      if (!mission.category) {
        try {
          const categoryRepository = this.dataSource.getRepository('category');
          const defaultCategory = await categoryRepository.findOne({
            where: { name: 'General' }
          });
          if (defaultCategory) {
            mission.category = defaultCategory as any;
            this.logger.log(`Assigned default category 'General' to mission ${mission.id}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to load default category for mission ${mission.id}: ${error.message}`);
        }
      }

      return this.mapToResponseDto(mission);
    } catch (error) {
      this.logger.error(`Error getting mission: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a specific diary mission for a student
   * @param id Mission ID
   * @param studentId Student ID
   * @returns The mission if it's available to the student
   */
  async getStudentMission(id: string, studentId: string): Promise<DiaryMissionResponseDto> {
    try {
      // Get the mission
      const mission = await this.diaryMissionRepository.findOne({
        where: { id },
        relations: ['category'],
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${id} not found`);
      }

      // All missions are available to all students

      // Check if the mission is active
      if (!mission.isActive) {
        throw new ForbiddenException('This mission is not active');
      }

      // Get current date
      const now = getCurrentUTCDate();

      // Check if the mission has expired
      if (mission.expiryDate && mission.expiryDate < now) {
        throw new ForbiddenException('This mission has expired');
      }

      // Handle missing category by loading it separately or assigning default
      if (!mission.category && mission.categoryId) {
        try {
          const categoryRepository = this.dataSource.getRepository('category');
          const category = await categoryRepository.findOne({
            where: { id: mission.categoryId }
          });
          if (category) {
            mission.category = category as any;
            this.logger.log(`Loaded missing category ${category.name} for mission ${mission.id}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to load category ${mission.categoryId} for mission ${mission.id}: ${error.message}`);
        }
      }

      // If still no category, assign a default one
      if (!mission.category) {
        try {
          const categoryRepository = this.dataSource.getRepository('category');
          const defaultCategory = await categoryRepository.findOne({
            where: { name: 'General' }
          });
          if (defaultCategory) {
            mission.category = defaultCategory as any;
            this.logger.log(`Assigned default category 'General' to mission ${mission.id}`);
          }
        } catch (error) {
          this.logger.warn(`Failed to load default category for mission ${mission.id}: ${error.message}`);
        }
      }

      // Check if the student has already completed this mission
      const completedEntry = await this.dataSource
        .getRepository('mission_diary_entry')
        .createQueryBuilder('entry')
        .where('entry.studentId = :studentId', { studentId })
        .andWhere('entry.missionId = :missionId', { missionId: mission.id })
        .andWhere('entry.status IN (:...statuses)', {
          statuses: [
            MissionEntryStatus.SUBMITTED,
            MissionEntryStatus.REVIEWED,
            MissionEntryStatus.CONFIRMED,
            // Include legacy values for backward compatibility
            MissionEntryStatus.LEGACY_SUBMIT,
            MissionEntryStatus.LEGACY_REVIEWED,
            MissionEntryStatus.LEGACY_CONFIRM,
          ],
        }) // Submitted or reviewed entries
        .getOne();

      const missionDto = await this.mapToResponseDto(mission, studentId);

      if (completedEntry) {
        // Add a note that this mission has been completed
        missionDto.description = `[COMPLETED] ${missionDto.description}`;
      }

      return missionDto;
    } catch (error) {
      this.logger.error(`Error getting student mission: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all diary missions with filtering and pagination
   * @param filterDto Filter criteria
   * @param paginationDto Pagination options
   * @returns Paged list of missions
   */
  async getMissions(filterDto: MissionFilterDto = {}, paginationDto: PaginationDto = { page: 1, limit: 10 }): Promise<PagedListDto<DiaryMissionResponseDto>> {
    try {
      const { page = 1, limit = 10 } = paginationDto;
      const skip = (page - 1) * limit;

      // Build query
      const queryBuilder = this.diaryMissionRepository.createQueryBuilder('mission')
        .leftJoinAndSelect('mission.tutor', 'tutor')
        .leftJoinAndSelect('mission.category', 'category')
        .orderBy('mission.publishDate', 'DESC');

      // Apply filters
      if (filterDto.isActive !== undefined) {
        queryBuilder.andWhere('mission.isActive = :isActive', { isActive: filterDto.isActive });
      }

      if (filterDto.publishDateFrom) {
        const fromDate = parseYYYYMMDDToUTC(filterDto.publishDateFrom);
        queryBuilder.andWhere('mission.publishDate >= :fromDate', { fromDate });
      }

      if (filterDto.publishDateTo) {
        const toDate = parseYYYYMMDDToUTC(filterDto.publishDateTo);
        queryBuilder.andWhere('mission.publishDate <= :toDate', { toDate });
      }

      if (filterDto.createdBy) {
        queryBuilder.andWhere('mission.adminId = :adminId', { adminId: filterDto.createdBy });
      }

      // Get total count
      const totalItems = await queryBuilder.getCount();

      // Get paginated results
      const missions = await queryBuilder.skip(skip).take(limit).getMany();

      // Map to response DTOs
      const items = await Promise.all(missions.map((mission) => this.mapToResponseDto(mission)));

      return new PagedListDto(items, totalItems, page, limit);
    } catch (error) {
      this.logger.error(`Error getting missions: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all missions for tutors
   * @param filterDto Filter criteria
   * @param paginationDto Pagination options
   * @returns Paged list of missions
   */
  async getTutorMissions(
    tutorId: string, // Parameter kept for backward compatibility
    filterDto: MissionFilterDto = {},
    paginationDto: PaginationDto = { page: 1, limit: 10 },
  ): Promise<PagedListDto<DiaryMissionResponseDto>> {
    // Return all missions without filtering by creator
    return this.getMissions(filterDto, paginationDto);
  }

  /**
   * Get missions available to a specific student
   * @param studentId Student ID
   * @param filterDto Filter criteria
   * @param paginationDto Pagination options
   * @returns Paged list of missions
   */
  async getStudentMissions(studentId: string, filterDto: MissionFilterDto = {}, paginationDto: PaginationDto = { page: 1, limit: 10 }): Promise<PagedListDto<DiaryMissionResponseDto>> {
    try {
      const { page = 1, limit = 10 } = paginationDto;
      const skip = (page - 1) * limit;

      // Build query - no longer filtering by tutor IDs
      const queryBuilder = this.diaryMissionRepository.createQueryBuilder('mission')
        .leftJoinAndSelect('mission.category', 'category')
        .where('mission.isActive = :isActive', { isActive: true })
        .orderBy('mission.publishDate', 'DESC');

      // Add expiry date condition if applicable
      queryBuilder.andWhere('(mission.expiryDate IS NULL OR mission.expiryDate >= :now)', { now: getCurrentUTCDate() });

      // Apply additional filters
      if (filterDto.publishDateFrom) {
        const fromDate = parseYYYYMMDDToUTC(filterDto.publishDateFrom);
        queryBuilder.andWhere('mission.publishDate >= :fromDate', { fromDate });
      }

      if (filterDto.publishDateTo) {
        const toDate = parseYYYYMMDDToUTC(filterDto.publishDateTo);
        queryBuilder.andWhere('mission.publishDate <= :toDate', { toDate });
      }

      if (filterDto.createdBy) {
        queryBuilder.andWhere('mission.tutorId = :tutorId', { tutorId: filterDto.createdBy });
      }

      // Get total count
      const totalItems = await queryBuilder.getCount();

      // Get paginated results
      const missions = await queryBuilder.skip(skip).take(limit).getMany();

      // Map to response DTOs
      const items = await Promise.all(missions.map((mission) => this.mapToResponseDto(mission, studentId)));

      return new PagedListDto(items, totalItems, page, limit);
    } catch (error) {
      this.logger.error(`Error getting student missions: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get today's featured mission for a student
   * @param studentId Student ID
   * @returns The featured mission for today
   */
  async getTodaysMission(studentId: string): Promise<DiaryMissionResponseDto> {
    try {
      // Get current date
      const now = getCurrentUTCDate();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      // First, check if there's a mission specifically published today
      const todayEnd = new Date(today);
      todayEnd.setHours(23, 59, 59, 999);

      let mission = await this.diaryMissionRepository
        .createQueryBuilder('mission')
        .where('mission.isActive = :isActive', { isActive: true })
        .andWhere('mission.publishDate >= :todayStart AND mission.publishDate <= :todayEnd', {
          todayStart: today,
          todayEnd: todayEnd
        })
        .andWhere('(mission.expiryDate IS NULL OR mission.expiryDate >= :now)', { now })
        .orderBy('mission.publishDate', 'DESC')
        .getOne();

      // If no mission was published today, check if the student has already started any mission
      if (!mission) {
        // Check if the student has any mission entries in progress
        const inProgressEntry = await this.dataSource
          .getRepository('mission_diary_entry')
          .createQueryBuilder('entry')
          .leftJoinAndSelect('entry.mission', 'mission')
          .leftJoinAndSelect('mission.tutor', 'tutor')
          .where('entry.studentId = :studentId', { studentId })
          .andWhere('entry.status = :status', { status: 'new' }) // Only draft entries
          .andWhere('mission.isActive = :isActive', { isActive: true })
          .andWhere('mission.publishDate <= :now', { now })
          .andWhere('(mission.expiryDate IS NULL OR mission.expiryDate >= :now)', { now })
          .orderBy('entry.createdAt', 'DESC')
          .getOne();

        if (inProgressEntry) {
          mission = inProgressEntry.mission;
        }
      }

      // If still no mission found, get the most recent active mission
      if (!mission) {
        mission = await this.diaryMissionRepository
          .createQueryBuilder('mission')
          .leftJoinAndSelect('mission.tutor', 'tutor')
          .where('mission.isActive = :isActive', { isActive: true })
          .andWhere('mission.publishDate <= :now', { now })
          .andWhere('(mission.expiryDate IS NULL OR mission.expiryDate >= :now)', { now })
          .orderBy('mission.publishDate', 'DESC')
          .getOne();
      }

      // If still no mission found, check if there are any upcoming missions
      if (!mission) {
        mission = await this.diaryMissionRepository
          .createQueryBuilder('mission')
          .leftJoinAndSelect('mission.tutor', 'tutor')
          .where('mission.isActive = :isActive', { isActive: true })
          .andWhere('mission.publishDate > :now', { now })
          .orderBy('mission.publishDate', 'ASC') // Get the next upcoming mission
          .getOne();

        if (mission) {
          // Add a note that this mission is upcoming
          const missionDto = await this.mapToResponseDto(mission, studentId);
          missionDto.description = `[UPCOMING - Available from ${mission.publishDate.toLocaleDateString()}] ${missionDto.description}`;
          return missionDto;
        }
      }

      if (!mission) {
        throw new NotFoundException('No active missions available for today');
      }

      // Check if the student has already completed this mission
      const completedEntry = await this.dataSource
        .getRepository('mission_diary_entry')
        .createQueryBuilder('entry')
        .where('entry.studentId = :studentId', { studentId })
        .andWhere('entry.missionId = :missionId', { missionId: mission.id })
        .andWhere('entry.status IN (:...statuses)', {
          statuses: [
            MissionEntryStatus.SUBMITTED,
            MissionEntryStatus.REVIEWED,
            MissionEntryStatus.CONFIRMED,
            // Include legacy values for backward compatibility
            MissionEntryStatus.LEGACY_SUBMIT,
            MissionEntryStatus.LEGACY_REVIEWED,
            MissionEntryStatus.LEGACY_CONFIRM,
          ],
        }) // Submitted or reviewed entries
        .getOne();

      if (mission) {
        const missionDto = await this.mapToResponseDto(mission, studentId);
        if (completedEntry) {
          missionDto.description = `[COMPLETED] ${missionDto.description}`;
        }
        return missionDto;
      }

      if (!mission) {
        throw new NotFoundException('No active missions available for today');
      }
    } catch (error) {
      this.logger.error(`Error getting today's mission: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete a diary mission
   * @param id Mission ID
   * @param adminId ID of the admin deleting the mission
   */
  async deleteMission(id: string, ad?: string): Promise<void> {
    try {
      // Check if mission exists first
      const mission = await this.diaryMissionRepository.findOne({
        where: { id },
        relations: ['entries'],
      });

      if (!mission) {
        throw new NotFoundException(`Mission with ID ${id} not found`);
      }

      // Check if mission has associated entries
      if (mission.entries && mission.entries.length > 0) {
        throw new BadRequestException({
          message: 'Cannot delete mission with associated entries',
          code: 'MISSION_HAS_ENTRIES',
          details: {
            missionId: id,
            entriesCount: mission.entries.length,
          },
        });
      }

      await this.diaryMissionRepository.remove(mission);
    } catch (error) {
      this.logger.error(`Error deleting mission ${id}: ${error.message}`, error.stack);

      // Handle foreign key constraint violation
      if (error.code === '23503') {
        // PostgreSQL foreign key violation code
        throw new BadRequestException({
          message: 'Cannot delete mission because it is referenced by diary entries',
          code: 'MISSION_FK_CONSTRAINT',
          details: {
            missionId: id,
            constraint: error.constraint,
          },
        });
      }

      // Re-throw other errors with proper formatting
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException({
        message: 'An error occurred while deleting the mission',
        code: 'MISSION_DELETE_ERROR',
        details: {
          missionId: id,
        },
      });
    }
  }

  /**
   * Map a DiaryMission entity to a DiaryMissionResponseDto
   * @param mission The mission entity
   * @param tutor The tutor user entity (optional)
   * @returns The mapped DTO
   */
  private async calculateProgress(mission: DiaryMission, studentId?: string): Promise<number> {
    if (!studentId) {
      return 0;
    }

    try {
      // First try to find a submitted/reviewed entry
      const submittedEntry = await this.dataSource
        .getRepository('mission_diary_entry')
        .createQueryBuilder('entry')
        .where('entry.missionId = :missionId', { missionId: mission.id })
        .andWhere('entry.studentId = :studentId', { studentId })
        .andWhere('entry.status IN (:...statuses)', {
          statuses: [
            MissionEntryStatus.SUBMITTED,
            MissionEntryStatus.REVIEWED,
            MissionEntryStatus.CONFIRMED,
            MissionEntryStatus.LEGACY_SUBMIT,
            MissionEntryStatus.LEGACY_REVIEWED,
            MissionEntryStatus.LEGACY_CONFIRM,
          ],
        })
        .orderBy('entry.updatedAt', 'DESC')
        .getOne();

      if (submittedEntry) {
        return Math.min(Math.round((submittedEntry.wordCount / mission.targetWordCount) * 100), 100);
      }

      // If no submitted entry, find the latest draft
      const draftEntry = await this.dataSource
        .getRepository('mission_diary_entry')
        .createQueryBuilder('entry')
        .where('entry.missionId = :missionId', { missionId: mission.id })
        .andWhere('entry.studentId = :studentId', { studentId })
        .andWhere('entry.status = :status', { status: MissionEntryStatus.NEW })
        .orderBy('entry.updatedAt', 'DESC')
        .getOne();

      if (!draftEntry) {
        return 0;
      }

      return Math.min(Math.round((draftEntry.wordCount / mission.targetWordCount) * 100), 100);
    } catch (error) {
      this.logger.error(`Error calculating progress: ${error.message}`);
      return 0;
    }
  }

  private async mapToResponseDto(mission: DiaryMission, studentId?: string): Promise<DiaryMissionResponseDto> {
    const progress = await this.calculateProgress(mission, studentId);

    // Map category entity to CategoryResponseDto
    const categoryDto: CategoryResponseDto | null = mission.category ? {
      id: mission.category.id,
      name: mission.category.name,
      description: mission.category.description,
      color: mission.category.color,
      isActive: mission.category.isActive,
      sortOrder: mission.category.sortOrder,
      createdAt: mission.category.createdAt,
      updatedAt: mission.category.updatedAt,
    } : null;

    return {
      id: mission.id,
      title: mission.title,
      description: mission.description,
      category: categoryDto,
      targetWordCount: mission.targetWordCount,
      targetMaxWordCount: mission.targetMaxWordCount,
      publishDate: mission.publishDate,
      expiryDate: mission.expiryDate,
      adminId: mission.adminId,
      createdBy: mission.adminId,
      progress: progress,
      isActive: mission.isActive,
      score: mission.score,
      createdAt: mission.createdAt,
      updatedAt: mission.updatedAt,
    };
  }
}
