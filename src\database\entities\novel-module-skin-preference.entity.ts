import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { DiarySkin } from './diary-skin.entity';

@Entity()
export class NovelModuleSkinPreference extends AuditableBaseEntity {
  @Column({ name: 'student_id' })
  studentId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  @Column({ name: 'default_skin_id' })
  defaultSkinId: string;

  @ManyToOne(() => DiarySkin)
  @JoinColumn({ name: 'default_skin_id' })
  defaultSkin: DiarySkin;
}
