import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { StudentFriendshipController } from './student-friendship.controller';
import { StudentFriendshipService } from './student-friendship.service';
import { StudentFriendship } from '../../database/entities/student-friendship.entity';
import { DiaryFollowRequest } from '../../database/entities/diary-follow-request.entity';
import { User } from '../../database/entities/user.entity';
import { Conversation } from '../../database/entities/conversation.entity';
import { DiaryEntryFriendShare } from '../../database/entities/diary-entry-friend-share.entity';
import { CommonModule } from '../../common/common.module';
import { JwtService } from '@nestjs/jwt';
import { ChatModule } from '../chat/chat.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [TypeOrmModule.forFeature([StudentFriendship, DiaryFollowRequest, User, Conversation, DiaryEntryFriendShare]), CommonModule, ChatModule, NotificationModule],
  controllers: [StudentFriendshipController],
  providers: [StudentFriendshipService, JwtService],
  exports: [StudentFriendshipService],
})
export class StudentModule {}
