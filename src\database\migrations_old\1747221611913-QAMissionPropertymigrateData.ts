import { MigrationInterface, QueryRunner } from 'typeorm';

export class QAMissionPropertymigrateData1747221611913 implements MigrationInterface {
  name = 'QAMissionPropertymigrateData1747221611913';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "qa_mission_tasks" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying(50) NOT NULL, "description" text NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "sequence" integer DEFAULT '1', "word_limit_minumum" integer NOT NULL, "word_limit_maximum" integer, "total_score" integer, "deadline" integer, "instructions" text NOT NULL, "mission_id" uuid NOT NULL, CONSTRAINT "PK_8d3da4ef23b295ce9bcfddc588c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "qa_mission_tasks" ADD CONSTRAINT "FK_802c484cd3199676dc0caee3527" FOREIGN KEY ("mission_id") REFERENCES "qa_mission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_mission_tasks" DROP CONSTRAINT "FK_802c484cd3199676dc0caee3527"`);
    await queryRunner.query(`DROP TABLE "qa_mission_tasks"`);
  }
}
