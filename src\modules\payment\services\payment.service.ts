import { Injectable, Logger, NotFoundException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Not } from 'typeorm';
import { PaymentTransaction, PaymentTransactionStatus, PurchaseType, KcpPaymentMethod } from '../../../database/entities/payment-transaction.entity';
import { PaymentWebhook, WebhookStatus, WebhookType } from '../../../database/entities/payment-webhook.entity';
import { ShopItemPurchase, PaymentMethod, PurchaseStatus } from '../../../database/entities/shop-item-purchase.entity';
import { UserPlan } from '../../../database/entities/user-plan.entity';
import { User } from '../../../database/entities/user.entity';
import { KcpService } from './kcp.service';
import { InitiatePaymentDto, PaymentInitiationResponseDto, PaymentStatusResponseDto, ProcessPaymentDto, WebhookPayloadDto, RefundRequestDto, RefundResponseDto } from '../dto/payment.dto';
import { PaymentInitiationRequest } from '../interfaces/kcp.interface';
import { AsyncNotificationHelperService } from '../../notification/async-notification-helper.service';
import { NotificationType } from '../../../database/entities/notification.entity';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  constructor(
    @InjectRepository(PaymentTransaction)
    private readonly paymentTransactionRepository: Repository<PaymentTransaction>,
    @InjectRepository(PaymentWebhook)
    private readonly paymentWebhookRepository: Repository<PaymentWebhook>,
    @InjectRepository(ShopItemPurchase)
    private readonly shopItemPurchaseRepository: Repository<ShopItemPurchase>,
    @InjectRepository(UserPlan)
    private readonly userPlanRepository: Repository<UserPlan>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly kcpService: KcpService,
    private readonly dataSource: DataSource,
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
  ) {}

  /**
   * Initiate payment process
   */
  async initiatePayment(userId: string, initiatePaymentDto: InitiatePaymentDto): Promise<PaymentInitiationResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`Initiating payment for user ${userId}, order ${initiatePaymentDto.orderId}`);

      // Validate user exists
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Generate unique transaction ID
      const transactionId = this.generateTransactionId();

      // Create payment transaction record
      const paymentTransaction = this.paymentTransactionRepository.create({
        transactionId,
        orderId: initiatePaymentDto.orderId,
        amount: initiatePaymentDto.amount,
        currency: initiatePaymentDto.currency || 'KRW',
        paymentMethod: initiatePaymentDto.paymentMethod,
        status: PaymentTransactionStatus.INITIATED,
        userId,
        purchaseType: initiatePaymentDto.purchaseType,
        referenceId: initiatePaymentDto.referenceId,
        requestData: initiatePaymentDto,
        expiresAt: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes
      });

      const savedTransaction = await queryRunner.manager.save(paymentTransaction);

      // Prepare KCP payment request
      const kcpRequest: PaymentInitiationRequest = {
        orderId: initiatePaymentDto.orderId,
        amount: initiatePaymentDto.amount,
        currency: initiatePaymentDto.currency || 'KRW',
        productName: initiatePaymentDto.productName,
        buyerName: initiatePaymentDto.buyerName,
        buyerEmail: initiatePaymentDto.buyerEmail,
        buyerPhone: initiatePaymentDto.buyerPhone,
        paymentMethod: initiatePaymentDto.paymentMethod,
        returnUrl: initiatePaymentDto.returnUrl,
        cancelUrl: initiatePaymentDto.cancelUrl,
        userId,
        purchaseType: initiatePaymentDto.purchaseType,
        referenceId: initiatePaymentDto.referenceId,
        metadata: initiatePaymentDto.metadata,
      };

      // Initiate payment with KCP
      const kcpResponse = await this.kcpService.initiatePayment(kcpRequest);

      if (kcpResponse.success) {
        // Update transaction with KCP response
        savedTransaction.kcpTransactionId = kcpResponse.transactionId;
        savedTransaction.status = PaymentTransactionStatus.PENDING;
        savedTransaction.responseData = kcpResponse;
        await queryRunner.manager.save(savedTransaction);

        await queryRunner.commitTransaction();

        this.logger.log(`Payment initiated successfully: ${transactionId}`);
        return {
          success: true,
          transactionId,
          paymentUrl: kcpResponse.paymentUrl,
          redirectUrl: kcpResponse.redirectUrl,
          message: 'Payment initiated successfully',
          expiresAt: kcpResponse.expiresAt,
        };
      } else {
        // Update transaction with error
        savedTransaction.status = PaymentTransactionStatus.FAILED;
        savedTransaction.errorMessage = kcpResponse.message;
        savedTransaction.responseData = kcpResponse;
        await queryRunner.manager.save(savedTransaction);

        await queryRunner.commitTransaction();

        return {
          success: false,
          transactionId,
          paymentUrl: undefined,
          redirectUrl: undefined,
          message: kcpResponse.message || 'Payment initiation failed',
          errorCode: kcpResponse.errorCode,
        };
      }
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Payment initiation failed: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to initiate payment');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Process payment completion
   */
  async processPayment(processPaymentDto: ProcessPaymentDto): Promise<PaymentStatusResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`Processing payment completion: ${processPaymentDto.transactionId}`);

      // Find payment transaction
      const transaction = await this.paymentTransactionRepository.findOne({
        where: { transactionId: processPaymentDto.transactionId },
      });

      if (!transaction) {
        throw new NotFoundException(`Payment transaction ${processPaymentDto.transactionId} not found`);
      }

      if (transaction.isFinalState()) {
        throw new BadRequestException(`Payment transaction ${processPaymentDto.transactionId} is already in final state`);
      }

      // Update transaction status
      transaction.status = PaymentTransactionStatus.PROCESSING;
      transaction.processedAt = new Date();
      await queryRunner.manager.save(transaction);

      // Verify payment with KCP
      const isValid = await this.kcpService.verifyPayment(processPaymentDto.additionalData);

      if (isValid) {
        // Mark transaction as completed
        transaction.status = PaymentTransactionStatus.COMPLETED;
        transaction.kcpApprovalTime = new Date();
        transaction.responseData = { ...transaction.responseData, ...processPaymentDto.additionalData };
        await queryRunner.manager.save(transaction);

        // Complete the purchase based on type
        if (transaction.purchaseType === PurchaseType.SHOP_ITEM) {
          await this.completeShopItemPurchase(queryRunner, transaction);
        } else if (transaction.purchaseType === PurchaseType.PLAN) {
          await this.completePlanSubscription(queryRunner, transaction);
        }

        await queryRunner.commitTransaction();

        this.logger.log(`Payment completed successfully: ${transaction.transactionId}`);
        return this.mapToStatusResponse(transaction);
      } else {
        // Mark transaction as failed
        transaction.status = PaymentTransactionStatus.FAILED;
        transaction.errorMessage = 'Payment verification failed';
        await queryRunner.manager.save(transaction);

        await queryRunner.commitTransaction();

        return this.mapToStatusResponse(transaction);
      }
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Payment processing failed: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to process payment');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(transactionId: string): Promise<PaymentStatusResponseDto> {
    const transaction = await this.paymentTransactionRepository.findOne({
      where: { transactionId },
    });

    if (!transaction) {
      throw new NotFoundException(`Payment transaction ${transactionId} not found`);
    }

    return this.mapToStatusResponse(transaction);
  }

  /**
   * Process webhook
   */
  async processWebhook(payload: WebhookPayloadDto, signature: string, sourceIp: string): Promise<void> {
    try {
      this.logger.log(`Processing webhook for order: ${payload.ordr_idxx}`);

      // Validate webhook signature
      const isValidSignature = this.kcpService.validateWebhookSignature(JSON.stringify(payload), signature);
      if (!isValidSignature) {
        this.logger.warn(`Invalid webhook signature for order: ${payload.ordr_idxx}`);
        return;
      }

      // Create webhook record
      const webhook = this.paymentWebhookRepository.create({
        transactionId: payload.tno,
        webhookType: this.determineWebhookType(payload),
        status: WebhookStatus.RECEIVED,
        payload,
        signature,
        sourceIp,
      });

      await this.paymentWebhookRepository.save(webhook);

      // Process webhook based on type
      await this.handleWebhookPayload(webhook);
    } catch (error) {
      this.logger.error(`Webhook processing failed: ${error.message}`, error.stack);
    }
  }

  // REMOVED: verifyKcpPayment - Redundant with processKcpPayment

  /**
   * Process refund
   */
  async processRefund(refundRequestDto: RefundRequestDto): Promise<RefundResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`Processing refund for transaction: ${refundRequestDto.transactionId}`);

      // Find the transaction
      const transaction = await queryRunner.manager.findOne(PaymentTransaction, {
        where: { transactionId: refundRequestDto.transactionId },
        relations: ['user'],
      });

      if (!transaction) {
        throw new NotFoundException(`Transaction not found: ${refundRequestDto.transactionId}`);
      }

      // Verify transaction is completed and can be refunded
      if (transaction.status !== PaymentTransactionStatus.COMPLETED) {
        throw new BadRequestException('Only completed transactions can be refunded');
      }

      // Check if refund amount is valid
      const refundAmount = refundRequestDto.amount || transaction.amount;
      if (refundAmount > transaction.amount) {
        throw new BadRequestException('Refund amount cannot exceed transaction amount');
      }

      // Process refund with KCP
      const kcpRefundResult = await this.kcpService.processRefund(transaction.kcpTransactionId, refundAmount, refundRequestDto.reason || 'Refund requested');

      // Update transaction status
      transaction.status = PaymentTransactionStatus.REFUNDED;
      transaction.responseData = {
        ...transaction.responseData,
        refund: {
          ...kcpRefundResult,
          refundedAt: new Date(),
          refundAmount: refundAmount,
          refundReason: refundRequestDto.reason,
        },
      };

      await queryRunner.manager.save(transaction);
      await queryRunner.commitTransaction();

      this.logger.log(`Refund processed successfully: ${refundRequestDto.transactionId}`);

      return {
        success: true,
        transactionId: refundRequestDto.transactionId,
        refundAmount: refundAmount,
        message: 'Refund processed successfully',
        refundId: kcpRefundResult.refundId,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Refund processing failed: ${error.message}`, error.stack);

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException('Failed to process refund');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get user transactions
   */
  async getUserTransactions(userId: string): Promise<any[]> {
    try {
      this.logger.log(`Getting transactions for user: ${userId}`);

      const transactions = await this.paymentTransactionRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
        take: 50, // Limit to last 50 transactions
      });

      return transactions.map((transaction) => ({
        transactionId: transaction.transactionId,
        orderId: transaction.orderId,
        amount: transaction.amount,
        currency: transaction.currency,
        paymentMethod: transaction.paymentMethod,
        status: transaction.status,
        purchaseType: transaction.purchaseType,
        referenceId: transaction.referenceId,
        createdAt: transaction.createdAt,
        processedAt: transaction.processedAt,
        kcpTransactionId: transaction.kcpTransactionId,
        refundAmount: transaction.responseData?.refund?.refundAmount || null,
        refundedAt: transaction.responseData?.refund?.refundedAt || null,
      }));
    } catch (error) {
      this.logger.error(`Failed to get user transactions: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to get user transactions');
    }
  }

  /**
   * Process KCP payment after authentication (equivalent to /kcp_api_pay)
   */
  async processKcpPayment(kcpPaymentData: any): Promise<any> {
    try {
      this.logger.log('Processing KCP payment after authentication...');

      // Step 1: Verify the payment with KCP (Multi-layer verification)
      this.logger.log('Step 1: Verifying payment with KCP...');

      // 1.1: Basic response validation
      if (!kcpPaymentData.res_cd || !kcpPaymentData.tno || !kcpPaymentData.ordr_idxx) {
        return {
          success: false,
          message: 'Invalid KCP payment data - missing required fields',
          details: { kcpData: kcpPaymentData },
        };
      }

      // 1.2: Check if payment was successful according to KCP
      if (kcpPaymentData.res_cd !== '0000') {
        return {
          success: false,
          message: `Payment failed: ${kcpPaymentData.res_msg || 'Unknown error'}`,
          details: {
            kcpCode: kcpPaymentData.res_cd,
            kcpMessage: kcpPaymentData.res_msg,
          },
        };
      }

      // 1.3: Verify with KCP API (double verification)
      const isValid = await this.kcpService.verifyPayment(kcpPaymentData);

      if (!isValid) {
        return {
          success: false,
          message: 'KCP API verification failed',
          details: { kcpData: kcpPaymentData },
        };
      }

      this.logger.log('✅ Step 1 Complete: Payment verified with KCP');

      // Step 2: Find and validate the pending transaction
      this.logger.log('Step 2: Finding and updating transaction...');

      const transaction = await this.paymentTransactionRepository.findOne({
        where: {
          orderId: kcpPaymentData.ordr_idxx,
          status: PaymentTransactionStatus.PENDING,
        },
      });

      if (!transaction) {
        return {
          success: false,
          message: 'Transaction not found or already processed',
          details: { orderId: kcpPaymentData.ordr_idxx },
        };
      }

      // Validate transaction amount matches KCP amount
      const kcpAmount = parseInt(kcpPaymentData.good_mny || '0');
      if (Math.abs(transaction.amount - kcpAmount) > 1) {
        // Allow 1 unit difference for rounding
        return {
          success: false,
          message: 'Amount mismatch between transaction and KCP payment',
          details: {
            transactionAmount: transaction.amount,
            kcpAmount: kcpAmount,
          },
        };
      }

      // Step 3: Update transaction status with comprehensive data
      this.logger.log('Step 3: Updating transaction status...');

      transaction.status = PaymentTransactionStatus.COMPLETED;
      transaction.processedAt = new Date();
      transaction.kcpTransactionId = kcpPaymentData.tno;
      transaction.responseData = {
        ...transaction.responseData,
        kcpPayment: kcpPaymentData,
        verificationTimestamp: new Date().toISOString(),
        paymentMethod: kcpPaymentData.pay_method || transaction.paymentMethod,
      };

      // Save transaction with error handling
      try {
        await this.paymentTransactionRepository.save(transaction);
        this.logger.log('✅ Step 3 Complete: Transaction status updated');
      } catch (error) {
        this.logger.error(`Failed to update transaction: ${error.message}`);
        return {
          success: false,
          message: 'Failed to update transaction status',
          details: { error: error.message },
        };
      }

      // Step 4: Complete the actual checkout process
      this.logger.log('Step 4: Completing checkout process...');

      try {
        await this.completeCheckoutAfterPayment(transaction);
        this.logger.log('✅ Step 4 Complete: Checkout process completed');
      } catch (error) {
        this.logger.error(`Checkout completion failed: ${error.message}`);

        // Rollback transaction status if checkout fails
        transaction.status = PaymentTransactionStatus.FAILED;
        transaction.errorMessage = `Checkout completion failed: ${error.message}`;
        await this.paymentTransactionRepository.save(transaction);

        return {
          success: false,
          message: 'Payment verified but checkout completion failed',
          details: { error: error.message },
        };
      }

      return {
        success: true,
        message: 'Payment completed successfully',
        transactionId: transaction.transactionId,
        orderId: transaction.orderId,
        kcpTransactionId: kcpPaymentData.tno,
      };
    } catch (error) {
      this.logger.error(`KCP payment processing failed: ${error.message}`, error.stack);
      return {
        success: false,
        message: `Payment processing failed: ${error.message}`,
        details: { error: error.message },
      };
    }
  }

  // REMOVED: handleKcpReturn - Redundant with processKcpPayment

  /**
   * Complete checkout after successful payment
   */
  private async completeCheckoutAfterPayment(transaction: any): Promise<void> {
    this.logger.log(`Completing checkout for transaction: ${transaction.transactionId}`);

    // Start database transaction for atomicity
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get transaction metadata
      const metadata = transaction.responseData?.metadata || {};
      const { cartId, userId, rewardPointsUsed } = metadata;

      if (!cartId || !userId) {
        throw new Error('Missing required metadata: cartId or userId');
      }

      // 1. Get the cart and its items
      const cart = await queryRunner.query(
        `SELECT sc.*, sci.id as item_id, sci.shop_item_id, sci.quantity, sci.price,
                si.title, si.reward_points, si.category_id
         FROM shopping_cart sc
         LEFT JOIN shopping_cart_item sci ON sc.id = sci.cart_id
         LEFT JOIN shop_item si ON sci.shop_item_id = si.id
         WHERE sc.id = $1 AND sc.status = 'ACTIVE'`,
        [cartId],
      );

      if (!cart || cart.length === 0) {
        throw new Error(`Cart not found or not active: ${cartId}`);
      }

      // 2. Create purchase records for each item
      const purchases = [];
      let totalRewardPoints = 0;

      for (const item of cart) {
        if (!item.item_id) continue; // Skip if no items in cart

        const now = new Date().toISOString();
        const itemRewardPoints = Number(item.reward_points) * Number(item.quantity);
        totalRewardPoints += itemRewardPoints;

        // Create purchase record
        const purchaseResult = await queryRunner.query(
          `INSERT INTO shop_item_purchase (
             user_id, shop_item_id, quantity, original_price, final_price,
             payment_method, status, payment_transaction_id, category_id,
             notes, created_at, updated_at, metadata
           )
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
           RETURNING id`,
          [
            userId,
            item.shop_item_id,
            item.quantity,
            item.price,
            item.price,
            'kcp_card',
            'COMPLETED',
            transaction.id,
            item.category_id,
            `Purchase completed via KCP payment. Transaction: ${transaction.transactionId}`,
            now,
            now,
            JSON.stringify({
              kcpTransactionId: transaction.kcpTransactionId,
              paymentVerified: true,
              completedAt: now,
            }),
          ],
        );

        const purchaseId = purchaseResult[0].id;
        purchases.push({ id: purchaseId, shopItemId: item.shop_item_id });

        // 3. Add item to user's owned items
        try {
          await queryRunner.query(
            `INSERT INTO student_owned_item (user_id, shop_item_id, purchase_id, acquired_at, created_at, updated_at)
             VALUES ($1, $2, $3, $4, $5, $6)
             ON CONFLICT (user_id, shop_item_id) DO NOTHING`,
            [userId, item.shop_item_id, purchaseId, now, now, now],
          );
        } catch (error) {
          this.logger.warn(`Failed to add owned item ${item.shop_item_id} for user ${userId}: ${error.message}`);
          // Don't fail the entire transaction for this
        }
      }

      // 4. Award reward points for the purchase
      if (totalRewardPoints > 0) {
        await queryRunner.query(
          `INSERT INTO reward_point (user_id, points, source, type, description, created_at, updated_at)
           VALUES ($1, $2, $3, $4, $5, $6, $7)`,
          [userId, totalRewardPoints, 'PURCHASE', 'EARNED', `Reward points earned from purchase. Transaction: ${transaction.transactionId}`, new Date().toISOString(), new Date().toISOString()],
        );
      }

      // 5. Deduct reward points if they were used (from metadata)
      if (rewardPointsUsed && rewardPointsUsed > 0) {
        await queryRunner.query(
          `INSERT INTO reward_point (user_id, points, source, type, description, created_at, updated_at)
           VALUES ($1, $2, $3, $4, $5, $6, $7)`,
          [userId, -rewardPointsUsed, 'PURCHASE', 'USED', `Reward points used for purchase. Transaction: ${transaction.transactionId}`, new Date().toISOString(), new Date().toISOString()],
        );
      }

      // 6. Mark cart as checked out
      await queryRunner.query(
        `UPDATE shopping_cart
         SET status = 'CHECKED_OUT', last_activity = $1, updated_at = $2
         WHERE id = $3`,
        [new Date().toISOString(), new Date().toISOString(), cartId],
      );

      // 7. Update transaction with completion details
      await queryRunner.query(
        `UPDATE payment_transaction
         SET response_data = $1, updated_at = $2
         WHERE id = $3`,
        [
          JSON.stringify({
            ...transaction.responseData,
            checkoutCompleted: true,
            purchaseIds: purchases.map((p) => p.id),
            totalRewardPointsAwarded: totalRewardPoints,
            completedAt: new Date().toISOString(),
          }),
          new Date().toISOString(),
          transaction.id,
        ],
      );

      // Commit the transaction
      await queryRunner.commitTransaction();

      this.logger.log(`✅ Checkout completed successfully for transaction: ${transaction.transactionId}`);
      this.logger.log(`Created ${purchases.length} purchase records, awarded ${totalRewardPoints} reward points`);
    } catch (error) {
      // Rollback on any error
      await queryRunner.rollbackTransaction();
      this.logger.error(`Checkout completion failed for transaction ${transaction.transactionId}: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Generate unique transaction ID
   */
  private generateTransactionId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `TXN-${timestamp}-${random}`.toUpperCase();
  }

  /**
   * Map transaction to status response
   */
  private mapToStatusResponse(transaction: PaymentTransaction): PaymentStatusResponseDto {
    return {
      transactionId: transaction.transactionId,
      status: transaction.status,
      amount: transaction.amount,
      currency: transaction.currency,
      paymentMethod: transaction.paymentMethod,
      completedAt: transaction.processedAt,
      errorMessage: transaction.errorMessage,
      kcpData: transaction.responseData,
    };
  }

  /**
   * Determine webhook type from payload based on KCP transaction codes
   */
  private determineWebhookType(payload: WebhookPayloadDto): WebhookType {
    // Use tx_cd if available (new KCP webhook format)
    if (payload.tx_cd) {
      switch (payload.tx_cd) {
        case 'TX00':
          return WebhookType.VIRTUAL_ACCOUNT_DEPOSIT;
        case 'TX01':
          return WebhookType.CREDIT_CARD_PAYMENT;
        case 'TX02':
          return WebhookType.BANK_TRANSFER;
        case 'TX03':
          return WebhookType.MOBILE_PAYMENT;
        default:
          return WebhookType.STATUS_UPDATE;
      }
    }

    // Fallback to legacy format
    if (payload.res_cd === '0000') {
      return WebhookType.PAYMENT_COMPLETE;
    } else {
      return WebhookType.PAYMENT_FAILED;
    }
  }

  /**
   * Handle webhook payload processing based on KCP official specification
   * Simplified and unified approach for all payment types
   */
  private async handleWebhookPayload(webhook: PaymentWebhook): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const payload = webhook.payload;
      this.logger.log(`Processing webhook for transaction: ${payload.tno}, type: ${payload.tx_cd}`);

      // Find the transaction first
      const transaction = await queryRunner.manager.findOne(PaymentTransaction, {
        where: { kcpTransactionId: payload.tno },
        relations: ['user'],
      });

      if (!transaction) {
        throw new Error(`Transaction not found: ${payload.tno}`);
      }

      // Check if already processed (idempotency)
      if (transaction.status === PaymentTransactionStatus.COMPLETED) {
        this.logger.log(`Transaction already completed: ${payload.tno}`);
        webhook.status = WebhookStatus.PROCESSED;
        webhook.processedAt = new Date();
        await queryRunner.manager.save(webhook);
        await queryRunner.commitTransaction();
        return;
      }

      // Validate payment success
      const isPaymentSuccessful = this.isPaymentSuccessful(payload);
      if (!isPaymentSuccessful) {
        await this.handleFailedPayment(transaction, payload, queryRunner);
        webhook.status = WebhookStatus.PROCESSED;
        webhook.processedAt = new Date();
        await queryRunner.manager.save(webhook);
        await queryRunner.commitTransaction();
        return;
      }

      // Update transaction status
      transaction.status = PaymentTransactionStatus.COMPLETED;
      transaction.processedAt = new Date();
      transaction.responseData = {
        ...transaction.responseData,
        webhookData: payload,
        completedViaWebhook: true,
      };
      await queryRunner.manager.save(transaction);

      // Complete purchase based on type
      await this.completePurchaseByType(transaction, queryRunner);

      // Mark webhook as processed
      webhook.status = WebhookStatus.PROCESSED;
      webhook.processedAt = new Date();
      await queryRunner.manager.save(webhook);

      await queryRunner.commitTransaction();
      this.logger.log(`Webhook processed successfully: ${webhook.id}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Webhook processing failed: ${error.message}`, error.stack);

      // Mark webhook as failed using entity method
      webhook.markAsFailed(error.message);
      await this.paymentWebhookRepository.save(webhook);

      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Complete checkout after webhook processing
   */
  private async completeCheckoutAfterWebhook(transaction: PaymentTransaction, _queryRunner: any): Promise<void> {
    // Only complete checkout if not already completed
    if (transaction.responseData?.checkoutCompleted) {
      this.logger.log(`Checkout already completed for transaction: ${transaction.transactionId}`);
      return;
    }

    // Use the same checkout completion logic as the main payment flow
    await this.completeCheckoutAfterPayment(transaction);

    this.logger.log(`Checkout completed via webhook for transaction: ${transaction.transactionId}`);
  }

  /**
   * Complete shop item purchase
   */
  private async completeShopItemPurchase(queryRunner: any, transaction: PaymentTransaction): Promise<void> {
    // Find and update shop item purchase
    const purchase = await queryRunner.manager.findOne(ShopItemPurchase, {
      where: { id: transaction.referenceId },
    });

    if (purchase) {
      purchase.status = PurchaseStatus.PAYMENT_CONFIRMED;
      purchase.paymentTransactionId = transaction.id;
      await queryRunner.manager.save(purchase);
    }
  }

  /**
   * Complete plan subscription
   */
  private async completePlanSubscription(queryRunner: any, transaction: PaymentTransaction): Promise<void> {
    // Find and update user plan
    const userPlan = await queryRunner.manager.findOne(UserPlan, {
      where: { id: transaction.referenceId },
      relations: ['plan'],
    });

    if (userPlan) {
      // Check if this is an upgrade by looking for metadata in requestData
      const isUpgrade = transaction.requestData && transaction.requestData.metadata && transaction.requestData.metadata.isUpgrade;

      if (isUpgrade) {
        // For upgrades, deactivate previous active plans before activating the new one
        const previousActivePlans = await queryRunner.manager.find(UserPlan, {
          where: {
            userId: userPlan.userId,
            isActive: true,
            id: Not(userPlan.id), // Exclude the current plan being activated
          },
        });

        // Deactivate previous plans
        for (const previousPlan of previousActivePlans) {
          previousPlan.isActive = false;
          previousPlan.autoRenew = false;
          previousPlan.cancellationDate = new Date();
          previousPlan.notes = previousPlan.notes || '';
          if (previousPlan.notes) previousPlan.notes += '\n';
          previousPlan.notes += `Automatically cancelled due to plan upgrade to ${userPlan.plan.name} (${userPlan.plan.id}) on ${new Date().toISOString()}`;

          await queryRunner.manager.save(previousPlan);
          this.logger.log(`Deactivated previous plan ${previousPlan.planId} for user ${userPlan.userId} during upgrade`);
        }
      }

      userPlan.isPaid = true;
      userPlan.isActive = true; // Activate the plan
      userPlan.paymentTransactionId = transaction.id;
      await queryRunner.manager.save(userPlan);
      this.logger.log(`Plan activated for user: ${userPlan.userId}, plan: ${userPlan.planId}`);

      // Send async notification about successful KCP payment and plan activation
      try {
        const plan = userPlan.plan;
        const endDate = new Date(userPlan.endDate);

        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2c3e50;">Payment Successful! Welcome to ${plan.name}! 🎉</h2>
            <p>Your payment has been processed successfully and your subscription is now active!</p>
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #495057; margin-top: 0;">Your Subscription Details:</h3>
              <ul style="color: #6c757d;">
                <li><strong>Plan:</strong> ${plan.name}</li>
                <li><strong>Type:</strong> ${plan.subscriptionType}</li>
                <li><strong>Valid Until:</strong> ${endDate.toLocaleDateString()}</li>
                <li><strong>Transaction ID:</strong> ${transaction.transactionId}</li>
                <li><strong>Auto-Renewal:</strong> ${userPlan.autoRenew ? 'Enabled' : 'Disabled'}</li>
              </ul>
            </div>
            <p>Start exploring all the amazing features your new plan has to offer. We're excited to have you on board!</p>
            <p style="color: #6c757d; font-size: 14px;">Thank you for choosing our premium services.</p>
          </div>
        `;

        // Use the async notification helper to send notification
        await this.asyncNotificationHelper.notifyAsync(
          userPlan.userId,
          NotificationType.SYSTEM,
          'Payment Successful - Plan Activated',
          `Your payment was successful! Welcome to ${plan.name}. Your subscription is now active.`,
          {
            relatedEntityId: userPlan.id,
            relatedEntityType: 'user_plan',
            htmlContent: htmlContent,
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false,
          },
          {
            submissionId: userPlan.id,
            entryType: 'kcp_payment_success',
            priority: 2, // Medium priority
          }
        );
        this.logger.log(`Sent KCP payment success notification to user ${userPlan.userId}`);
      } catch (notificationError) {
        this.logger.error(`Failed to send KCP payment success notification: ${notificationError.message}`, notificationError.stack);
        // Don't fail the payment completion if notification fails
      }
    }
  }

  /**
   * Check if payment is successful based on webhook payload
   */
  private isPaymentSuccessful(payload: any): boolean {
    // Check result code - '0000' indicates success
    if (payload.res_cd && payload.res_cd !== '0000') {
      return false;
    }

    // For virtual account deposits, check if it's not a cancellation
    if (payload.tx_cd === 'TX00' && payload.op_cd === '13') {
      return false; // Deposit cancellation
    }

    return true;
  }

  /**
   * Handle failed payment
   */
  private async handleFailedPayment(transaction: PaymentTransaction, payload: any, queryRunner: any): Promise<void> {
    transaction.status = PaymentTransactionStatus.FAILED;
    transaction.errorMessage = payload.res_msg || 'Payment failed';
    transaction.responseData = {
      ...transaction.responseData,
      webhookData: payload,
      failureReason: payload.res_msg,
    };
    await queryRunner.manager.save(transaction);
    this.logger.warn(`Payment failed for transaction: ${transaction.transactionId}, reason: ${payload.res_msg}`);
  }

  /**
   * Complete purchase based on transaction type
   */
  private async completePurchaseByType(transaction: PaymentTransaction, queryRunner: any): Promise<void> {
    switch (transaction.purchaseType) {
      case PurchaseType.SHOP_ITEM:
        await this.completeShopItemPurchase(queryRunner, transaction);
        break;
      case PurchaseType.PLAN:
        await this.completePlanSubscription(queryRunner, transaction);
        break;
      default:
        this.logger.warn(`Unknown purchase type: ${transaction.purchaseType}`);
    }
  }

  // REMOVED: activatePurchaseInternal - No longer used (replaced by completeCheckoutAfterPayment)
}
