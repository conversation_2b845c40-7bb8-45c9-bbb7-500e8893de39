import { BadRequestException } from '@nestjs/common';

/**
 * Validates an uploaded image file
 * @param file The uploaded file
 * @param options Validation options
 * @returns The validated file
 */
export function validateImageFile(
  file: any,
  options: {
    maxSizeInMB?: number;
    allowedMimeTypes?: string[];
    minWidth?: number;
    minHeight?: number;
    maxWidth?: number;
    maxHeight?: number;
  } = {},
): any {
  if (!file) {
    throw new BadRequestException('No file uploaded');
  }

  // Set default options
  const { maxSizeInMB = 5, allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'], minWidth = 0, minHeight = 0, maxWidth = 0, maxHeight = 0 } = options;

  // Check file size
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  if (file.size > maxSizeInBytes) {
    throw new BadRequestException(`File size exceeds the limit of ${maxSizeInMB}MB`);
  }

  // Check file type
  if (!allowedMimeTypes.includes(file.mimetype)) {
    throw new BadRequestException(`Invalid file type: ${file.mimetype}. Allowed types: ${allowedMimeTypes.join(', ')}`);
  }

  // For image dimension validation, we would need to use a library like sharp or image-size
  // This would require additional dependencies and is left as a future enhancement
  // The implementation would involve reading the image buffer and checking dimensions

  return file;
}
