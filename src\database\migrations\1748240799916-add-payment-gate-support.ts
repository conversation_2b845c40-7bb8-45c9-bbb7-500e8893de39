import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPaymentGateSupport1748240799916 implements MigrationInterface {
  name = 'AddPaymentGateSupport1748240799916';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."payment_transaction_payment_method_enum" AS ENUM('card', 'bank', 'mobile', 'vacct')`);
    await queryRunner.query(`CREATE TYPE "public"."payment_transaction_status_enum" AS ENUM('initiated', 'pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')`);
    await queryRunner.query(`CREATE TYPE "public"."payment_transaction_purchase_type_enum" AS ENUM('shop_item', 'plan')`);
    await queryRunner.query(
      `CREATE TABLE "payment_transaction" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "transaction_id" character varying NOT NULL, "kcp_transaction_id" character varying, "order_id" character varying NOT NULL, "amount" numeric(10,2) NOT NULL, "currency" character varying NOT NULL DEFAULT 'KRW', "payment_method" "public"."payment_transaction_payment_method_enum" NOT NULL, "status" "public"."payment_transaction_status_enum" NOT NULL DEFAULT 'initiated', "payment_status" character varying, "user_id" uuid NOT NULL, "purchase_type" "public"."payment_transaction_purchase_type_enum" NOT NULL, "reference_id" character varying NOT NULL, "kcp_trade_key" character varying, "kcp_approval_key" character varying, "kcp_approval_time" TIMESTAMP, "kcp_card_code" character varying, "kcp_card_name" character varying, "kcp_card_no" character varying, "request_data" jsonb, "response_data" jsonb, "error_message" text, "processed_at" TIMESTAMP, "expires_at" TIMESTAMP, CONSTRAINT "UQ_5d662065a6c8a45d35e77e9cc81" UNIQUE ("transaction_id"), CONSTRAINT "PK_82c3470854cf4642dfb0d7150cd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE TYPE "public"."payment_webhook_webhook_type_enum" AS ENUM('payment_complete', 'payment_failed', 'payment_cancelled', 'refund', 'status_update')`);
    await queryRunner.query(`CREATE TYPE "public"."payment_webhook_status_enum" AS ENUM('received', 'processing', 'processed', 'failed', 'ignored')`);
    await queryRunner.query(
      `CREATE TABLE "payment_webhook" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "transaction_id" character varying NOT NULL, "webhook_type" "public"."payment_webhook_webhook_type_enum" NOT NULL, "status" "public"."payment_webhook_status_enum" NOT NULL DEFAULT 'received', "payload" jsonb NOT NULL, "processed" boolean NOT NULL DEFAULT false, "processed_at" TIMESTAMP, "error_message" text, "retry_count" integer NOT NULL DEFAULT '0', "max_retries" integer NOT NULL DEFAULT '3', "next_retry_at" TIMESTAMP, "signature" character varying, "source_ip" character varying, "user_agent" character varying, CONSTRAINT "PK_2171f35dcdaf3618449fc1cc6bd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "user_plan" ADD "payment_transaction_id" character varying`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ADD "payment_transaction_id" character varying`);
    await queryRunner.query(`ALTER TYPE "public"."shop_item_purchase_payment_method_enum" RENAME TO "shop_item_purchase_payment_method_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."shop_item_purchase_payment_method_enum" AS ENUM('reward_points', 'credit_card', 'free', 'kcp_card', 'kcp_bank', 'kcp_mobile')`);
    await queryRunner.query(
      `ALTER TABLE "shop_item_purchase" ALTER COLUMN "payment_method" TYPE "public"."shop_item_purchase_payment_method_enum" USING "payment_method"::"text"::"public"."shop_item_purchase_payment_method_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."shop_item_purchase_payment_method_enum_old"`);
    await queryRunner.query(`ALTER TYPE "public"."shop_item_purchase_status_enum" RENAME TO "shop_item_purchase_status_enum_old"`);
    await queryRunner.query(
      `CREATE TYPE "public"."shop_item_purchase_status_enum" AS ENUM('completed', 'pending', 'failed', 'refunded', 'payment_pending', 'payment_processing', 'payment_confirmed')`,
    );
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ALTER COLUMN "status" TYPE "public"."shop_item_purchase_status_enum" USING "status"::"text"::"public"."shop_item_purchase_status_enum"`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ALTER COLUMN "status" SET DEFAULT 'completed'`);
    await queryRunner.query(`DROP TYPE "public"."shop_item_purchase_status_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "payment_transaction" ADD CONSTRAINT "FK_0362c4a86732d07164a772f8292" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment_webhook" ADD CONSTRAINT "FK_ce3cb6e42ca8d2cce6bfa974db2" FOREIGN KEY ("transaction_id") REFERENCES "payment_transaction"("transaction_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "payment_webhook" DROP CONSTRAINT "FK_ce3cb6e42ca8d2cce6bfa974db2"`);
    await queryRunner.query(`ALTER TABLE "payment_transaction" DROP CONSTRAINT "FK_0362c4a86732d07164a772f8292"`);
    await queryRunner.query(`CREATE TYPE "public"."shop_item_purchase_status_enum_old" AS ENUM('completed', 'failed', 'pending', 'refunded')`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "shop_item_purchase" ALTER COLUMN "status" TYPE "public"."shop_item_purchase_status_enum_old" USING "status"::"text"::"public"."shop_item_purchase_status_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" ALTER COLUMN "status" SET DEFAULT 'completed'`);
    await queryRunner.query(`DROP TYPE "public"."shop_item_purchase_status_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."shop_item_purchase_status_enum_old" RENAME TO "shop_item_purchase_status_enum"`);
    await queryRunner.query(`CREATE TYPE "public"."shop_item_purchase_payment_method_enum_old" AS ENUM('credit_card', 'free', 'reward_points')`);
    await queryRunner.query(
      `ALTER TABLE "shop_item_purchase" ALTER COLUMN "payment_method" TYPE "public"."shop_item_purchase_payment_method_enum_old" USING "payment_method"::"text"::"public"."shop_item_purchase_payment_method_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."shop_item_purchase_payment_method_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."shop_item_purchase_payment_method_enum_old" RENAME TO "shop_item_purchase_payment_method_enum"`);
    await queryRunner.query(`ALTER TABLE "shop_item_purchase" DROP COLUMN "payment_transaction_id"`);
    await queryRunner.query(`ALTER TABLE "user_plan" DROP COLUMN "payment_transaction_id"`);
    await queryRunner.query(`DROP TABLE "payment_webhook"`);
    await queryRunner.query(`DROP TYPE "public"."payment_webhook_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."payment_webhook_webhook_type_enum"`);
    await queryRunner.query(`DROP TABLE "payment_transaction"`);
    await queryRunner.query(`DROP TYPE "public"."payment_transaction_purchase_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."payment_transaction_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."payment_transaction_payment_method_enum"`);
  }
}
