import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsNumber, IsBoolean } from 'class-validator';

/**
 * DTO for saving story draft
 */
export class StoryMakerDraftDto {
  @ApiProperty({
    description: 'The draft content of the story',
    example: 'Once upon a time in a magical forest...',
  })
  @IsString()
  @IsNotEmpty()
  content: string;
}

/**
 * DTO for auto-saving story content
 */
export class StoryMakerAutoSaveDto {
  @ApiProperty({
    description: 'The content to auto-save',
    example: 'Once upon a time...',
  })
  @IsString()
  content: string; // Can be empty for auto-save
}

/**
 * DTO for draft response
 */
export class StoryMakerDraftResponseDto {
  @ApiProperty({
    description: 'The draft content',
    example: 'Once upon a time in a magical forest...',
  })
  content: string;

  @ApiPropertyOptional({
    description: 'When the draft was last saved',
    example: '2023-01-01T12:00:00.000Z',
  })
  last_saved_at: Date;

  @ApiProperty({
    description: 'Current word count',
    example: 150,
  })
  word_count: number;

  @ApiProperty({
    description: 'Current character count',
    example: 750,
  })
  character_count: number;

  @ApiProperty({
    description: 'Number of auto-saves performed',
    example: 25,
  })
  auto_save_count: number;

  @ApiProperty({
    description: 'Whether the story has been submitted',
    example: false,
  })
  is_submitted: boolean;
}

/**
 * DTO for final story submission
 */
export class StoryMakerFinalSubmissionDto {
  @ApiProperty({
    description: 'The final content of the story to submit',
    example: 'Once upon a time in a magical forest, there lived a wise old owl...',
  })
  @IsString()
  @IsNotEmpty()
  content: string;
}

/**
 * DTO for AI evaluation response
 */
export class StoryMakerEvaluationResponseDto {
  @ApiProperty({
    description: 'The ID of the evaluation',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The ID of the submission',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  submission_id: string;

  @ApiProperty({
    description: 'Number of sentences in the story',
    example: 5,
  })
  sentence_count: number;

  @ApiProperty({
    description: 'Score based on sentence count',
    example: 5,
  })
  sentence_score: number;

  @ApiProperty({
    description: 'Creativity score (1-5)',
    example: 4,
  })
  creativity_score: number;

  @ApiProperty({
    description: 'Sentence power/writing quality score (1-3)',
    example: 3,
  })
  sentence_power_score: number;

  @ApiProperty({
    description: 'Participation score based on word count (1-5)',
    example: 4,
  })
  participation_score: number;

  @ApiProperty({
    description: 'Accuracy score based on grammar (1-3)',
    example: 3,
  })
  accuracy_score: number;

  @ApiProperty({
    description: 'Popularity score based on likes (1-5)',
    example: 2,
  })
  popularity_score: number;

  @ApiProperty({
    description: 'Relevance score - how well the story relates to the image prompt (1-5)',
    example: 4,
    required: false,
  })
  relevance_score?: number;

  @ApiProperty({
    description: 'Total score',
    example: 21,
  })
  total_score: number;

  @ApiProperty({
    description: 'Word count of the story',
    example: 125,
  })
  word_count: number;

  @ApiProperty({
    description: 'Number of grammar errors found',
    example: 1,
  })
  grammar_error_count: number;

  @ApiPropertyOptional({
    description: 'AI-generated feedback',
    example: 'Great creativity and imagination! Your story has wonderful descriptive elements.',
  })
  ai_feedback?: string;

  @ApiProperty({
    description: 'When the evaluation was completed',
    example: '2023-01-01T12:05:00.000Z',
  })
  evaluated_at: Date;

  @ApiProperty({
    description: 'Processing time in milliseconds',
    example: 2500,
  })
  processing_time: number;
}

/**
 * DTO for submission confirmation response
 */
export class StoryMakerSubmissionConfirmationDto {
  @ApiProperty({
    description: 'Confirmation message',
    example: 'Your story has been submitted and is being evaluated!',
  })
  message: string;

  @ApiProperty({
    description: 'The ID of the submission',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  submission_id: string;

  @ApiProperty({
    description: 'When the story was submitted',
    example: '2023-01-01T12:00:00.000Z',
  })
  submitted_at: Date;
}

/**
 * DTO for auto-save response
 */
export class StoryMakerAutoSaveResponseDto {
  @ApiProperty({
    description: 'When the content was auto-saved',
    example: '2023-01-01T12:00:00.000Z',
  })
  saved_at: Date;
}
