import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { StoryMakerSubmission } from './story-maker-submission.entity';
import { User } from './user.entity';

@Entity()
export class StoryMakerShare extends AuditableBaseEntity {
  @Column({ name: 'submission_id' })
  submissionId: string;

  @ManyToOne(() => StoryMakerSubmission, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'submission_id' })
  submission: StoryMakerSubmission;

  @Column({ name: 'sharer_id' })
  sharerId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'sharer_id' })
  sharer: User;

  @Column({ name: 'shared_with_id' })
  sharedWithId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'shared_with_id' })
  sharedWith: User;

  @Column({ name: 'share_message', nullable: true })
  shareMessage: string;

  @Column({ name: 'is_viewed', default: false })
  isViewed: boolean;

  @Column({ name: 'viewed_at', nullable: true })
  viewedAt: Date;

  /**
   * Convert to DTO for API responses
   */
  toDto(): any {
    return {
      id: this.id,
      submission_id: this.submissionId,
      sharer_id: this.sharerId,
      sharer_name: this.sharer?.name,
      sharer_profile_picture: this.sharer?.profilePicture,
      shared_with_id: this.sharedWithId,
      shared_with_name: this.sharedWith?.name,
      shared_with_profile_picture: this.sharedWith?.profilePicture,
      share_message: this.shareMessage,
      is_viewed: this.isViewed,
      viewed_at: this.viewedAt,
      created_at: this.createdAt,
    };
  }
}
