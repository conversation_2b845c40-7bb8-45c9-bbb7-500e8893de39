import { Controller, Get, Put, Param, Body, Query, UseGuards, Request, Post, NotFoundException } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiQuery, ApiBody } from '@nestjs/swagger';
import { QAService } from './qa.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { TutorGuard } from '../../common/guards/tutor.guard';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType, ApiOkResponseWithArrayType } from '../../common/decorators/api-response.decorator';
import {
  ReviewSubmissionDto,
  QASubmissionResponseDto,
  CreateQAQuestionDto,
  QAQuestionResponseDto,
  QAQuestionPaginationDto,
  CreateQAAssignmentDto,
  QAAssignmentResponseDto,
  CreateQAAssignmentItemsDto,
  QAAssignmentItemsResponseDto,
} from '../../database/models/qa.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { StudentDropdownDto } from 'src/database/models/qa/student-dropdown.dto';
import { TutorQAService } from './tutor-qa.service';
import { TutorStudentDto } from 'src/database/models/tutor-matching.dto';
import { GetUser } from 'src/common/decorators/get-user.decorator';
import { User } from 'src/database/entities/user.entity';

@ApiTags('Tutor Q&A')
@Controller('tutor/qa')
@UseGuards(JwtAuthGuard, TutorGuard)
@ApiBearerAuth('JWT-auth')
export class TutorQAController {
  constructor(
    private readonly tutorQAService: TutorQAService,
    private readonly qaService: QAService,
  ) {}

  // @Post('create')
  // @UseGuards(JwtAuthGuard, TutorGuard)
  // @ApiOperation({ summary: 'Create a new QA question' })
  // @ApiBody({
  //   type: CreateQAQuestionDto,
  //   description: 'QA question creation data',
  //   examples: {
  //     example1: {
  //       value: {
  //         question: 'What are the key principles of Object-Oriented Programming?',
  //         points: 10,
  //         minimumWords: 200,
  //         isActive: true
  //       }
  //     }
  //   }
  // })
  // @ApiOkResponseWithType(QAQuestionResponseDto, 'QA question created successfully')
  // @ApiErrorResponse(400, 'Invalid input data')
  // @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  // @ApiErrorResponse(403, 'Forbidden - Access required')

  // async create(
  //   @Body() createQuestionDto: CreateQAQuestionDto,
  //   @Request() req
  //   ): Promise<ApiResponse<QAQuestionResponseDto>> {
  //   console.log('Creating question with data:', createQuestionDto);
  //   const result = await this.qaService.create(createQuestionDto, req.user.id);
  //   return ApiResponse.success(
  //     result,
  //     'QA question created successfully',
  //     201
  //   );
  // }

  // @Get('questions')
  //   @UseGuards(JwtAuthGuard, TutorGuard)
  //   @ApiOperation({ summary: 'Get all QA questions' })
  //   @ApiQuery({
  //     name: 'page',
  //     required: false,
  //     type: Number,
  //     description: 'Page number',
  //   })
  //   @ApiQuery({
  //     name: 'limit',
  //     required: false,
  //     type: Number,
  //     description: 'Number of items per page',
  //   })
  //   @ApiQuery({
  //     name: 'sortBy',
  //     required: false,
  //     type: String,
  //     description: 'Field to sort by',
  //   })
  //   @ApiQuery({
  //     name: 'sortDirection',
  //     required: false,
  //     type: String,
  //     enum: ['ASC', 'DESC'],
  //     description: 'Sort direction',
  //   })
  //   @ApiOkResponseWithPagedListType(QAQuestionResponseDto, 'Questions retrieved successfully')
  //   @ApiErrorResponse(401, 'Unauthorized')
  //   @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  //   async getAllQuestions(
  //     @Query() paginationDto: QAQuestionPaginationDto
  //   ): Promise<ApiResponse<PagedListDto<QAQuestionResponseDto>>> {
  //     return this.qaService.getAllQuestions(paginationDto);
  // }

  // @Get('tutor-students-dropdown')
  // @ApiOperation({ summary: 'Get all students assigned to the current tutor' })
  // @ApiOkResponseWithArrayType(TutorStudentDto, 'Students retrieved successfully')
  // @ApiErrorResponse(404, 'Tutor not found')
  // @ApiErrorResponse(500, 'Internal server error')
  // async getMyStudents(
  //   @GetUser() user: User
  // ): Promise<ApiResponse<TutorStudentDto[]>> {
  //   const students = await this.qaService.getTutorStudents(user.id);
  //   return ApiResponse.success(students, 'Students retrieved successfully');
  // }

  // @Post('assignments')
  //   @ApiOperation({
  //     summary: 'Create assignments for students',
  //     description: 'Creates new assignments for selected students.'
  //   })
  //   @ApiBody({
  //     type: CreateQAAssignmentDto,
  //     description: 'QA assignment creation data',
  //     examples: {
  //       example1: {
  //         value: {
  //           questionId: '123e4567-e89b-12d3-a456-426614174000',
  //           studentId: '123e4567-e89b-12d3-a456-426614174001',
  //           points: 10,
  //           deadline: '2023-12-31T23:59:59Z',
  //           //assignedDate: '2023-11-01T00:00:00Z',
  //           instructions: 'Please focus on SOLID principles in your answer'
  //         }
  //       }
  //     }
  //   })

  //   @ApiOkResponseWithType(QAAssignmentResponseDto, 'Assignments created successfully')
  //   @ApiErrorResponse(400, 'Invalid input')
  //   @ApiErrorResponse(401, 'Unauthorized')
  //   @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  //   async createAssignments(
  //     @Body() createAssignmentDto: CreateQAAssignmentDto
  //   ): Promise<ApiResponse<QAAssignmentResponseDto[]>> {
  //     const assignments = await this.qaService.createAssignments(createAssignmentDto);
  //     return ApiResponse.success(assignments, 'Assignments created successfully', 201);
  // }

  @Post('create-assignment')
  @ApiOperation({
    summary: 'Create assignments for students',
    description: 'Creates new assignments for selected students.',
  })
  // @ApiBody({
  //   type: CreateQAAssignmentItemsDto,
  //   description: 'QA assignment creation data',
  //   examples: {
  //     example1: {
  //       value: {
  //         questionId: '123e4567-e89b-12d3-a456-426614174000',
  //         studentId: '123e4567-e89b-12d3-a456-426614174001',
  //         points: 10,
  //         deadline: '2023-12-31T23:59:59Z',
  //         //assignedDate: '2023-11-01T00:00:00Z',
  //         instructions: 'Please focus on SOLID principles in your answer'
  //       }
  //     }
  //   }
  // })
  @ApiBody({
    type: CreateQAAssignmentItemsDto,
    description: 'QA assignment creation data',
    examples: {
      example1: {
        value: {
          questionIds: ['09616cfc-5d5b-4856-9ee0-0d5b8a4a9b6f', '159f5775-14bd-46ed-afc7-86e62f11faee'],
          studentId: '88ef6a13-0c47-4392-bb4c-b9b5f76e9d0b',
          instructions: 'Please focus on SOLID principles in your answer',
        },
      },
    },
  })
  @ApiOkResponseWithType(QAAssignmentItemsResponseDto, 'Assignments created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  async create(@Body() createQAAssignmentItemsDto: CreateQAAssignmentItemsDto): Promise<ApiResponse<QAAssignmentItemsResponseDto[]>> {
    const assignments = await this.tutorQAService.createAssignments(createQAAssignmentItemsDto);
    return ApiResponse.success(assignments, 'Assignments created successfully', 201);
  }

  @Get('submissions/pending')
  @ApiOperation({
    summary: 'Get pending submissions (Tutor only)',
    description: 'Retrieves all pending submissions that need review.',
  })
  @ApiOkResponseWithPagedListType(QASubmissionResponseDto, 'Pending submissions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  async getPendingSubmissions(@GetUser() user: User, @Query('page') page?: number, @Query('limit') limit?: number): Promise<ApiResponse<PagedListDto<QASubmissionResponseDto>>> {
    const submissions = await this.qaService.getPendingSubmissions(user.id, { page, limit });
    return submissions;
  }

  @Get('submissions/reviewed')
  @ApiOperation({
    summary: 'Get reviewed submissions (Tutor only)',
    description: 'Retrieves all submissions reviewed by the tutor.',
  })
  @ApiOkResponseWithPagedListType(QASubmissionResponseDto, 'Reviewed submissions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  async getReviewedSubmissions(@Request() req, @Query('page') page?: number, @Query('limit') limit?: number): Promise<ApiResponse<PagedListDto<QASubmissionResponseDto>>> {
    const submissions = await this.tutorQAService.getReviewedSubmissions(req.user.id, { page, limit });
    //    return ApiResponse.success(submissions, 'Reviewed submissions retrieved successfully');
    return submissions;
  }

  @Put('submissions/:id/review')
  @ApiOperation({
    summary: 'Review a submission (Tutor only)',
    description: "Reviews a student's submission with feedback and corrections.",
  })
  @ApiOkResponseWithType(QASubmissionResponseDto, 'Submission reviewed successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  @ApiErrorResponse(404, 'Submission not found')
  async reviewSubmission(@Param('id') id: string, @Body() reviewDto: ReviewSubmissionDto, @Request() req): Promise<ApiResponse<QASubmissionResponseDto>> {
    const submission = await this.qaService.reviewSubmission(id, reviewDto, req.user.id);
    return ApiResponse.success(submission, 'Submission reviewed successfully');
  }

  @Get('assignment-submission/:id')
  async getSubmissionHistoryById(@Param('id') id: string) {
    const result = await this.tutorQAService.getSubmissionById(id);
    if (!result) {
      throw new NotFoundException('Submission not found');
    }
    return result;
  }
}
