import { Controller, Post, Get, Body, Param, Req, HttpStatus, UseG<PERSON>s, Lo<PERSON>, <PERSON>ers, Ip, HttpCode } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { Request } from 'express';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { StudentGuard } from '../../common/guards/student.guard';
import { PaymentService } from './services/payment.service';
import { PaymentStatusResponseDto, WebhookPayloadDto, RefundRequestDto, RefundResponseDto, PaymentTransactionDto, PaymentHealthResponseDto, WebhookResponseDto } from './dto/payment.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithArrayType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';

interface RequestWithUser extends Request {
  user: { id: string; sub: string; [key: string]: any };
}

@ApiTags('Payment')
@ApiBearerAuth('JWT-auth')
@Controller('payment')
export class PaymentController {
  private readonly logger = new Logger(PaymentController.name);

  constructor(private readonly paymentService: PaymentService) {}

  // REMOVED: POST /payment/initiate - Replaced by /shop/cart/checkout
  // REMOVED: POST /payment/process - Replaced by /payment/kcp/process

  @Get('status/:transactionId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get payment status',
    description: 'Retrieves the current status of a payment transaction by transaction ID.',
  })
  @ApiParam({ name: 'transactionId', description: 'Payment transaction ID' })
  @ApiOkResponseWithType(PaymentStatusResponseDto, 'Payment status retrieved successfully')
  @ApiErrorResponse(404, 'Transaction not found')
  @ApiErrorResponse(500, 'Failed to get payment status')
  async getPaymentStatus(@Param('transactionId') transactionId: string): Promise<ApiResponse<PaymentStatusResponseDto>> {
    this.logger.log(`Getting payment status: ${transactionId}`);

    const result = await this.paymentService.getPaymentStatus(transactionId);
    return ApiResponse.success(result, 'Payment status retrieved successfully');
  }

  @Post('webhook/kcp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Handle KCP webhook notifications',
    description: 'Processes webhook notifications from KCP payment gateway for payment status updates. Returns result code as per KCP specification.',
  })
  @ApiOkResponseWithType(WebhookResponseDto, 'Webhook processed successfully')
  @ApiErrorResponse(400, 'Invalid webhook data')
  @ApiErrorResponse(500, 'Webhook processing failed')
  async handleKcpWebhook(@Body() payload: WebhookPayloadDto, @Headers('x-kcp-signature') signature: string, @Ip() sourceIp: string): Promise<WebhookResponseDto> {
    this.logger.log(`Received KCP webhook for transaction: ${payload.tno}, type: ${payload.tx_cd}`);

    try {
      await this.paymentService.processWebhook(payload, signature, sourceIp);

      // Return success result code as per KCP specification
      // KCP checks this result value - if not "0000", it will retry up to 10 times
      const result: WebhookResponseDto = {
        result: '0000',
        success: true,
        message: 'Webhook processed successfully',
      };

      this.logger.log(`Webhook processed successfully for transaction: ${payload.tno}`);
      return result;
    } catch (error) {
      this.logger.error(`Webhook processing failed for transaction: ${payload.tno}: ${error.message}`, error.stack);

      // Return error result code - KCP will retry
      const result: WebhookResponseDto = {
        result: '9999',
        success: false,
        message: `Webhook processing failed: ${error.message}`,
      };

      return result;
    }
  }

  // REMOVED: POST /payment/kcp/verify - Redundant with /payment/kcp/process

  // REMOVED: GET /payment/kcp/redirect - Should be handled by frontend, not backend

  @Post('refund')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Process payment refund',
    description: 'Processes a refund request for a completed payment transaction.',
  })
  @ApiOkResponseWithType(RefundResponseDto, 'Refund processed successfully')
  @ApiErrorResponse(400, 'Invalid refund request')
  @ApiErrorResponse(404, 'Transaction not found')
  @ApiErrorResponse(500, 'Refund processing failed')
  async processRefund(@Body() refundRequestDto: RefundRequestDto): Promise<ApiResponse<RefundResponseDto>> {
    this.logger.log(`Processing refund for transaction: ${refundRequestDto.transactionId}`);

    const result = await this.paymentService.processRefund(refundRequestDto);
    return ApiResponse.success(result, 'Refund processed successfully');
  }

  @Get('transactions')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get user payment transactions',
    description: 'Retrieves all payment transactions for the authenticated user.',
  })
  @ApiOkResponseWithArrayType(PaymentTransactionDto, 'Transactions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(500, 'Failed to get transactions')
  async getUserTransactions(@Req() req: RequestWithUser): Promise<ApiResponse<PaymentTransactionDto[]>> {
    const userId = req.user.sub || req.user.id;
    this.logger.log(`Getting transactions for user: ${userId}`);

    const transactions = await this.paymentService.getUserTransactions(userId);
    return ApiResponse.success(transactions, 'Transactions retrieved successfully');
  }

  @Post('kcp/process')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Process KCP payment completion',
    description: 'Processes KCP payment after user completes authentication. Handles form submission from KCP payment page and redirects to appropriate success/failure page.',
  })
  @ApiOkResponseWithType(Object, 'Payment processed and redirected')
  @ApiErrorResponse(400, 'Invalid payment data')
  @ApiErrorResponse(404, 'Transaction not found')
  @ApiErrorResponse(500, 'Payment processing failed')
  async processKcpPayment(@Body() kcpPaymentData: any, @Req() req: Request): Promise<any> {
    this.logger.log('Processing KCP payment completion...');
    this.logger.log('KCP Payment Data:', kcpPaymentData);

    try {
      const result = await this.paymentService.processKcpPayment(kcpPaymentData);

      if (result.success) {
        this.logger.log(`Payment completed successfully: ${result.transactionId}`);

        // For successful payments, return redirect to success page
        return {
          success: true,
          redirect: `${process.env.FRONTEND_URL || 'http://**************:3010'}/payment/success?orderId=${result.orderId}&transactionId=${result.transactionId}`,
          message: 'Payment completed successfully',
          data: {
            transactionId: result.transactionId,
            orderId: result.orderId,
            kcpTransactionId: result.kcpTransactionId,
          },
        };
      } else {
        this.logger.error(`Payment processing failed: ${result.message}`);

        // For failed payments, return redirect to cancel page
        return {
          success: false,
          redirect: `${process.env.FRONTEND_URL || 'http://**************:3010'}/payment/cancel?error=${encodeURIComponent(result.message)}`,
          message: result.message,
          details: result.details,
        };
      }
    } catch (error) {
      this.logger.error(`KCP payment processing error: ${error.message}`, error.stack);

      // For processing errors, return redirect to cancel page
      return {
        success: false,
        redirect: `${process.env.FRONTEND_URL || 'http://**************:3010'}/payment/cancel?error=${encodeURIComponent('Payment processing failed')}`,
        message: 'Payment processing failed',
        details: { error: error.message },
      };
    }
  }

  // REMOVED: POST /payment/kcp/return - Redundant with /payment/kcp/process

  @Get('health')
  @ApiOperation({
    summary: 'Payment service health check',
    description: 'Checks the health status of the payment service.',
  })
  @ApiOkResponseWithType(PaymentHealthResponseDto, 'Payment service is healthy')
  async healthCheck(): Promise<ApiResponse<PaymentHealthResponseDto>> {
    const result: PaymentHealthResponseDto = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
    };

    return ApiResponse.success(result, 'Payment service is healthy');
  }
}
