import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMinWordLimitToDiarySettings1750576722496 implements MigrationInterface {
  name = 'AddMinWordLimitToDiarySettings1750576722496';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add minWordLimit column to diary_settings_template table
    await queryRunner.query(`
      ALTER TABLE "diary_settings_template" 
      ADD COLUMN "min_word_limit" integer NOT NULL DEFAULT 10
    `);

    // Add minWordLimit column to diary_entry_settings table
    await queryRunner.query(`
      ALTER TABLE "diary_entry_settings" 
      ADD COLUMN "min_word_limit" integer NOT NULL DEFAULT 10
    `);

    // Update existing records to have reasonable minimum word limits based on their current word limits
    await queryRunner.query(`
      UPDATE "diary_settings_template"
      SET "min_word_limit" = CASE
        WHEN "word_limit" <= 50 THEN 10
        WHEN "word_limit" <= 100 THEN 51
        WHEN "word_limit" <= 200 THEN 101
        WHEN "word_limit" <= 500 THEN 201
        ELSE 501
      END
    `);

    await queryRunner.query(`
      UPDATE "diary_entry_settings"
      SET "min_word_limit" = CASE
        WHEN "word_limit" <= 50 THEN 10
        WHEN "word_limit" <= 100 THEN 51
        WHEN "word_limit" <= 200 THEN 101
        WHEN "word_limit" <= 500 THEN 201
        ELSE 501
      END
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove minWordLimit column from diary_entry_settings table
    await queryRunner.query(`
      ALTER TABLE "diary_entry_settings" 
      DROP COLUMN "min_word_limit"
    `);

    // Remove minWordLimit column from diary_settings_template table
    await queryRunner.query(`
      ALTER TABLE "diary_settings_template" 
      DROP COLUMN "min_word_limit"
    `);
  }
}
