import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID, IsArray, ValidateNested, ArrayMinSize } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for block game sentence in student view
 */
export class BlockGameSentenceResponseDto {
  @ApiProperty({
    description: 'The starting part of the sentence',
    example: 'The fan makes a',
  })
  starting_part: string;

  @ApiProperty({
    description: 'The expanding part of the sentence',
    example: 'strange sound',
  })
  expanding_part: string;

  @ApiProperty({
    description: 'The order of the sentence in the game',
    example: 1,
  })
  sentence_order: number;
}

/**
 * DTO for block game word blocks
 */
export class BlockGameWordsDto {
  @ApiProperty({
    description: 'Words from all starting parts (randomized)',
    example: ['The', 'fan', 'makes', 'a', 'I', 'will', 'check'],
    type: [String],
  })
  starting_words: string[];

  @ApiProperty({
    description: 'Words from all expanding parts (randomized)',
    example: ['strange', 'sound', 'it', 'tomorrow', 'morning'],
    type: [String],
  })
  expanding_words: string[];
}

/**
 * DTO for block game detail (student view)
 */
export class BlockGameDetailDto {
  @ApiProperty({
    description: 'The ID of the block game',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the block game',
    example: 'Basic Sentence Building',
  })
  title: string;

  @ApiProperty({
    description: 'The total score for the block game',
    example: 20,
  })
  score: number;

  @ApiProperty({
    description: 'The number of sentences to construct',
    example: 5,
  })
  sentence_count: number;

  @ApiProperty({
    description: 'The correct sentences (for reference)',
    type: [BlockGameSentenceResponseDto],
  })
  sentences: BlockGameSentenceResponseDto[];

  @ApiProperty({
    description: 'The randomized word blocks for gameplay',
    type: BlockGameWordsDto,
  })
  word_blocks: BlockGameWordsDto;
}

/**
 * DTO for student sentence construction
 */
export class StudentSentenceConstructionDto {
  @ApiProperty({
    description: 'The starting part constructed by student',
    example: 'The fan makes a',
  })
  @IsString()
  @IsNotEmpty()
  starting_sentence: string;

  @ApiProperty({
    description: 'The expanding part constructed by student',
    example: 'strange sound',
  })
  @IsString()
  @IsNotEmpty()
  expanding_sentence: string;

  @ApiProperty({
    description: 'The order of the sentence',
    example: 1,
  })
  sentence_order: number;
}

/**
 * DTO for submitting block game attempt
 */
export class SubmitBlockGameDto {
  @ApiProperty({
    description: 'The ID of the block game',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  block_game_id: string;

  @ApiProperty({
    description: 'Array of sentence constructions by the student',
    type: [StudentSentenceConstructionDto],
    example: [
      {
        starting_sentence: 'The fan makes a',
        expanding_sentence: 'strange sound',
        sentence_order: 1,
      },
      {
        starting_sentence: 'I will check',
        expanding_sentence: 'it tomorrow morning',
        sentence_order: 2,
      },
    ],
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one sentence construction must be provided' })
  @ValidateNested({ each: true })
  @Type(() => StudentSentenceConstructionDto)
  sentence_constructions: StudentSentenceConstructionDto[];
}

/**
 * DTO for block game attempt result
 */
export class BlockGameAttemptResultDto {
  @ApiProperty({
    description: 'The score achieved',
    example: 16,
  })
  score: number;

  @ApiProperty({
    description: 'The total possible score',
    example: 20,
  })
  total_score: number;

  @ApiProperty({
    description: 'The percentage score',
    example: 80,
  })
  percentage: number;

  @ApiProperty({
    description: 'Detailed results for each sentence',
    example: [
      {
        sentence_order: 1,
        student_starting: 'The fan makes a',
        student_expanding: 'strange sound',
        correct_starting: 'The fan makes a',
        correct_expanding: 'strange sound',
        is_correct: true,
        points_earned: 4,
      },
      {
        sentence_order: 2,
        student_starting: 'I will check',
        student_expanding: 'it tomorrow',
        correct_starting: 'I will check',
        correct_expanding: 'it tomorrow morning',
        is_correct: false,
        points_earned: 0,
      },
    ],
  })
  sentence_results: Array<{
    sentence_order: number;
    student_starting: string;
    student_expanding: string;
    correct_starting: string;
    correct_expanding: string;
    is_correct: boolean;
    points_earned: number;
  }>;

  @ApiProperty({
    description: 'When the attempt was submitted',
    example: '2023-01-15T10:30:00.000Z',
  })
  submitted_at: Date;
}

/**
 * DTO for block game list (student view)
 */
export class BlockGameListItemDto {
  @ApiProperty({
    description: 'The ID of the block game',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the block game',
    example: 'Basic Sentence Building',
  })
  title: string;

  @ApiProperty({
    description: 'The total score for the block game',
    example: 20,
  })
  score: number;

  @ApiProperty({
    description: 'The number of sentences in the game',
    example: 5,
  })
  sentence_count: number;

  @ApiProperty({
    description: 'Whether the student has played this game',
    example: true,
  })
  is_played: boolean;

  @ApiProperty({
    description: 'The best score achieved by the student (if played)',
    example: 18,
    required: false,
  })
  best_score?: number;

  @ApiProperty({
    description: 'The number of attempts made by the student',
    example: 3,
  })
  attempt_count: number;
}
