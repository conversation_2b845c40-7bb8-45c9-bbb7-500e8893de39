import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddShopItemCategoryColumn1746300000000 implements MigrationInterface {
  name = 'AddShopItemCategoryColumn1746300000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the enum type
    await queryRunner.query(`CREATE TYPE "public"."shop_item_category_enum" AS ENUM('skin', 'emoticon')`);

    // Add the column with default value 'skin'
    await queryRunner.query(`ALTER TABLE "shop_item" ADD "shop_item_category" "public"."shop_item_category_enum" NOT NULL DEFAULT 'skin'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the column
    await queryRunner.query(`ALTER TABLE "shop_item" DROP COLUMN "shop_item_category"`);

    // Drop the enum type
    await queryRunner.query(`DROP TYPE "public"."shop_item_category_enum"`);
  }
}
