import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, IsNotEmpty, IsUUID, ValidateNested, ArrayMinSize, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for submitting a waterfall answer
 */
export class SubmitWaterfallAnswerDto {
  @ApiProperty({
    description: 'The ID of the question',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  question_id: string;

  @ApiProperty({
    description: 'The answer provided by the student',
    example: ['sat'],
  })
  @IsArray()
  @IsNotEmpty()
  answers: string[];
}

/**
 * DTO for submitting waterfall answers
 */
export class SubmitWaterfallAnswersDto {
  @ApiProperty({
    description: 'The ID of the waterfall set',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  set_id: string;

  @ApiProperty({
    description: 'Array of answers for each question',
    type: [SubmitWaterfallAnswerDto],
  })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => SubmitWaterfallAnswerDto)
  answers: SubmitWaterfallAnswerDto[];
}

/**
 * DTO for submitting a waterfall game
 */
export class SubmitWaterfallGameDto {
  @ApiProperty({
    description: 'The ID of the waterfall set',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  set_id: string;

  @ApiProperty({
    description: 'Array of answers for each question',
    type: [SubmitWaterfallAnswerDto],
  })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => SubmitWaterfallAnswerDto)
  answers: SubmitWaterfallAnswerDto[];
}

/**
 * DTO for waterfall game result
 */
export class WaterfallGameResultDto {
  @ApiProperty({
    description: 'The ID of the participation record',
    example: '123e4567-e89b-12d3-a456-************',
  })
  participation_id: string;

  @ApiProperty({
    description: 'The total number of questions in the set',
    example: 10,
  })
  total_questions: number;

  @ApiProperty({
    description: 'The number of correct answers',
    example: 8,
  })
  correct_answers: number;

  @ApiProperty({
    description: 'The score achieved',
    example: 40,
  })
  score: number;

  @ApiProperty({
    description: 'The total possible score for the set',
    example: 50,
  })
  total_score: number;
}

/**
 * DTO for waterfall answer response
 */
export class WaterfallAnswerResponseDto {
  @ApiProperty({
    description: 'The ID of the question',
    example: '123e4567-e89b-12d3-a456-************',
  })
  question_id: string;

  @ApiProperty({
    description: 'The answers submitted by the user',
    example: ['sat', 'mat'],
  })
  submitted_answers: string[];

  @ApiProperty({
    description: 'Whether the answer was correct',
    example: true,
  })
  is_correct: boolean;

  @ApiProperty({
    description: 'The correct answers for the question',
    example: ['sat', 'mat'],
  })
  correct_answers: string[];
}

/**
 * DTO for waterfall participation response
 */
export class WaterfallParticipationResponseDto {
  @ApiProperty({
    description: 'The ID of the participation record',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The ID of the set',
    example: '123e4567-e89b-12d3-a456-************',
  })
  set_id: string;

  @ApiProperty({
    description: 'The title of the set',
    example: 'Basic Grammar Set 1',
  })
  set_title: string;

  @ApiProperty({
    description: 'The total number of correct answers',
    example: 8,
  })
  total_correct_answers: number;

  @ApiProperty({
    description: 'The total number of questions in the set',
    example: 10,
  })
  total_questions: number;

  @ApiProperty({
    description: 'The score achieved',
    example: 80,
  })
  score: number;

  @ApiProperty({
    description: 'The date and time when the game was submitted',
    example: '2023-07-25T12:34:56.789Z',
  })
  submitted_at: Date;

  @ApiProperty({
    description: 'Detailed information about each answer',
    type: [WaterfallAnswerResponseDto],
  })
  answers: WaterfallAnswerResponseDto[];
}
