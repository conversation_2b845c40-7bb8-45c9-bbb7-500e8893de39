import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID, IsArray, IsBoolean } from 'class-validator';
import { MappingStatus } from '../entities/student-tutor-mapping.entity';
import { FeatureType } from '../entities/plan-feature.entity';

export class AssignTutorDto {
  @ApiProperty({ description: 'Student ID' })
  @IsUUID()
  @IsNotEmpty()
  studentId: string;

  @ApiProperty({ description: 'Tutor ID' })
  @IsUUID()
  @IsNotEmpty()
  tutorId: string;

  @ApiProperty({ description: 'Module ID (plan feature ID)' })
  @IsUUID()
  @IsNotEmpty()
  planFeatureId: string;

  @ApiProperty({ description: 'Notes about the assignment (optional)', required: false })
  @IsString()
  @IsOptional()
  notes?: string;
}

export class UpdateTutorAssignmentDto {
  @ApiProperty({ description: 'Tutor ID' })
  @IsUUID()
  @IsNotEmpty()
  tutorId: string;

  @ApiProperty({ description: 'Status of the assignment', enum: MappingStatus, required: false })
  @IsEnum(MappingStatus)
  @IsOptional()
  status?: MappingStatus;

  @ApiProperty({ description: 'Notes about the assignment (optional)', required: false })
  @IsString()
  @IsOptional()
  notes?: string;
}

export class TutorAssignmentResponseDto {
  @ApiProperty({ description: 'Assignment ID' })
  id: string;

  @ApiProperty({ description: 'Student ID' })
  studentId: string;

  @ApiProperty({ description: 'Student name' })
  studentName: string;

  @ApiProperty({ description: 'Tutor ID' })
  tutorId: string;

  @ApiProperty({ description: 'Tutor name' })
  tutorName: string;

  @ApiProperty({ description: 'Plan Feature ID (Module)' })
  planFeatureId: string;

  @ApiProperty({ description: 'Plan Feature name (Module)' })
  moduleName: string;

  @ApiProperty({
    description: 'Module type identifier',
    enum: FeatureType,
    example: FeatureType.HEC_USER_DIARY,
  })
  moduleType: FeatureType;

  @ApiProperty({ description: 'Status of the assignment', enum: MappingStatus })
  status: MappingStatus;

  @ApiProperty({ description: 'Date when the tutor was assigned' })
  assignedDate: Date;

  @ApiProperty({ description: 'Date of last activity', required: false })
  lastActivityDate?: Date;

  @ApiProperty({ description: 'Notes about the assignment', required: false })
  notes?: string;

  @ApiProperty({ description: 'When the assignment was created' })
  createdAt: Date;

  @ApiProperty({ description: 'When the assignment was last updated' })
  updatedAt: Date;
}

export class StudentTutorDto {
  @ApiProperty({ description: 'Tutor ID' })
  id: string;

  @ApiProperty({ description: 'Tutor name' })
  name: string;

  @ApiProperty({ description: 'Tutor email' })
  email: string;

  @ApiProperty({ description: 'Tutor profile picture URL', required: false })
  profilePicture?: string;

  @ApiProperty({ description: 'Tutor bio', required: false })
  bio?: string;

  @ApiProperty({ description: 'Plan Feature ID (Module)' })
  planFeatureId: string;

  @ApiProperty({ description: 'Plan Feature name (Module)' })
  moduleName: string;

  @ApiProperty({
    description: 'Module type identifier',
    enum: FeatureType,
    example: FeatureType.HEC_USER_DIARY,
  })
  moduleType: FeatureType;

  @ApiProperty({ description: 'Assignment status', enum: MappingStatus })
  status: MappingStatus;

  @ApiProperty({ description: 'Date when the tutor was assigned' })
  assignedDate: Date;
}

export class TutorStudentDto {
  @ApiProperty({ description: 'Student ID' })
  id: string;

  @ApiProperty({ description: 'Student name' })
  name: string;

  @ApiProperty({ description: 'Student email' })
  email: string;

  @ApiProperty({ description: 'Student profile picture URL', required: false })
  profilePicture?: string;

  @ApiProperty({ description: 'Plan Feature ID (Module)' })
  planFeatureId: string;

  @ApiProperty({ description: 'Plan Feature name (Module)' })
  moduleName: string;

  @ApiProperty({
    description: 'Module type identifier',
    enum: FeatureType,
    example: FeatureType.HEC_USER_DIARY,
  })
  moduleType: FeatureType;

  @ApiProperty({ description: 'Assignment status', enum: MappingStatus })
  status: MappingStatus;

  @ApiProperty({ description: 'Date when the tutor was assigned' })
  assignedDate: Date;

  @ApiProperty({ description: 'Date of last activity', required: false })
  lastActivityDate?: Date;
}

export class StudentModuleDto {
  @ApiProperty({ description: 'Plan Feature ID (Module)' })
  planFeatureId: string;

  @ApiProperty({ description: 'Plan Feature name (Module)' })
  moduleName: string;

  @ApiProperty({
    description: 'Module type identifier',
    enum: FeatureType,
    example: FeatureType.HEC_USER_DIARY,
  })
  moduleType: FeatureType;

  @ApiProperty({ description: 'Assignment status', enum: MappingStatus })
  status: MappingStatus;

  @ApiProperty({ description: 'Date when the tutor was assigned' })
  assignedDate: Date;

  @ApiProperty({ description: 'Date of last activity', required: false })
  lastActivityDate?: Date;
}

export class TutorStudentFlattenedDto {
  @ApiProperty({ description: 'Student ID' })
  id: string;

  @ApiProperty({ description: 'Student name' })
  name: string;

  @ApiProperty({ description: 'Student email' })
  email: string;

  @ApiProperty({ description: 'Student profile picture URL', required: false })
  profilePicture?: string;

  @ApiProperty({ description: 'Array of modules assigned to this student', type: [StudentModuleDto] })
  modules: StudentModuleDto[];
}

export class TutorWorkloadDto {
  @ApiProperty({ description: 'Tutor ID' })
  id: string;

  @ApiProperty({ description: 'Tutor name' })
  name: string;

  @ApiProperty({ description: 'Number of active students assigned to this tutor' })
  activeStudentCount: number;

  @ApiProperty({ description: 'Number of pending diary entries to review' })
  pendingReviewCount: number;

  @ApiProperty({ description: 'Last activity date', required: false })
  lastActivityDate?: Date;
}

export class AssignmentFilterDto {
  @ApiProperty({ description: 'Filter by student name', required: false })
  @IsString()
  @IsOptional()
  studentName?: string;

  @ApiProperty({ description: 'Filter by tutor name', required: false })
  @IsString()
  @IsOptional()
  tutorName?: string;

  @ApiProperty({ description: 'Filter by plan feature ID (module)', required: false })
  @IsUUID()
  @IsOptional()
  planFeatureId?: string;

  @ApiProperty({ description: 'Filter by status', enum: MappingStatus, required: false })
  @IsEnum(MappingStatus)
  @IsOptional()
  status?: MappingStatus;
}

export class StudentTutorFilterDto {
  @ApiProperty({ description: 'Filter by tutor name', required: false })
  @IsString()
  @IsOptional()
  tutorName?: string;

  @ApiProperty({ description: 'Filter by plan feature ID (module)', required: false })
  @IsUUID()
  @IsOptional()
  planFeatureId?: string;

  @ApiProperty({ description: 'Filter by status', enum: MappingStatus, required: false })
  @IsEnum(MappingStatus)
  @IsOptional()
  status?: MappingStatus;

  @ApiProperty({ description: 'Plan feature type', required: false, enum: FeatureType })
  @IsEnum(FeatureType)
  @IsOptional()
  planFeatureType?: FeatureType;
}

export class AutoAssignTutorsDto {
  @ApiProperty({ description: 'List of student IDs to assign tutors to' })
  @IsArray()
  @IsUUID(undefined, { each: true })
  @IsNotEmpty()
  studentIds: string[];

  @ApiProperty({ description: 'Plan Feature ID (module)' })
  @IsUUID()
  @IsNotEmpty()
  planFeatureId: string;

  @ApiProperty({ description: 'Whether to reassign students who already have tutors', required: false })
  @IsBoolean()
  @IsOptional()
  reassignExisting?: boolean;
}

export class ChangeStudentTutorDto {
  @ApiProperty({ description: 'Student ID' })
  @IsUUID()
  @IsNotEmpty()
  studentId: string;

  @ApiProperty({ description: 'New Tutor ID' })
  @IsUUID()
  @IsNotEmpty()
  newTutorId: string;

  @ApiProperty({ description: 'Reason for changing tutor (optional)', required: false })
  @IsString()
  @IsOptional()
  reason?: string;
}

export class ChangeStudentTutorResponseDto {
  @ApiProperty({ description: 'Student ID' })
  studentId: string;

  @ApiProperty({ description: 'Student name' })
  studentName: string;

  @ApiProperty({ description: 'Previous tutor ID', required: false })
  previousTutorId?: string;

  @ApiProperty({ description: 'Previous tutor name', required: false })
  previousTutorName?: string;

  @ApiProperty({ description: 'New tutor ID' })
  newTutorId: string;

  @ApiProperty({ description: 'New tutor name' })
  newTutorName: string;

  @ApiProperty({ description: 'List of modules where tutor was changed' })
  changedModules: Array<{
    moduleId: string;
    moduleName: string;
    moduleType: string;
  }>;

  @ApiProperty({ description: 'Total number of modules affected' })
  totalModulesAffected: number;

  @ApiProperty({ description: 'Reason for change', required: false })
  reason?: string;

  @ApiProperty({ description: 'Date when the change was made' })
  changeDate: Date;
}
