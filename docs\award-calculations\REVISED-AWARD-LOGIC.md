# Revised Award Calculation Logic

## Overview

The award calculation logic has been revised to make it more specific and focused for each award type across all modules (Diary, Novel, Essay).

## Key Changes

### **Best Writer Award**: Focus on Most Likes/Engagement
- **Diary Module**: Prioritizes peer appreciation (likes from students and tutors)
- **Novel Module**: Uses word count and engagement as proxy for "likes"
- **Essay Module**: Uses completion rate and engagement as proxy for "likes"

### **Best Perfect Award**: Focus on Fewest Errors (Highest Accuracy)
- **All Modules**: Heavily weighted toward quality/accuracy (70% weight)
- **Secondary factors**: Consistency and completion (30% combined)

### **Other Awards**: Maintain Existing Logic
- **Best Designer Award** (Diary): Decoration and design creativity
- **Best Friendship Award** (Diary): Social engagement and interaction
- **Best Performance Award** (Novel): Comprehensive performance metrics

## Detailed Calculation Logic

### Diary Module

#### Best Writer Award
```typescript
// Weighted like calculation: Student likes more valuable than tutor likes
const weightedLikes = (studentLikes * 1.0) + (tutorLikes * 0.7);
const likesPerEntry = weightedLikes / entriesCount;
// Scale: 3 weighted likes per entry = 100 points
const score = Math.min((likesPerEntry / 3) * 100, 100);
```

#### Best Perfect Award
```typescript
// Quality Component (70% weight) - Higher score means fewer errors
const qualityScore = Math.min((avgEntryScore / 10) * 100, 100);
// Consistency Component (20% weight)
const consistencyScore = (entriesCount / targetEntries) * 100;
// Attendance Component (10% weight)
const attendanceScore = (totalAttendance / targetDays) * 100;

const finalScore = (qualityScore * 0.7) + (consistencyScore * 0.2) + (attendanceScore * 0.1);
```

### Novel Module

#### Best Writer Award
```typescript
// Engagement Score (60% weight) - Higher word count shows engagement
const engagementScore = Math.min((averageWordCount / 500) * 100, 100);
// Quality Score (40% weight)
const qualityScore = Math.min(averageScore * 10, 100);

const combinedScore = (engagementScore * 0.6) + (qualityScore * 0.4);
```

#### Best Perfect Award
```typescript
// Quality Score (70% weight) - Primary focus on accuracy
const qualityScore = Math.min(averageScore * 10, 100);
// Completion Score (20% weight)
const completionScore = Math.min(completionRate, 100);
// Consistency Score (10% weight)
const consistencyScore = Math.min(submissionFrequency * 20, 100);

const combinedScore = (qualityScore * 0.7) + (completionScore * 0.2) + (consistencyScore * 0.1);
```

### Essay Module

#### Best Writer Award
```typescript
// Engagement Score (60% weight) - Higher completion shows engagement
const engagementScore = Math.min(completionRate, 100);
// Quality Score (40% weight)
const qualityScore = Math.min(averageScore * 10, 100);

const combinedScore = (engagementScore * 0.6) + (qualityScore * 0.4);
```

#### Best Perfect Award
```typescript
// Quality Score (70% weight) - Primary focus on accuracy
const qualityScore = Math.min(averageScore * 10, 100);
// Completion Score (20% weight)
const completionScore = Math.min(completionRate, 100);
// Consistency Score (10% weight)
const consistencyScore = Math.min((submissions.length / 5) * 100, 100);

const combinedScore = (qualityScore * 0.7) + (completionScore * 0.2) + (consistencyScore * 0.1);
```

## Implementation Details

### New Methods Added

1. **Diary Module** (`diary-award.service.ts`):
   - `calculateAwardSpecificScore()`: Routes to specific calculation based on award name
   - `calculateBestWriterScore()`: Focuses on likes and peer appreciation
   - `calculateBestPerfectScore()`: Focuses on quality and accuracy
   - `checkMinimumCriteria()`: Award-specific minimum requirements

2. **Novel Module** (`novel-award.service.ts`):
   - Updated existing `calculateNovelPerformanceScore()` with award-specific logic

3. **Essay Module** (`essay-award.service.ts`):
   - `calculateAwardSpecificScore()`: Award-specific calculation logic
   - Updated `calculateEssayPerformanceScore()` to support award-specific calculations

### Backward Compatibility

- All changes maintain backward compatibility with existing award configurations
- Legacy criteria checks are still performed for validation
- Default calculations are provided for unknown award types

## Benefits of the Revised Logic

1. **Clarity**: Each award has a clear, specific focus
2. **Fairness**: Awards recognize different types of excellence
3. **Motivation**: Students can excel in different areas
4. **Transparency**: Calculation logic is more understandable

## Configuration Examples

### Best Writer Award (Diary)
```json
{
  "minLikes": 5,
  "entriesRequired": 10,
  "daysRequired": 15,
  "maxWinners": 3
}
```

### Best Perfect Award (All Modules)
```json
{
  "minPerfectScore": 7,
  "targetEntries": 15,
  "daysRequired": 20,
  "maxWinners": 1
}
```

## Testing Recommendations

1. **Unit Tests**: Test each award-specific calculation method
2. **Integration Tests**: Test complete award generation process
3. **Performance Tests**: Ensure calculations perform well with large datasets
4. **Edge Case Tests**: Test with minimal data, missing data, etc.

## Future Enhancements

1. **Like System**: Implement actual like/feedback systems for Novel and Essay modules
2. **Error Tracking**: Implement detailed error tracking for more accurate "fewest errors" calculations
3. **Peer Review**: Add peer review systems to better measure "Best Writer" across all modules
4. **Analytics**: Add detailed analytics for award distribution and fairness
