import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Payment API Consistency (e2e)', () => {
  let app: INestApplication;
  let authToken: string;
  let testUserId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Login to get auth token
    const loginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });

    authToken = loginResponse.body.data.access_token;
    testUserId = loginResponse.body.data.user.id;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Response Format Consistency', () => {
    it('should return consistent format for shop checkout', async () => {
      // Add item to cart first
      await request(app.getHttpServer())
        .post('/shop/cart/add')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          shopItemId: 'test-item-id',
          quantity: 1
        });

      // Checkout
      const response = await request(app.getHttpServer())
        .post('/shop/cart/checkout')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          paymentMethod: 'kcp_card',
          returnUrl: 'https://example.com/success',
          cancelUrl: 'https://example.com/cancel'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('paymentUrl');
      expect(response.body.data).toHaveProperty('paymentTransactionId');
      expect(response.body.data).toHaveProperty('totalAmount');
      expect(response.body.data).toHaveProperty('orderId');
    });

    it('should return consistent format for plan subscription', async () => {
      const response = await request(app.getHttpServer())
        .post('/plans/subscribe')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          planId: 'test-plan-id',
          paymentMethod: 'kcp_card',
          returnUrl: 'https://example.com/success',
          cancelUrl: 'https://example.com/cancel'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('paymentUrl');
      expect(response.body.data).toHaveProperty('paymentTransactionId');
      expect(response.body.data).toHaveProperty('access_token');
    });

    it('should return consistent format for plan upgrade', async () => {
      // First subscribe to a plan
      await request(app.getHttpServer())
        .post('/plans/subscribe')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          planId: 'basic-plan-id',
          paymentMethod: 'reward_points'
        });

      // Then upgrade
      const response = await request(app.getHttpServer())
        .post('/plans/upgrade')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          planId: 'premium-plan-id',
          paymentMethod: 'kcp_card',
          returnUrl: 'https://example.com/success',
          cancelUrl: 'https://example.com/cancel'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('paymentUrl');
      expect(response.body.data).toHaveProperty('paymentTransactionId');
      expect(response.body.data).toHaveProperty('access_token');
    });
  });

  describe('Webhook Processing', () => {
    it('should process shop item webhook idempotently', async () => {
      const webhookPayload = {
        site_cd: 'TEST_SITE',
        ordr_idxx: `TEST-SHOP-${Date.now()}`,
        tno: `TXN-${Date.now()}`,
        tx_cd: 'TX01',
        tx_tm: new Date().toISOString(),
        res_cd: '0000',
        res_msg: 'SUCCESS',
        amount: '5000',
        good_name: 'Test Shop Item',
        buyr_name: 'Test Student',
        buyr_mail: '<EMAIL>',
        pay_method: '100000000000',
      };

      // Process webhook first time
      const response1 = await request(app.getHttpServer())
        .post('/payment/webhook/kcp')
        .set('x-kcp-signature', 'test-signature')
        .send(webhookPayload);

      expect(response1.status).toBe(200);
      expect(response1.body.result).toBe('0000');

      // Process same webhook again (should be idempotent)
      const response2 = await request(app.getHttpServer())
        .post('/payment/webhook/kcp')
        .set('x-kcp-signature', 'test-signature')
        .send(webhookPayload);

      expect(response2.status).toBe(200);
      expect(response2.body.result).toBe('0000');
    });

    it('should process plan subscription webhook and activate plan', async () => {
      const webhookPayload = {
        site_cd: 'TEST_SITE',
        ordr_idxx: `TEST-PLAN-${Date.now()}`,
        tno: `TXN-${Date.now()}`,
        tx_cd: 'TX01',
        tx_tm: new Date().toISOString(),
        res_cd: '0000',
        res_msg: 'SUCCESS',
        amount: '29900',
        good_name: 'Premium Plan Subscription',
        buyr_name: 'Test Student',
        buyr_mail: '<EMAIL>',
        pay_method: '100000000000',
      };

      const response = await request(app.getHttpServer())
        .post('/payment/webhook/kcp')
        .set('x-kcp-signature', 'test-signature')
        .send(webhookPayload);

      expect(response.status).toBe(200);
      expect(response.body.result).toBe('0000');
      expect(response.body.success).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle failed payment webhook gracefully', async () => {
      const failedWebhookPayload = {
        site_cd: 'TEST_SITE',
        ordr_idxx: `TEST-FAILED-${Date.now()}`,
        tno: `TXN-${Date.now()}`,
        tx_cd: 'TX01',
        tx_tm: new Date().toISOString(),
        res_cd: '9999',
        res_msg: 'PAYMENT_FAILED',
        amount: '5000',
        good_name: 'Test Item',
        buyr_name: 'Test Student',
        buyr_mail: '<EMAIL>',
        pay_method: '100000000000',
      };

      const response = await request(app.getHttpServer())
        .post('/payment/webhook/kcp')
        .set('x-kcp-signature', 'test-signature')
        .send(failedWebhookPayload);

      expect(response.status).toBe(200);
      expect(response.body.result).toBe('0000'); // Still return success to prevent retries
    });

    it('should return consistent error format for invalid requests', async () => {
      const response = await request(app.getHttpServer())
        .post('/plans/subscribe')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          planId: 'invalid-plan-id',
          paymentMethod: 'kcp_card'
        });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body).toHaveProperty('message');
    });
  });
});
