import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTutorGreetingNotificationType1746123456789 implements MigrationInterface {
  name = 'AddTutorGreetingNotificationType1746123456789';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the new notification type to the enum
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'tutor_greeting'`);

    // Also add it to the user notification preference enum
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'tutor_greeting'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // PostgreSQL doesn't support removing values from enums
    // We would need to create a new enum without the value and replace the old one
    // This is complex and potentially dangerous, so we'll leave it as is
  }
}
