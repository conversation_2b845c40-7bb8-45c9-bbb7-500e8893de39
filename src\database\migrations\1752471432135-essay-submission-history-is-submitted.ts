import { MigrationInterface, QueryRunner } from "typeorm";

export class EssaySubmissionHistoryIsSubmitted1752471432135 implements MigrationInterface {
    name = 'EssaySubmissionHistoryIsSubmitted1752471432135'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD "is_submitted" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP COLUMN "is_submitted"`);
    }

}
