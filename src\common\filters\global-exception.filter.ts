import { ExceptionFilter, Catch, ArgumentsHost, HttpStatus, Inject } from '@nestjs/common';
import { Response } from 'express';
import LoggerService from '../services/logger.service';
import { Messages } from '../../constants/messages';

// Define Request interface with all required properties
interface Request {
  user?: { id: string; [key: string]: any };
  method: string;
  url: string;
  headers: any;
  body: any;
  params: any;
  query: any;
}

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  constructor(@Inject(LoggerService) private readonly logger: LoggerService) {}

  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // Default to internal server error
    const status = exception.status || HttpStatus.INTERNAL_SERVER_ERROR;

    // Extract error message
    const message = exception.message || Messages.INTERNAL_SERVER_ERROR;

    // Get detailed error information
    const errorDetail = this.getErrorDetail(exception);

    // Create a unique error reference ID for tracking
    const errorRefId = this.generateErrorRefId();

    // Log the error with stack trace and reference ID
    this.logger.error(`[ERROR-REF:${errorRefId}] [${request.method}] ${request.url} - ${status} - ${message}\n${errorDetail}\n${exception.stack}`);

    // Process validation errors if this is a validation exception
    let validationErrors = null;

    if (status === HttpStatus.BAD_REQUEST) {
      // Check for class-validator validation errors
      if (exception.response && Array.isArray(exception.response.message)) {
        validationErrors = this.processValidationErrors(exception.response.message);
      }
      // Check for custom validation errors format
      else if (exception.response && exception.response.validationErrors) {
        validationErrors = exception.response.validationErrors;
      }
    }

    // Format the response according to our API response model
    const responseBody: any = {
      success: false,
      message: validationErrors ? 'Validation failed' : message,
      data: null, // Always include data field, even if null
      error: {
        type: exception.name || 'Error',
        status: status,
        detail: process.env.NODE_ENV === 'production' ? 'An unexpected error occurred' : errorDetail,
        refId: errorRefId,
      },
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    // Add validation errors if present
    if (validationErrors) {
      responseBody.validationErrors = validationErrors;
    }

    response.status(status).json(responseBody);
  }

  /**
   * Generate a unique error reference ID for tracking
   */
  private generateErrorRefId(): string {
    return `ERR-${Date.now().toString(36)}-${Math.floor(Math.random() * 100000)
      .toString()
      .padStart(5, '0')}`.toUpperCase();
  }

  /**
   * Extract detailed error information from the exception
   */
  private getErrorDetail(exception: any): string {
    if (exception.response) {
      if (typeof exception.response === 'string') {
        return exception.response;
      }
      if (typeof exception.response === 'object') {
        return JSON.stringify(exception.response);
      }
    }

    if (exception.sql) {
      // SQL errors
      return `Database error: ${exception.message}`;
    }

    if (exception.code) {
      // System errors with code
      return `System error (${exception.code}): ${exception.message}`;
    }

    return exception.message || 'Unknown error';
  }

  /**
   * Process validation errors from class-validator
   * @param validationErrors Array of validation error messages
   * @returns Structured validation errors object
   */
  private processValidationErrors(validationErrors: any[]): Record<string, string[]> {
    const errors: Record<string, string[]> = {};

    validationErrors.forEach((error) => {
      // Handle different formats of validation errors
      if (typeof error === 'string') {
        // Format: "property: error message"
        if (error.includes(':')) {
          const [field, message] = error.split(':', 2);
          const fieldName = field.trim();
          const errorMessage = message.trim();

          if (!errors[fieldName]) {
            errors[fieldName] = [];
          }
          errors[fieldName].push(errorMessage);
        }
        // Format: "property property should not exist"
        else if (error.includes('property') && error.includes('should not exist')) {
          // Extract the property name from the error message
          const match = error.match(/property\s+([^\s]+)\s+should/);
          if (match && match[1]) {
            const fieldName = match[1];
            if (!errors[fieldName]) {
              errors[fieldName] = [];
            }
            errors[fieldName].push(error.trim());
          } else {
            // Fallback if we can't extract the field name
            if (!errors['general']) {
              errors['general'] = [];
            }
            errors['general'].push(error.trim());
          }
        }
        // Format: "error message"
        else {
          if (!errors['general']) {
            errors['general'] = [];
          }
          errors['general'].push(error.trim());
        }
      }
      // Handle object format from class-validator
      else if (typeof error === 'object' && error !== null) {
        // Format: { property: string, constraints: { [type]: message } }
        if (error.property && error.constraints) {
          const fieldName = error.property;

          if (!errors[fieldName]) {
            errors[fieldName] = [];
          }

          // Add all constraint messages
          Object.values(error.constraints).forEach((constraint) => {
            errors[fieldName].push(constraint as string);
          });
        }
        // Format: { target: object, property: string, ... }
        else if (error.property) {
          const fieldName = error.property;

          if (!errors[fieldName]) {
            errors[fieldName] = [];
          }

          errors[fieldName].push(error.message || 'Invalid value');
        }
        // Handle whitelist validation errors
        else if (error.children && error.children.length > 0) {
          // Process nested validation errors
          this.processNestedValidationErrors(error, errors);
        }
      }
    });

    return errors;
  }

  /**
   * Process nested validation errors
   * @param error The error object with children
   * @param errors The errors object to populate
   * @param prefix Optional prefix for nested property names
   */
  private processNestedValidationErrors(error: any, errors: Record<string, string[]>, prefix: string = ''): void {
    if (error.children && error.children.length > 0) {
      error.children.forEach((child: any) => {
        const nestedPrefix = prefix ? `${prefix}.${child.property}` : child.property;

        if (child.constraints) {
          const fieldName = nestedPrefix;

          if (!errors[fieldName]) {
            errors[fieldName] = [];
          }

          Object.values(child.constraints).forEach((constraint: any) => {
            errors[fieldName].push(constraint);
          });
        }

        if (child.children && child.children.length > 0) {
          this.processNestedValidationErrors(child, errors, nestedPrefix);
        }
      });
    }
  }
}
