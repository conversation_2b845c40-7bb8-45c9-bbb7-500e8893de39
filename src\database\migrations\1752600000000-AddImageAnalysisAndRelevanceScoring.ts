import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddImageAnalysisAndRelevanceScoring1752600000000 implements MigrationInterface {
  name = 'AddImageAnalysisAndRelevanceScoring1752600000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add image_analysis column to story_maker table
    await queryRunner.query(`
      ALTER TABLE "story_maker" 
      ADD COLUMN "image_analysis" jsonb
    `);

    // Add comment to explain the image_analysis structure
    await queryRunner.query(`
      COMMENT ON COLUMN "story_maker"."image_analysis" IS 
      'AI-generated analysis of the story prompt image containing objects, scene, mood, themes, colors, setting, characters, emotions, description, and relevance keywords'
    `);

    // Add relevance_score column to story_maker_evaluation table
    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "relevance_score" integer
    `);

    // Add comment to explain the relevance_score field
    await queryRunner.query(`
      COMMENT ON COLUMN "story_maker_evaluation"."relevance_score" IS 
      'AI-evaluated relevance score (1-5) indicating how well the story relates to the image prompt. Only populated when image context is available.'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove relevance_score column from story_maker_evaluation table
    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      DROP COLUMN "relevance_score"
    `);

    // Remove image_analysis column from story_maker table
    await queryRunner.query(`
      ALTER TABLE "story_maker" 
      DROP COLUMN "image_analysis"
    `);
  }
}
