import { Injectable, NotFoundException, BadRequestException, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { ShopItem, ShopItemType } from '../../database/entities/shop-item.entity';
import { ShopSkinMapping } from '../../database/entities/shop-skin-mapping.entity';
import { DiarySkin } from '../../database/entities/diary-skin.entity';
import { DiaryService } from '../diary/diary.service';
import { ShopCategoryService } from './shop-category.service';
import { ShopPurchaseService } from './shop-purchase.service';
import { CreateShopItemDto, ShopItemResponseDto, DiarySkinDraftShopItemResponseDto, ShopCategoryResponseDto } from '../../database/models/shop.dto';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';

@Injectable()
export class ShopSkinService {
  private readonly logger = new Logger(ShopSkinService.name);

  constructor(
    @InjectRepository(ShopItem)
    private readonly shopItemRepository: Repository<ShopItem>,
    @InjectRepository(ShopSkinMapping)
    private readonly shopSkinMappingRepository: Repository<ShopSkinMapping>,
    @InjectRepository(DiarySkin)
    private readonly diarySkinRepository: Repository<DiarySkin>,
    @Inject(forwardRef(() => DiaryService))
    private readonly diaryService: DiaryService,
    private readonly dataSource: DataSource,
    private readonly shopCategoryService: ShopCategoryService,
    private readonly shopPurchaseService: ShopPurchaseService,
    @Inject(FileRegistryService)
    private readonly fileRegistryService: FileRegistryService,
  ) {}

  /**
   * Create a shop item from a diary skin
   * @param diarySkinId ID of the diary skin
   * @param createShopItemDto Shop item creation data
   * @returns Created shop item
   */
  async createShopItemFromDiarySkin(diarySkinId: string, createShopItemDto: CreateShopItemDto): Promise<ShopItemResponseDto> {
    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the diary skin using QueryBuilder
      const diarySkinQueryBuilder = this.diarySkinRepository.createQueryBuilder('diarySkin').where('diarySkin.id = :diarySkinId', { diarySkinId });

      const diarySkin = await diarySkinQueryBuilder.getOne();

      if (!diarySkin) {
        throw new NotFoundException(`Diary skin with ID ${diarySkinId} not found`);
      }

      // If no item number is provided, generate one
      if (!createShopItemDto.itemNumber) {
        createShopItemDto.itemNumber = await this.generateItemNumber({
          categoryId: createShopItemDto.categoryId,
        });
      }

      // For skin items, we don't need a file path
      // Instead, we'll use the previewImagePath from the diary skin
      // Get the media URL for the diary skin preview image
      const previewImageUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, diarySkin.id);
      createShopItemDto.filePath = previewImageUrl || diarySkin.previewImagePath;

      // Create the shop item
      const shopItem = this.shopItemRepository.create({
        ...createShopItemDto,
      });

      // Save the shop item using the transaction
      const savedShopItem = await queryRunner.manager.save(shopItem);

      // Create the mapping between the shop item and the diary skin
      const skinMapping = this.shopSkinMappingRepository.create({
        shopItemId: savedShopItem.id,
        diarySkinId: diarySkin.id,
      });

      // Save the mapping using the transaction
      await queryRunner.manager.save(skinMapping);

      // Commit the transaction
      await queryRunner.commitTransaction();

      // Get category name
      const categoryInfo = await this.shopCategoryService.getShopCategoryById(savedShopItem.categoryId);

      return this.mapShopItemToDto(savedShopItem, categoryInfo.name);
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();

      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error(`Error creating shop item from diary skin: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create shop item from diary skin: ${error.message}`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Get or create the skin category
   * @returns The skin category
   */
  async getOrCreateSkinCategory(): Promise<ShopCategoryResponseDto> {
    return this.shopCategoryService.getOrCreateSkinCategory();
  }

  /**
   * Create a draft shop item from a diary skin
   * This doesn't save anything to the database, just returns a draft object
   * @param diarySkinId ID of the diary skin
   * @returns Draft shop item data
   */
  async createDraftShopItemFromDiarySkin(diarySkinId: string): Promise<DiarySkinDraftShopItemResponseDto> {
    try {
      // Get the diary skin
      const diarySkin = await this.diarySkinRepository.findOne({
        where: { id: diarySkinId },
      });

      if (!diarySkin) {
        throw new NotFoundException(`Diary skin with ID ${diarySkinId} not found`);
      }

      // Get or create the skin category
      const skinCategory = await this.shopCategoryService.getOrCreateSkinCategory();

      // Generate a suggested item number
      const suggestedItemNumber = await this.generateItemNumber({
        categoryId: skinCategory.id,
      });

      // Determine if this is a student-created skin
      const isStudentCreated = diarySkin.createdBy ? diarySkin.createdBy.startsWith('student-') : false;
      const studentId = isStudentCreated ? diarySkin.createdBy.replace('student-', '') : undefined;

      // Get the media URL for the diary skin preview image
      const previewImageUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, diarySkin.id);

      // Create the draft response
      const draftShopItem: DiarySkinDraftShopItemResponseDto = {
        diarySkinId: diarySkin.id,
        diarySkinName: diarySkin.name,
        diarySkinDescription: diarySkin.description,
        previewImagePath: previewImageUrl || diarySkin.previewImagePath,
        skinCategoryId: skinCategory.id,
        skinCategoryName: skinCategory.name,
        suggestedItemNumber: suggestedItemNumber,
        suggestedTitle: diarySkin.name,
        suggestedDescription: diarySkin.description,
        suggestedPrice: 9.99, // Default suggested price
        suggestedIsPurchasableInRewardpoint: true, // Default suggested isPurchasableInRewardpoint
        suggestedType: ShopItemType.IN_APP_PURCHASE,
        suggestedIsActive: true,
        suggestedIsFeatured: false,
        suggestedPromotionId: null,
        suggestedMetadata: '',
        isStudentCreated: isStudentCreated,
        studentId: studentId,
      };

      return draftShopItem;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error(`Error creating draft shop item from diary skin: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create draft shop item from diary skin: ${error.message}`);
    }
  }

  /**
   * Get the diary skin associated with a shop item
   * @param shopItemId Shop item ID
   * @returns Diary skin ID
   */
  async getDiarySkinIdForShopItem(shopItemId: string): Promise<string> {
    const queryBuilder = this.shopSkinMappingRepository.createQueryBuilder('mapping').where('mapping.shopItemId = :shopItemId', { shopItemId });

    const mapping = await queryBuilder.getOne();

    if (!mapping) {
      throw new NotFoundException(`No diary skin found for shop item with ID ${shopItemId}`);
    }

    return mapping.diarySkinId;
  }

  /**
   * Apply a purchased skin to a user's diary
   * @param userId User ID
   * @param shopItemId Shop item ID
   * @returns Success message
   */
  async applyPurchasedSkinToDiary(userId: string, shopItemId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Get the shop item first
      const shopItem = await this.shopItemRepository.findOne({
        where: { id: shopItemId },
      });

      if (!shopItem) {
        throw new NotFoundException(`Shop item with ID ${shopItemId} not found`);
      }

      if (!shopItem.isActive) {
        throw new BadRequestException('This skin is no longer available');
      }

      // Check if user can access this item (free items or purchased items)
      let canAccess = false;

      if (shopItem.type === ShopItemType.FREE) {
        // Free items can always be used
        canAccess = true;
      } else {
        // Paid items require purchase validation
        canAccess = await this.shopPurchaseService.hasUserPurchasedItem(userId, shopItemId);
      }

      if (!canAccess) {
        throw new BadRequestException('You must purchase this skin before applying it to your diary');
      }

      // Get the diary skin ID
      const diarySkinId = await this.getDiarySkinIdForShopItem(shopItemId);

      // Apply the skin to the user's diary
      await this.diaryService.setDefaultDiarySkin(userId, diarySkinId);

      return { success: true, message: 'Skin applied to diary successfully' };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      this.logger.error(`Error applying skin to diary: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to apply skin to diary: ${error.message}`);
    }
  }

  /**
   * Generate an item number for a shop item
   * @param options Options for generating the item number
   * @returns Generated item number
   */
  private async generateItemNumber(options: { categoryId: string }): Promise<string> {
    try {
      const { categoryId } = options;

      // Get the category to determine the prefix
      const category = await this.shopCategoryService.getShopCategoryById(categoryId);

      // Use the first two letters of the category name as the prefix
      const prefix = category.name.substring(0, 2).toUpperCase();

      // Find the highest item number with this prefix using QueryBuilder
      const queryBuilder = this.shopItemRepository
        .createQueryBuilder('shopItem')
        .where('shopItem.itemNumber LIKE :pattern', { pattern: `${prefix}-%` })
        .orderBy('shopItem.itemNumber', 'DESC')
        .take(1);

      const item = await queryBuilder.getOne();

      let nextNumber = 1;

      if (item) {
        // Extract the number part from the highest item number
        const highestItemNumber = item.itemNumber;
        const match = highestItemNumber.match(/-(\d+)$/);

        if (match && match[1]) {
          nextNumber = parseInt(match[1], 10) + 1;
        }
      }

      // Format the number with leading zeros (e.g., 001, 010, 100)
      const formattedNumber = nextNumber.toString().padStart(3, '0');
      const itemNumber = `${prefix}-${formattedNumber}`;

      return itemNumber;
    } catch (error) {
      this.logger.error(`Error generating item number: ${error.message}`, error.stack);
      // Return a fallback item number
      const timestamp = Date.now().toString().slice(-6);
      return `ITEM-${timestamp}`;
    }
  }

  /**
   * Map shop item entity to DTO
   * @param shopItem Shop item entity
   * @param categoryName Optional category name
   * @returns Shop item DTO
   */
  private mapShopItemToDto(shopItem: ShopItem, categoryName?: string): ShopItemResponseDto {
    return {
      id: shopItem.id,
      itemNumber: shopItem.itemNumber,
      title: shopItem.title,
      description: shopItem.description,
      categoryId: shopItem.categoryId,
      categoryName: categoryName,
      type: shopItem.type,
      price: Number(shopItem.price),
      isPurchasableInRewardpoint: shopItem.isPurchasableInRewardpoint,
      filePath: shopItem.filePath,
      isActive: shopItem.isActive,
      isFeatured: shopItem.isFeatured,
      promotionId: shopItem.promotionId,
      isPromotionActive: shopItem.isPromotionActive,
      discountedPrice: shopItem.discountedPrice ? Number(shopItem.discountedPrice) : null,
      finalPrice: shopItem.getFinalPrice(),
      isOnSale: shopItem.isOnSale(),
      discountPercentage: shopItem.getDiscountPercentage(),
      metadata: shopItem.metadata,
      purchaseCount: shopItem.purchaseCount,
      viewCount: shopItem.viewCount,
      createdAt: shopItem.createdAt,
      updatedAt: shopItem.updatedAt,
    };
  }
}
