import { applyDecorators } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger';
import { ApiResponse } from '../dto/api-response.dto';
import { PlanResponseDto } from '../../database/models/plans.dto';

/**
 * Custom decorator for documenting plan list responses
 * @param description Description of the response
 * @returns Decorator
 */
export const ApiPlanListResponse = (description = 'Returns all active plans') => {
  return applyDecorators(
    ApiExtraModels(ApiResponse, PlanResponseDto),
    ApiOkResponse({
      description,
      schema: {
        properties: {
          success: { type: 'boolean', example: true },
          message: { type: 'string', example: 'Plans retrieved successfully' },
          statusCode: { type: 'number', example: 200 },
          data: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string', example: '123e4567-e89b-12d3-a456-426614174000' },
                name: { type: 'string', example: 'Premium Plan' },
                type: { type: 'string', example: 'pro' },
                subscriptionType: { type: 'string', example: 'monthly' },
                description: { type: 'string', example: 'Premium features' },
                price: { type: 'number', example: 29.99 },
                durationDays: { type: 'number', example: 30 },
                autoRenew: { type: 'boolean', example: false },
                isActive: { type: 'boolean', example: true },
                legacyFeatures: { type: 'array', items: { type: 'object' } },
                planFeatures: { type: 'array', items: { type: 'object' } },
                createdAt: { type: 'string', format: 'date-time', example: '2023-01-01T00:00:00.000Z' },
                updatedAt: { type: 'string', format: 'date-time', example: '2023-01-01T00:00:00.000Z' },
              },
            },
          },
        },
        required: ['success', 'message', 'statusCode', 'data'],
      },
    }),
  );
};
