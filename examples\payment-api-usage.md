# Payment API Usage Examples

This document provides examples of how to use the improved payment processing APIs with consistent response formats and payment method integration.

## Payment Method Integration

All payment-related APIs now support the following payment methods consistently:
- **`kcp_card`**: KCP Credit Card Payment (maps to KCP: `CARD`)
- **`kcp_bank`**: KCP Bank Transfer (maps to KCP: `BANK`)
- **`kcp_virtual_account`**: KCP Virtual Account (maps to KCP: `VCNT`)
- **`kcp_mobile`**: KCP Mobile Payment (maps to KCP: `MOBX`)
- **`reward_points`**: Use Accumulated Reward Points (no external payment)

**Note**: All KCP payment methods are now supported across both subscription plans and shop item purchases.

## 1. Shop Item Checkout

### Request
```http
POST /shop/cart/checkout
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "paymentMethod": "kcp_card",
  "returnUrl": "https://yourapp.com/payment/success",
  "cancelUrl": "https://yourapp.com/payment/cancel"
}
```

### Response
```json
{
  "success": true,
  "message": "Payment initiated successfully",
  "data": {
    "orderId": "ORDER-123",
    "paymentTransactionId": "TXN-456",
    "paymentUrl": "https://payment.gateway.url/pay?token=abc123",
    "status": "PAYMENT_PENDING",
    "totalAmount": 19980,
    "expiresAt": "2024-01-01T12:30:00Z",
    "items": [...],
    "rewardPointInfo": {...}
  }
}
```

## 2. Plan Subscription

### Request Examples

#### 2.1 Subscribe with KCP Credit Card
```http
POST /plans/subscribe
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "planId": "plan-premium-monthly",
  "paymentMethod": "kcp_card",
  "autoRenew": true,
  "returnUrl": "https://yourapp.com/payment/success",
  "cancelUrl": "https://yourapp.com/payment/cancel"
}
```

#### 2.2 Subscribe with Reward Points (No External Payment)
```http
POST /plans/subscribe
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "planId": "plan-starter-monthly",
  "paymentMethod": "reward_points",
  "autoRenew": false
}
```

#### 2.3 Subscribe with Bank Transfer
```http
POST /plans/subscribe
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "planId": "plan-pro-yearly",
  "paymentMethod": "kcp_bank",
  "autoRenew": true,
  "returnUrl": "https://yourapp.com/payment/success",
  "cancelUrl": "https://yourapp.com/payment/cancel"
}
```

### Response
```json
{
  "success": true,
  "message": "Successfully subscribed to plan",
  "data": {
    "id": "user-plan-123",
    "userId": "user-456",
    "planId": "plan-premium-monthly",
    "planName": "Premium Monthly",
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2024-02-01T00:00:00Z",
    "isActive": false,
    "isPaid": false,
    "autoRenew": true,
    "access_token": "new-jwt-token-with-plan-info",
    "paymentTransactionId": "TXN-789",
    "paymentUrl": "https://payment.gateway.url/pay?token=def456"
  }
}
```

## 3. Plan Upgrade (Now Consistent!)

### Request Examples

#### 3.1 Upgrade with KCP Credit Card
```http
POST /plans/upgrade
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "planId": "plan-premium-yearly",
  "paymentMethod": "kcp_card",
  "autoRenew": true,
  "returnUrl": "https://yourapp.com/payment/success",
  "cancelUrl": "https://yourapp.com/payment/cancel"
}
```

#### 3.2 Upgrade with Reward Points
```http
POST /plans/upgrade
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "planId": "plan-ultimate-monthly",
  "paymentMethod": "reward_points",
  "autoRenew": false
}
```

#### 3.3 Downgrade (with Partial Refund)
```http
POST /plans/upgrade
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "planId": "plan-starter-monthly",
  "paymentMethod": "kcp_card",
  "autoRenew": false,
  "returnUrl": "https://yourapp.com/payment/success",
  "cancelUrl": "https://yourapp.com/payment/cancel"
}
```

### Response (Now includes paymentUrl like other APIs!)
```json
{
  "success": true,
  "message": "Successfully upgraded plan",
  "data": {
    "id": "user-plan-124",
    "userId": "user-456",
    "planId": "plan-premium-yearly",
    "planName": "Premium Yearly",
    "startDate": "2024-01-01T00:00:00Z",
    "endDate": "2025-01-01T00:00:00Z",
    "isActive": false,
    "isPaid": false,
    "autoRenew": true,
    "access_token": "new-jwt-token-with-updated-plan",
    "paymentTransactionId": "TXN-101",
    "paymentUrl": "https://payment.gateway.url/pay?token=ghi789"
  }
}
```

## 4. Payment Flow Explanation

### 4.1 Payment Method Behavior

#### KCP Payment Methods (`kcp_card`, `kcp_bank`, `kcp_virtual_account`, `kcp_mobile`)
1. **API Call**: Include `paymentMethod`, `returnUrl`, and `cancelUrl` in request
2. **Response**: Receive `paymentUrl` for redirection to KCP payment gateway
3. **User Flow**: User completes payment on KCP gateway
4. **Webhook**: KCP sends webhook to complete the transaction
5. **Activation**: Plan/purchase is activated after successful webhook processing

#### Reward Points Payment (`reward_points`)
1. **API Call**: Include `paymentMethod: "reward_points"` (no URLs needed)
2. **Response**: Immediate activation if user has sufficient points
3. **No External Flow**: No redirection or webhook needed
4. **Instant Activation**: Plan/purchase is activated immediately

### 4.2 Response Format Consistency

All payment APIs now return the same structure:
- **With External Payment**: `paymentUrl` provided for redirection
- **With Reward Points**: `paymentUrl` is empty, plan activated immediately
- **Transaction Tracking**: `paymentTransactionId` for all payment types
- **Expiration**: `expiresAt` for payment timeout (KCP payments only)

## 5. Frontend Integration Pattern

### Unified Payment Handler
```javascript
// Enhanced handler for all payment types and methods!
async function handlePaymentResponse(response, paymentMethod) {
  if (!response.success) {
    handlePaymentError(response);
    return;
  }

  const { data } = response;

  // Store transaction ID for tracking
  localStorage.setItem('paymentTransactionId', data.paymentTransactionId);

  if (paymentMethod === 'reward_points') {
    // Reward points payment - immediate activation
    showSuccessMessage('Plan activated successfully using reward points!');

    // Update user token if provided
    if (data.access_token) {
      localStorage.setItem('authToken', data.access_token);
    }

    // Redirect to dashboard or refresh page
    window.location.href = '/dashboard';

  } else if (data.paymentUrl) {
    // KCP payment methods - redirect to gateway
    showInfoMessage('Redirecting to payment gateway...');

    // Set up payment timeout if expiration time is provided
    if (data.expiresAt) {
      const expiresAt = new Date(data.expiresAt);
      const timeoutMs = expiresAt.getTime() - Date.now();

      setTimeout(() => {
        alert('Payment session expired. Please try again.');
        window.location.href = '/plans'; // Redirect back to plans
      }, timeoutMs);
    }

    // Redirect to payment gateway
    window.location.href = data.paymentUrl;

  } else {
    // Unexpected case - no payment URL and not reward points
    console.error('Unexpected payment response:', data);
    showErrorMessage('Payment initialization failed. Please try again.');
  }
}

// Usage examples for different payment methods
async function subscribeWithCard(planId) {
  const requestBody = {
    planId,
    paymentMethod: 'kcp_card',
    autoRenew: true,
    returnUrl: `${window.location.origin}/payment/success`,
    cancelUrl: `${window.location.origin}/payment/cancel`
  };

  const response = await fetch('/plans/subscribe', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getAuthToken()}`
    },
    body: JSON.stringify(requestBody)
  });

  const result = await response.json();
  await handlePaymentResponse(result, 'kcp_card');
}

async function subscribeWithPoints(planId) {
  const requestBody = {
    planId,
    paymentMethod: 'reward_points',
    autoRenew: false
  };

  const response = await fetch('/plans/subscribe', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getAuthToken()}`
    },
    body: JSON.stringify(requestBody)
  });

  const result = await response.json();
  await handlePaymentResponse(result, 'reward_points');
}

async function upgradeWithBankTransfer(planId) {
  const requestBody = {
    planId,
    paymentMethod: 'kcp_bank',
    autoRenew: true,
    returnUrl: `${window.location.origin}/payment/success`,
    cancelUrl: `${window.location.origin}/payment/cancel`
  };

  const response = await fetch('/plans/upgrade', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getAuthToken()}`
    },
    body: JSON.stringify(requestBody)
  });

  const result = await response.json();
  await handlePaymentResponse(result, 'kcp_bank');
}
```

## 6. Payment Method Validation

### 6.1 Frontend Validation
```javascript
function validatePaymentMethod(paymentMethod, returnUrl, cancelUrl) {
  const kcpMethods = ['kcp_card', 'kcp_bank', 'kcp_virtual_account', 'kcp_mobile'];

  if (!paymentMethod) {
    throw new Error('Payment method is required');
  }

  if (!['kcp_card', 'kcp_bank', 'kcp_virtual_account', 'kcp_mobile', 'reward_points', 'free'].includes(paymentMethod)) {
    throw new Error('Invalid payment method');
  }

  // KCP methods require return and cancel URLs
  if (kcpMethods.includes(paymentMethod)) {
    if (!returnUrl || !cancelUrl) {
      throw new Error('Return URL and Cancel URL are required for KCP payment methods');
    }
  }

  return true;
}

// Usage
try {
  validatePaymentMethod('kcp_card', returnUrl, cancelUrl);
  // Proceed with API call
} catch (error) {
  showErrorMessage(error.message);
}
```

### 6.2 Payment Method Selection UI
```javascript
function createPaymentMethodSelector() {
  const paymentMethods = [
    { value: 'kcp_card', label: 'Credit Card', icon: '💳', requiresUrls: true },
    { value: 'kcp_bank', label: 'Bank Transfer', icon: '🏦', requiresUrls: true },
    { value: 'kcp_virtual_account', label: 'Virtual Account', icon: '🏧', requiresUrls: true },
    { value: 'kcp_mobile', label: 'Mobile Payment', icon: '📱', requiresUrls: true },
    { value: 'reward_points', label: 'Reward Points', icon: '🎁', requiresUrls: false },
    { value: 'free', label: 'Free (Testing)', icon: '🆓', requiresUrls: false, devOnly: true }
  ];

  return paymentMethods.map(method => ({
    ...method,
    disabled: method.value === 'reward_points' && !hasEnoughPoints()
  }));
}
```

## 7. Error Handling

### Consistent Error Format
```json
{
  "success": false,
  "message": "Plan not found",
  "error": {
    "code": "PLAN_NOT_FOUND",
    "details": "Plan with ID 'invalid-plan' does not exist"
  }
}
```

### Error Handler
```javascript
function handlePaymentError(response) {
  if (!response.success) {
    console.error('Payment failed:', response.message);

    // Show user-friendly error message
    showErrorMessage(response.message);

    // Log for debugging
    if (response.error?.details) {
      console.debug('Error details:', response.error.details);
    }
  }
}
```

## 6. Webhook Processing

The webhook processing is now simplified and idempotent:

- **Idempotent**: Same webhook can be processed multiple times safely
- **Unified**: Handles both shop items and plan subscriptions
- **Reliable**: Proper error handling and logging

### Webhook Response
```json
{
  "result": "0000",
  "success": true,
  "message": "Webhook processed successfully"
}
```

## 7. Payment Status Tracking

### Check Payment Status
```http
GET /payment/status/TXN-456
Authorization: Bearer <JWT_TOKEN>
```

### Response
```json
{
  "success": true,
  "data": {
    "transactionId": "TXN-456",
    "status": "COMPLETED",
    "amount": 29900,
    "currency": "KRW",
    "paymentMethod": "kcp_card",
    "completedAt": "2024-01-01T12:15:00Z"
  }
}
```

## Benefits of the Improved System

1. **Consistency**: All payment APIs return the same structure
2. **Reliability**: Idempotent webhook processing
3. **Simplicity**: Unified frontend handling logic
4. **Debugging**: Better logging and error messages
5. **Maintainability**: Less code duplication

## Free Payment Method (Development/Testing)

### Option 1: Using Regular Subscribe Endpoint
```http
POST /plans/subscribe
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "planId": "plan-premium-monthly",
  "paymentMethod": "free",
  "autoRenew": false
}
```

### Option 2: Using Dedicated Free Subscribe Endpoint
```http
POST /plans/subscribe/free
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "planId": "plan-premium-monthly",
  "autoRenew": false,
  "reason": "Development testing"
}
```

### Response (Both endpoints)
```json
{
  "success": true,
  "data": {
    "id": "userplan-123",
    "userId": "user-456",
    "planId": "plan-premium-monthly",
    "planName": "Premium Monthly",
    "planType": "premium",
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-02-01T00:00:00.000Z",
    "isActive": true,
    "isPaid": true,
    "autoRenew": false,
    "access_token": "new-jwt-token-with-plan-info",
    "paymentTransactionId": null,
    "paymentUrl": null,
    "expiresAt": null
  }
}
```

### Environment Configuration
```env
# Enable free payment method
ENABLE_FREE_PAYMENT=true
NODE_ENV=development
```

### Important Notes
- **Development Only**: Free payment is protected by environment checks
- **Production Safety**: Automatically disabled in production unless explicitly enabled
- **Audit Logging**: All free payments are logged for tracking
- **Immediate Activation**: Plan is activated instantly without external payment
- **JWT Integration**: Returns updated token with plan information

## Migration Guide

If you're updating existing frontend code:

1. **No Breaking Changes**: All existing fields are still present
2. **New Fields Available**: `paymentUrl`, `paymentTransactionId`, `expiresAt`
3. **Consistent Structure**: Use the same handling logic for all payment types
4. **Better Error Handling**: More detailed error information available
5. **Free Payment Support**: Add 'free' to payment method validation
