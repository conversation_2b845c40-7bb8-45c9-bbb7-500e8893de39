import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveShareTokenFromDiaryEntryFriendShare1721116800000 implements MigrationInterface {
  name = 'RemoveShareTokenFromDiaryEntryFriendShare1721116800000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Remove unused share_token column from diary_entry_friend_share table
    await queryRunner.query(`ALTER TABLE "diary_entry_friend_share" DROP CONSTRAINT IF EXISTS "UQ_diary_entry_friend_share_token"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_entry_friend_share_share_token"`);
    await queryRunner.query(`ALTER TABLE "diary_entry_friend_share" DROP COLUMN IF EXISTS "share_token"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back share_token column if needed for rollback
    await queryRunner.query(`ALTER TABLE "diary_entry_friend_share" ADD COLUMN IF NOT EXISTS "share_token" character varying(255)`);
    await queryRunner.query(`CREATE UNIQUE INDEX IF NOT EXISTS "UQ_diary_entry_friend_share_token" ON "diary_entry_friend_share" ("share_token")`);
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_diary_entry_friend_share_share_token" ON "diary_entry_friend_share" ("share_token")`);
  }
}
