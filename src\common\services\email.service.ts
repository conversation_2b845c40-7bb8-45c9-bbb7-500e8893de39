import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import LoggerService from './logger.service';
import { DeeplinkService, DeeplinkType } from '../utils/deeplink.service';

@Injectable()
class EmailService {
  private transporter: any; // Using any type for simplicity

  constructor(
    private readonly logger: LoggerService,
    private readonly deeplinkService?: DeeplinkService,
  ) {
    // Use Bravo Mail configuration
    this.transporter = nodemailer.createTransport({
      host: process.env.MAIL_HOST || 'smtp-relay.brevo.com',
      port: parseInt(process.env.MAIL_PORT || '587', 10),
      secure: false, // TLS requires secure to be false
      auth: {
        user: process.env.MAIL_USERNAME || '<EMAIL>',
        pass: process.env.MAIL_PASSWORD || 'GRD8dnAEfUjW0stT',
      },
      debug: process.env.NODE_ENV !== 'production', // Enable debug output in non-production environments
    });

    // Log mail configuration
    this.logger.info(`Email service initialized with host: ${process.env.MAIL_HOST || 'smtp-relay.brevo.com'}, port: ${process.env.MAIL_PORT || '587'}`);

    // Verify email configuration on startup in development mode
    if (process.env.NODE_ENV !== 'production') {
      this.verifyConfiguration();
    }
  }

  /**
   * Verify the email configuration by checking the connection to the SMTP server
   * @returns Promise<boolean> True if the configuration is valid, false otherwise
   */
  async verifyConfiguration(): Promise<boolean> {
    try {
      // Verify the connection configuration
      const verification = await this.transporter.verify();
      if (verification) {
        this.logger.info('Email configuration verified successfully');
        return true;
      } else {
        this.logger.error('Email configuration verification failed');
        return false;
      }
    } catch (error) {
      this.logger.error(`Email configuration verification error: ${error.message}`);
      return false;
    }
  }

  async sendEmail(to: string, subject: string, text: string, html?: string) {
    const mailOptions = {
      from: `"${process.env.MAIL_FROM_NAME || 'HEC'}" <${process.env.MAIL_FROM_ADDRESS || '<EMAIL>'}>`,
      to,
      subject,
      text,
      html,
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      this.logger.info(`Email sent to ${to}: ${info.messageId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error sending email to ${to}: ${error}`);
      return false;
    }
  }

  async sendOtp(to: string, otp: string, isPasswordReset: boolean = false) {
    const purpose = isPasswordReset ? 'password reset' : 'account verification';
    const subject = isPasswordReset ? 'HEC Password Reset OTP' : 'HEC Account Verification OTP';
    const expiryTime = isPasswordReset ? '15 minutes' : '5 minutes';

    const text = `Your OTP for ${purpose} is ${otp}. This OTP will expire in ${expiryTime}.`;

    const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #333;">HEC ${isPasswordReset ? 'Password Reset' : 'Account Verification'}</h2>
            </div>
            <div style="margin-bottom: 20px;">
                <p>Hello,</p>
                <p>Your one-time password (OTP) for ${purpose} is:</p>
                <div style="text-align: center; margin: 20px 0;">
                    <div style="font-size: 24px; font-weight: bold; letter-spacing: 5px; padding: 10px; background-color: #f5f5f5; border-radius: 5px;">${otp}</div>
                </div>
                <p>This OTP will expire in ${expiryTime}.</p>
                <p>If you did not request this OTP, please ignore this email or contact support if you have concerns.</p>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                <p>This is an automated message, please do not reply to this email.</p>
                <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
        </div>
        `;

    return this.sendEmail(to, subject, text, html);
  }

  async sendVerificationLink(to: string, token: string) {
    let verificationLink: string;
    if (this.deeplinkService) {
      verificationLink = this.deeplinkService.getWebLink(DeeplinkType.VERIFICATION, {
        additionalParams: { token },
      });
    } else {
      verificationLink = `${process.env.FRONTEND_URL || 'http://localhost:3011'}/verify-email?token=${token}`;
    }

    const subject = 'HEC Account Verification';
    const text = `Thank you for registering with HEC. Please click the following link to verify your email address: ${verificationLink}\n\nThis link will expire in 5 minutes.\n\nIf you did not create an account with us, please ignore this email or contact support if you have concerns.`;

    const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #333;">HEC Account Verification</h2>
            </div>
            <div style="margin-bottom: 20px;">
                <p>Hello,</p>
                <p>Thank you for registering with HEC. Please click the button below to verify your email address:</p>
                <div style="text-align: center; margin: 20px 0;">
                    <a href="${verificationLink}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;">Verify Email</a>
                </div>
                <p>Or copy and paste the following link into your browser:</p>
                <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 5px;">${verificationLink}</p>
                <p>This link will expire in 5 minutes.</p>
                <p>If you did not create an account with us, please ignore this email or contact support if you have concerns.</p>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                <p>This is an automated message, please do not reply to this email.</p>
                <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
        </div>
        `;

    return this.sendEmail(to, subject, text, html);
  }

  async sendPasswordResetLink(to: string, token: string) {
    let resetLink: string;
    if (this.deeplinkService) {
      resetLink = this.deeplinkService.getWebLink(DeeplinkType.PASSWORD_RESET, {
        additionalParams: { token },
      });
    } else {
      resetLink = `${process.env.FRONTEND_URL || 'http://localhost:3011'}/reset-password?token=${token}`;
    }

    const subject = 'HEC Password Reset Link';
    const text = `You requested to reset your password. Please click the following link to reset your password: ${resetLink}\n\nThis link will expire in 5 minutes.\n\nIf you did not request this password reset, please ignore this email or contact support if you have concerns.`;

    const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #333;">HEC Password Reset</h2>
            </div>
            <div style="margin-bottom: 20px;">
                <p>Hello,</p>
                <p>You requested to reset your password. Please click the button below to reset your password:</p>
                <div style="text-align: center; margin: 20px 0;">
                    <a href="${resetLink}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;">Reset Password</a>
                </div>
                <p>Or copy and paste the following link into your browser:</p>
                <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 5px;">${resetLink}</p>
                <p>This link will expire in 5 minutes.</p>
                <p>If you did not request this password reset, please ignore this email or contact support if you have concerns.</p>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                <p>This is an automated message, please do not reply to this email.</p>
                <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
        </div>
        `;

    return this.sendEmail(to, subject, text, html);
  }

  async sendUserId(to: string, userId: string) {
    const subject = 'Your User ID for HEC System';
    const text = `Your User ID is ${userId}. Please use this ID to login to the system.`;

    const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #333;">HEC System - Your User ID</h2>
            </div>
            <div style="margin-bottom: 20px;">
                <p>Hello,</p>
                <p>You requested to recover your User ID. Your User ID is:</p>
                <div style="text-align: center; margin: 20px 0;">
                    <div style="font-size: 24px; font-weight: bold; letter-spacing: 2px; padding: 10px; background-color: #f5f5f5; border-radius: 5px;">${userId}</div>
                </div>
                <p>Please use this User ID to login to the system.</p>
                <p>If you did not request this information, please ignore this email or contact support if you have concerns.</p>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                <p>This is an automated message, please do not reply to this email.</p>
                <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
        </div>
        `;

    return this.sendEmail(to, subject, text, html);
  }

  async sendTutorApprovalEmail(to: string, name: string) {
    const subject = 'HEC Tutor Application Approved';
    const text = `Congratulations ${name}! Your application to become a tutor at HEC has been approved. You can now log in to the system and start using the tutor features.`;

    const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #333;">HEC Tutor Application Approved</h2>
            </div>
            <div style="margin-bottom: 20px;">
                <p>Hello ${name},</p>
                <p>Congratulations! Your application to become a tutor at HEC has been approved.</p>
                <div style="text-align: center; margin: 20px 0;">
                    <div style="font-size: 18px; font-weight: bold; padding: 15px; background-color: #f0f8ff; border-radius: 5px; color: #4CAF50;">Your account is now active</div>
                </div>
                <p>You can now log in to the system and start using the tutor features. We're excited to have you as part of our teaching team!</p>
                <p>If you have any questions or need assistance, please contact our support team.</p>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                <p>This is an automated message, please do not reply to this email.</p>
                <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
        </div>
        `;

    return this.sendEmail(to, subject, text, html);
  }

  async sendTutorRejectionEmail(to: string, name: string, reason: string) {
    const subject = 'HEC Tutor Application Status';
    const text = `Hello ${name}, We regret to inform you that your application to become a tutor at HEC has not been approved at this time. Reason: ${reason}. If you have any questions, please contact our support team.`;

    const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #333;">HEC Tutor Application Status</h2>
            </div>
            <div style="margin-bottom: 20px;">
                <p>Hello ${name},</p>
                <p>We regret to inform you that your application to become a tutor at HEC has not been approved at this time.</p>
                <div style="margin: 20px 0; padding: 15px; background-color: #f5f5f5; border-radius: 5px;">
                    <p><strong>Reason:</strong> ${reason}</p>
                </div>
                <p>If you have any questions or would like to discuss this further, please contact our support team.</p>
                <p>Thank you for your interest in teaching with HEC.</p>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                <p>This is an automated message, please do not reply to this email.</p>
                <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
        </div>
        `;

    return this.sendEmail(to, subject, text, html);
  }

  async sendPasswordChangeNotification(to: string, name: string, changeTime: Date, ipAddress?: string, userAgent?: string) {
    const subject = 'HEC Password Changed Successfully';
    const formattedTime = changeTime.toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short',
    });

    const text = `Hello ${name},

Your HEC account password has been successfully changed.

Change Details:
- Date & Time: ${formattedTime}
${ipAddress ? `- IP Address: ${ipAddress}` : ''}
${userAgent ? `- Device/Browser: ${userAgent}` : ''}

If you did not make this change, please contact our support team immediately or reset your password using the forgot password feature.

For your security:
- Never share your password with anyone
- Use a strong, unique password
- Log out from shared devices

Best regards,
The HEC Team

This is an automated security notification. Please do not reply to this email.`;

    const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
            <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #2c3e50; margin: 0; font-size: 24px;">HEC</h1>
                    <p style="color: #7f8c8d; margin: 5px 0 0 0; font-size: 14px;">Password Change Notification</p>
                </div>

                <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                    <h2 style="color: #155724; margin: 0 0 10px 0; font-size: 18px;">✓ Password Changed Successfully</h2>
                    <p style="color: #155724; margin: 0; font-size: 14px;">Your account password has been updated.</p>
                </div>

                <p style="color: #2c3e50; font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                    Hello <strong>${name}</strong>,
                </p>

                <p style="color: #2c3e50; font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
                    Your HEC account password has been successfully changed.
                </p>

                <div style="background-color: #f8f9fa; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                    <h3 style="color: #2c3e50; margin: 0 0 10px 0; font-size: 16px;">Change Details:</h3>
                    <ul style="color: #2c3e50; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
                        <li><strong>Date & Time:</strong> ${formattedTime}</li>
                        ${ipAddress ? `<li><strong>IP Address:</strong> ${ipAddress}</li>` : ''}
                        ${userAgent ? `<li><strong>Device/Browser:</strong> ${userAgent}</li>` : ''}
                    </ul>
                </div>

                <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                    <h3 style="color: #856404; margin: 0 0 10px 0; font-size: 16px;">⚠️ Security Notice</h3>
                    <p style="color: #856404; margin: 0; font-size: 14px;">
                        If you did not make this change, please contact our support team immediately or reset your password using the forgot password feature.
                    </p>
                </div>

                <div style="background-color: #f8f9fa; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
                    <h3 style="color: #2c3e50; margin: 0 0 10px 0; font-size: 16px;">Security Tips:</h3>
                    <ul style="color: #2c3e50; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
                        <li>Never share your password with anyone</li>
                        <li>Use a strong, unique password</li>
                        <li>Log out from shared devices</li>
                        <li>Enable two-factor authentication if available</li>
                    </ul>
                </div>

                <p style="color: #2c3e50; font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
                    Best regards,<br>
                    <strong>The HEC Team</strong>
                </p>
            </div>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                <p>This is an automated security notification, please do not reply to this email.</p>
                <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
        </div>
        `;

    return this.sendEmail(to, subject, text, html);
  }

  async sendUserCreationEmail(to: string, userId: string, temporaryPassword: string, userType: string) {
    const subject = `Welcome to HEC - Your ${userType} Account Created`;
    const text = `Welcome to HEC! Your ${userType} account has been created. User ID: ${userId}, Temporary Password: ${temporaryPassword}. Please change your password after first login and update your profile for security.`;

    const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #333;">Welcome to HEC!</h2>
                <p style="color: #666; font-size: 16px;">Your ${userType} account has been created</p>
            </div>
            <div style="margin-bottom: 20px;">
                <p>Hello,</p>
                <p>Your ${userType} account has been successfully created by our administrator. Below are your login credentials:</p>

                <div style="margin: 20px 0; padding: 20px; background-color: #f8f9fa; border-radius: 8px; border-left: 4px solid #4CAF50;">
                    <h3 style="margin: 0 0 15px 0; color: #333;">Login Credentials</h3>
                    <p style="margin: 5px 0;"><strong>User ID:</strong> <span style="font-family: monospace; background-color: #e9ecef; padding: 2px 6px; border-radius: 3px;">${userId}</span></p>
                    <p style="margin: 5px 0;"><strong>Temporary Password:</strong> <span style="font-family: monospace; background-color: #e9ecef; padding: 2px 6px; border-radius: 3px;">${temporaryPassword}</span></p>
                </div>

                <div style="margin: 20px 0; padding: 15px; background-color: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
                    <h4 style="margin: 0 0 10px 0; color: #856404;">🔒 Important Security Notice</h4>
                    <ul style="margin: 0; padding-left: 20px; color: #856404;">
                        <li>Please change your password immediately after your first login</li>
                        <li>Update your profile information for better security</li>
                        <li>Do not share your login credentials with anyone</li>
                    </ul>
                </div>

                <div style="margin: 20px 0; padding: 15px; background-color: #d1ecf1; border-radius: 5px; border-left: 4px solid #17a2b8;">
                    <h4 style="margin: 0 0 10px 0; color: #0c5460;">📝 Getting Started</h4>
                    <ul style="margin: 0; padding-left: 20px; color: #0c5460;">
                        <li>Log in to the platform using your credentials</li>
                        <li>Complete your profile setup</li>
                        <li>Explore the platform features</li>
                        <li>Contact support if you need any assistance</li>
                    </ul>
                </div>

                <p>We're excited to have you as part of the HEC community!</p>
            </div>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                <p>This is an automated message, please do not reply to this email.</p>
                <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
        </div>
        `;

    return this.sendEmail(to, subject, text, html);
  }
}

export default EmailService;
