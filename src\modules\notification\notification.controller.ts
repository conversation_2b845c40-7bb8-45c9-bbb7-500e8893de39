import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { GetUser } from '../../common/decorators/get-user.decorator';
import { User } from '../../database/entities/user.entity';
import { NotificationService } from './notification.service';
import { NotificationResponseDto, MarkNotificationReadDto, NotificationFilterDto } from '../../database/models/notification.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { NotificationType } from '../../database/entities/notification.entity';

@ApiTags('notifications')
@Controller('notifications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get()
  @ApiOperation({ summary: 'Get notifications for the current user' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortDirection', required: false, enum: ['ASC', 'DESC'] })
  @ApiQuery({ name: 'type', required: false, enum: ['diary_submission', 'diary_review', 'diary_feedback', 'tutor_assignment', 'system'] })
  @ApiQuery({ name: 'isRead', required: false, type: Boolean })
  @ApiOkResponseWithType(PagedListDto, 'Notifications retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getNotifications(
    @GetUser() user: User,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
    @Query('type') type?: NotificationType,
    @Query('isRead') isRead?: boolean,
  ): Promise<ApiResponse<PagedListDto<NotificationResponseDto>>> {
    const paginationDto: PaginationDto = {
      page: page ? Number(page) : 1,
      limit: limit ? Number(limit) : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC',
    };

    const filterDto: NotificationFilterDto = {
      type,
      isRead: isRead !== undefined ? (typeof isRead === 'string' ? isRead === 'true' : isRead) : undefined,
    };

    const notifications = await this.notificationService.getUserNotifications(user.id, filterDto, paginationDto);
    return ApiResponse.success(notifications, 'Notifications retrieved successfully');
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific notification' })
  @ApiOkResponseWithType(NotificationResponseDto, 'Notification retrieved successfully')
  @ApiErrorResponse(404, 'Notification not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getNotification(@GetUser() user: User, @Param('id') id: string): Promise<ApiResponse<NotificationResponseDto>> {
    const notifications = await this.notificationService.getUserNotifications(user.id);
    const notification = notifications.items.find((n) => n.id === id);

    if (!notification) {
      return ApiResponse.error('Notification not found', 404, {});
    }

    return ApiResponse.success(notification, 'Notification retrieved successfully');
  }

  @Post(':id/read')
  @ApiOperation({ summary: 'Mark a notification as read or unread' })
  @ApiOkResponseWithType(NotificationResponseDto, 'Notification updated successfully')
  @ApiErrorResponse(404, 'Notification not found')
  @ApiErrorResponse(500, 'Internal server error')
  async markNotificationRead(@GetUser() user: User, @Param('id') id: string, @Body() markReadDto: MarkNotificationReadDto): Promise<ApiResponse<NotificationResponseDto>> {
    const notification = await this.notificationService.markNotificationRead(id, user.id, markReadDto.isRead);
    return ApiResponse.success(notification, 'Notification updated successfully');
  }

  @Post('read-all')
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiOkResponseWithType(Number, 'Notifications marked as read successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async markAllNotificationsRead(@GetUser() user: User): Promise<ApiResponse<number>> {
    const count = await this.notificationService.markAllNotificationsRead(user.id);
    return ApiResponse.success(count, `${count} notifications marked as read`);
  }
}
