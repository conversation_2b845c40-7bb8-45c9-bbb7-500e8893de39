import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Inject } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserType } from '../../database/entities/user.entity';
import { Messages } from '../../constants/messages';
import { JwtPayload } from '../../modules/auth/interfaces/jwt-payload.interface';
import LoggerService from '../services/logger.service';
import { SubscriptionService } from '../services/subscription.service';
import { STUDENT_ONLY_KEY } from '../decorators/student-only.decorator';
import { STRICT_STUDENT_ONLY_KEY } from '../decorators/strict-student-only.decorator';
import { IS_PUBLIC_KEY } from '../decorators/public-api.decorator';

@Injectable()
export class StudentGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    @Inject(LoggerService) private logger: LoggerService,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [context.getHandler(), context.getClass()]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user as JwtPayload;

    // SECURITY FIX: Always require authentication for non-public endpoints
    if (!user) {
      this.logger.warn(`Student access check failed: No user in request - ${request.method} ${request.url}`);
      throw new ForbiddenException(Messages.UNAUTHORIZED);
    }

    const isStudentOnly = this.reflector.getAllAndOverride<boolean>(STUDENT_ONLY_KEY, [context.getHandler(), context.getClass()]);
    const isStrictStudentOnly = this.reflector.getAllAndOverride<boolean>(STRICT_STUDENT_ONLY_KEY, [context.getHandler(), context.getClass()]);

    // If the endpoint is not marked as student-only or strict-student-only,
    // still allow access but only for authenticated users (which we've already checked above)
    if (!isStudentOnly && !isStrictStudentOnly) {
      return true;
    }

    // Check if user is a student
    const isStudent = user.type === UserType.STUDENT || (user.roles && Array.isArray(user.roles) && user.roles.includes('student'));

    // For strict student-only endpoints, only allow students
    if (isStrictStudentOnly) {
      if (!isStudent) {
        this.logger.warn(`Strict student access check failed: User ${user.id} with role ${user.type} is not a student`);
        throw new ForbiddenException('This resource is only accessible to students');
      }

      // Check if student has an active subscription by querying database
      // This fixes the issue where JWT payload might be stale during incomplete upgrades
      try {
        await this.subscriptionService.validateActiveSubscription(user);
      } catch (error) {
        this.logger.warn(`Student access check failed: User ${user.id} has no active plan`);
        throw error; // Re-throw the ForbiddenException from the service
      }

      return true;
    }

    // For regular student-only endpoints, allow admin and tutors as well
    const isAdmin = user.type === UserType.ADMIN || (user.roles && Array.isArray(user.roles) && user.roles.includes('admin'));
    const isTutor = user.type === UserType.TUTOR || (user.roles && Array.isArray(user.roles) && user.roles.includes('tutor'));

    // Check if student has an active subscription by querying database
    // This fixes the issue where JWT payload might be stale during incomplete upgrades
    if (isStudent) {
      try {
        await this.subscriptionService.validateActiveSubscription(user);
      } catch (error) {
        this.logger.warn(`Student access check failed: User ${user.id} has no active plan`);
        throw error; // Re-throw the ForbiddenException from the service
      }
    }

    if (isStudent || isAdmin || isTutor) {
      return true;
    }

    this.logger.warn(`Student access check failed: User ${user.id} with role ${user.type} is not authorized`);
    throw new ForbiddenException(Messages.FORBIDDEN);
  }

}
