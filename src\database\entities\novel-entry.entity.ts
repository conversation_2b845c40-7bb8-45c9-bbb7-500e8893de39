import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToMany, OneToOne } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { NovelTopic } from './novel-topic.entity';
import { User } from './user.entity';
import { DiarySkin } from './diary-skin.entity';
import { NovelFeedback } from './novel-feedback.entity';
import { NovelCorrection } from './novel-correction.entity';
import { NovelEntryHistory } from './novel-entry-history.entity';
import { WritingEntryStatus } from '../../common/enums/writing-entry-status.enum';
import { IWritingEntry } from '../../common/interfaces/writing-entry.interface';

/**
 * Status of a novel entry in its lifecycle
 * LIFECYCLE: NEW → SUBMITTED → REVIEWED (Final State)
 * Note: CONFIRMED stage has been removed from the lifecycle
 */
export enum NovelEntryStatus {
  /** Entry is still being edited by the student */
  NEW = 'new',
  /** Entry has been submitted for review */
  SUBMITTED = 'submitted',
  /** Entry has been reviewed by a tutor - FINAL STATE */
  REVIEWED = 'reviewed',
  /** @deprecated CONFIRMED stage removed - use REVIEWED instead */
  CONFIRMED = 'confirmed',

  // Legacy values for backward compatibility
  /** @deprecated Use SUBMITTED instead */
  LEGACY_UPDATED = 'updated',
  /** @deprecated Use REVIEWED instead */
  LEGACY_CORRECTION_GIVEN = 'correction_given',
  /** @deprecated Use SUBMITTED instead */
  LEGACY_UNDER_REVIEW = 'under_review',
}

@Entity()
export class NovelEntry extends AuditableBaseEntity implements Partial<IWritingEntry> {
  @Column({ name: 'topic_id' })
  topicId: string;

  @ManyToOne(() => NovelTopic, (topic) => topic.entries)
  @JoinColumn({ name: 'topic_id' })
  topic: NovelTopic;

  @Column({ name: 'student_id' })
  studentId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({ name: 'word_count', type: 'integer', default: 0 })
  wordCount: number;

  @Column({
    name: 'status',
    type: 'enum',
    enum: NovelEntryStatus,
    default: NovelEntryStatus.NEW,
  })
  status: NovelEntryStatus;

  @Column({
    name: 'unified_status',
    type: 'enum',
    enum: WritingEntryStatus,
    default: WritingEntryStatus.DRAFT,
  })
  unifiedStatus: WritingEntryStatus;

  @Column({ name: 'skin_id', nullable: true })
  skinId: string;

  @ManyToOne(() => DiarySkin, { nullable: true })
  @JoinColumn({ name: 'skin_id' })
  skin: DiarySkin;

  @Column({ name: 'background_color', nullable: true })
  backgroundColor: string;

  @Column({ name: 'submitted_at', type: 'timestamp', nullable: true })
  submittedAt: Date;

  @Column({ name: 'reviewed_at', type: 'timestamp', nullable: true })
  reviewedAt: Date;

  @Column({ name: 'reviewed_by', nullable: true })
  reviewedBy: string;

  @Column({ name: 'evaluated_at', type: 'timestamp', nullable: true })
  evaluatedAt: Date;

  @Column({ name: 'evaluated_by', nullable: true })
  evaluatedBy: string;

  @Column({ name: 'gained_score', type: 'integer', nullable: true })
  gainedScore: number;

  @Column({ name: 'score', type: 'integer', nullable: true })
  score: number;

  @OneToMany(() => NovelFeedback, (feedback) => feedback.entry)
  feedbacks: NovelFeedback[];

  @OneToOne(() => NovelCorrection, (correction) => correction.entry)
  correction: NovelCorrection;

  // New fields for submission/draft logic
  @Column({ name: 'is_draft', type: 'boolean', default: true })
  isDraft: boolean;

  @Column({ name: 'last_submitted_at', type: 'timestamp', nullable: true })
  lastSubmittedAt: Date;

  @Column({ name: 'last_reviewed_at', type: 'timestamp', nullable: true })
  lastReviewedAt: Date;

  @Column({ name: 'can_submit_new_version', type: 'boolean', default: true })
  canSubmitNewVersion: boolean;

  @Column({ name: 'submitted_version_count', type: 'integer', default: 0 })
  submittedVersionCount: number;

  @Column({ name: 'current_submitted_version_id', type: 'uuid', nullable: true })
  currentSubmittedVersionId: string;

  // Resubmission tracking fields
  @Column({ name: 'is_resubmission', type: 'boolean', default: false })
  isResubmission: boolean;

  @Column({ name: 'resubmission_type', type: 'varchar', nullable: true })
  resubmissionType: 'after_review' | null;

  @Column({ name: 'previous_review_count', type: 'integer', default: 0 })
  previousReviewCount: number;

  @Column({ name: 'previous_confirmation_count', type: 'integer', default: 0 })
  previousConfirmationCount: number;

  @OneToOne(() => NovelEntryHistory, { nullable: true })
  @JoinColumn({ name: 'current_submitted_version_id' })
  currentSubmittedVersion: NovelEntryHistory;

  // Version tracking fields
  @Column({ name: 'current_version_id', type: 'uuid', nullable: true })
  currentVersionId: string;

  @OneToOne(() => NovelEntryHistory, { nullable: true })
  @JoinColumn({ name: 'current_version_id' })
  currentVersion: NovelEntryHistory;

  @Column({ name: 'total_edit_history', type: 'integer', default: 0 })
  totalEditHistory: number;

  @OneToMany(() => NovelEntryHistory, (version) => version.novelEntry)
  versions: NovelEntryHistory[];

  // Notification tracking fields
  @Column({ name: 'first_submission_notified', type: 'boolean', default: false })
  firstSubmissionNotified: boolean;

  @Column({ name: 'original_reviewed_version_id', type: 'uuid', nullable: true })
  originalReviewedVersionId: string;

  @OneToOne(() => NovelEntryHistory, { nullable: true })
  @JoinColumn({ name: 'original_reviewed_version_id' })
  originalReviewedVersion: NovelEntryHistory;
}
