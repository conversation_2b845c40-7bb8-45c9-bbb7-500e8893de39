import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { Plan } from './plan.entity';

@Entity()
export class UserPlan extends AuditableBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'plan_id' })
  planId: string;

  @ManyToOne(() => User, (user) => user.userPlans)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => Plan, (plan) => plan.userPlans)
  @JoinColumn({ name: 'plan_id' })
  plan: Plan;

  // Helper method to get a simple representation of this user plan
  toSimpleObject() {
    // Make sure the plan has planFeatures loaded
    const planObject = this.plan ? this.plan.toSimpleObject() : null;

    // If plan exists but doesn't have features, try to add them
    if (planObject && (!planObject.features || planObject.features.length === 0) && this.plan.planFeatures) {
      planObject.features = this.plan.planFeatures.map((feature) => feature.toSimpleObject());
    }

    return {
      id: this.id,
      userId: this.userId,
      planId: this.planId,
      startDate: this.startDate,
      endDate: this.endDate,
      isActive: this.isActive,
      isPaid: this.isPaid,
      autoRenew: this.autoRenew,
      lastRenewalDate: this.lastRenewalDate,
      nextRenewalDate: this.nextRenewalDate,
      cancellationDate: this.cancellationDate,
      notes: this.notes,
      plan: planObject,
    };
  }

  @Column({ name: 'start_date' })
  startDate: Date;

  @Column({ name: 'end_date' })
  endDate: Date;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'payment_reference', nullable: true })
  paymentReference: string;

  @Column({ name: 'is_paid', default: false })
  isPaid: boolean;

  @Column({ name: 'auto_renew', default: false })
  autoRenew: boolean;

  @Column({ name: 'last_renewal_date', nullable: true })
  lastRenewalDate: Date;

  @Column({ name: 'next_renewal_date', nullable: true })
  nextRenewalDate: Date;

  @Column({ name: 'cancellation_date', nullable: true })
  cancellationDate: Date;

  @Column({ name: 'notes', type: 'text', nullable: true })
  notes: string;

  @Column({ name: 'payment_transaction_id', nullable: true })
  paymentTransactionId: string;
}
