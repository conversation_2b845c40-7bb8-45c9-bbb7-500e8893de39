import { MigrationInterface, QueryRunner } from 'typeorm';

export class QAMissionSeed1746339411890 implements MigrationInterface {
  name = 'QAMissionSeed1746339411890';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "qa_mission_month" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "title" character varying NOT NULL, "display" character varying NOT NULL, "sequence" integer NOT NULL, "year" integer NOT NULL, CONSTRAINT "PK_bb13566db454bdf3b7c4a0d0c5d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "qa_mission_week" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "title" character varying NOT NULL, "sequence" integer NOT NULL, "month" character varying NOT NULL, "start_date" date NOT NULL, "end_date" date NOT NULL, "year" integer NOT NULL, CONSTRAINT "PK_3abefaedf983f685f2c25a4292e" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "qa_mission_week"`);
    await queryRunner.query(`DROP TABLE "qa_mission_month"`);
  }
}
