import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>T<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { QAWeeklyMissionTasks } from './qa-weekly-mission-tasks.entity';
import { QAMonthlyMissionTasks } from './qa-monthly-mission-tasks.entity';
import { QAMissionMonth } from './qa-mission-month.entity';
import { QAMissionWeek } from './qa-mission-week.entity';
import { QAMissionTasks } from './qa-mission-tasks.entity';

export enum QAMissionFrequency {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

@Entity()
export class QAMission extends AuditableBaseEntity {
  @Column({
    name: 'time_frequency',
    type: 'enum',
    enum: QAMissionFrequency,
    default: QAMissionFrequency.WEEKLY,
  })
  timeFrequency: QAMissionFrequency;

  @Column({
    name: 'is_active',
    default: true,
    type: 'boolean',
  })
  isActive: boolean;

  @OneToMany(() => QAMissionTasks, (task) => task.mission)
  tasks: QAMissionTasks[];

  @OneToMany(() => QAWeeklyMissionTasks, (task) => task.mission)
  weeklyTasks: QAWeeklyMissionTasks[];

  @OneToMany(() => QAMonthlyMissionTasks, (task) => task.mission)
  monthlyTasks: QAMonthlyMissionTasks[];

  // Optional relation to weekly mission
  @ManyToOne(() => QAMissionWeek, { nullable: true })
  @JoinColumn({ name: 'week_id' })
  week?: QAMissionWeek;

  @Column({ name: 'week_id', nullable: true })
  weekId?: string;

  // Optional relation to monthly mission
  @ManyToOne(() => QAMissionMonth, { nullable: true })
  @JoinColumn({ name: 'month_id' })
  month?: QAMissionMonth;

  @Column({ name: 'month_id', nullable: true })
  monthId?: string;

  @Column({ name: 'sequence_number', type: 'int', nullable: true })
  sequenceNumber?: number;

  // Optional: helper getter (not saved to DB)
  get taskLists(): QAWeeklyMissionTasks[] | QAMonthlyMissionTasks[] {
    return this.timeFrequency === QAMissionFrequency.WEEKLY ? this.weeklyTasks : this.monthlyTasks;
  }
}
