import { Entity, Column } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';

/**
 * Module that the award belongs to
 * @enum {string}
 */
export enum AwardModule {
  /** Awards for the diary module */
  DIARY = 'diary',
  /** Awards for the novel module */
  NOVEL = 'novel',
  /** Awards for the essay module */
  ESSAY = 'essay',
}

/**
 * Criteria for the award
 * @enum {string}
 */
export enum AwardCriteria {
  /** Based on diary entry scores */
  DIARY_SCORE = 'diary_score',
  /** Based on attendance */
  ATTENDANCE = 'attendance',
  /** Based on diary decoration */
  DIARY_DECORATION = 'diary_decoration',
  /** Based on diary friendship and social engagement */
  DIARY_FRIENDSHIP = 'diary_friendship',
  /** Based on play module performance */
  PLAY_PERFORMANCE = 'play_performance',
  /** Based on Q&A module performance */
  QA_PERFORMANCE = 'qa_performance',
  /** Based on novel module performance */
  NOVEL_PERFORMANCE = 'novel_performance',
  /** Based on essay module performance */
  ESSAY_PERFORMANCE = 'essay_performance',
  /** Custom criteria */
  CUSTOM = 'custom',
}

/**
 * Frequency of the award
 * @enum {string}
 */
export enum AwardFrequency {
  /** Weekly awards */
  WEEKLY = 'weekly',
  /** Monthly awards */
  MONTHLY = 'monthly',
  /** Quarterly awards */
  QUARTERLY = 'quarterly',
  /** Yearly awards */
  YEARLY = 'yearly',
  /** One-time awards */
  ONE_TIME = 'one_time',
}

@Entity()
export class Award extends AuditableBaseEntity {
  @Column({ name: 'name' })
  name: string;

  @Column({ name: 'description', type: 'text' })
  description: string;

  @Column({
    name: 'module',
    type: 'enum',
    enum: AwardModule,
  })
  module: AwardModule;

  @Column({
    name: 'criteria',
    type: 'enum',
    enum: AwardCriteria,
    array: true,
  })
  criteria: AwardCriteria[];

  @Column({
    name: 'frequency',
    type: 'enum',
    enum: AwardFrequency,
  })
  frequency: AwardFrequency;

  @Column({ name: 'reward_points', type: 'int' })
  rewardPoints: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'criteria_config', nullable: true, type: 'json' })
  criteriaConfig: any;

  @Column({ name: 'start_date', nullable: true })
  startDate: Date;

  @Column({ name: 'end_date', nullable: true })
  endDate: Date;

  @Column({ name: 'image_url', nullable: true })
  imageUrl: string;
}
