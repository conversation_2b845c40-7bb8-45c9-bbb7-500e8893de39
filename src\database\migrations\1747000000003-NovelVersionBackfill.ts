import { MigrationInterface, QueryRunner } from 'typeorm';

export class NovelVersionBackfill1747000000003 implements MigrationInterface {
  name = 'NovelVersionBackfill1747000000003';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('Starting novel version backfill migration...');

    // Check if novel_entry_history table exists
    const historyTableExists = await queryRunner.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'novel_entry_history'
      )
    `);

    if (!historyTableExists[0].exists) {
      console.log('novel_entry_history table does not exist. Skipping backfill.');
      return;
    }

    // Get entries that need backfill
    const entries = await queryRunner.query(`
      SELECT id, content, student_id, created_at, updated_at, status
      FROM novel_entry
      WHERE content IS NOT NULL 
        AND content != ''
        AND (total_versions = 0 OR total_versions IS NULL)
        AND NOT EXISTS (
          SELECT 1 FROM novel_entry_history 
          WHERE novel_entry_id = novel_entry.id
        )
    `);

    console.log(`Found ${entries.length} entries to backfill`);

    if (entries.length === 0) {
      console.log('No entries need backfill');
      return;
    }

    let processed = 0;

    for (const entry of entries) {
      try {
        // Calculate word count
        const content = entry.content || '';
        const wordCount = content
          .trim()
          .split(/\s+/)
          .filter((word) => word.length > 0).length;

        // Insert version history
        const versionResult = await queryRunner.query(
          `
          INSERT INTO novel_entry_history (
            novel_entry_id, content, version_number, is_latest, word_count,
            meta_data, created_by, updated_by, created_at, updated_at
          ) VALUES ($1, $2, 1, true, $3, $4, $5, $6, $7, $8)
          RETURNING id
        `,
          [entry.id, content, wordCount, JSON.stringify({ updateTrigger: 'migration', note: 'Initial version' }), entry.student_id, entry.student_id, entry.created_at, entry.updated_at],
        );

        const versionId = versionResult[0].id;

        // Update novel entry
        await queryRunner.query(
          `
          UPDATE novel_entry 
          SET 
            current_version_id = $1,
            total_versions = 1,
            first_submission_notified = CASE 
              WHEN status IN ('submitted', 'updated', 'correction_given', 'reviewed', 'confirmed') 
              THEN true 
              ELSE false 
            END
          WHERE id = $2
        `,
          [versionId, entry.id],
        );

        processed++;

        if (processed % 10 === 0) {
          console.log(`Processed ${processed}/${entries.length} entries`);
        }
      } catch (error) {
        console.error(`Error processing entry ${entry.id}:`, error.message);
      }
    }

    console.log(`Migration completed. Processed ${processed} entries.`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('Rolling back novel version backfill...');

    // Remove version history records created by migration
    await queryRunner.query(`
      DELETE FROM novel_entry_history 
      WHERE meta_data->>'updateTrigger' = 'migration'
    `);

    // Reset novel entry fields
    await queryRunner.query(`
      UPDATE novel_entry 
      SET 
        current_version_id = NULL,
        total_versions = 0,
        first_submission_notified = false
      WHERE total_versions = 1
    `);

    console.log('Rollback completed');
  }
}
