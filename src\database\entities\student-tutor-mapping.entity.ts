import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, Join<PERSON><PERSON>umn, Unique } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { PlanFeature } from './plan-feature.entity';

/**
 * Status of the student-tutor mapping
 * @enum {string}
 */
export enum MappingStatus {
  /** Mapping is active */
  ACTIVE = 'active',
  /** Mapping is inactive */
  INACTIVE = 'inactive',
}

@Entity()
@Unique(['studentId', 'planFeatureId'])
export class StudentTutorMapping extends AuditableBaseEntity {
  @Column({ name: 'student_id' })
  studentId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  @Column({ name: 'tutor_id' })
  tutorId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'tutor_id' })
  tutor: User;

  @Column({ name: 'plan_feature_id' })
  planFeatureId: string;

  @ManyToOne(() => PlanFeature)
  @JoinColumn({ name: 'plan_feature_id' })
  planFeature: PlanFeature;

  @Column({
    name: 'status',
    type: 'enum',
    enum: MappingStatus,
    default: MappingStatus.ACTIVE,
  })
  status: MappingStatus;

  @Column({ name: 'assigned_date' })
  assignedDate: Date;

  @Column({ name: 'last_activity_date', nullable: true })
  lastActivityDate: Date;

  @Column({ name: 'notes', nullable: true, type: 'text' })
  notes: string;
}
