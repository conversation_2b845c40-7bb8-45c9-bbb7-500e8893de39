import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

/**
 * Entity representing the virtual admin user used in admin conversations
 * This entity ensures there's always a consistent admin participant for all admin conversations
 */
@Entity('virtual_admin')
export class VirtualAdmin extends AuditableBaseEntity {
  /**
   * The user ID that represents this virtual admin
   * This will be used as participant1_id in admin conversations
   */
  @Column({ name: 'user_id', unique: true })
  userId: string;

  @OneToOne(() => User, { eager: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  /**
   * Display name for the virtual admin
   */
  @Column({ name: 'display_name', default: 'HEC Admin' })
  displayName: string;

  /**
   * Description of this virtual admin's purpose
   */
  @Column({ name: 'description', nullable: true })
  description: string;

  /**
   * Whether this virtual admin is currently active
   */
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  /**
   * Configuration settings for the virtual admin (stored as JSON)
   */
  @Column({ name: 'settings', type: 'jsonb', nullable: true })
  settings: Record<string, any>;
}
