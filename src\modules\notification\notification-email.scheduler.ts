import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { NotificationService } from './notification.service';

@Injectable()
export class NotificationEmailScheduler {
  private readonly logger = new Logger(NotificationEmailScheduler.name);

  constructor(private readonly notificationService: NotificationService) {}

  /**
   * Retry failed notification deliveries every hour
   */
  @Cron(CronExpression.EVERY_HOUR)
  async retryFailedDeliveries(): Promise<void> {
    try {
      this.logger.log('Starting retry of failed notification deliveries');
      const retriedCount = await this.notificationService.retryFailedDeliveries();
      this.logger.log(`Successfully retried ${retriedCount} failed deliveries`);
    } catch (error) {
      this.logger.error(`Error retrying failed deliveries: ${error.message}`, error.stack);
    }
  }
}
