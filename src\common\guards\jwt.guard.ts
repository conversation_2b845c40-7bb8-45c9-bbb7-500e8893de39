import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, Inject } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { IS_PUBLIC_KEY } from '../decorators/public-api.decorator';
import LoggerService from '../services/logger.service';
import { TokenBlacklistService } from '../services/token-blacklist.service';
import { Messages } from '../../constants/messages';
import { JwtPayload } from '../../modules/auth/interfaces/jwt-payload.interface';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    @Inject(JwtService) private jwtService: JwtService,
    private reflector: Reflector,
    private logger: LoggerService,
    private configService: ConfigService,
    private tokenBlacklistService: TokenBlacklistService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [context.getHandler(), context.getClass()]);

    // Allow access to public endpoints without authentication
    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    // Check if Authorization header exists
    if (!authHeader) {
      this.logger.warn(`Unauthorized access attempt: No authorization header - ${request.method} ${request.url}`);
      throw new UnauthorizedException(Messages.UNAUTHORIZED);
    }

    // Extract and verify token
    const token = authHeader.split(' ')[1];
    if (!token) {
      this.logger.warn(`Unauthorized access attempt: No token provided - ${request.method} ${request.url}`);
      throw new UnauthorizedException(Messages.UNAUTHORIZED);
    }

    try {
      // Verify JWT token with the secret from config
      const secret = this.configService.get<string>('JWT_SECRET');
      if (!secret) {
        this.logger.error('JWT_SECRET is not defined in environment variables');
        throw new Error('JWT_SECRET is not defined');
      }
      const decoded = this.jwtService.verify<JwtPayload>(token, { secret });

      // Check if token is blacklisted (logged out)
      const isBlacklisted = await this.tokenBlacklistService.isTokenBlacklisted(token);
      if (isBlacklisted) {
        this.logger.warn(`Blacklisted token used: ${decoded.sub} - ${request.method} ${request.url}`);
        throw new UnauthorizedException('Token has been invalidated');
      }

      request.user = decoded;

      // Log successful authentication with detailed user info
      this.logger.info(`Authenticated user: ${decoded.sub} - ${request.method} ${request.url}`);
      this.logger.info(`User details: type=${decoded.type}, roles=${JSON.stringify(decoded.roles)}, selectedRole=${decoded.selectedRole || 'none'}`);

      return true;
    } catch (err) {
      // Log failed authentication
      this.logger.warn(`Invalid token: ${err.message} - ${request.method} ${request.url}`);
      throw new UnauthorizedException(Messages.UNAUTHORIZED);
    }
  }
}
