import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMissingPaymentMethodColumns1703123456796 implements MigrationInterface {
  name = 'AddMissingPaymentMethodColumns1703123456796';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add missing payment_method columns to tables

    // 1. Check if payment_transaction table exists and add payment_method column if missing
    try {
      const paymentTableExists = await queryRunner.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'payment_transaction'
        );
      `);

      if (paymentTableExists[0]?.exists) {
        // Check if payment_method column exists
        const columnExists = await queryRunner.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'payment_transaction' 
            AND column_name = 'payment_method'
          );
        `);

        if (!columnExists[0]?.exists) {
          // Add payment_method column
          await queryRunner.query(`
            ALTER TABLE payment_transaction 
            ADD COLUMN payment_method payment_transaction_payment_method_enum NOT NULL DEFAULT 'CARD'
          `);
        }
      }
    } catch (error) {
      // Continue even if this fails
    }

    // 2. Check if shop_item_purchase table exists and add payment_method column if missing
    try {
      const shopTableExists = await queryRunner.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'shop_item_purchase'
        );
      `);

      if (shopTableExists[0]?.exists) {
        // Check if payment_method column exists
        const columnExists = await queryRunner.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'shop_item_purchase' 
            AND column_name = 'payment_method'
          );
        `);

        if (!columnExists[0]?.exists) {
          // Add payment_method column
          await queryRunner.query(`
            ALTER TABLE shop_item_purchase 
            ADD COLUMN payment_method shop_item_purchase_payment_method_enum NOT NULL DEFAULT 'reward_points'
          `);
        }
      }
    } catch (error) {
      // Continue even if this fails
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the columns if they exist
    try {
      await queryRunner.query(`ALTER TABLE payment_transaction DROP COLUMN IF EXISTS payment_method`);
    } catch (error) {
      // Continue even if this fails
    }

    try {
      await queryRunner.query(`ALTER TABLE shop_item_purchase DROP COLUMN IF EXISTS payment_method`);
    } catch (error) {
      // Continue even if this fails
    }
  }
}
