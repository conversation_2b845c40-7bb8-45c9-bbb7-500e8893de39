import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { StoryMakerParticipation } from './story-maker-participation.entity';
import { StoryMakerEvaluation } from './story-maker-evaluation.entity';

@Entity()
export class StoryMakerSubmission extends AuditableBaseEntity {
  @Column({ name: 'participation_id', type: 'uuid' })
  participationId: string;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({ name: 'submitted_at', nullable: true }) // Now nullable for drafts
  submittedAt: Date;

  @Column({ name: 'is_evaluated', default: false })
  isEvaluated: boolean;

  // NEW: Draft/Submit status
  @Column({
    name: 'status',
    type: 'enum',
    enum: ['DRAFT', 'SUBMITTED'],
    default: 'DRAFT'
  })
  status: 'DRAFT' | 'SUBMITTED';

  // NEW: Auto-save tracking
  @Column({ name: 'last_auto_saved_at', nullable: true })
  lastAutoSavedAt: Date;

  @Column({ name: 'auto_save_count', default: 0 })
  autoSaveCount: number;

  // NEW: Draft metadata
  @Column({ name: 'word_count_draft', default: 0 })
  wordCountDraft: number;

  @Column({ name: 'character_count_draft', default: 0 })
  characterCountDraft: number;

  // Relationships
  @ManyToOne(() => StoryMakerParticipation, (participation) => participation.submissions)
  @JoinColumn({ name: 'participation_id' })
  participation: StoryMakerParticipation;

  @OneToMany(() => StoryMakerEvaluation, (evaluation) => evaluation.submission)
  evaluations: StoryMakerEvaluation[];
}
