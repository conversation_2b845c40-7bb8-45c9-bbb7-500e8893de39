import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { DiaryEntry } from './diary-entry.entity';
import { DiarySettingsTemplate } from './diary-settings-template.entity';

@Entity()
export class DiaryEntrySettings extends AuditableBaseEntity {
  @Column({ name: 'diary_entry_id' })
  diaryEntryId: string;

  @OneToOne(() => DiaryEntry, (entry) => entry.settings, { nullable: false })
  @JoinColumn({ name: 'diary_entry_id' })
  diaryEntry: DiaryEntry;

  @Column({ name: 'settings_template_id' })
  settingsTemplateId: string;

  @ManyToOne(() => DiarySettingsTemplate)
  @JoinColumn({ name: 'settings_template_id' })
  settingsTemplate: DiarySettingsTemplate;

  @Column({ name: 'title' })
  title: string;

  @Column({ name: 'level', type: 'int' })
  level: number;

  @Column({ name: 'word_limit', type: 'int' })
  wordLimit: number;

  @Column({ name: 'min_word_limit', type: 'int', default: 10 })
  minWordLimit: number;
}
