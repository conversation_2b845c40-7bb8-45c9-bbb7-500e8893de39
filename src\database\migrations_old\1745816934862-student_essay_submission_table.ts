import { MigrationInterface, QueryRunner } from 'typeorm';

export class StudentEssaySubmissionTable1745816934862 implements MigrationInterface {
  name = 'StudentEssaySubmissionTable1745816934862';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_mission_tasks" DROP CONSTRAINT "FK_e96796b88e99efa96d93e10c502"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_703b42d862207320da43d5144ad"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1cb1730e57651f9168f9b01017"`);
    await queryRunner.query(`ALTER TABLE "essay_mission_tasks" ADD "mission" uuid`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD "task" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD "submission_id" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "essay_mission_tasks" ALTER COLUMN "mission_id" SET NOT NULL`);
    await queryRunner.query(`CREATE INDEX "IDX_f7d75d2781798b22d628fd65a2" ON "essay_task_submissions" ("task", "created_by") `);
    await queryRunner.query(
      `ALTER TABLE "essay_mission_tasks" ADD CONSTRAINT "FK_75de5fc9088435658ed90ce0a9a" FOREIGN KEY ("mission") REFERENCES "essay_mission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_9500bf12d147c9a0cde67f37318" FOREIGN KEY ("task") REFERENCES "essay_mission_tasks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_9500bf12d147c9a0cde67f37318"`);
    await queryRunner.query(`ALTER TABLE "essay_mission_tasks" DROP CONSTRAINT "FK_75de5fc9088435658ed90ce0a9a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f7d75d2781798b22d628fd65a2"`);
    await queryRunner.query(`ALTER TABLE "essay_mission_tasks" ALTER COLUMN "mission_id" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP COLUMN "submission_id"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP COLUMN "task"`);
    await queryRunner.query(`ALTER TABLE "essay_mission_tasks" DROP COLUMN "mission"`);
    await queryRunner.query(`CREATE INDEX "IDX_1cb1730e57651f9168f9b01017" ON "essay_task_submissions" ("created_by", "task_id") `);
    await queryRunner.query(
      `ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_703b42d862207320da43d5144ad" FOREIGN KEY ("task_id") REFERENCES "essay_mission_tasks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "essay_mission_tasks" ADD CONSTRAINT "FK_e96796b88e99efa96d93e10c502" FOREIGN KEY ("mission_id") REFERENCES "essay_mission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
