import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { ApiOkResponseWithArrayType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { QAMissionTimeService } from './qa-mission-time.service';

@ApiTags('Admin Q&A Mission Time')
@Controller('qa-mission/time')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class QAMissionTimeController {
  constructor(private readonly qaMissionTimeService: QAMissionTimeService) {}

  @Get('months')
  @ApiOperation({ summary: 'Get all months for dropdown' })
  @ApiOkResponseWithArrayType(Object, 'Months retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  async getMonths(): Promise<ApiResponse<{ id: string; title: string; sequence: number }[]>> {
    const months = await this.qaMissionTimeService.getMonths();
    return ApiResponse.success(months, 'Months retrieved successfully');
  }

  @Get('weeks')
  @ApiOperation({ summary: 'Get all weeks for dropdown' })
  @ApiOkResponseWithArrayType(Object, 'Weeks retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  async getWeeks(): Promise<
    ApiResponse<
      {
        id: string;
        title: string;
        sequence: number;
        month: string;
      }[]
    >
  > {
    const weeks = await this.qaMissionTimeService.getWeeks();
    return ApiResponse.success(weeks, 'Weeks retrieved successfully');
  }
}
