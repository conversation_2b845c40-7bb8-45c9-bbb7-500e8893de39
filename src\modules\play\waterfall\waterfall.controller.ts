import { Controller, Post, Get, Body, UseGuards, Logger, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../common/guards/jwt.guard';
import { GetUser } from '../../../common/decorators/get-user.decorator';
import { StudentGuard } from '../../../common/guards/student.guard';
import { WaterfallService } from './waterfall.service';
import { SubmitWaterfallGameDto, WaterfallParticipationResponseDto } from '../../../database/models/waterfall/waterfall-submission.dto';
import { WaterfallGameResponseDto } from '../../../database/models/waterfall/waterfall-response.dto';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../../common/decorators/api-response.decorator';

@ApiTags('Play-Waterfall')
@ApiBearerAuth('JWT-auth')
@Controller('play/waterfall')
export class WaterfallController {
  private readonly logger = new Logger(WaterfallController.name);

  constructor(private readonly waterfallService: WaterfallService) {}

  @Get('new-game')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({
    summary: 'Get a random waterfall set to play',
    description: 'Returns a random waterfall set with questions for the student to play',
  })
  @ApiOkResponseWithType(WaterfallGameResponseDto, 'Random waterfall set retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'No games available at the moment')
  async getRandomSet(@GetUser() user: any): Promise<ApiResponse<WaterfallGameResponseDto>> {
    const userId = user.sub || user.id;
    const result = await this.waterfallService.getRandomSet(userId);
    return ApiResponse.success(result, 'Random waterfall set retrieved successfully');
  }

  @Post('submit')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({
    summary: 'Submit game result',
    description: 'Submit answers for a waterfall game and get your score',
  })
  @ApiBody({
    type: SubmitWaterfallGameDto,
    description: 'The game submission data with set ID and answers',
    examples: {
      example1: {
        summary: 'Example submission',
        value: {
          set_id: '123e4567-e89b-12d3-a456-426614174000',
          answers: [
            {
              question_id: '123e4567-e89b-12d3-a456-426614174001',
              answers: ['sat'],
            },
            {
              question_id: '123e4567-e89b-12d3-a456-426614174002',
              answers: ['want', 'go'],
            },
          ],
        },
      },
    },
  })
  @ApiOkResponseWithType(WaterfallParticipationResponseDto, 'You completed successfully')
  @ApiErrorResponse(400, 'Invalid game data')
  @ApiErrorResponse(401, 'Please log in to play')
  @ApiErrorResponse(403, 'Only students can play this game')
  @ApiErrorResponse(404, 'Game not found')
  async submitGame(@Body() dto: SubmitWaterfallGameDto, @GetUser() user: any): Promise<ApiResponse<WaterfallParticipationResponseDto>> {
    const userId = user.sub || user.id;
    const result = await this.waterfallService.submitGame(userId, dto.set_id, dto);

    // Simple success message as requested
    return ApiResponse.success(result, 'You completed successfully.');
  }
}
