import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNovelNotificationTypes1748600000000 implements MigrationInterface {
  name = 'AddNovelNotificationTypes1748600000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the new novel notification types to the notification_type_enum
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'novel_submission'`);
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'novel_update'`);
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'novel_review'`);
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'novel_feedback'`);

    // Also add them to the user notification preference enum
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'novel_submission'`);
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'novel_update'`);
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'novel_review'`);
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'novel_feedback'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: PostgreSQL doesn't support removing enum values directly
    // This would require recreating the enum type, which is complex
    // For now, we'll leave the values in place as they don't cause harm
    console.log('Note: Novel notification types will remain in the database enum as PostgreSQL does not support removing enum values directly.');
  }
}
