import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { Conversation } from './conversation.entity';

/**
 * Entity to track which admins have access to admin conversations
 * This allows us to manage admin participation in shared admin conversations
 */
@Entity()
@Index(['conversationId', 'adminId'], { unique: true })
export class AdminConversationParticipant extends AuditableBaseEntity {
  @Column({ name: 'conversation_id' })
  conversationId: string;

  @ManyToOne(() => Conversation, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'conversation_id' })
  conversation: Conversation;

  @Column({ name: 'admin_id' })
  adminId: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'admin_id' })
  admin: User;

  /**
   * Indicates if this admin is currently active in the conversation
   * Can be used to temporarily remove admin access without deleting the record
   */
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  /**
   * Last time this admin accessed the conversation
   * Useful for tracking admin engagement
   */
  @Column({ name: 'last_accessed_at', nullable: true })
  lastAccessedAt: Date;

  /**
   * Number of unread messages for this specific admin
   */
  @Column({ name: 'unread_count', default: 0 })
  unreadCount: number;
}
