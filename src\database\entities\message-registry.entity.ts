import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseFileRegistry } from './base-file-registry.entity';
import { User } from './user.entity';

/**
 * Entity for registering message attachments in the file system
 */
@Entity()
export class MessageRegistry extends BaseFileRegistry {
  @Column({ name: 'message_id', nullable: true })
  messageId: string;

  @Column({ name: 'user_id', nullable: true })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'thumbnail_path', nullable: true })
  thumbnailPath: string;

  @Column({ name: 'is_temporary', default: true })
  isTemporary: boolean;

  /**
   * Convert to DTO for API responses
   */
  toDto(): any {
    return {
      ...this.toSimpleObject(),
      messageId: this.messageId,
      userId: this.userId,
      thumbnailPath: this.thumbnailPath,
      isTemporary: this.isTemporary,
      user: this.user
        ? {
            id: this.user.id,
            name: this.user.name,
            userId: this.user.userId,
          }
        : null,
    };
  }
}
