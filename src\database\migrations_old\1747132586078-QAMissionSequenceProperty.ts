import { MigrationInterface, QueryRunner } from 'typeorm';

export class QAMissionSequenceProperty1747132586078 implements MigrationInterface {
  name = 'QAMissionSequenceProperty1747132586078';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_submission" ADD "points" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_submission" DROP COLUMN "points"`);
  }
}
