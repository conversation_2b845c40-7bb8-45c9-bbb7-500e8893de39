import { ApiProperty } from '@nestjs/swagger';
import { UserResponseDto } from './users.dto';

/**
 * Response DTO for login endpoint
 */
export class LoginResponseDto {
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'JWT access token for authentication',
  })
  access_token: string;

  @ApiProperty({
    example: '2023-12-31T23:59:59.999Z',
    description: 'Expiration date and time of the access token',
    format: 'date-time',
  })
  token_expires: string;

  @ApiProperty({
    type: UserResponseDto,
    description: 'User information',
  })
  user: UserResponseDto;

  @ApiProperty({
    example: '/admin/dashboard',
    description: 'URL to redirect to after successful login',
  })
  returnUrl: string;

  @ApiProperty({
    example: 'abc123...',
    description: 'Refresh token for obtaining a new access token without re-authentication (only included when rememberMe is true)',
    required: false,
  })
  refresh_token?: string;

  @ApiProperty({
    example: '2023-12-31T23:59:59.999Z',
    description: 'Expiration date of the refresh token (only included when rememberMe is true)',
    required: false,
    format: 'date-time',
  })
  refresh_token_expires?: Date;
}
