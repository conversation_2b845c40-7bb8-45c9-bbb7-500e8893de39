import { Entity, Column, OneToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { EssayMissionTasks } from './essay-mission-tasks.entity';

export enum MissionFrequency {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

@Entity()
export class EssayMission extends AuditableBaseEntity {
  @Column({
    name: 'time_frequency',
    type: 'enum',
    enum: MissionFrequency,
    default: MissionFrequency.WEEKLY,
  })
  timeFrequency: MissionFrequency;

  @Column({
    name: 'is_active',
    default: true,
    type: 'boolean',
  })
  isActive: boolean;

  @Column({
    name: 'sequence_number',
    default: 1,
    type: 'int',
  })
  sequenceNumber: number;

  @OneToMany(() => EssayMissionTasks, (task) => task.mission)
  tasks: EssayMissionTasks[];
}
