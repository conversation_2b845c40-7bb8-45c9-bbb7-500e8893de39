import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsPurchasableInRewardpoint1747000000000 implements MigrationInterface {
  name = 'AddIsPurchasableInRewardpoint1747000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the new boolean column with default value false
    await queryRunner.query(`ALTER TABLE "shop_item" ADD "is_purchasable_in_rewardpoint" boolean NOT NULL DEFAULT false`);

    // Update existing records to set is_purchasable_in_rewardpoint=true where price_equivalent_to_reward_point > 0
    await queryRunner.query(`UPDATE "shop_item" SET "is_purchasable_in_rewardpoint" = true WHERE "price_equivalent_to_reward_point" > 0`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the new column
    await queryRunner.query(`ALTER TABLE "shop_item" DROP COLUMN "is_purchasable_in_rewardpoint"`);
  }
}
