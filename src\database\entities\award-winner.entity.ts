import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { Award } from './award.entity';

@Entity()
export class AwardWinner extends AuditableBaseEntity {
  @Column({ name: 'user_id', nullable: false })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'award_id', nullable: false })
  awardId: string;

  @ManyToOne(() => Award)
  @JoinColumn({ name: 'award_id' })
  award: Award;

  @Column({ name: 'award_date', type: 'date' })
  awardDate: Date;

  @Column({ name: 'award_reason', nullable: true, type: 'text' })
  awardReason: string;

  @Column({ name: 'metadata', nullable: true, type: 'json' })
  metadata: any;
}
