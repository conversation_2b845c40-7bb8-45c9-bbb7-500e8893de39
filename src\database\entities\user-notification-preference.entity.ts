import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Un<PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { NotificationType } from './notification.entity';
import { NotificationChannel } from './notification-delivery.entity';

@Entity()
@Unique(['userId', 'notificationType', 'channel'])
export class UserNotificationPreference extends AuditableBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({
    name: 'notification_type',
    type: 'enum',
    enum: NotificationType,
  })
  notificationType: NotificationType;

  @Column({
    name: 'channel',
    type: 'enum',
    enum: NotificationChannel,
  })
  channel: NotificationChannel;

  @Column({ name: 'is_enabled', default: true })
  isEnabled: boolean;

  @Column({ name: 'time_window', nullable: true })
  timeWindow: string;

  @Column({ name: 'days_of_week', type: 'simple-array', nullable: true })
  daysOfWeek: string[];
}
