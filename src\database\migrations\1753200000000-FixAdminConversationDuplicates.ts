import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixAdminConversationDuplicates1753200000000 implements MigrationInterface {
  name = 'FixAdminConversationDuplicates1753200000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, create the virtual_admin table
    console.log('Creating virtual_admin table...');
    await queryRunner.query(`
      CREATE TABLE "virtual_admin" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "user_id" uuid NOT NULL,
        "display_name" character varying NOT NULL DEFAULT 'HEC Admin',
        "description" text,
        "is_active" boolean NOT NULL DEFAULT true,
        "settings" jsonb,
        CONSTRAINT "PK_virtual_admin" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_virtual_admin_user_id" UNIQUE ("user_id"),
        CONSTRAINT "FK_virtual_admin_user_id" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE
      )
    `);

    // Create index for better performance
    await queryRunner.query(`
      CREATE INDEX "IDX_virtual_admin_is_active" ON "virtual_admin" ("is_active")
    `);

    // Create the virtual admin user
    console.log('Creating virtual admin user...');
    const virtualAdminUserResult = await queryRunner.query(`
      INSERT INTO "user" (
        user_id, name, email, password, type, is_active, is_confirmed, phone_number, gender, created_at, updated_at
      ) VALUES (
        'virtual-admin', 'HEC Admin', '<EMAIL>', 'virtual-admin-secure-password',
        'admin', true, true, '******-HEC-ADMIN', 'other', NOW(), NOW()
      ) RETURNING id
    `);

    const virtualAdminUserId = virtualAdminUserResult[0].id;
    console.log(`Created virtual admin user with ID: ${virtualAdminUserId}`);

    // Create the virtual admin entity
    await queryRunner.query(`
      INSERT INTO "virtual_admin" (
        user_id, display_name, description, is_active, settings, created_at, updated_at
      ) VALUES (
        $1, 'HEC Admin', 'Virtual admin user for unified admin conversations', true,
        '{"autoResponse": false, "notificationEnabled": true, "maxConcurrentChats": 1000}', NOW(), NOW()
      )
    `, [virtualAdminUserId]);

    // Now let's identify and fix any existing duplicate admin conversations
    console.log('Checking for duplicate admin conversations...');
    
    // Find duplicate admin conversations (same user with multiple admin conversations)
    const duplicates = await queryRunner.query(`
      SELECT admin_conversation_user_id, COUNT(*) as count
      FROM conversation 
      WHERE is_admin_conversation = true 
        AND admin_conversation_user_id IS NOT NULL
      GROUP BY admin_conversation_user_id 
      HAVING COUNT(*) > 1
    `);

    if (duplicates.length > 0) {
      console.log(`Found ${duplicates.length} users with duplicate admin conversations. Fixing...`);
      
      for (const duplicate of duplicates) {
        const userId = duplicate.admin_conversation_user_id;
        
        // Get all admin conversations for this user, ordered by creation date
        const conversations = await queryRunner.query(`
          SELECT id, participant1_id, created_at
          FROM conversation 
          WHERE is_admin_conversation = true 
            AND admin_conversation_user_id = $1
          ORDER BY created_at ASC
        `, [userId]);

        // Keep the first conversation, merge others into it
        const primaryConversation = conversations[0];
        const conversationsToMerge = conversations.slice(1);

        console.log(`Merging ${conversationsToMerge.length} conversations into ${primaryConversation.id} for user ${userId}`);

        for (const conv of conversationsToMerge) {
          // Move all messages from duplicate conversation to primary conversation
          await queryRunner.query(`
            UPDATE message 
            SET conversation_id = $1 
            WHERE conversation_id = $2
          `, [primaryConversation.id, conv.id]);

          // Move all admin participants from duplicate conversation to primary conversation
          await queryRunner.query(`
            INSERT INTO admin_conversation_participant (conversation_id, admin_id, is_active, last_accessed_at, unread_count, created_at, updated_at)
            SELECT $1, admin_id, is_active, last_accessed_at, unread_count, created_at, updated_at
            FROM admin_conversation_participant 
            WHERE conversation_id = $2
            ON CONFLICT (conversation_id, admin_id) DO NOTHING
          `, [primaryConversation.id, conv.id]);

          // Delete the duplicate conversation (this will cascade delete admin participants)
          await queryRunner.query(`
            DELETE FROM conversation WHERE id = $1
          `, [conv.id]);
        }

        // Update the primary conversation to use the virtual admin ID
        await queryRunner.query(`
          UPDATE conversation
          SET participant1_id = $1
          WHERE id = $2
        `, [virtualAdminUserId, primaryConversation.id]);
      }
    }

    // Update any remaining admin conversations to use the virtual admin ID
    console.log('Updating all admin conversations to use virtual admin ID...');
    await queryRunner.query(`
      UPDATE conversation
      SET participant1_id = $1
      WHERE is_admin_conversation = true
    `, [virtualAdminUserId]);

    // Now add the unique index to prevent future duplicates
    // Note: Using partial index instead of constraint because PostgreSQL doesn't allow
    // unique constraints on partial indexes
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_conversation_admin_user_unique"
      ON "conversation" ("admin_conversation_user_id")
      WHERE "is_admin_conversation" = true AND "admin_conversation_user_id" IS NOT NULL
    `);

    console.log('Admin conversation duplicates fixed and unique constraint added.');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the unique index
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_conversation_admin_user_unique"
    `);

    // Drop the virtual_admin table (this will cascade delete the virtual admin user due to FK constraint)
    await queryRunner.query(`DROP TABLE IF EXISTS "virtual_admin"`);

    // Remove the virtual admin user (if it still exists)
    await queryRunner.query(`
      DELETE FROM "user" WHERE user_id = 'virtual-admin'
    `);

    console.log('Virtual admin table and unique index removed.');
  }
}
