import { Injectable, NotFoundException, ConflictException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Not, In, IsNull } from 'typeorm';
import { EssayMissionTasks } from 'src/database/entities/essay-mission-tasks.entity';
import { EssayTaskSubmissions } from 'src/database/entities/essay-task-submissions.entity';
import { EssayTaskSubmissionHistory } from 'src/database/entities/essay-task-submission-history.entity';
import { EssayModuleSkinPreference } from 'src/database/entities/essay-preferences.entity';

import {
  EssayTaskSubmissionDto,
  EssayTaskSubmissionHistoryDto,
  CreateMissionTaskSubmissionDto,
  EssayTaskActiveDto,
  EssayTaskSubmissionUpdate,
  EssayModuleSkinPreferenceDto,
  TaskSkinInfoResponseDto,
} from 'src/database/models/mission.dto';

import { CurrentUserService } from 'src/common/services/current-user.service';
import { getCurrentUTCDate } from '../../common/utils/date-utils';
import { NotificationHelperService } from '../../modules/notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { SkinScopeType } from 'src/database/entities/essay-preferences.entity';
import { DiarySkinService } from '../diary/diary-skin.service';
import { DiarySkin } from 'src/database/entities/diary-skin.entity';
import { User } from 'src/database/entities/user.entity';
import { isNull } from 'util';
import { SubmissionStatus } from 'src/constants/submission.enum';

@Injectable()
export class EssaySubmissionService {
  private readonly logger = new Logger(EssaySubmissionService.name);

  constructor(
    @InjectRepository(EssayMissionTasks)
    private readonly essayMissionTasksRepository: Repository<EssayMissionTasks>,
    @InjectRepository(EssayTaskSubmissions)
    private readonly essayTaskSubmissionsRepository: Repository<EssayTaskSubmissions>,
    @InjectRepository(EssayTaskSubmissionHistory)
    private readonly essayTaskSubmissionHistoryRepository: Repository<EssayTaskSubmissionHistory>,
    @InjectRepository(EssayModuleSkinPreference)
    private readonly essayModuleSkinPreferenceRepository: Repository<EssayModuleSkinPreference>,
    @InjectRepository(DiarySkin)
    private readonly diarySkinRepository: Repository<DiarySkin>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly currentUserService: CurrentUserService,
    private readonly dataSource: DataSource,
    private readonly notificationHelper: NotificationHelperService,
    private readonly deeplinkService: DeeplinkService,
    // private readonly diarySkinService: DiarySkinService
  ) {}

  private toMissionTaskSubmissionResponseDto(taskSubmission: EssayTaskSubmissions): EssayTaskSubmissionDto | null {
    if (!taskSubmission) return null;
    const submissionHistory = taskSubmission?.submissionHistory?.map((history) => ({
      id: history.id,
      content: history.content,
      wordCount: history.wordCount,
      submissionDate: history.submissionDate,
      sequenceNumber: history.sequenceNumber,
      metaData: history.metaData,
      createdAt: history.createdAt,
      updatedAt: history.updatedAt,
      updatedBy: history.updatedBy,
      submissionMark: history.submissionMark
        ? {
            id: history.submissionMark.id,
            mark: history.submissionMark.points,
            submissionFeedback: history.submissionMark.submissionFeedback,
            taskRemarks: history.submissionMark.taskRemarks,
            createdAt: history.submissionMark.createdAt,
            updatedAt: history.submissionMark.updatedAt,
            createdBy: history.submissionMark.createdBy,
            updatedBy: history.submissionMark.updatedBy,
          }
        : null,
    }));
    const missionTaskSubmission: EssayTaskSubmissionDto = {
      id: taskSubmission.id,
      title: taskSubmission.title,
      status: taskSubmission.status,
      isActive: taskSubmission.isActive,
      task: taskSubmission.task
        ? {
            id: taskSubmission.task.id,
            wordLimitMinimum: taskSubmission.task.wordLimitMinimum,
            wordLimitMaximum: taskSubmission.task.wordLimitMaximum,
            title: taskSubmission.task.title,
            instructions: taskSubmission.task.instructions,
          }
        : null,
      currentRevision: taskSubmission.currentRevision,
      createdAt: taskSubmission.createdAt,
      updatedAt: taskSubmission.updatedAt,
      updatedBy: taskSubmission.updatedBy,
      submissionHistory: submissionHistory,
      diarySkin: taskSubmission.submissionSkin,
      submissionMark: taskSubmission.submissionMark
    };
    return missionTaskSubmission;
  }

  private countWords(content: string): number {
    return content.split(/\s+/).filter((word) => word.length > 0).length;
  }

  /**
   * Get the tutor ID for a student
   * @param studentId The student ID
   * @returns The tutor ID or null if no tutor is assigned
   */
  private async getTutorIdForStudent(studentId: string): Promise<string | null> {
    try {
      // Query the database to find the tutor assigned to this student
      // This is a simplified implementation - you may need to adjust based on your actual data model
      const query = `
        SELECT tutor_id
        FROM student_tutor_mapping
        WHERE student_id = $1
        AND status = 'active'
        LIMIT 1
      `;

      const result = await this.dataSource.query(query, [studentId]);

      if (result && result.length > 0) {
        return result[0].tutor_id;
      }

      return null;
    } catch (error) {
      this.logger.error(`Error getting tutor for student ${studentId}: ${error.message}`, error.stack);
      return null;
    }
  }

  async startTask(taskId: string): Promise<EssayTaskSubmissionDto> {
    const task = await this.essayMissionTasksRepository.findOne({
      where: { id: taskId, isActive: true },
    });

    if (!task) {
      throw new NotFoundException(`Task with ID ${taskId} not found`);
    }

    const currentUserId = this.currentUserService.getCurrentUserId();

    const activeSubmission = await this.essayTaskSubmissionsRepository.findOne({
      where: {
        taskId: taskId,
        createdBy: currentUserId,
        isActive: true,
        status: In([SubmissionStatus.DRAFT, SubmissionStatus.SUBMITTED]),
      },
    });

    if (activeSubmission) {
      throw new ConflictException(`Task with ID ${taskId} already started`);
    }

    const existingSubmissions = await this.essayTaskSubmissionsRepository.findOne({
      where: {
        taskId: taskId,
        createdBy: currentUserId,
        status: SubmissionStatus.REVIEWED,
      },
      order: { currentRevision: 'DESC' },
    });

    const currentRevision = existingSubmissions ? existingSubmissions.currentRevision + 1 : 1;
    const totalRevisions = existingSubmissions ? existingSubmissions.totalRevisions + 1 : 0;
    const isFirstRevision = currentRevision === 1 ? true : false;

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const submission = this.essayTaskSubmissionsRepository.create({
        task,
        taskId: task.id,
        currentRevision,
        totalRevisions,
        isFirstRevision,
        status: SubmissionStatus.DRAFT,
        firstSubmittedAt: getCurrentUTCDate(),
      });

      const savedSubmission = await queryRunner.manager.save(submission);

      const submissionHistory = this.essayTaskSubmissionHistoryRepository.create({
        versionNumber: 1,
        submission: savedSubmission,
        submissionId: savedSubmission.id,
        content: '',
        wordCount: 0,
        submissionDate: getCurrentUTCDate(),
        sequenceNumber: savedSubmission.currentRevision,
        metaData: {
          lastDraftSavedAt: getCurrentUTCDate(),
          submissionAttempts: 0,
        },
      });

      const savedHistory = await queryRunner.manager.save(submissionHistory);

      savedSubmission.latestSubmissionId = savedHistory.id;

      await queryRunner.manager.save(savedSubmission);

      const missionTaskSubmission = await queryRunner.manager.findOne(EssayTaskSubmissions, {
        where: { id: savedSubmission.id },
        relations: ['task', 'submissionHistory'],
      });

      await queryRunner.commitTransaction();
      return this.toMissionTaskSubmissionResponseDto(missionTaskSubmission);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new ConflictException(`Failed to start task: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  async startEssay(): Promise<EssayTaskSubmissionDto> {
    const currentUserId = this.currentUserService.getCurrentUserId();

    const activeSubmission = await this.essayTaskSubmissionsRepository.findOne({
      where: {
        taskId: IsNull(),
        createdBy: currentUserId,
        isActive: true,
        status: In([SubmissionStatus.DRAFT]),
      },
    });

    if (activeSubmission) {
      throw new ConflictException(`One Essay is already already started`);
    }

    const existingSubmissions = await this.essayTaskSubmissionsRepository.findOne({
      where: {
        taskId: IsNull(),
        createdBy: currentUserId,
        status: SubmissionStatus.REVIEWED,
      },
      order: { currentRevision: 'DESC' },
    });

    const currentRevision = existingSubmissions ? existingSubmissions.currentRevision + 1 : 1;
    const totalRevisions = existingSubmissions ? existingSubmissions.totalRevisions + 1 : 0;
    const isFirstRevision = currentRevision === 1 ? true : false;

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const submission = this.essayTaskSubmissionsRepository.create({
        task: null,
        taskId: null,
        currentRevision,
        totalRevisions,
        isFirstRevision,
        status: SubmissionStatus.DRAFT,
        firstSubmittedAt: getCurrentUTCDate(),
      });

      const savedSubmission = await queryRunner.manager.save(submission);

      const submissionHistory = this.essayTaskSubmissionHistoryRepository.create({
        submission: savedSubmission,
        submissionId: savedSubmission.id,
        content: '',
        wordCount: 0,
        submissionDate: getCurrentUTCDate(),
        sequenceNumber: savedSubmission.currentRevision,
        metaData: {
          lastDraftSavedAt: getCurrentUTCDate(),
          submissionAttempts: 0,
        },
      });

      const savedHistory = await queryRunner.manager.save(submissionHistory);

      savedSubmission.latestSubmissionId = savedHistory.id;

      await queryRunner.manager.save(savedSubmission);

      const missionTaskSubmission = await queryRunner.manager.findOne(EssayTaskSubmissions, {
        where: { id: savedSubmission.id },
        relations: ['task', 'submissionHistory'],
      });

      if (!missionTaskSubmission.submissionSkin) {
        const skin = await this.essayModuleSkinPreferenceRepository.findOne({
          where: {
            createdBy: currentUserId,
            isActive: true,
            scopeType: SkinScopeType.MODULE_DEFAULT,
          },
          relations: ['skin'],
        });
        missionTaskSubmission.submissionSkin = skin?.skin || null;
        missionTaskSubmission.submissionSkinId = skin?.skinId || null;
      }

      await queryRunner.commitTransaction();
      return this.toMissionTaskSubmissionResponseDto(missionTaskSubmission);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new ConflictException(`Failed to start Essay: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  async autoSaveContent(essaySubmissionUpdate: EssayTaskSubmissionUpdate): Promise<EssayTaskSubmissionDto> {
    const { title, skinId, submissionId, content, metaData: metadata } = essaySubmissionUpdate;
    const currentUserId = this.currentUserService.getCurrentUserId();

      const submission = await this.essayTaskSubmissionsRepository.findOne({
        where: {
          id: submissionId,
          status: In([SubmissionStatus.DRAFT, SubmissionStatus.REVIEWED, SubmissionStatus.RESUBMITTED]),
          createdBy: currentUserId,
        },
        relations: ['task'],
      });

    if (!submission) {
      throw new NotFoundException('Active submission not found');
    }

    const latestHistory = await this.essayTaskSubmissionHistoryRepository.findOne({
      where: { id: submission.latestSubmissionId },
    });
    submission.latestHistory = latestHistory;

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (content) {
        const history = this.essayTaskSubmissionHistoryRepository.create({
          versionNumber: submission.latestHistory.versionNumber,
          status: submission.status,
          submission: { id: submission.id },
          submissionId: submission.id,
          content,
          wordCount: this.countWords(content),
          submissionDate: getCurrentUTCDate(),
          sequenceNumber: submission.currentRevision,
          metaData: {
            ...metadata,
            lastDraftSavedAt: getCurrentUTCDate(),
            submissionAttempts: (submission.totalRevisions || 0) + 1,
          },
        });

        const savedHistory = await queryRunner.manager.save(history);
        submission.latestSubmissionId = savedHistory.id;
        submission.firstRevisionProgress =
          submission.isFirstRevision && submission.task ? Math.min((savedHistory.wordCount / submission.task.wordLimitMinimum) * 100, 100) : submission.firstRevisionProgress;
      }

      if (skinId) {
        const skin = await this.diarySkinRepository.findOne({
          where: { id: skinId, isActive: true },
        });
        if (!skin) {
          throw new NotFoundException(`Skin with ID ${skinId} not found`);
        }
        submission.submissionSkin = skin;
        submission.submissionSkinId = skin.id;
      }

      submission.totalRevisions = (submission.totalRevisions || 0) + 1;
      submission.lastSubmittedAt = getCurrentUTCDate();
      submission.title = title ? title : submission.title;

      await queryRunner.manager.save(submission);

      const missionTaskSubmission = await queryRunner.manager.findOne(EssayTaskSubmissions, {
        where: { id: submission.id },
        relations: ['task', 'submissionHistory'],
      });

      await queryRunner.commitTransaction();
      return this.toMissionTaskSubmissionResponseDto(missionTaskSubmission);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async submitEssay(createMissionTaskSubmissionDto: CreateMissionTaskSubmissionDto): Promise<EssayTaskSubmissionDto> {
    const { taskId, content, metaData, skinId, title } = createMissionTaskSubmissionDto;

    const currentUserId = this.currentUserService.getCurrentUserId();

    const submission = await this.essayTaskSubmissionsRepository.findOne({
        where: {
          taskId: taskId || IsNull(),
          status: taskId ? In([SubmissionStatus.DRAFT, SubmissionStatus.REVIEWED, SubmissionStatus.RESUBMITTED]) : In([SubmissionStatus.DRAFT]),
          createdBy: currentUserId,
        },
        relations: ['task'],
      });

    if (!submission) {
      throw new NotFoundException('Active submission not found');
    }

    const latestHistory = await this.essayTaskSubmissionHistoryRepository.findOne({
      where: { id: submission.latestSubmissionId },
    });
    submission.latestHistory = latestHistory;

    if (submission.status === SubmissionStatus.SUBMITTED) {
      throw new ConflictException('Submission already submitted');
    }

    const wordCount = this.countWords(content);

    if (submission.task && submission.task.wordLimitMinimum && wordCount < submission.task.wordLimitMinimum) {
      throw new ConflictException(`Word count (${wordCount}) must be at least ${submission.task.wordLimitMinimum}`);
    }

    if (submission.task && submission.task.wordLimitMaximum && wordCount > submission.task.wordLimitMaximum) {
      throw new ConflictException(`Word count (${wordCount}) must not exceed ${submission.task.wordLimitMaximum}`);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const wordCountDiff =
        !submission.submissionHistory || submission.submissionHistory.length === 0 ? 0 : wordCount - submission.submissionHistory[submission.submissionHistory.length - 1].wordCount;

      const timeSpent =
        !submission.submissionHistory || submission.submissionHistory.length === 0 ? 0 : Math.floor((getCurrentUTCDate().getTime() - submission.submissionHistory[0].submissionDate.getTime()) / 1000);

      const history = queryRunner.manager.create(EssayTaskSubmissionHistory, {
        versionNumber: submission.latestHistory.versionNumber + 1,
        isSubmitted: true,
        status: submission.status == SubmissionStatus.DRAFT ? SubmissionStatus.SUBMITTED : SubmissionStatus.RESUBMITTED,
        content: content,
        wordCount: wordCount,
        submissionDate: getCurrentUTCDate(),
        sequenceNumber: submission.currentRevision + 1,
        metaData: {
          ...metaData,
          lastDraftSavedAt: getCurrentUTCDate(),
          submissionAttempts: (submission.totalRevisions || 0) + 1,
          wordCountDiff,
          timeSpent,
        },
        submission,
        submissionId: submission.id,
      });

      const savedHistory = await queryRunner.manager.save(history);

      if (skinId) {
        const skin = await this.diarySkinRepository.findOne({
          where: { id: skinId, isActive: true },
        });
        if (!skin) {
          throw new NotFoundException(`Skin with ID ${skinId} not found`);
        }

        const createModuleSkinPreference = queryRunner.manager.create(EssayModuleSkinPreference, {
          skinId: skin.id,
          skin: skin,
          taskId: submission.task ? submission.task.id : null,
          task: submission.task ? submission.task : null,
          scopeType: SkinScopeType.TASK_SPECIFIC,
        });

        await queryRunner.manager.save(createModuleSkinPreference);

        submission.submissionSkin = skin;
        submission.submissionSkinId = skin.id;
      }

      submission.title = title;
      submission.status = submission.status == SubmissionStatus.DRAFT ? SubmissionStatus.SUBMITTED : SubmissionStatus.RESUBMITTED;
      submission.latestSubmissionId = savedHistory.id;
      submission.currentRevision += 1;
      submission.totalRevisions = (submission.totalRevisions || 0) + 1;
      submission.lastSubmittedAt = getCurrentUTCDate();
      submission.firstRevisionProgress =
        submission.isFirstRevision && submission.task ? Math.min((savedHistory.wordCount / submission.task.wordLimitMinimum) * 100, 100) : submission.firstRevisionProgress;

      if (!submission.firstSubmittedAt) {
        submission.firstSubmittedAt = getCurrentUTCDate();
      }

      await queryRunner.manager.save(submission);
      await queryRunner.commitTransaction();

      const finalSubmission = await queryRunner.manager.findOne(EssayTaskSubmissions, {
        where: { id: submission.id },
        relations: ['task'],
      });

      if(finalSubmission.status === SubmissionStatus.SUBMITTED) {
        // Send notification to the assigned tutor
        try {
          const studentId = this.currentUserService.getCurrentUserId();
          const tutorId = await this.getTutorIdForStudent(studentId);

          if (tutorId) {
            // Generate deeplinks
            const webLink = this.deeplinkService.getWebLink(DeeplinkType.ESSAY_SUBMISSION, {
              id: submission.id,
            });

            const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.ESSAY_SUBMISSION, {
              id: submission.id,
            });

            // Create HTML content for rich notifications
            const htmlContent = `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #333;">New Essay Submission</h2>
                <p>A student has submitted a new essay for your review.</p>
                <p><strong>Task:</strong> ${submission.task?.title || 'Essay Task'}</p>
                <p><strong>Word Count:</strong> ${wordCount}</p>
                <div style="margin: 20px 0;">
                  <a href="${webLink}" style="background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; display: inline-block;">View Submission</a>
                </div>
              </div>
            `;

            // Send notification
            await this.notificationHelper.notify(tutorId, NotificationType.ESSAY_SUBMISSION, 'New Essay Submission', `A student has submitted a new essay for your review.`, {
              relatedEntityId: submission.id,
              relatedEntityType: 'essay_submission',
              htmlContent: htmlContent,
              webLink: webLink,
              deepLink: deepLink,
              sendEmail: true,
              sendPush: true,
              sendInApp: true,
              sendMobile: true,
              sendSms: false,
              sendRealtime: false,
            });

            this.logger.log(`Sent notification to tutor ${tutorId} for essay submission ${submission.id}`);
          }
        } catch (notificationError) {
          // Log the error but don't fail the submission
          this.logger.error(`Error sending notification: ${notificationError.message}`, notificationError.stack);
        }
      }

      return this.toMissionTaskSubmissionResponseDto(finalSubmission);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new ConflictException(`Failed to submit essay: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  async discardTask(submissionId: string): Promise<EssayTaskSubmissionDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const submission = await queryRunner.manager.findOne(EssayTaskSubmissions, {
        where: {
          id: submissionId,
          isActive: true,
        },
        relations: ['submissionHistory', 'task'],
      });

      if (!submission) {
        throw new NotFoundException('Active submission not found');
      }

      if (submission.submissionHistory?.length > 0) {
        await queryRunner.manager.delete(
          EssayTaskSubmissionHistory,
          submission.submissionHistory.map((history) => history.id),
        );
      }

      submission.isActive = false;
      submission.status = SubmissionStatus.DISCARDED;
      submission.latestSubmissionId = null;
      submission.firstRevisionProgress = submission.isFirstRevision ? 0 : submission.firstRevisionProgress;

      await queryRunner.manager.save(submission);

      const discardedSubmission = await queryRunner.manager.findOne(EssayTaskSubmissions, {
        where: { id: submission.task.id },
        relations: ['submissionHistory', 'task'],
      });
      await queryRunner.commitTransaction();
      return this.toMissionTaskSubmissionResponseDto(discardedSubmission);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new ConflictException(`Failed to discard task: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  async getSubmissionHistory(submissionId: string): Promise<EssayTaskSubmissionHistoryDto[]> {
    const submission = await this.essayTaskSubmissionsRepository.findOne({
      where: { id: submissionId },
      relations: ['submissionHistory'],
    });

    if (!submission) {
      throw new NotFoundException('Active submission not found');
    }

    return submission.submissionHistory.map((history) => ({
      id: history.id,
      content: history.content,
      wordCount: history.wordCount,
      submissionDate: history.submissionDate,
      sequenceNumber: history.sequenceNumber,
      metaData: history.metaData,
      createdAt: history.createdAt,
      updatedAt: history.updatedAt,
      createdBy: history.createdBy,
      updatedBy: history.updatedBy,
    }));
  }

  async getSubmission(submissionId: string): Promise<EssayTaskSubmissionDto> {
    const submission = await this.essayTaskSubmissionsRepository.findOne({
      where: { id: submissionId },
      relations: ['submissionHistory', 'task'],
    });

    if (!submission) {
      throw new NotFoundException('Active submission not found');
    }

    return this.toMissionTaskSubmissionResponseDto(submission);
  }

  async getSubmissionsTaskById(taskId: string): Promise<EssayTaskSubmissionDto> {
    const task = await this.essayMissionTasksRepository.findOne({
      where: { id: taskId, isActive: true },
    });
    if (!task) {
      throw new NotFoundException(`Task with ID ${taskId} not found`);
    }
    const currentUserId = this.currentUserService.getCurrentUserId();
    
    const submissions = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .leftJoinAndSelect('submission.task', 'task')
      .leftJoinAndSelect('submission.submissionMark', 'submissionMark')
      .leftJoinAndSelect('submission.submissionHistory', 'history', 
        'history.id = (SELECT h2.id FROM essay_task_submission_history h2 WHERE h2.submission_id = submission.id ORDER BY h2.created_at DESC LIMIT 1)')
      .leftJoinAndSelect('history.submissionMark', 'historySubmissionMark')
      .where('submission.task_id = :taskId', { taskId })
      .andWhere('submission.created_by = :currentUserId', { currentUserId })
      .getOne();
      
    return this.toMissionTaskSubmissionResponseDto(submissions);
  }

  async getActiveTask(): Promise<EssayTaskSubmissionDto> {
    const currentUserId = this.currentUserService.getCurrentUserId();
    
    const activeSubmission = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .leftJoinAndSelect('submission.task', 'task')
      .leftJoinAndSelect('submission.submissionHistory', 'history', 
        'history.id = (SELECT h2.id FROM essay_task_submission_history h2 WHERE h2.submission_id = submission.id ORDER BY h2.created_at DESC LIMIT 1)')
      .leftJoinAndSelect('history.submissionMark', 'submissionMark')
      .leftJoinAndSelect('submission.submissionSkin', 'submissionSkin')
      .where('submission.created_by = :currentUserId', { currentUserId })
      .andWhere('submission.is_active = :isActive', { isActive: true })
      .andWhere('submission.status = :status', { status: SubmissionStatus.DRAFT })
      .andWhere('submission.task_id IS NOT NULL')
      .getOne();

    if (!activeSubmission) {
      return null;
    }

    if (!activeSubmission.submissionSkin) {
      const skin = await this.essayModuleSkinPreferenceRepository.findOne({
        where: {
          createdBy: currentUserId,
          isActive: true,
          scopeType: SkinScopeType.MODULE_DEFAULT,
        },
        relations: ['skin'],
      });
      activeSubmission.submissionSkin = skin?.skin || null;
      activeSubmission.submissionSkinId = skin?.skinId || null;
    }

    return this.toMissionTaskSubmissionResponseDto(activeSubmission);
  }

  async getActiveEssay(): Promise<EssayTaskSubmissionDto> {
    const currentUserId = this.currentUserService.getCurrentUserId();
    
    const activeEssay = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .leftJoinAndSelect('submission.submissionHistory', 'history', 
        'history.id = (SELECT h2.id FROM essay_task_submission_history h2 WHERE h2.submission_id = submission.id ORDER BY h2.created_at DESC LIMIT 1)')
      .leftJoinAndSelect('history.submissionMark', 'submissionMark')
      .leftJoinAndSelect('submission.submissionSkin', 'submissionSkin')
      .where('submission.created_by = :currentUserId', { currentUserId })
      .andWhere('submission.is_active = :isActive', { isActive: true })
      .andWhere('submission.status = :status', { status: SubmissionStatus.DRAFT })
      .andWhere('submission.task_id IS NULL')
      .getOne();

    if (!activeEssay) {
      return null;
    }
    if (!activeEssay.submissionSkin) {
      const skin = await this.essayModuleSkinPreferenceRepository.findOne({
        where: {
          createdBy: currentUserId,
          isActive: true,
          scopeType: SkinScopeType.MODULE_DEFAULT,
        },
        relations: ['skin'],
      });
      activeEssay.submissionSkin = skin?.skin || null;
      activeEssay.submissionSkinId = skin?.skinId || null;
    }
    return this.toMissionTaskSubmissionResponseDto(activeEssay);
  }

  async getTaskSkinInfo(taskId: string, userId?: string): Promise<TaskSkinInfoResponseDto> {
    const task = await this.essayMissionTasksRepository.findOne({
      where: { id: taskId, isActive: true },
    });

    if (!task) {
      throw new NotFoundException(`Task with ID ${taskId} not found`);
    }

    const user = await this.userRepository.findOne({
      where: {
        id: userId,
        isActive: true,
      },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    const skins = await this.essayModuleSkinPreferenceRepository.find({
      where: [
        {
          taskId: taskId,
          isActive: true,
          scopeType: SkinScopeType.TASK_SPECIFIC,
          createdBy: user.id,
        },
        {
          isActive: true,
          scopeType: SkinScopeType.MODULE_DEFAULT,
        },
      ],
      relations: ['skin'],
    });

    const response = new TaskSkinInfoResponseDto();

    if (!skins || skins.length === 0) {
      response.taskSpecificSkin = null;
      response.moduleDefaultSkin = null;
      return response;
    }

    response.taskSpecificSkin = skins.find((skin) => skin.scopeType === SkinScopeType.TASK_SPECIFIC && skin.taskId === taskId) || null;

    response.moduleDefaultSkin = skins.find((skin) => skin.scopeType === SkinScopeType.MODULE_DEFAULT) || null;

    return response;
  }

  async setDefaultSkin(skinId: string, studentId?: string): Promise<EssayModuleSkinPreferenceDto> {
    const diarySkin = await this.diarySkinRepository.findOne({
      where: { id: skinId, isActive: true },
    });

    if (!diarySkin) {
      throw new NotFoundException(`Skin with ID ${skinId} not found`);
    }

    const existingDefaultSkin = await this.essayModuleSkinPreferenceRepository.findOne({
      where: {
        isActive: true,
        scopeType: SkinScopeType.MODULE_DEFAULT,
      },
    });

    if (existingDefaultSkin) {
      existingDefaultSkin.skinId = skinId;
      existingDefaultSkin.skin = diarySkin;

      const updatedSkin = await this.essayModuleSkinPreferenceRepository.save(existingDefaultSkin);

      const fullUpdatedSkin = await this.essayModuleSkinPreferenceRepository.findOne({
        where: { id: updatedSkin.id },
        relations: ['skin'],
      });

      return this.toEssayModuleSkinPreferenceDto(fullUpdatedSkin);
    }

    const newSkinPreference = this.essayModuleSkinPreferenceRepository.create({
      skinId: skinId,
      skin: diarySkin,
      scopeType: SkinScopeType.MODULE_DEFAULT,
    });

    const savedSkinPreference = await this.essayModuleSkinPreferenceRepository.save(newSkinPreference);

    const fullSavedSkin = await this.essayModuleSkinPreferenceRepository.findOne({
      where: { id: savedSkinPreference.id },
      relations: ['skin'],
    });

    return this.toEssayModuleSkinPreferenceDto(fullSavedSkin);
  }

  private toEssayModuleSkinPreferenceDto(skinPreference: EssayModuleSkinPreference): EssayModuleSkinPreferenceDto {
    return {
      skinId: skinPreference.skinId,
      scopeType: skinPreference.scopeType,
      taskId: skinPreference.taskId,
      isActive: skinPreference.isActive,
    };
  }
}
