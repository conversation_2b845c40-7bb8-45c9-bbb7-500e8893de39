import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, Index } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { NovelEntry } from './novel-entry.entity';

@Entity()
@Index(['novelEntryId', 'versionNumber'])
@Index(['novelEntryId', 'isLatest'])
@Index(['createdAt'])
export class NovelEntryHistory extends AuditableBaseEntity {
  @Column({ name: 'novel_entry_id', type: 'uuid' })
  novelEntryId: string;

  @ManyToOne(() => NovelEntry, (entry) => entry.versions)
  @JoinColumn({ name: 'novel_entry_id' })
  novelEntry: NovelEntry;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({ name: 'version_number', type: 'integer' })
  versionNumber: number;

  @Column({ name: 'is_latest', type: 'boolean', default: false })
  isLatest: boolean;

  @Column({ name: 'is_submitted_version', type: 'boolean', default: false })
  isSubmittedVersion: boolean;

  @Column({ name: 'submission_number', type: 'integer', nullable: true })
  submissionNumber: number;

  @Column({ name: 'submitted_at', type: 'timestamp', nullable: true })
  submittedAt: Date;

  // Resubmission tracking fields
  @Column({ name: 'is_resubmission', type: 'boolean', default: false })
  isResubmission: boolean;

  @Column({ name: 'resubmission_type', type: 'varchar', nullable: true })
  resubmissionType: 'after_review' | null;

  @Column({ name: 'previous_status', type: 'varchar', nullable: true })
  previousStatus: string;

  @Column({ name: 'word_count', type: 'integer' })
  wordCount: number;

  @Column({
    name: 'meta_data',
    type: 'json',
    nullable: true,
  })
  metaData: any;
}
