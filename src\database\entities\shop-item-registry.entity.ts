import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseFileRegistry } from './base-file-registry.entity';
import { ShopItem } from './shop-item.entity';

@Entity()
export class ShopItemRegistry extends BaseFileRegistry {
  @Column({ name: 'shop_item_id' })
  shopItemId: string;

  @ManyToOne(() => ShopItem, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'shop_item_id' })
  shopItem: ShopItem;

  @Column({ name: 'user_id', nullable: true })
  userId: string;

  /**
   * Convert to DTO for API responses
   */
  toDto(): any {
    return {
      ...this.toSimpleObject(),
      shopItemId: this.shopItemId,
      userId: this.userId,
      shopItem: this.shopItem
        ? {
            id: this.shopItem.id,
            itemNumber: this.shopItem.itemNumber,
            title: this.shopItem.title,
            categoryId: this.shopItem.categoryId,
          }
        : null,
    };
  }
}
