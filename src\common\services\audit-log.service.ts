import { Injectable, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuditLog, AuditAction } from '../../database/entities/audit-log.entity';
import LoggerService from './logger.service';

@Injectable()
export class AuditLogService {
  constructor(
    @InjectRepository(AuditLog)
    private auditLogRepository: Repository<AuditLog>,
    @Inject(LoggerService) private logger: LoggerService,
  ) {}

  async log(action: string, resource: string, userId: string | null, ipAddress: string, userAgent: string, details?: Record<string, any>, status?: string, errorMessage?: string): Promise<void> {
    try {
      const auditLog = new AuditLog();
      auditLog.userId = userId ?? '';
      auditLog.action = action;
      auditLog.resource = resource;
      auditLog.details = details ?? {};
      auditLog.ipAddress = ipAddress;
      auditLog.userAgent = userAgent;
      auditLog.timestamp = new Date();
      auditLog.status = status ?? '';
      auditLog.errorMessage = errorMessage ?? '';

      await this.auditLogRepository.save(auditLog);

      // Also log to file for redundancy
      this.logger.info(`AUDIT: [${action}] ${resource} by ${userId || 'anonymous'} from ${ipAddress} - ${status || 'success'}`);
    } catch (error) {
      // If audit logging fails, we don't want to break the application
      // Just log the error to the regular logger
      this.logger.error(`Failed to save audit log: ${error.message}`);
    }
  }

  async getUserAuditLogs(userId: string): Promise<AuditLog[]> {
    return this.auditLogRepository.find({
      where: { userId },
      order: { timestamp: 'DESC' },
    });
  }

  async getResourceAuditLogs(resource: string): Promise<AuditLog[]> {
    return this.auditLogRepository.find({
      where: { resource },
      order: { timestamp: 'DESC' },
    });
  }

  async getActionAuditLogs(action: string): Promise<AuditLog[]> {
    return this.auditLogRepository.find({
      where: { action },
      order: { timestamp: 'DESC' },
    });
  }
}
