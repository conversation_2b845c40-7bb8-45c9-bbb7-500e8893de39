import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EssayTaskSubmissions } from '../../database/entities/essay-task-submissions.entity';

import { EssayTaskSubmissionMarking } from '../../database/entities/essay-task-submission-marking.entity';
import { EssayMissionTasks } from '../../database/entities/essay-mission-tasks.entity';
import { AwardCriteria, AwardFrequency } from '../../database/entities/award.entity';
import { AwardsService } from '../awards/awards.service';
import { SubmissionStatus } from 'src/constants/submission.enum';

@Injectable()
export class EssayAwardService {
  private readonly logger = new Logger(EssayAwardService.name);

  constructor(
    @InjectRepository(EssayTaskSubmissions)
    private essayTaskSubmissionsRepository: Repository<EssayTaskSubmissions>,
    @InjectRepository(EssayTaskSubmissionMarking)
    private essayTaskSubmissionMarkingRepository: Repository<EssayTaskSubmissionMarking>,
    @InjectRepository(EssayMissionTasks)
    private essayMissionTasksRepository: Repository<EssayMissionTasks>,
    @Inject(forwardRef(() => AwardsService))
    private readonly awardsService: AwardsService,
  ) {}

  // Client-required award names for essay module
  private readonly CLIENT_AWARD_NAMES = ['Best Writer Award', 'Best Perfect Award'];

  /**
   * Generate essay awards for a specific date range
   * @param startDate Start date of the period
   * @param endDate End date of the period
   */
  async generateAwardsForRange(startDate: Date, endDate: Date): Promise<void> {
    this.logger.log(`Generating essay awards for period: ${startDate.toISOString()} to ${endDate.toISOString()}`);

    try {
      // Get all active essay awards that match client requirements
      const allAwardsResponse = await this.awardsService.getAllAwards(
        'essay' as any,
        false, // only active awards
        undefined,
        undefined,
        this.determineFrequency(startDate, endDate),
      );
      const awards = allAwardsResponse.items.filter((award) => this.CLIENT_AWARD_NAMES.includes(award.name));

      if (awards.length === 0) {
        this.logger.log('No client-required essay awards found');
        return;
      }

      this.logger.log(`Found ${awards.length} client-required essay awards: ${awards.map((a) => a.name).join(', ')}`);

      // Filter out any non-client awards
      const filteredCount = allAwardsResponse.items.length - awards.length;
      if (filteredCount > 0) {
        this.logger.log(`Filtered out ${filteredCount} non-client awards from calculation`);
      }

      // Get all students who submitted essays in the period
      const studentSubmissions = await this.getStudentSubmissionsInPeriod(startDate, endDate);

      if (studentSubmissions.length === 0) {
        this.logger.log('No essay submissions found in the period');
        return;
      }

      // Process each award
      for (const award of awards) {
        await this.processAward(award, studentSubmissions, startDate, endDate);
      }

      this.logger.log('Essay award generation completed successfully');
    } catch (error) {
      this.logger.error(`Error generating essay awards: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process a specific award
   */
  private async processAward(
    award: any, // Use any to accept both Award and AwardResponseDto
    studentSubmissions: any[],
    startDate: Date,
    endDate: Date,
  ): Promise<void> {
    this.logger.log(`Processing award: ${award.name}`);

    const scorers = [];

    for (const studentData of studentSubmissions) {
      const userId = studentData.userId;
      let totalScore = 0;
      let meetsMinimumCriteria = true;

      // Calculate scores for each criterion
      for (const criterion of award.criteria) {
        switch (criterion) {
          case AwardCriteria.ESSAY_PERFORMANCE: {
            const essayScore = await this.calculateEssayPerformanceScore(
              userId,
              studentData,
              award.criteriaConfig,
              award.name, // Pass award name for specific calculation logic
            );

            const minScore = award.criteriaConfig?.minScore || 0;
            if (essayScore < minScore) {
              meetsMinimumCriteria = false;
              break;
            }

            totalScore += essayScore;
            break;
          }
        }

        if (!meetsMinimumCriteria) break;
      }

      if (meetsMinimumCriteria && totalScore > 0) {
        scorers.push({
          userId,
          totalScore,
          metrics: {
            totalEssays: studentData.submissions.length,
            averageScore: studentData.averageScore,
            completionRate: studentData.completionRate,
            totalPoints: studentData.totalPoints,
          },
        });
      }
    }

    // Sort by score and select winners
    scorers.sort((a, b) => b.totalScore - a.totalScore);
    const maxWinners = award.criteriaConfig?.maxWinners || 1;
    const winners = scorers.slice(0, maxWinners);

    // Create award records
    for (const winner of winners) {
      await this.awardsService.createAwardWinner({
        userId: winner.userId,
        awardId: award.id,
        awardDate: endDate.toISOString().split('T')[0], // Convert to YYYY-MM-DD format
        awardReason: `Achieved ${winner.totalScore.toFixed(1)} points in ${award.name}`,
        metadata: {
          ...winner.metrics,
          calculationPeriod: {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          },
          totalScore: winner.totalScore,
        },
      });
    }

    this.logger.log(`Award ${award.name} processed: ${winners.length} winners selected from ${scorers.length} candidates`);
  }

  /**
   * Calculate essay performance score with award-specific logic
   */
  private async calculateEssayPerformanceScore(userId: string, studentData: any, criteriaConfig: any, awardName?: string): Promise<number> {
    try {
      const submissions = studentData.submissions;
      const averageScore = studentData.averageScore;
      const completionRate = studentData.completionRate;

      // Use award-specific calculation if award name is provided
      if (awardName) {
        return this.calculateAwardSpecificScore(awardName, studentData, criteriaConfig);
      }

      // Default calculation for backward compatibility
      // Quality Score (70% weight) - based on average essay points
      const qualityScore = Math.min(averageScore * 10, 100); // Convert to 0-100 scale

      // Quantity Score (30% weight) - based on number of completed essays
      const minEssays = criteriaConfig?.minEssays || 1;
      const quantityRatio = Math.min(submissions.length / minEssays, 2); // Cap at 200%
      const quantityScore = Math.min(quantityRatio * 50, 100);

      // Combined score with weights
      const combinedScore = qualityScore * 0.7 + quantityScore * 0.3;

      this.logger.log(`Essay performance score for user ${userId}: ${combinedScore.toFixed(1)}
        (Quality: ${qualityScore.toFixed(1)}, Quantity: ${quantityScore.toFixed(1)},
        Essays: ${submissions.length}, Avg Score: ${averageScore.toFixed(1)})`);

      return Math.round(combinedScore);
    } catch (error) {
      this.logger.error(`Error calculating essay performance score for user ${userId}: ${error.message}`);
      return 0;
    }
  }

  /**
   * Calculate award-specific score based on award type
   * @param awardName Name of the award
   * @param studentData Student submission data
   * @param criteriaConfig Award criteria configuration
   * @returns Calculated score for the specific award type
   */
  private calculateAwardSpecificScore(awardName: string, studentData: any, criteriaConfig: any): number {
    try {
      const submissions = studentData.submissions;
      const averageScore = studentData.averageScore;
      const completionRate = studentData.completionRate;

      if (awardName.includes('Best Writer')) {
        // Best Writer Award: Focus on most engagement/feedback (use completion rate and quality as proxy)
        const engagementScore = Math.min(completionRate, 100); // Higher completion shows engagement
        const qualityScore = Math.min(averageScore * 10, 100); // Convert to 0-100 scale
        const combinedScore = engagementScore * 0.6 + qualityScore * 0.4; // Prioritize engagement

        this.logger.log(`Essay Best Writer score for user:
          Engagement Score: ${engagementScore.toFixed(1)} (60% weight) - ${completionRate.toFixed(1)}% completion
          Quality Score: ${qualityScore.toFixed(1)} (40% weight) - ${averageScore.toFixed(2)} avg score
          Final Score: ${combinedScore.toFixed(1)}`);

        return Math.round(combinedScore);
      } else if (awardName.includes('Best Perfect')) {
        // Best Perfect Award: Focus on fewest errors (highest accuracy/quality)
        const qualityScore = Math.min(averageScore * 10, 100); // Primary focus on quality
        const completionScore = Math.min(completionRate, 100); // Consistency in completion
        const consistencyScore = Math.min((submissions.length / 5) * 100, 100); // Regular submissions (5 essays = 100%)
        const combinedScore = qualityScore * 0.7 + completionScore * 0.2 + consistencyScore * 0.1; // Heavily weighted toward quality

        this.logger.log(`Essay Best Perfect score for user:
          Quality Score: ${qualityScore.toFixed(1)} (70% weight) - ${averageScore.toFixed(2)} avg score
          Completion Score: ${completionScore.toFixed(1)} (20% weight) - ${completionRate.toFixed(1)}% completion
          Consistency Score: ${consistencyScore.toFixed(1)} (10% weight) - ${submissions.length} essays
          Final Score: ${combinedScore.toFixed(1)}`);

        return Math.round(combinedScore);
      } else {
        // Default calculation for unknown award types
        this.logger.warn(`Unknown essay award type: ${awardName}, using default calculation`);
        const qualityScore = Math.min(averageScore * 10, 100);
        const quantityScore = Math.min((submissions.length / 3) * 100, 100);
        const combinedScore = qualityScore * 0.7 + quantityScore * 0.3;
        return Math.round(combinedScore);
      }
    } catch (error) {
      this.logger.error(`Error calculating award-specific score for ${awardName}: ${error.message}`);
      return 0;
    }
  }

  /**
   * Get student submissions in the specified period
   */
  private async getStudentSubmissionsInPeriod(startDate: Date, endDate: Date): Promise<any[]> {
    const submissions = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .innerJoin('submission.submissionMark', 'marking')
      .innerJoin('submission.task', 'task')
      .where('submission.status = :status', { status: SubmissionStatus.REVIEWED })
      .andWhere('submission.lastSubmittedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere('submission.createdBy IS NOT NULL') // Exclude submissions without createdBy
      .select(['submission.createdBy as userId', 'COUNT(submission.id) as submissionCount', 'AVG(marking.points) as averageScore', 'SUM(marking.points) as totalPoints'])
      .groupBy('submission.createdBy')
      .getRawMany();

    // Get detailed submission data for each student
    const studentData = [];
    for (const submission of submissions) {
      const userId = submission.userId;

      // Skip submissions without valid user ID
      if (!userId) {
        this.logger.warn(`Skipping essay submission - missing userId`);
        continue;
      }

      // Get all submissions for this student in the period
      const userSubmissions = await this.essayTaskSubmissionsRepository
        .createQueryBuilder('submission')
        .innerJoin('submission.submissionMark', 'marking')
        .where('submission.createdBy = :userId', { userId })
        .andWhere('submission.status = :status', { status: SubmissionStatus.REVIEWED })
        .andWhere('submission.lastSubmittedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
        .select(['submission.id', 'marking.points', 'submission.lastSubmittedAt'])
        .getRawMany();

      // Get total available tasks in the period
      const availableTasks = await this.essayMissionTasksRepository.createQueryBuilder('task').where('task.createdAt <= :endDate', { endDate }).getCount();

      const completionRate = availableTasks > 0 ? (userSubmissions.length / availableTasks) * 100 : 0;

      studentData.push({
        userId,
        submissions: userSubmissions,
        averageScore: parseFloat(submission.averageScore) || 0,
        totalPoints: parseFloat(submission.totalPoints) || 0,
        completionRate: Math.min(completionRate, 100),
      });
    }

    return studentData;
  }

  /**
   * Determine award frequency based on date range
   */
  private determineFrequency(startDate: Date, endDate: Date): AwardFrequency {
    const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    if (days <= 7) return AwardFrequency.WEEKLY;
    if (days <= 31) return AwardFrequency.MONTHLY;
    if (days <= 93) return AwardFrequency.QUARTERLY;
    return AwardFrequency.YEARLY;
  }
}
