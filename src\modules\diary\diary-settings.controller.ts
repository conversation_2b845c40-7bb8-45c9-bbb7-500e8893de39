import { Controller, Get, UseGuards, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags, ApiQuery } from '@nestjs/swagger';
import { DiaryService } from './diary.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { DiarySettingsTemplateResponseDto } from '../../database/models/diary-settings.dto';
import { ApiResponse } from 'src/common/dto/api-response.dto';
import { ApiOkResponseWithArrayType, ApiErrorResponse } from 'src/common/decorators/api-response.decorator';
import { PagedListDto } from 'src/common/models/paged-list.dto';
import { PaginationDto } from 'src/common/models/pagination.dto';

@Controller('diary/settings')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('diary-settings')
export class DiarySettingsController {
  constructor(private readonly diaryService: DiaryService) {}

  @Get()
  @ApiOperation({
    summary: 'Get all available diary settings templates',
    description: 'Get a list of all active diary settings templates that students can use.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiOkResponseWithArrayType(DiarySettingsTemplateResponseDto, 'Diary settings templates retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getAllDiarySettingsTemplates(@Query() paginationDto: PaginationDto): Promise<ApiResponse<PagedListDto<DiarySettingsTemplateResponseDto>>> {
    const templates = await this.diaryService.getActiveDiarySettingsTemplates(paginationDto);
    return ApiResponse.success(templates, 'Diary settings templates retrieved successfully');
  }
}
