import { ApiProperty } from '@nestjs/swagger';

// ===== ADMIN DASHBOARD DTOs =====

export class AdminStudentCountDto {
  @ApiProperty({ example: 1250, description: 'Total number of active students' })
  totalActiveStudents: number;
}

export class AdminAttendanceStatsDto {
  @ApiProperty({ example: 850, description: 'Number of students present today' })
  presentCount: number;

  @ApiProperty({ example: 400, description: 'Number of students absent today' })
  absentCount: number;

  @ApiProperty({ example: 1250, description: 'Total students tracked for attendance' })
  totalStudents: number;

  @ApiProperty({ example: 68.0, description: 'Attendance percentage for today' })
  attendancePercentage: number;
}

export class ModuleSubmissionStatsDto {
  @ApiProperty({ example: 'hec_user_diary', description: 'Module feature type' })
  moduleType: string;

  @ApiProperty({ example: 'Diary', description: 'Module display name' })
  moduleName: string;

  @ApiProperty({ example: 2450, description: 'Total submissions in this module' })
  totalSubmissions: number;

  @ApiProperty({ example: 180, description: 'Pending submissions in this module' })
  pendingSubmissions: number;
}

export class AdminSubmissionStatsDto {
  @ApiProperty({ example: 8750, description: 'Total submissions across all modules' })
  totalSubmissions: number;

  @ApiProperty({ example: 650, description: 'Total pending submissions across all modules' })
  totalPendingSubmissions: number;

  @ApiProperty({ type: [ModuleSubmissionStatsDto], description: 'Breakdown by individual modules' })
  moduleBreakdown: ModuleSubmissionStatsDto[];
}

export class AdminTutorCountDto {
  @ApiProperty({ example: 45, description: 'Total number of assigned tutors' })
  totalAssignedTutors: number;

  @ApiProperty({ example: 38, description: 'Number of active tutors (with current assignments)' })
  activeTutors: number;
}

export class SubscriptionStatusBreakdownDto {
  @ApiProperty({ example: 'starter', description: 'Plan type' })
  planType: string;

  @ApiProperty({ example: 'Starter Plan', description: 'Plan display name' })
  planName: string;

  @ApiProperty({ example: 450, description: 'Number of students with this plan' })
  studentCount: number;

  @ApiProperty({ example: 36.0, description: 'Percentage of total students' })
  percentage: number;
}

export class AdminSubscriptionStatusDto {
  @ApiProperty({ example: 1250, description: 'Total students with active subscriptions' })
  totalStudents: number;

  @ApiProperty({ type: [SubscriptionStatusBreakdownDto], description: 'Breakdown by subscription plan' })
  planBreakdown: SubscriptionStatusBreakdownDto[];
}

export class UserModuleCompletionDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000', description: 'Student user ID' })
  userId: string;

  @ApiProperty({ example: 'John Doe', description: 'Student name' })
  userName: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Student email' })
  userEmail: string;

  @ApiProperty({ example: 'hec_user_diary', description: 'Module feature type' })
  moduleType: string;

  @ApiProperty({ example: 'Diary', description: 'Module display name' })
  moduleName: string;

  @ApiProperty({ example: 15, description: 'Total submissions in this module' })
  totalSubmissions: number;

  @ApiProperty({ example: 12, description: 'Confirmed submissions in this module' })
  confirmedSubmissions: number;

  @ApiProperty({ example: 80.0, description: 'Completion rate percentage' })
  completionRate: number;
}

export class AdminModuleCompletionRatesDto {
  @ApiProperty({ type: [UserModuleCompletionDto], description: 'Completion rates for each user-module combination' })
  userModuleCompletions: UserModuleCompletionDto[];

  @ApiProperty({ example: 1250, description: 'Total number of students evaluated' })
  totalStudents: number;
}

export class DailyActivityStatsDto {
  @ApiProperty({ example: '2024-01-15', description: 'Date of the activity' })
  date: string;

  @ApiProperty({ example: 45, description: 'Number of diary entries submitted' })
  diaryEntries: number;

  @ApiProperty({ example: 23, description: 'Number of QA assignments completed' })
  qaSubmissions: number;

  @ApiProperty({ example: 12, description: 'Number of essay submissions' })
  essaySubmissions: number;

  @ApiProperty({ example: 34, description: 'Number of novel entries' })
  novelEntries: number;

  @ApiProperty({ example: 18, description: 'Number of game attempts' })
  gameAttempts: number;

  @ApiProperty({ example: 8, description: 'Number of story maker participations' })
  storyMakerParticipations: number;

  @ApiProperty({ example: 140, description: 'Total activities for the day' })
  totalActivities: number;

  @ApiProperty({ example: 67, description: 'Number of unique active students' })
  activeStudents: number;
}

export class MonthlyActivityStatsDto {
  @ApiProperty({ example: 1, description: 'Month number (1-12)' })
  month: number;

  @ApiProperty({ example: 'January', description: 'Month name' })
  monthName: string;

  @ApiProperty({ example: 1250, description: 'Total diary entries for the month' })
  diaryEntries: number;

  @ApiProperty({ example: 890, description: 'Total QA submissions for the month' })
  qaSubmissions: number;

  @ApiProperty({ example: 456, description: 'Total essay submissions for the month' })
  essaySubmissions: number;

  @ApiProperty({ example: 678, description: 'Total novel entries for the month' })
  novelEntries: number;

  @ApiProperty({ example: 543, description: 'Total game attempts for the month' })
  gameAttempts: number;

  @ApiProperty({ example: 234, description: 'Total story maker participations for the month' })
  storyMakerParticipations: number;

  @ApiProperty({ example: 4051, description: 'Total activities for the month' })
  totalActivities: number;

  @ApiProperty({ example: 1180, description: 'Number of unique active students for the month' })
  activeStudents: number;
}

export class YearlyActivityStatsDto {
  @ApiProperty({ example: 2024, description: 'Year' })
  year: number;

  @ApiProperty({ type: [MonthlyActivityStatsDto], description: 'Monthly breakdown of activities' })
  monthlyStats: MonthlyActivityStatsDto[];

  @ApiProperty({ example: 15678, description: 'Total diary entries for the year' })
  totalDiaryEntries: number;

  @ApiProperty({ example: 12456, description: 'Total QA submissions for the year' })
  totalQaSubmissions: number;

  @ApiProperty({ example: 8934, description: 'Total essay submissions for the year' })
  totalEssaySubmissions: number;

  @ApiProperty({ example: 9876, description: 'Total novel entries for the year' })
  totalNovelEntries: number;

  @ApiProperty({ example: 7654, description: 'Total game attempts for the year' })
  totalGameAttempts: number;

  @ApiProperty({ example: 3456, description: 'Total story maker participations for the year' })
  totalStoryMakerParticipations: number;

  @ApiProperty({ example: 58054, description: 'Total activities for the year' })
  totalActivities: number;

  @ApiProperty({ example: 1450, description: 'Number of unique active students for the year' })
  totalActiveStudents: number;
}

export class StudentDailyActivityResponseDto {
  @ApiProperty({ type: [YearlyActivityStatsDto], description: 'Activity statistics grouped by year' })
  yearlyStats: YearlyActivityStatsDto[];

  @ApiProperty({ example: 3, description: 'Number of years with data' })
  totalYears: number;

  @ApiProperty({ example: 156789, description: 'Grand total of all activities across all years' })
  grandTotalActivities: number;
}

// ===== TUTOR DASHBOARD DTOs =====

export class TutorStudentCountDto {
  @ApiProperty({ example: 25, description: 'Total number of active students assigned to this tutor' })
  totalAssignedStudents: number;
}

export class TutorAttendanceStatsDto {
  @ApiProperty({ example: 18, description: 'Number of assigned students present today' })
  presentCount: number;

  @ApiProperty({ example: 7, description: 'Number of assigned students absent today' })
  absentCount: number;

  @ApiProperty({ example: 25, description: 'Total assigned students tracked for attendance' })
  totalStudents: number;

  @ApiProperty({ example: 72.0, description: 'Attendance percentage for today' })
  attendancePercentage: number;
}

export class TutorModuleReviewStatsDto {
  @ApiProperty({ example: 'hec_user_diary', description: 'Module feature type' })
  moduleType: string;

  @ApiProperty({ example: 'Diary', description: 'Module display name' })
  moduleName: string;

  @ApiProperty({ example: 145, description: 'Total reviews given in this module' })
  totalReviews: number;

  @ApiProperty({ example: 89, description: 'Total feedback given in this module' })
  totalFeedback: number;
}

export class TutorReviewStatsDto {
  @ApiProperty({ example: 425, description: 'Total reviews given across all modules' })
  totalReviews: number;

  @ApiProperty({ example: 312, description: 'Total feedback given across all modules' })
  totalFeedback: number;

  @ApiProperty({ type: [TutorModuleReviewStatsDto], description: 'Breakdown by individual modules' })
  moduleBreakdown: TutorModuleReviewStatsDto[];
}

export class TutorModuleSubmissionStatsDto {
  @ApiProperty({ example: 'hec_user_diary', description: 'Module feature type' })
  moduleType: string;

  @ApiProperty({ example: 'Diary', description: 'Module display name' })
  moduleName: string;

  @ApiProperty({ example: 89, description: 'Confirmed submissions in this module' })
  confirmedSubmissions: number;

  @ApiProperty({ example: 12, description: 'Pending/ongoing submissions in this module' })
  pendingSubmissions: number;
}

export class TutorSubmissionStatsDto {
  @ApiProperty({ example: 267, description: 'Total confirmed submissions across required modules (excluding HECplay)' })
  totalConfirmedSubmissions: number;

  @ApiProperty({ example: 45, description: 'Total pending/ongoing submissions across required modules' })
  totalPendingSubmissions: number;

  @ApiProperty({ type: [TutorModuleSubmissionStatsDto], description: 'Breakdown by individual modules (excluding HECplay)' })
  moduleBreakdown: TutorModuleSubmissionStatsDto[];
}
