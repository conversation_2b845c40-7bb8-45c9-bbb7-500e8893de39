import { MigrationInterface, QueryRunner } from 'typeorm';

export class TutorEssayProgressTracking1745900738456 implements MigrationInterface {
  name = 'TutorEssayProgressTracking1745900738456';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP CONSTRAINT "FK_2c0f158665674b8d3cbbe35e05f"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_28cca731b1dda53dbe764ee2834"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" RENAME COLUMN "marking" TO "submission_mark_id"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" RENAME CONSTRAINT "UQ_2c0f158665674b8d3cbbe35e05f" TO "UQ_f06f909a5ae83cb331a1651496b"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "UQ_28cca731b1dda53dbe764ee2834"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP COLUMN "marking"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD "first_revision_progress" double precision NOT NULL DEFAULT '0'`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD "is_first_revision" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD "submission_mark_id" uuid`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "UQ_fc522fa06e151db1d68346f83cd" UNIQUE ("submission_mark_id")`);
    await queryRunner.query(
      `ALTER TABLE "essay_task_submission_history" ADD CONSTRAINT "FK_f06f909a5ae83cb331a1651496b" FOREIGN KEY ("submission_mark_id") REFERENCES "essay_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_fc522fa06e151db1d68346f83cd" FOREIGN KEY ("submission_mark_id") REFERENCES "essay_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_fc522fa06e151db1d68346f83cd"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP CONSTRAINT "FK_f06f909a5ae83cb331a1651496b"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "UQ_fc522fa06e151db1d68346f83cd"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP COLUMN "submission_mark_id"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP COLUMN "is_first_revision"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP COLUMN "first_revision_progress"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD "marking" uuid`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "UQ_28cca731b1dda53dbe764ee2834" UNIQUE ("marking")`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" RENAME CONSTRAINT "UQ_f06f909a5ae83cb331a1651496b" TO "UQ_2c0f158665674b8d3cbbe35e05f"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submission_history" RENAME COLUMN "submission_mark_id" TO "marking"`);
    await queryRunner.query(
      `ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_28cca731b1dda53dbe764ee2834" FOREIGN KEY ("marking") REFERENCES "essay_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "essay_task_submission_history" ADD CONSTRAINT "FK_2c0f158665674b8d3cbbe35e05f" FOREIGN KEY ("marking") REFERENCES "essay_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
