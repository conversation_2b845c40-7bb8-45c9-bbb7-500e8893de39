import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { Message } from './message.entity';

/**
 * Entity for message attachments
 */
@Entity()
export class MessageAttachment extends AuditableBaseEntity {
  @Column({ name: 'message_id' })
  messageId: string;

  @ManyToOne(() => Message, (message) => message.attachments)
  @JoinColumn({ name: 'message_id' })
  message: Message;

  @Column({ name: 'file_path' })
  filePath: string;

  @Column({ name: 'file_name' })
  fileName: string;

  @Column({ name: 'mime_type' })
  mimeType: string;

  @Column({ name: 'file_size' })
  fileSize: number;

  @Column({ name: 'thumbnail_path', nullable: true })
  thumbnailPath: string;
}
