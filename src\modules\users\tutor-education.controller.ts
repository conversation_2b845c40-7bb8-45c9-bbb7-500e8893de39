import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, ForbiddenException } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { UserType } from '../../database/entities/user.entity';
import { TutorEducationService } from './tutor-education.service';
import { CreateTutorEducationDto, UpdateTutorEducationDto, TutorEducationResponseDto } from './dto/tutor-education.dto';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';

// Define a type for the request object with user information
interface RequestWithUser {
  user: {
    id: string;
    type: UserType;
    sub: string;
    selectedRole?: UserType;
  };
}

@ApiTags('Tutor Education')
@ApiBearerAuth('JWT-auth')
@Controller('tutors')
@UseGuards(JwtAuthGuard, RolesGuard)
export class TutorEducationController {
  constructor(private readonly tutorEducationService: TutorEducationService) {}

  @Post(':tutorId/education')
  @Roles(UserType.ADMIN, UserType.TUTOR)
  @ApiOperation({ summary: 'Create a new education entry for a tutor' })
  @ApiParam({ name: 'tutorId', description: 'The ID of the tutor' })
  @ApiResponse({ status: 201, description: 'The education entry has been created', type: TutorEducationResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async create(@Param('tutorId') tutorId: string, @Body() createDto: CreateTutorEducationDto, @Request() req: RequestWithUser): Promise<TutorEducationResponseDto> {
    // Check if the current user is the tutor or an admin
    if (req.user.type !== UserType.ADMIN && req.user.id !== tutorId) {
      throw new ForbiddenException('You are not authorized to create education entries for this tutor');
    }

    return this.tutorEducationService.create(tutorId, createDto, req.user.id);
  }

  @Get(':tutorId/education')
  @ApiOperation({ summary: 'Get all education entries for a tutor' })
  @ApiParam({ name: 'tutorId', description: 'The ID of the tutor' })
  @ApiResponse({ status: 200, description: 'The education entries have been retrieved', type: [TutorEducationResponseDto] })
  async findAll(@Param('tutorId') tutorId: string): Promise<TutorEducationResponseDto[]> {
    return this.tutorEducationService.findAllForTutor(tutorId);
  }

  @Get('education/:id')
  @ApiOperation({ summary: 'Get a specific education entry by ID' })
  @ApiParam({ name: 'id', description: 'The ID of the education entry' })
  @ApiResponse({ status: 200, description: 'The education entry has been retrieved', type: TutorEducationResponseDto })
  @ApiResponse({ status: 404, description: 'Education entry not found' })
  async findOne(@Param('id') id: string): Promise<TutorEducationResponseDto> {
    return this.tutorEducationService.findOne(id);
  }

  @Patch('education/:id')
  @Roles(UserType.ADMIN, UserType.TUTOR)
  @ApiOperation({ summary: 'Update an education entry' })
  @ApiParam({ name: 'id', description: 'The ID of the education entry' })
  @ApiResponse({ status: 200, description: 'The education entry has been updated', type: TutorEducationResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Education entry not found' })
  async update(@Param('id') id: string, @Body() updateDto: UpdateTutorEducationDto, @Request() req: RequestWithUser): Promise<TutorEducationResponseDto> {
    return this.tutorEducationService.update(id, updateDto, req.user.id);
  }

  @Delete('education/:id')
  @Roles(UserType.ADMIN, UserType.TUTOR)
  @ApiOperation({ summary: 'Delete an education entry' })
  @ApiParam({ name: 'id', description: 'The ID of the education entry' })
  @ApiResponse({ status: 200, description: 'The education entry has been deleted' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Education entry not found' })
  async remove(@Param('id') id: string, @Request() req: RequestWithUser): Promise<void> {
    return this.tutorEducationService.remove(id, req.user.id);
  }

  @Get('my-education')
  @Roles(UserType.TUTOR)
  @ApiOperation({ summary: 'Get all education entries for the current tutor' })
  @ApiResponse({ status: 200, description: 'The education entries have been retrieved', type: [TutorEducationResponseDto] })
  async findMyEducation(@Request() req: RequestWithUser): Promise<TutorEducationResponseDto[]> {
    return this.tutorEducationService.findAllForTutor(req.user.id);
  }

  @Post('my-education')
  @Roles(UserType.TUTOR)
  @ApiOperation({ summary: 'Create a new education entry for the current tutor' })
  @ApiResponse({ status: 201, description: 'The education entry has been created', type: TutorEducationResponseDto })
  async createMyEducation(@Body() createDto: CreateTutorEducationDto, @Request() req: RequestWithUser): Promise<TutorEducationResponseDto> {
    return this.tutorEducationService.create(req.user.id, createDto, req.user.id);
  }
}
