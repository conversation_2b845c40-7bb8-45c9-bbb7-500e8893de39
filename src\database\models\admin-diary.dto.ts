import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsDateString, IsNumber } from 'class-validator';
import { DiaryEntryStatus } from '../entities/diary-entry.entity';
import { Transform } from 'class-transformer';

export class AdminDiaryEntryFilterDto {
  @ApiProperty({
    description: 'Filter entries by student name',
    required: false,
  })
  @IsOptional()
  @IsString()
  studentName?: string;

  @ApiProperty({
    description: 'Filter entries by entry status',
    required: false,
    enum: DiaryEntryStatus,
  })
  @IsOptional()
  @IsEnum(DiaryEntryStatus)
  status?: DiaryEntryStatus;

  @ApiProperty({
    description: 'Filter entries by start date (YYYY-MM-DD)',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  dateFrom?: string;

  @ApiProperty({
    description: 'Filter entries by end date (YYYY-MM-DD)',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  dateTo?: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  page?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  limit?: number;

  @IsOptional()
  @IsString()
  sortBy?: string;

  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;
}

export class AdminUpdateDiaryEntryDto {
  @ApiProperty({
    description: 'The updated title of the diary entry',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: 'The updated content of the diary entry',
    required: false,
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({
    description: 'The updated status of the diary entry',
    required: false,
    enum: DiaryEntryStatus,
  })
  @IsOptional()
  @IsEnum(DiaryEntryStatus)
  status?: DiaryEntryStatus;
}
