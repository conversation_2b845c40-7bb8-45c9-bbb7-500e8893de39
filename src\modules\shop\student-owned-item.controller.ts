import { Controller, Get, Param, Patch, Body, Query, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { StudentOwnedItemService } from './student-owned-item.service';
import {
  StudentOwnedItemResponseDto,
  UpdateStudentOwnedItemDto,
  StudentOwnedItemsResponseDto,
  GroupedOwnedItemsResponseDto,
  CategoryWithItemsDto,
  StudentOwnedItemsQueryDto,
  StudentOwnedItemsByCategoryQueryDto,
  StudentOwnedItemsGroupedQueryDto,
} from '../../database/models/student-owned-item.dto';
import { OwnedItemStatus } from '../../database/entities/student-owned-item.entity';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { StudentGuard } from '../../common/guards/student.guard';
import { SubscriptionFeatureGuard } from '../../common/guards/subscription-feature.guard';
import { StrictStudentOnly } from '../../common/decorators/strict-student-only.decorator';
import { PaginationDto } from '../../common/models/pagination.dto';
import { Request } from 'express';

// Define a custom interface to extend the Express Request type
interface RequestWithUser extends Request {
  user: { id: string; [key: string]: any };
}

@ApiTags('Student Owned Items')
@Controller('student/owned-items')
@UseGuards(JwtAuthGuard, StudentGuard, SubscriptionFeatureGuard)
@StrictStudentOnly()
@ApiBearerAuth('JWT-auth')
export class StudentOwnedItemController {
  constructor(private readonly studentOwnedItemService: StudentOwnedItemService) {}

  @Get()
  @ApiOperation({
    summary: 'Get student owned items',
    description: 'Get all items owned by the current student',
  })
  @ApiOkResponseWithType(StudentOwnedItemsResponseDto, 'Student owned items retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getOwnedItems(@Req() req: RequestWithUser, @Query() queryDto: StudentOwnedItemsQueryDto): Promise<ApiResponse<StudentOwnedItemsResponseDto>> {
    const items = await this.studentOwnedItemService.getStudentOwnedItems(req.user['id'], queryDto.categoryId, queryDto.status, queryDto);
    return ApiResponse.success(items, 'Student owned items retrieved successfully');
  }

  @Get('grouped')
  @ApiOperation({
    summary: 'Get student owned items grouped by category',
    description: 'Get all items owned by the current student, grouped by category',
  })
  @ApiOkResponseWithType(GroupedOwnedItemsResponseDto, 'Student owned items retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getOwnedItemsGrouped(@Req() req: RequestWithUser, @Query() queryDto: StudentOwnedItemsGroupedQueryDto): Promise<ApiResponse<GroupedOwnedItemsResponseDto>> {
    const groupedItems = await this.studentOwnedItemService.getStudentOwnedItemsGroupedByCategory(req.user['id'], queryDto.categoryId, queryDto.status);

    // Convert the object to the expected format
    const categories: CategoryWithItemsDto[] = Object.entries(groupedItems).map(([categoryName, items]) => {
      // Find the categoryId from the first item (all items in a group have the same categoryId)
      const categoryId = items.length > 0 ? items[0].categoryId : null;

      return {
        categoryId,
        categoryName,
        items,
      };
    });

    return ApiResponse.success({ categories }, 'Student owned items grouped by category retrieved successfully');
  }

  @Get('categories/:categoryId')
  @ApiOperation({
    summary: 'Get student owned items by category',
    description: 'Get items owned by the current student in a specific category',
  })
  @ApiParam({
    name: 'categoryId',
    description: 'Category ID',
    type: String,
  })
  @ApiOkResponseWithType(StudentOwnedItemsResponseDto, 'Student owned items retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Category not found')
  async getOwnedItemsByCategory(
    @Req() req: RequestWithUser,
    @Param('categoryId') categoryId: string,
    @Query() queryDto: StudentOwnedItemsByCategoryQueryDto,
  ): Promise<ApiResponse<StudentOwnedItemsResponseDto>> {
    const items = await this.studentOwnedItemService.getStudentOwnedItemsByCategory(req.user['id'], categoryId, queryDto.status, queryDto);
    return ApiResponse.success(items, 'Student owned items by category retrieved successfully');
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get owned item by ID',
    description: 'Get a specific owned item by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Owned item ID',
    type: String,
  })
  @ApiOkResponseWithType(StudentOwnedItemResponseDto, 'Owned item retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Owned item not found')
  async getOwnedItemById(@Req() req: RequestWithUser, @Param('id') id: string): Promise<ApiResponse<StudentOwnedItemResponseDto>> {
    const item = await this.studentOwnedItemService.getOwnedItemById(id, req.user['id']);
    return ApiResponse.success(item, 'Owned item retrieved successfully');
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update owned item',
    description: 'Update a specific owned item',
  })
  @ApiParam({
    name: 'id',
    description: 'Owned item ID',
    type: String,
  })
  @ApiOkResponseWithType(StudentOwnedItemResponseDto, 'Owned item updated successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Owned item not found')
  async updateOwnedItem(@Req() req: RequestWithUser, @Param('id') id: string, @Body() updateDto: UpdateStudentOwnedItemDto): Promise<ApiResponse<StudentOwnedItemResponseDto>> {
    const item = await this.studentOwnedItemService.updateOwnedItem(id, req.user['id'], updateDto);
    return ApiResponse.success(item, 'Owned item updated successfully');
  }
}
