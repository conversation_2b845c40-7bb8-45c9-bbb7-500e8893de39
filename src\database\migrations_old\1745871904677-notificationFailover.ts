import { MigrationInterface, QueryRunner } from 'typeorm';

export class NotificationFailover1745871904677 implements MigrationInterface {
  name = 'NotificationFailover1745871904677';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "notification_delivery" ADD "error_code" character varying`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" ADD "error_details" text`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" ADD "max_retries" integer NOT NULL DEFAULT '3'`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" ADD "last_retry_at" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" ADD "retry_strategy" character varying DEFAULT 'exponential'`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" ADD "priority" integer NOT NULL DEFAULT '1'`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" ADD "payload" text`);
    await queryRunner.query(`ALTER TYPE "public"."notification_delivery_status_enum" RENAME TO "notification_delivery_status_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."notification_delivery_status_enum" AS ENUM('pending', 'sent', 'failed', 'failed_permanent', 'retry_scheduled', 'read')`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "notification_delivery" ALTER COLUMN "status" TYPE "public"."notification_delivery_status_enum" USING "status"::"text"::"public"."notification_delivery_status_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "notification_delivery" ALTER COLUMN "status" SET DEFAULT 'pending'`);
    await queryRunner.query(`DROP TYPE "public"."notification_delivery_status_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."notification_delivery_status_enum_old" AS ENUM('pending', 'sent', 'failed', 'read')`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(
      `ALTER TABLE "notification_delivery" ALTER COLUMN "status" TYPE "public"."notification_delivery_status_enum_old" USING "status"::"text"::"public"."notification_delivery_status_enum_old"`,
    );
    await queryRunner.query(`ALTER TABLE "notification_delivery" ALTER COLUMN "status" SET DEFAULT 'pending'`);
    await queryRunner.query(`DROP TYPE "public"."notification_delivery_status_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."notification_delivery_status_enum_old" RENAME TO "notification_delivery_status_enum"`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" DROP COLUMN "payload"`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" DROP COLUMN "priority"`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" DROP COLUMN "retry_strategy"`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" DROP COLUMN "last_retry_at"`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" DROP COLUMN "max_retries"`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" DROP COLUMN "error_details"`);
    await queryRunner.query(`ALTER TABLE "notification_delivery" DROP COLUMN "error_code"`);
  }
}
