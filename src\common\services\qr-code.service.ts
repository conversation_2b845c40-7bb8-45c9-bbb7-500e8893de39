import { Injectable, Logger } from '@nestjs/common';
import * as QRCode from 'qrcode';
import * as fs from 'fs';
import * as path from 'path';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class QrCodeService {
  private readonly logger = new Logger(QrCodeService.name);
  private readonly uploadDir: string;

  constructor(private readonly configService: ConfigService) {
    this.uploadDir = this.configService.get<string>('UPLOAD_DIR') || 'uploads';

    // Create diary-qr directory if it doesn't exist
    const diaryQrDir = path.join(this.uploadDir, 'diary-qr');
    if (!fs.existsSync(diaryQrDir)) {
      fs.mkdirSync(diaryQrDir, { recursive: true });
    }
  }

  /**
   * Generate a QR code as a buffer
   * @param url The URL to encode in the QR code
   * @returns Buffer containing the QR code image
   */
  async generateQrCode(url: string): Promise<Buffer> {
    try {
      this.logger.log(`Generating QR code for URL: ${url}`);

      // Generate QR code as a buffer
      return await QRCode.toBuffer(url, {
        errorCorrectionLevel: 'H',
        type: 'png',
        margin: 1,
        scale: 8,
      });
    } catch (error) {
      this.logger.error(`Error generating QR code: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate and save a QR code to the file system
   * @param url The URL to encode in the QR code
   * @param filename The filename to save the QR code as
   * @returns Path to the saved QR code file
   */
  async generateAndSaveQrCode(url: string, filename: string): Promise<{ filePath: string; fileSize: number }> {
    try {
      this.logger.log(`Generating and saving QR code for URL: ${url}`);

      // Generate QR code as a buffer
      const qrBuffer = await this.generateQrCode(url);

      // Create the full path
      const diaryQrDir = path.join(this.uploadDir, 'diary-qr');
      const filePath = path.join('diary-qr', filename);
      const fullPath = path.join(this.uploadDir, filePath);

      // Save the QR code to the file system
      fs.writeFileSync(fullPath, qrBuffer);

      this.logger.log(`QR code saved to: ${fullPath}`);

      return {
        filePath,
        fileSize: qrBuffer.length,
      };
    } catch (error) {
      this.logger.error(`Error generating and saving QR code: ${error.message}`);
      throw error;
    }
  }
}
