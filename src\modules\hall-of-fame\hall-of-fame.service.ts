import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AwardWinner } from '../../database/entities/award-winner.entity';
import { Award, AwardModule, AwardFrequency } from '../../database/entities/award.entity';

import {
  HallOfFameQueryDto,
  OngoingAwardsQueryDto,
  HallOfFameResponseDto,
  OngoingAwardsResponseDto,
  HallOfFameWinnerDto,
  HallOfFameStatsDto,
  ModuleHallOfFameDto,
} from '../../database/models/hall-of-fame.dto';
import { getCurrentUTCDate, getStartOfMonthUTC, getEndOfMonthUTC, getStartOfWeekUTC, getEndOfWeekUTC } from '../../common/utils/date-utils';

@Injectable()
export class HallOfFameService {
  private readonly logger = new Logger(HallOfFameService.name);

  constructor(
    @InjectRepository(AwardWinner)
    private readonly awardWinnerRepository: Repository<AwardWinner>,
    @InjectRepository(Award)
    private readonly awardRepository: Repository<Award>,
  ) {}

  /**
   * Get Hall of Fame with pagination and filters
   * @param queryDto Query parameters for filtering and pagination
   * @returns Paginated Hall of Fame response
   */
  async getHallOfFame(queryDto: HallOfFameQueryDto): Promise<HallOfFameResponseDto> {
    try {
      const { page = 1, limit = 20, sortBy = 'awardDate', sortDirection = 'DESC', module, frequency, year, awardName, winnerName } = queryDto;

      // Build query
      const queryBuilder = this.awardWinnerRepository.createQueryBuilder('winner').leftJoinAndSelect('winner.award', 'award').leftJoinAndSelect('winner.user', 'user');

      // Apply filters
      if (module) {
        queryBuilder.andWhere('award.module = :module', { module });
      }

      if (frequency) {
        queryBuilder.andWhere('award.frequency = :frequency', { frequency });
      }

      if (year) {
        const startOfYear = new Date(year, 0, 1);
        const endOfYear = new Date(year, 11, 31, 23, 59, 59, 999);
        queryBuilder.andWhere('winner.awardDate BETWEEN :startOfYear AND :endOfYear', {
          startOfYear,
          endOfYear,
        });
      }

      if (awardName) {
        queryBuilder.andWhere('LOWER(award.name) LIKE LOWER(:awardName)', {
          awardName: `%${awardName}%`,
        });
      }

      if (winnerName) {
        queryBuilder.andWhere('LOWER(user.name) LIKE LOWER(:winnerName)', {
          winnerName: `%${winnerName}%`,
        });
      }

      // Get total count
      const totalWinners = await queryBuilder.getCount();

      // Apply pagination and sorting
      const skip = (page - 1) * limit;
      queryBuilder.orderBy(`winner.${sortBy}`, sortDirection).skip(skip).take(limit);

      const winners = await queryBuilder.getMany();

      // Map to DTOs with additional information
      const winnerDtos = await Promise.all(
        winners.map(async (winner, index) => {
          const totalAwards = await this.getUserTotalAwards(winner.userId);
          return this.mapToHallOfFameWinnerDto(winner, skip + index + 1, totalAwards);
        }),
      );

      const totalPages = Math.ceil(totalWinners / limit);

      return {
        winners: winnerDtos,
        totalWinners,
        currentPage: page,
        totalPages,
        itemsPerPage: limit,
        filters: {
          module,
          frequency,
          year,
          awardName,
          winnerName,
        },
      };
    } catch (error) {
      this.logger.error(`Error getting Hall of Fame: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get Hall of Fame');
    }
  }

  /**
   * Get ongoing awards for current period
   * @param queryDto Query parameters for filtering
   * @returns Ongoing awards response
   */
  async getOngoingAwards(queryDto: OngoingAwardsQueryDto): Promise<OngoingAwardsResponseDto> {
    try {
      const { module, frequency } = queryDto;
      const now = getCurrentUTCDate();

      // Determine current period based on frequency
      let startDate: Date;
      let endDate: Date;
      let periodType: string;
      let description: string;

      if (frequency === AwardFrequency.WEEKLY) {
        startDate = getStartOfWeekUTC(now);
        endDate = getEndOfWeekUTC(now);
        periodType = 'current_week';
        description = `Current Week (${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()})`;
      } else if (frequency === AwardFrequency.MONTHLY) {
        startDate = getStartOfMonthUTC(now);
        endDate = getEndOfMonthUTC(now);
        periodType = 'current_month';
        description = `Current Month (${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()})`;
      } else {
        // Default to current month if no frequency specified
        startDate = getStartOfMonthUTC(now);
        endDate = getEndOfMonthUTC(now);
        periodType = 'current_month';
        description = `Current Month (${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()})`;
      }

      // Build query for current period
      const queryBuilder = this.awardWinnerRepository
        .createQueryBuilder('winner')
        .leftJoinAndSelect('winner.award', 'award')
        .leftJoinAndSelect('winner.user', 'user')
        .where('winner.awardDate BETWEEN :startDate AND :endDate', { startDate, endDate });

      // Apply filters
      if (module) {
        queryBuilder.andWhere('award.module = :module', { module });
      }

      if (frequency) {
        queryBuilder.andWhere('award.frequency = :frequency', { frequency });
      }

      // Order by award date descending
      queryBuilder.orderBy('winner.awardDate', 'DESC');

      const winners = await queryBuilder.getMany();

      // Map to DTOs
      const winnerDtos = await Promise.all(
        winners.map(async (winner, index) => {
          const totalAwards = await this.getUserTotalAwards(winner.userId);
          return this.mapToHallOfFameWinnerDto(winner, index + 1, totalAwards);
        }),
      );

      return {
        currentWinners: winnerDtos,
        periodInfo: {
          type: periodType,
          startDate,
          endDate,
          description,
        },
        totalWinners: winners.length,
        filters: {
          module,
          frequency,
        },
      };
    } catch (error) {
      this.logger.error(`Error getting ongoing awards: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get ongoing awards');
    }
  }

  /**
   * Get Hall of Fame statistics
   * @returns Hall of Fame statistics
   */
  async getHallOfFameStats(): Promise<HallOfFameStatsDto> {
    try {
      // Get total awards and winners
      const totalAwards = await this.awardWinnerRepository.count();
      const uniqueWinners = await this.awardWinnerRepository.createQueryBuilder('winner').select('COUNT(DISTINCT winner.userId)', 'count').getRawOne();
      const totalWinners = parseInt(uniqueWinners.count);

      // Get awards by module
      const awardsByModuleRaw = await this.awardWinnerRepository
        .createQueryBuilder('winner')
        .leftJoin('winner.award', 'award')
        .select('award.module', 'module')
        .addSelect('COUNT(*)', 'count')
        .groupBy('award.module')
        .getRawMany();

      const awardsByModule = awardsByModuleRaw.reduce(
        (acc, item) => {
          acc[item.module] = parseInt(item.count);
          return acc;
        },
        {} as { [key in AwardModule]: number },
      );

      // Get awards by frequency
      const awardsByFrequencyRaw = await this.awardWinnerRepository
        .createQueryBuilder('winner')
        .leftJoin('winner.award', 'award')
        .select('award.frequency', 'frequency')
        .addSelect('COUNT(*)', 'count')
        .groupBy('award.frequency')
        .getRawMany();

      const awardsByFrequency = awardsByFrequencyRaw.reduce(
        (acc, item) => {
          acc[item.frequency] = parseInt(item.count);
          return acc;
        },
        {} as { [key in AwardFrequency]: number },
      );

      // Get top winners (top 10)
      const topWinnersRaw = await this.awardWinnerRepository
        .createQueryBuilder('winner')
        .leftJoin('winner.user', 'user')
        .leftJoin('winner.award', 'award')
        .select('winner.userId', 'userId')
        .addSelect('user.name', 'userName')
        .addSelect('user.profilePicture', 'userProfilePicture')
        .addSelect('COUNT(*)', 'totalAwards')
        .addSelect('array_agg(DISTINCT award.module)', 'modules')
        .groupBy('winner.userId, user.name, user.profilePicture')
        .orderBy('COUNT(*)', 'DESC')
        .limit(10)
        .getRawMany();

      const topWinners = topWinnersRaw.map((winner) => ({
        userId: winner.userId,
        userName: winner.userName,
        userProfilePicture: winner.userProfilePicture,
        totalAwards: parseInt(winner.totalAwards),
        modules: winner.modules || [],
      }));

      // Get recent awards (last 10)
      const recentWinners = await this.awardWinnerRepository.find({
        relations: ['award', 'user'],
        order: { awardDate: 'DESC' },
        take: 10,
      });

      const recentAwards = await Promise.all(
        recentWinners.map(async (winner, index) => {
          const totalAwards = await this.getUserTotalAwards(winner.userId);
          return this.mapToHallOfFameWinnerDto(winner, index + 1, totalAwards);
        }),
      );

      // Get period covered
      const oldestAward = await this.awardWinnerRepository.findOne({
        order: { awardDate: 'ASC' },
      });
      const newestAward = await this.awardWinnerRepository.findOne({
        order: { awardDate: 'DESC' },
      });

      return {
        totalAwards,
        totalWinners,
        awardsByModule,
        awardsByFrequency,
        topWinners,
        recentAwards,
        periodCovered: {
          startDate: oldestAward?.awardDate || new Date(),
          endDate: newestAward?.awardDate || new Date(),
        },
      };
    } catch (error) {
      this.logger.error(`Error getting Hall of Fame stats: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get Hall of Fame statistics');
    }
  }

  /**
   * Get Hall of Fame by specific module
   * @param module Award module to filter by
   * @param limit Number of winners to return
   * @returns Module-specific Hall of Fame
   */
  async getModuleHallOfFame(module: AwardModule, limit: number = 50): Promise<ModuleHallOfFameDto> {
    try {
      // Get winners for the module
      const winners = await this.awardWinnerRepository.find({
        where: { award: { module } },
        relations: ['award', 'user'],
        order: { awardDate: 'DESC' },
        take: limit,
      });

      // Get unique award types with their frequencies in this module
      const awardTypesRaw = await this.awardRepository.find({
        where: { module, isActive: true },
        select: ['name', 'frequency'],
      });

      // Group award types by name and collect their frequencies
      const awardTypesMap = new Map<string, Set<AwardFrequency>>();
      awardTypesRaw.forEach((award) => {
        if (!awardTypesMap.has(award.name)) {
          awardTypesMap.set(award.name, new Set());
        }
        awardTypesMap.get(award.name).add(award.frequency);
      });

      // Convert to array of objects with name and frequencies
      const awardTypes = Array.from(awardTypesMap.entries()).map(([name, frequencies]) => ({
        name,
        frequencies: Array.from(frequencies).sort(),
      }));

      // Map to DTOs
      const winnerDtos = await Promise.all(
        winners.map(async (winner, index) => {
          const totalAwards = await this.getUserTotalAwards(winner.userId);
          return this.mapToHallOfFameWinnerDto(winner, index + 1, totalAwards);
        }),
      );

      // Find top performer for this module
      const topPerformerRaw = await this.awardWinnerRepository
        .createQueryBuilder('winner')
        .leftJoin('winner.user', 'user')
        .leftJoin('winner.award', 'award')
        .where('award.module = :module', { module })
        .select('winner.userId', 'userId')
        .addSelect('user.name', 'userName')
        .addSelect('user.profilePicture', 'userProfilePicture')
        .addSelect('COUNT(*)', 'totalAwards')
        .groupBy('winner.userId, user.name, user.profilePicture')
        .orderBy('COUNT(*)', 'DESC')
        .limit(1)
        .getRawOne();

      let topPerformer = null;
      if (topPerformerRaw) {
        const latestAward = await this.awardWinnerRepository.findOne({
          where: { userId: topPerformerRaw.userId, award: { module } },
          relations: ['award', 'user'],
          order: { awardDate: 'DESC' },
        });

        if (latestAward) {
          const totalAwards = await this.getUserTotalAwards(topPerformerRaw.userId);
          topPerformer = {
            userId: topPerformerRaw.userId,
            userName: topPerformerRaw.userName,
            userProfilePicture: topPerformerRaw.userProfilePicture,
            totalAwards: parseInt(topPerformerRaw.totalAwards),
            latestAward: this.mapToHallOfFameWinnerDto(latestAward, 1, totalAwards),
          };
        }
      }

      return {
        module,
        moduleName: this.getModuleDisplayName(module),
        winners: winnerDtos,
        totalWinners: winners.length,
        awardTypes: awardTypes,
        topPerformer,
      };
    } catch (error) {
      this.logger.error(`Error getting module Hall of Fame for ${module}: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get Hall of Fame for ${module} module`);
    }
  }

  /**
   * Get user's total awards count
   * @param userId User ID
   * @returns Total number of awards won by the user
   */
  private async getUserTotalAwards(userId: string): Promise<number> {
    return await this.awardWinnerRepository.count({ where: { userId } });
  }

  /**
   * Map AwardWinner entity to HallOfFameWinnerDto
   * @param winner Award winner entity
   * @param rank Rank/position in the list
   * @param totalAwards Total awards won by this user
   * @returns Hall of Fame winner DTO
   */
  private mapToHallOfFameWinnerDto(winner: AwardWinner, rank: number, totalAwards: number): HallOfFameWinnerDto {
    return {
      id: winner.id,
      userId: winner.userId,
      userName: winner.user?.name || 'Unknown User',
      userProfilePicture: winner.user?.profilePicture || null,
      awardId: winner.awardId,
      awardName: winner.award?.name || 'Unknown Award',
      awardModule: winner.award?.module || AwardModule.DIARY,
      awardFrequency: winner.award?.frequency || AwardFrequency.MONTHLY,
      awardDate: winner.awardDate,
      awardReason: winner.awardReason,
      rewardPoints: winner.award?.rewardPoints || 0,
      metadata: winner.metadata,
      rank,
      totalAwards,
      createdAt: winner.createdAt,
    };
  }

  /**
   * Get display name for module
   * @param module Award module
   * @returns Human-readable module name
   */
  private getModuleDisplayName(module: AwardModule): string {
    switch (module) {
      case AwardModule.DIARY:
        return 'Diary';
      case AwardModule.NOVEL:
        return 'Novel';
      case AwardModule.ESSAY:
        return 'Essay';
      default:
        return 'Unknown';
    }
  }
}
