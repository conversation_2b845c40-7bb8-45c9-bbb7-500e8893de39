import { Entity, Column, OneToMany, Index } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';

export enum NovelTopicCategory {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
}

@Entity()
@Index(['sequenceTitle', 'category'], { unique: true })
export class NovelTopic extends AuditableBaseEntity {
  @Column({ name: 'title' })
  title: string;

  @Column({ name: 'sequence_title' })
  sequenceTitle: string;

  @Column({
    name: 'category',
    type: 'enum',
    enum: NovelTopicCategory,
  })
  category: NovelTopicCategory;

  @Column({ name: 'instruction', type: 'text' })
  instruction: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'min_word_count', type: 'integer', nullable: true })
  minWordCount: number;

  @Column({ name: 'max_word_count', type: 'integer', nullable: true })
  maxWordCount: number;

  @OneToMany('NovelEntry', 'topic')
  entries: any[];
}
