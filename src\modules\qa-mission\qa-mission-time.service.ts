import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QAMissionMonth } from '../../database/entities/qa-mission-month.entity';
import { QAMissionWeek } from '../../database/entities/qa-mission-week.entity';

@Injectable()
export class QAMissionTimeService {
  private readonly logger = new Logger(QAMissionTimeService.name);

  constructor(
    @InjectRepository(QAMissionMonth)
    private readonly monthRepository: Repository<QAMissionMonth>,
    @InjectRepository(QAMissionWeek)
    private readonly weekRepository: Repository<QAMissionWeek>,
  ) {}

  /**
   * Get all months for dropdown
   * @returns List of months with id, title, and sequence
   */
  async getMonths(): Promise<{ id: string; title: string; sequence: number }[]> {
    const months = await this.monthRepository.find({
      select: ['id', 'title', 'sequence'],
      order: { sequence: 'ASC' },
    });

    return months;
  }

  /**
   * Get all weeks for dropdown
   * @returns List of weeks with id, title, sequence, month
   */
  async getWeeks(): Promise<
    {
      id: string;
      title: string;
      sequence: number;
      month: string;
    }[]
  > {
    const weeks = await this.weekRepository.find({
      select: ['id', 'title', 'sequence', 'month', 'startDate', 'endDate'],
      order: { sequence: 'ASC' },
    });

    return weeks;
  }
}
