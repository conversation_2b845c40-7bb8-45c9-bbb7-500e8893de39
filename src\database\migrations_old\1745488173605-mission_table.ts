import { MigrationInterface, QueryRunner } from 'typeorm';

export class MissionTable1745488173605 implements MigrationInterface {
  name = 'MissionTable1745488173605';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."essay_mission_time_frequency_enum" AS ENUM('weekly', 'monthly')`);
    await queryRunner.query(
      `CREATE TABLE "essay_mission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "time_frequency" "public"."essay_mission_time_frequency_enum" NOT NULL DEFAULT 'weekly', "is_active" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_d884c1d984da37d0842f6e0e11e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "essay_mission_tasks" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying(50) NOT NULL, "description" text NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "time_period_unit" integer DEFAULT '1', "word_limit_minumum" integer NOT NULL DEFAULT '1', "word_limit_maximum" integer, "deadline" integer DEFAULT '1', "instructions" text NOT NULL, "meta_data" json, "mission_id" uuid, CONSTRAINT "PK_89ec22c90fb5407e279761c7dcf" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "essay_mission_tasks" ADD CONSTRAINT "FK_e96796b88e99efa96d93e10c502" FOREIGN KEY ("mission_id") REFERENCES "essay_mission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_mission_tasks" DROP CONSTRAINT "FK_e96796b88e99efa96d93e10c502"`);
    await queryRunner.query(`DROP TABLE "essay_mission_tasks"`);
    await queryRunner.query(`DROP TABLE "essay_mission"`);
    await queryRunner.query(`DROP TYPE "public"."essay_mission_time_frequency_enum"`);
  }
}
