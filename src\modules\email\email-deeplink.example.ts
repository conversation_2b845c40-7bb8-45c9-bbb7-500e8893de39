import { Injectable } from '@nestjs/common';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';

/**
 * Example service showing how to use the DeeplinkService in the EmailService
 * This is not meant to be used directly, but as a reference for refactoring
 * the EmailService to use the DeeplinkService.
 */
@Injectable()
export class EmailDeeplinkExample {
  constructor(private readonly deeplinkService: DeeplinkService) {}

  /**
   * Generate a verification link for a user
   * @param token The verification token
   * @returns A URL for verifying the user's email
   */
  getVerificationLink(token: string): string {
    return this.deeplinkService.getWebLink(DeeplinkType.VERIFICATION, {
      additionalParams: { token },
    });
  }

  /**
   * Generate a password reset link for a user
   * @param token The password reset token
   * @returns A URL for resetting the user's password
   */
  getPasswordResetLink(token: string): string {
    return this.deeplinkService.getWebLink(DeeplinkType.PASSWORD_RESET, {
      additionalParams: { token },
    });
  }

  /**
   * Generate an HTML verification link for a user
   * @param token The verification token
   * @returns An HTML anchor tag for verifying the user's email
   */
  getVerificationLinkHtml(token: string): string {
    return this.deeplinkService.getLinkHtml(DeeplinkType.VERIFICATION, {
      additionalParams: { token },
      linkText: 'Verify Email',
      buttonStyle: true,
    });
  }

  /**
   * Generate an HTML password reset link for a user
   * @param token The password reset token
   * @returns An HTML anchor tag for resetting the user's password
   */
  getPasswordResetLinkHtml(token: string): string {
    return this.deeplinkService.getLinkHtml(DeeplinkType.PASSWORD_RESET, {
      additionalParams: { token },
      linkText: 'Reset Password',
      buttonStyle: true,
    });
  }
}
