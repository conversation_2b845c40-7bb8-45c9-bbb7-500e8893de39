import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserType } from '../../database/entities/user.entity';
import { PlanFeature, FeatureType } from '../../database/entities/plan-feature.entity';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { DiaryEntryAttendance, AttendanceStatus } from '../../database/entities/diary-entry-attendance.entity';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import { MissionDiaryEntry, MissionEntryStatus } from '../../database/entities/mission-diary-entry.entity';
import { NovelEntry, NovelEntryStatus } from '../../database/entities/novel-entry.entity';
import { QATaskSubmissions, QASubmissionStatus } from '../../database/entities/qa-task-submissions.entity';
import { QAMissionTasks } from '../../database/entities/qa-mission-tasks.entity';
import { EssayTaskSubmissions } from '../../database/entities/essay-task-submissions.entity';
import { SubmissionStatus as EssaySubmissionStatus } from '../../constants/submission.enum';
import { EssayMissionTasks } from '../../database/entities/essay-mission-tasks.entity';
import { DiaryFeedback } from '../../database/entities/diary-feedback.entity';
import { DiaryCorrection } from '../../database/entities/diary-correction.entity';
import { MissionDiaryEntryFeedback } from '../../database/entities/mission-diary-entry-feedback.entity';
import { NovelFeedback } from '../../database/entities/novel-feedback.entity';
import { NovelCorrection } from '../../database/entities/novel-correction.entity';
import { QATaskSubmissionMarking } from '../../database/entities/qa-task-submission-marking.entity';
import { EssayTaskSubmissionMarking } from '../../database/entities/essay-task-submission-marking.entity';
import {
  TutorStudentCountDto,
  TutorAttendanceStatsDto,
  TutorReviewStatsDto,
  TutorSubmissionStatsDto,
  TutorModuleReviewStatsDto,
  TutorModuleSubmissionStatsDto,
} from '../../database/models/dashboard.dto';

@Injectable()
export class TutorDashboardService {
  private readonly logger = new Logger(TutorDashboardService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(PlanFeature)
    private readonly planFeatureRepository: Repository<PlanFeature>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(DiaryEntryAttendance)
    private readonly diaryEntryAttendanceRepository: Repository<DiaryEntryAttendance>,
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(MissionDiaryEntry)
    private readonly missionDiaryEntryRepository: Repository<MissionDiaryEntry>,
    @InjectRepository(NovelEntry)
    private readonly novelEntryRepository: Repository<NovelEntry>,
    @InjectRepository(QATaskSubmissions)
    private readonly qaTaskSubmissionsRepository: Repository<QATaskSubmissions>,
    @InjectRepository(QAMissionTasks)
    private readonly qaMissionTasksRepository: Repository<QAMissionTasks>,
    @InjectRepository(EssayTaskSubmissions)
    private readonly essayTaskSubmissionsRepository: Repository<EssayTaskSubmissions>,
    @InjectRepository(EssayMissionTasks)
    private readonly essayMissionTasksRepository: Repository<EssayMissionTasks>,
    @InjectRepository(DiaryFeedback)
    private readonly diaryFeedbackRepository: Repository<DiaryFeedback>,
    @InjectRepository(DiaryCorrection)
    private readonly diaryCorrectionRepository: Repository<DiaryCorrection>,
    @InjectRepository(MissionDiaryEntryFeedback)
    private readonly missionDiaryEntryFeedbackRepository: Repository<MissionDiaryEntryFeedback>,
    @InjectRepository(NovelFeedback)
    private readonly novelFeedbackRepository: Repository<NovelFeedback>,
    @InjectRepository(NovelCorrection)
    private readonly novelCorrectionRepository: Repository<NovelCorrection>,
    @InjectRepository(QATaskSubmissionMarking)
    private readonly qaTaskSubmissionMarkingRepository: Repository<QATaskSubmissionMarking>,
    @InjectRepository(EssayTaskSubmissionMarking)
    private readonly essayTaskSubmissionMarkingRepository: Repository<EssayTaskSubmissionMarking>,
  ) {}

  /**
   * Get total count of active students assigned to the tutor
   */
  async getAssignedStudentCount(tutorId: string): Promise<TutorStudentCountDto> {
    try {
      const totalAssignedStudents = await this.studentTutorMappingRepository
        .createQueryBuilder('mapping')
        .leftJoin('mapping.student', 'student')
        .leftJoin('student.userPlans', 'userPlan')
        .leftJoin('userPlan.plan', 'plan')
        .where('mapping.tutorId = :tutorId', { tutorId })
        .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
        .andWhere('student.type = :userType', { userType: UserType.STUDENT })
        .andWhere('userPlan.isActive = :isActive', { isActive: true })
        .andWhere('plan.isActive = :planActive', { planActive: true })
        .select('COUNT(DISTINCT mapping.studentId)', 'count')
        .getRawOne();

      return {
        totalAssignedStudents: parseInt(totalAssignedStudents.count, 10),
      };
    } catch (error) {
      this.logger.error(`Error getting assigned student count for tutor ${tutorId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get assigned student count');
    }
  }

  /**
   * Get today's attendance statistics for tutor's assigned students
   */
  async getTodayAttendanceStats(tutorId: string): Promise<TutorAttendanceStatsDto> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Get total count of assigned students (same logic as getAssignedStudentCount)
      const totalAssignedStudents = await this.studentTutorMappingRepository
        .createQueryBuilder('mapping')
        .leftJoin('mapping.student', 'student')
        .leftJoin('student.userPlans', 'userPlan')
        .leftJoin('userPlan.plan', 'plan')
        .where('mapping.tutorId = :tutorId', { tutorId })
        .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
        .andWhere('student.type = :userType', { userType: UserType.STUDENT })
        .andWhere('userPlan.isActive = :isActive', { isActive: true })
        .andWhere('plan.isActive = :planActive', { planActive: true })
        .select('COUNT(DISTINCT mapping.studentId)', 'count')
        .getRawOne()
        .then((result) => parseInt(result.count, 10));

      if (totalAssignedStudents === 0) {
        return {
          presentCount: 0,
          absentCount: 0,
          totalStudents: 0,
          attendancePercentage: 0,
        };
      }

      // Get attendance stats for today for assigned students
      const todayEnd = new Date(today);
      todayEnd.setHours(23, 59, 59, 999);

      const attendanceStats = await this.diaryEntryAttendanceRepository
        .createQueryBuilder('attendance')
        .innerJoin('attendance.student', 'student')
        .innerJoin(StudentTutorMapping, 'mapping', 'mapping.studentId = student.id')
        .leftJoin('student.userPlans', 'userPlan')
        .leftJoin('userPlan.plan', 'plan')
        .select('attendance.status', 'status')
        .addSelect('COUNT(*)', 'count')
        .where('attendance.entryDate >= :todayStart AND attendance.entryDate <= :todayEnd', {
          todayStart: today,
          todayEnd: todayEnd
        })
        .andWhere('mapping.tutorId = :tutorId', { tutorId })
        .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
        .andWhere('student.type = :userType', { userType: UserType.STUDENT })
        .andWhere('userPlan.isActive = :isActive', { isActive: true })
        .andWhere('plan.isActive = :planActive', { planActive: true })
        .groupBy('attendance.status')
        .getRawMany();

      let presentCount = 0;
      let absentCount = 0;

      attendanceStats.forEach((stat) => {
        const count = parseInt(stat.count, 10);
        if (stat.status === AttendanceStatus.PRESENT) {
          presentCount = count;
        } else if (stat.status === AttendanceStatus.ABSENT) {
          absentCount = count;
        }
      });

      // Calculate students with no attendance record for today
      const studentsWithAttendance = presentCount + absentCount;
      const studentsWithoutAttendance = totalAssignedStudents - studentsWithAttendance;

      // For attendance percentage calculation, use total assigned students as denominator
      const attendancePercentage = totalAssignedStudents > 0 ? (presentCount / totalAssignedStudents) * 100 : 0;

      return {
        presentCount,
        absentCount: absentCount + studentsWithoutAttendance, // Include students without attendance as absent
        totalStudents: totalAssignedStudents,
        attendancePercentage: Math.round(attendancePercentage * 100) / 100,
      };
    } catch (error) {
      this.logger.error(`Error getting today's attendance stats for tutor ${tutorId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get attendance statistics');
    }
  }

  /**
   * Get total review and feedback statistics for the tutor
   */
  async getReviewStats(tutorId: string): Promise<TutorReviewStatsDto> {
    try {
      const modules = await this.planFeatureRepository.find({
        where: [{ type: FeatureType.HEC_USER_DIARY }, { type: FeatureType.ENGLISH_NOVEL }, { type: FeatureType.ENGLISH_QA_WRITING }, { type: FeatureType.ENGLISH_ESSAY }],
      });

      const moduleBreakdown: TutorModuleReviewStatsDto[] = [];
      let totalReviews = 0;
      let totalFeedback = 0;

      for (const module of modules) {
        const stats = await this.getModuleReviewStats(tutorId, module);
        moduleBreakdown.push(stats);
        totalReviews += stats.totalReviews;
        totalFeedback += stats.totalFeedback;
      }

      return {
        totalReviews,
        totalFeedback,
        moduleBreakdown,
      };
    } catch (error) {
      this.logger.error(`Error getting review stats for tutor ${tutorId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get review statistics');
    }
  }

  /**
   * Get review statistics for a specific module
   */
  private async getModuleReviewStats(tutorId: string, module: PlanFeature): Promise<TutorModuleReviewStatsDto> {
    let totalReviews = 0;
    let totalFeedback = 0;

    switch (module.type) {
      case FeatureType.HEC_USER_DIARY:
        const diaryStats = await this.getDiaryReviewStats(tutorId);
        totalReviews = diaryStats.reviews;
        totalFeedback = diaryStats.feedback;
        break;

      case FeatureType.ENGLISH_NOVEL:
        const novelStats = await this.getNovelReviewStats(tutorId);
        totalReviews = novelStats.reviews;
        totalFeedback = novelStats.feedback;
        break;

      case FeatureType.ENGLISH_QA_WRITING:
        const qaStats = await this.getQAReviewStats(tutorId);
        totalReviews = qaStats.reviews;
        totalFeedback = qaStats.feedback;
        break;

      case FeatureType.ENGLISH_ESSAY:
        const essayStats = await this.getEssayReviewStats(tutorId);
        totalReviews = essayStats.reviews;
        totalFeedback = essayStats.feedback;
        break;
    }

    return {
      moduleType: module.type,
      moduleName: module.name,
      totalReviews,
      totalFeedback,
    };
  }

  /**
   * Get diary review statistics for tutor
   */
  private async getDiaryReviewStats(tutorId: string): Promise<{ reviews: number; feedback: number }> {
    // Regular diary corrections and feedback
    const diaryCorrections = await this.diaryCorrectionRepository.count({
      where: { tutorId },
    });
    const diaryFeedback = await this.diaryFeedbackRepository.count({
      where: { tutorId },
    });

    // Mission diary feedback
    const missionFeedback = await this.missionDiaryEntryFeedbackRepository.count({
      where: { tutorId },
    });

    return {
      reviews: diaryCorrections,
      feedback: diaryFeedback + missionFeedback,
    };
  }

  /**
   * Get novel review statistics for tutor
   */
  private async getNovelReviewStats(tutorId: string): Promise<{ reviews: number; feedback: number }> {
    const corrections = await this.novelCorrectionRepository.count({
      where: { tutorId },
    });
    const feedback = await this.novelFeedbackRepository.count({
      where: { tutorId },
    });

    return {
      reviews: corrections,
      feedback,
    };
  }

  /**
   * Get QA review statistics for tutor
   */
  private async getQAReviewStats(tutorId: string): Promise<{ reviews: number; feedback: number }> {
    const markings = await this.qaTaskSubmissionMarkingRepository.count({
      where: { createdBy: tutorId },
    });

    return {
      reviews: markings,
      feedback: markings, // QA markings serve as both reviews and feedback
    };
  }

  /**
   * Get essay review statistics for tutor
   */
  private async getEssayReviewStats(tutorId: string): Promise<{ reviews: number; feedback: number }> {
    const markings = await this.essayTaskSubmissionMarkingRepository.count({
      where: { createdBy: tutorId },
    });

    return {
      reviews: markings,
      feedback: markings, // Essay markings serve as both reviews and feedback
    };
  }

  /**
   * Get submission statistics for tutor (excluding HECplay)
   */
  async getSubmissionStats(tutorId: string): Promise<TutorSubmissionStatsDto> {
    try {
      const modules = await this.planFeatureRepository.find({
        where: [{ type: FeatureType.HEC_USER_DIARY }, { type: FeatureType.ENGLISH_NOVEL }, { type: FeatureType.ENGLISH_QA_WRITING }, { type: FeatureType.ENGLISH_ESSAY }],
      });

      const moduleBreakdown: TutorModuleSubmissionStatsDto[] = [];
      let totalConfirmedSubmissions = 0;
      let totalPendingSubmissions = 0;

      for (const module of modules) {
        const stats = await this.getModuleSubmissionStats(tutorId, module);
        moduleBreakdown.push(stats);
        totalConfirmedSubmissions += stats.confirmedSubmissions;
        totalPendingSubmissions += stats.pendingSubmissions;
      }

      return {
        totalConfirmedSubmissions,
        totalPendingSubmissions,
        moduleBreakdown,
      };
    } catch (error) {
      this.logger.error(`Error getting submission stats for tutor ${tutorId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get submission statistics');
    }
  }

  /**
   * Get submission statistics for a specific module
   */
  private async getModuleSubmissionStats(tutorId: string, module: PlanFeature): Promise<TutorModuleSubmissionStatsDto> {
    let confirmedSubmissions = 0;
    let pendingSubmissions = 0;

    // Get students assigned to this tutor for this module
    const assignedStudentIds = await this.studentTutorMappingRepository
      .createQueryBuilder('mapping')
      .select('mapping.studentId', 'studentId')
      .where('mapping.tutorId = :tutorId', { tutorId })
      .andWhere('mapping.planFeatureId = :planFeatureId', { planFeatureId: module.id })
      .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
      .getRawMany();

    const studentIds = assignedStudentIds.map((item) => item.studentId);

    if (studentIds.length === 0) {
      return {
        moduleType: module.type,
        moduleName: module.name,
        confirmedSubmissions: 0,
        pendingSubmissions: 0,
      };
    }

    switch (module.type) {
      case FeatureType.HEC_USER_DIARY:
        const diaryStats = await this.getTutorDiarySubmissionStats(studentIds);
        confirmedSubmissions = diaryStats.confirmed;
        pendingSubmissions = diaryStats.pending;
        break;

      case FeatureType.ENGLISH_NOVEL:
        const novelStats = await this.getTutorNovelSubmissionStats(studentIds);
        confirmedSubmissions = novelStats.confirmed;
        pendingSubmissions = novelStats.pending;
        break;

      case FeatureType.ENGLISH_QA_WRITING:
        const qaStats = await this.getTutorQASubmissionStats(studentIds);
        confirmedSubmissions = qaStats.confirmed;
        pendingSubmissions = qaStats.pending;
        break;

      case FeatureType.ENGLISH_ESSAY:
        const essayStats = await this.getTutorEssaySubmissionStats(studentIds);
        confirmedSubmissions = essayStats.confirmed;
        pendingSubmissions = essayStats.pending;
        break;
    }

    return {
      moduleType: module.type,
      moduleName: module.name,
      confirmedSubmissions,
      pendingSubmissions,
    };
  }

  /**
   * Get diary submission statistics for tutor's students
   */
  private async getTutorDiarySubmissionStats(studentIds: string[]): Promise<{ confirmed: number; pending: number }> {
    // Regular diary entries
    const diaryConfirmed = await this.diaryEntryRepository
      .createQueryBuilder('entry')
      .leftJoin('entry.diary', 'diary')
      .where('diary.userId IN (:...studentIds)', { studentIds })
      .andWhere('entry.status = :status', { status: DiaryEntryStatus.CONFIRM })
      .getCount();

    const diaryPending = await this.diaryEntryRepository
      .createQueryBuilder('entry')
      .leftJoin('entry.diary', 'diary')
      .where('diary.userId IN (:...studentIds)', { studentIds })
      .andWhere('entry.status IN (:...statuses)', { statuses: [DiaryEntryStatus.SUBMIT, DiaryEntryStatus.REVIEWED] })
      .getCount();

    // Mission diary entries
    const missionConfirmed = await this.missionDiaryEntryRepository
      .createQueryBuilder('entry')
      .where('entry.studentId IN (:...studentIds)', { studentIds })
      .andWhere('entry.status = :status', { status: MissionEntryStatus.CONFIRMED })
      .getCount();

    const missionPending = await this.missionDiaryEntryRepository
      .createQueryBuilder('entry')
      .where('entry.studentId IN (:...studentIds)', { studentIds })
      .andWhere('entry.status IN (:...statuses)', { statuses: [MissionEntryStatus.SUBMITTED, MissionEntryStatus.REVIEWED] })
      .getCount();

    return {
      confirmed: diaryConfirmed + missionConfirmed,
      pending: diaryPending + missionPending,
    };
  }

  /**
   * Get novel submission statistics for tutor's students
   */
  private async getTutorNovelSubmissionStats(studentIds: string[]): Promise<{ confirmed: number; pending: number }> {
    const confirmed = await this.novelEntryRepository
      .createQueryBuilder('entry')
      .where('entry.studentId IN (:...studentIds)', { studentIds })
      .andWhere('entry.status = :status', { status: NovelEntryStatus.CONFIRMED })
      .getCount();

    const pending = await this.novelEntryRepository
      .createQueryBuilder('entry')
      .where('entry.studentId IN (:...studentIds)', { studentIds })
      .andWhere('entry.status IN (:...statuses)', { statuses: [NovelEntryStatus.SUBMITTED, NovelEntryStatus.LEGACY_UPDATED] })
      .getCount();

    return { confirmed, pending };
  }

  /**
   * Get QA submission statistics for tutor's students
   */
  private async getTutorQASubmissionStats(studentIds: string[]): Promise<{ confirmed: number; pending: number }> {
    const confirmed = await this.qaTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .where('submission.createdBy IN (:...studentIds)', { studentIds })
      .andWhere('submission.status = :status', { status: QASubmissionStatus.REVIEWED })
      .getCount();

    const pending = await this.qaTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .where('submission.createdBy IN (:...studentIds)', { studentIds })
      .andWhere('submission.status = :status', { status: QASubmissionStatus.SUBMITTED })
      .getCount();

    return { confirmed, pending };
  }

  /**
   * Get essay submission statistics for tutor's students
   */
  private async getTutorEssaySubmissionStats(studentIds: string[]): Promise<{ confirmed: number; pending: number }> {
    const confirmed = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .where('submission.createdBy IN (:...studentIds)', { studentIds })
      .andWhere('submission.status = :status', { status: EssaySubmissionStatus.REVIEWED })
      .getCount();

    const pending = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .where('submission.createdBy IN (:...studentIds)', { studentIds })
      .andWhere('submission.status = :status', { status: EssaySubmissionStatus.SUBMITTED })
      .getCount();

    return { confirmed, pending };
  }
}
