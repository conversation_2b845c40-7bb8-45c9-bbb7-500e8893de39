import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStoryMakerEntities1747134752862 implements MigrationInterface {
  name = 'AddStoryMakerEntities1747134752862';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "story_maker" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying NOT NULL, "instruction" text NOT NULL, "picture" character varying NOT NULL, "score" integer NOT NULL, "is_active" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_eed86ea930591c6a2d20c45532c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "story_maker_registry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "story_maker_id" uuid NOT NULL, "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying, "file_size" integer, CONSTRAINT "PK_36424f4fdc8c8901305d8e02ffe" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "story_maker_registry" ADD CONSTRAINT "FK_26306eeea7d42eb0d22ebc160d3" FOREIGN KEY ("story_maker_id") REFERENCES "story_maker"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "story_maker_registry" DROP CONSTRAINT "FK_26306eeea7d42eb0d22ebc160d3"`);
    await queryRunner.query(`DROP TABLE "story_maker_registry"`);
    await queryRunner.query(`DROP TABLE "story_maker"`);
  }
}
