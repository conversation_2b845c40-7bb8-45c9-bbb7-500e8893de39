import { MigrationInterface, QueryRunner } from 'typeorm';

export class StandardizeWritingModuleStatuses1750576722498 implements MigrationInterface {
  name = 'StandardizeWritingModuleStatuses1750576722498';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('Starting writing module status standardization migration...');

    // The enum already contains both uppercase and lowercase values from previous migrations
    // We just need to update the data to use lowercase values consistently

    // 1. Update Mission Diary Entry statuses from uppercase to lowercase
    console.log('Standardizing mission diary entry statuses to lowercase...');

    // Update existing data from uppercase to lowercase (using existing enum values)
    const missionStatusMappings = [
      { old: 'NEW', new: 'new' },
      { old: 'SUBMITTED', new: 'submit' }, // Use 'submit' which already exists in enum
      { old: 'REVIEWED', new: 'reviewed' },
      { old: 'CONFIRMED', new: 'confirm' }, // Use 'confirm' which already exists in enum
    ];

    for (const mapping of missionStatusMappings) {
      const result = await queryRunner.query(
        `
        UPDATE "mission_diary_entry"
        SET "status" = $1
        WHERE "status" = $2
      `,
        [mapping.new, mapping.old],
      );
      console.log(`Updated ${result.affectedRows || 0} rows from ${mapping.old} to ${mapping.new}`);
    }

    // 3. Novel entry enum already has the correct values, no changes needed
    console.log('Novel entry enum already supports the required values...');

    // 4. Update Novel Entry statuses to simplified 4-state lifecycle
    console.log('Simplifying novel entry statuses to 4-state lifecycle...');

    // Map complex novel statuses to simplified lifecycle
    const novelStatusMappings = [
      { old: 'updated', new: 'submitted' },
      { old: 'correction_given', new: 'reviewed' },
      { old: 'under_review', new: 'submitted' },
      // 'new', 'submitted', 'reviewed', 'confirmed' remain unchanged
    ];

    for (const mapping of novelStatusMappings) {
      await queryRunner.query(
        `
        UPDATE "novel_entry"
        SET "status" = $1
        WHERE "status" = $2
      `,
        [mapping.new, mapping.old],
      );
    }

    // 3. Update unified_status to ensure consistency
    console.log('Updating unified_status fields for consistency...');

    // Update mission diary unified status based on standardized status
    await queryRunner.query(`
      UPDATE "mission_diary_entry"
      SET "unified_status" = CASE
        WHEN "status" IN ('new', 'NEW') THEN 'draft'
        WHEN "status" IN ('submit', 'SUBMITTED') THEN 'submitted'
        WHEN "status" IN ('reviewed', 'REVIEWED') THEN 'reviewed'
        WHEN "status" IN ('confirm', 'CONFIRMED') THEN 'confirmed'
        ELSE 'draft'
      END
    `);

    // Update novel unified status based on simplified status
    await queryRunner.query(`
      UPDATE "novel_entry"
      SET "unified_status" = CASE
        WHEN "status" = 'new' THEN 'draft'
        WHEN "status" = 'submitted' THEN 'submitted'
        WHEN "status" = 'reviewed' THEN 'reviewed'
        WHEN "status" = 'confirmed' THEN 'confirmed'
        ELSE 'draft'
      END
    `);

    console.log('Writing module status standardization completed successfully');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('Rolling back writing module status standardization...');

    // Revert mission diary statuses to uppercase
    const revertMissionMappings = [
      { old: 'new', new: 'NEW' },
      { old: 'submitted', new: 'SUBMITTED' },
      { old: 'reviewed', new: 'REVIEWED' },
      { old: 'confirmed', new: 'CONFIRMED' },
    ];

    for (const mapping of revertMissionMappings) {
      await queryRunner.query(
        `
        UPDATE "mission_diary_entry"
        SET "status" = $1
        WHERE "status" = $2
      `,
        [mapping.new, mapping.old],
      );
    }

    // Note: Novel status reversion would require manual data analysis
    // as we've simplified complex statuses to basic ones
    console.log('Note: Novel status reversion requires manual intervention due to data simplification');

    console.log('Writing module status standardization rollback completed');
  }
}
