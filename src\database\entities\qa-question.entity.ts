import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { QAAssignment } from './qa-assignment.entity';

@Entity()
export class QAQuestion extends AuditableBaseEntity {
  @Column({ type: 'text' })
  question: string;

  @Column()
  points: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'minimum_words' })
  minimumWords: number;

  @OneToMany(() => QAAssignment, (assignment: QAAssignment) => assignment.question)
  assignments: QAAssignment[];
}
