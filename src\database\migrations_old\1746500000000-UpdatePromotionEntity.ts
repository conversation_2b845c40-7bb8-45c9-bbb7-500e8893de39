import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePromotionEntity1746500000000 implements MigrationInterface {
  name = 'UpdatePromotionEntity1746500000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    try {
      // Create the promotion_type enum
      await queryRunner.query(`CREATE TYPE "public"."promotion_promotion_type_enum" AS ENUM('percentage', 'fixed_amount')`);

      // Add new columns
      await queryRunner.query(`ALTER TABLE "promotion" ADD "promotion_type" "public"."promotion_promotion_type_enum" NOT NULL DEFAULT 'percentage'`);

      // Update existing records to set promotion_type equal to discount_type with proper casting
      await queryRunner.query(`
                UPDATE "promotion"
                SET "promotion_type" = CASE
                    WHEN "discount_type" = 'percentage' THEN 'percentage'::promotion_promotion_type_enum
                    WHEN "discount_type" = 'fixed_amount' THEN 'fixed_amount'::promotion_promotion_type_enum
                    ELSE 'percentage'::promotion_promotion_type_enum
                END
            `);

      // Add remaining columns
      await queryRunner.query(`ALTER TABLE "promotion" ADD "discount_category" character varying`);
      await queryRunner.query(`ALTER TABLE "promotion" ADD "applied_to_plan" boolean DEFAULT false`);
      await queryRunner.query(`ALTER TABLE "promotion" ADD "maximum_purchase_amount" decimal(10,2)`);

      // Make start_date and end_date nullable
      await queryRunner.query(`ALTER TABLE "promotion" ALTER COLUMN "start_date" DROP NOT NULL`);
      await queryRunner.query(`ALTER TABLE "promotion" ALTER COLUMN "end_date" DROP NOT NULL`);
    } catch (error) {
      console.error('Migration error:', error);
      throw error;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    try {
      // Make start_date and end_date required again
      await queryRunner.query(`ALTER TABLE "promotion" ALTER COLUMN "start_date" SET NOT NULL`);
      await queryRunner.query(`ALTER TABLE "promotion" ALTER COLUMN "end_date" SET NOT NULL`);

      // Drop new columns
      await queryRunner.query(`ALTER TABLE "promotion" DROP COLUMN "maximum_purchase_amount"`);
      await queryRunner.query(`ALTER TABLE "promotion" DROP COLUMN "applied_to_plan"`);
      await queryRunner.query(`ALTER TABLE "promotion" DROP COLUMN "discount_category"`);
      await queryRunner.query(`ALTER TABLE "promotion" DROP COLUMN "discount_amount"`);
      await queryRunner.query(`ALTER TABLE "promotion" DROP COLUMN "promotion_type"`);

      // Drop the enum
      await queryRunner.query(`DROP TYPE "public"."promotion_promotion_type_enum"`);
    } catch (error) {
      console.error('Migration rollback error:', error);
      throw error;
    }
  }
}
