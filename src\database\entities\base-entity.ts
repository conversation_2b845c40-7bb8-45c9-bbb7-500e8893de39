import { PrimaryGeneratedColumn, CreateDate<PERSON><PERSON>umn, UpdateDateColumn, Column } from 'typeorm';

export abstract class NonAuditableBaseEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
}

export abstract class AuditableBaseEntity extends NonAuditableBaseEntity {
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', nullable: true })
  updatedAt: Date | null;

  @Column({ name: 'created_by', nullable: true, length: 36 })
  createdBy: string | null;

  @Column({ name: 'updated_by', nullable: true, length: 36 })
  updatedBy: string | null;
}
