import { Entity, Column } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';

@Entity()
export class DiarySettingsTemplate extends AuditableBaseEntity {
  @Column({ name: 'title' })
  title: string;

  @Column({ name: 'level', type: 'int' })
  level: number;

  @Column({ name: 'word_limit', type: 'int' })
  wordLimit: number;

  @Column({ name: 'min_word_limit', type: 'int', default: 10 })
  minWordLimit: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;
}
