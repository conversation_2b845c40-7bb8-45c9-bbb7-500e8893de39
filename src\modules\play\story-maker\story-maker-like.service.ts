import { Injectable, NotFoundException, ConflictException, ForbiddenException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThanOrEqual } from 'typeorm';
import { StoryMakerLike, LikerType } from '../../../database/entities/story-maker-like.entity';
import { StoryMakerSubmission } from '../../../database/entities/story-maker-submission.entity';
import { User } from '../../../database/entities/user.entity';
import { NotificationHelperService } from '../../notification/notification-helper.service';
import { NotificationType } from '../../../database/entities/notification.entity';

@Injectable()
export class StoryMakerLikeService {
  private readonly logger = new Logger(StoryMakerLikeService.name);

  constructor(
    @InjectRepository(StoryMakerLike)
    private readonly storyMakerLikeRepository: Repository<StoryMakerLike>,
    @InjectRepository(StoryMakerSubmission)
    private readonly storyMakerSubmissionRepository: Repository<StoryMakerSubmission>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly notificationHelper: NotificationHelperService,
  ) {}

  /**
   * Add a like to a story maker submission
   * @param submissionId ID of the submission to like
   * @param likerId ID of the user liking the submission
   * @param likerType Type of user (student or tutor)
   * @returns The created like entity
   */
  async addLike(submissionId: string, likerId: string, likerType: LikerType): Promise<StoryMakerLike> {
    try {
      this.logger.log(`Adding like: submission ${submissionId}, liker ${likerId}, type ${likerType}`);

      // Check if submission exists and get author info
      const submission = await this.storyMakerSubmissionRepository.findOne({
        where: { id: submissionId },
        relations: ['participation', 'participation.student']
      });

      if (!submission) {
        throw new NotFoundException('Story submission not found');
      }

      // Validate user type
      if (likerType !== LikerType.STUDENT && likerType !== LikerType.TUTOR) {
        throw new ForbiddenException('Only students and tutors can like story submissions');
      }

      // Prevent self-liking
      if (submission.participation?.studentId === likerId) {
        throw new ForbiddenException('You cannot like your own story');
      }

      // Check if user has already liked this submission
      const existingLike = await this.storyMakerLikeRepository.findOne({
        where: { submissionId, likerId },
      });

      if (existingLike) {
        throw new ConflictException('User has already liked this story submission');
      }

      // Create new like
      const like = this.storyMakerLikeRepository.create({
        submissionId,
        likerId,
        likerType,
      });

      const savedLike = await this.storyMakerLikeRepository.save(like);

      // Send notification to story author
      await this.sendLikeNotification(submission, likerId, likerType);

      this.logger.log(`Like added successfully: ${savedLike.id}`);
      return savedLike;

    } catch (error) {
      this.logger.error(`Failed to add like: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Remove a like from a story maker submission
   * @param submissionId ID of the submission
   * @param likerId ID of the user who liked the submission
   */
  async removeLike(submissionId: string, likerId: string): Promise<void> {
    try {
      this.logger.log(`Removing like: submission ${submissionId}, liker ${likerId}`);

      const like = await this.storyMakerLikeRepository.findOne({
        where: { submissionId, likerId },
      });

      if (!like) {
        throw new NotFoundException('Like not found');
      }

      await this.storyMakerLikeRepository.remove(like);
      this.logger.log(`Like removed successfully: ${like.id}`);

    } catch (error) {
      this.logger.error(`Failed to remove like: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get the number of likes for a story submission
   * @param submissionId ID of the submission
   * @returns The number of likes
   */
  async getLikeCount(submissionId: string): Promise<number> {
    try {
      const count = await this.storyMakerLikeRepository.count({
        where: { submissionId },
      });
      return count;
    } catch (error) {
      this.logger.error(`Failed to get like count: ${error.message}`);
      return 0;
    }
  }

  /**
   * Check if a user has liked a story submission
   * @param submissionId ID of the submission
   * @param likerId ID of the user
   * @returns True if the user has liked the submission, false otherwise
   */
  async hasUserLiked(submissionId: string, likerId: string): Promise<boolean> {
    try {
      const count = await this.storyMakerLikeRepository.count({
        where: { submissionId, likerId },
      });
      return count > 0;
    } catch (error) {
      this.logger.error(`Failed to check if user liked: ${error.message}`);
      return false;
    }
  }

  /**
   * Get likes within 24 hours for popularity scoring
   * @param submissionId ID of the submission
   * @returns Number of likes within 24 hours
   */
  async getLikesWithin24Hours(submissionId: string): Promise<number> {
    try {
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

      const count = await this.storyMakerLikeRepository.count({
        where: {
          submissionId,
          createdAt: MoreThanOrEqual(twentyFourHoursAgo)
        },
      });

      return count;
    } catch (error) {
      this.logger.error(`Failed to get 24h like count: ${error.message}`);
      return 0;
    }
  }

  /**
   * Get weighted like count for popularity scoring (student likes = 1.0, tutor likes = 0.7)
   * @param submissionId ID of the submission
   * @returns Weighted like count within 24 hours
   */
  async getWeightedLikesWithin24Hours(submissionId: string): Promise<number> {
    try {
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

      const likes = await this.storyMakerLikeRepository.find({
        where: {
          submissionId,
          createdAt: MoreThanOrEqual(twentyFourHoursAgo)
        },
      });

      // Calculate weighted score: student likes = 1.0, tutor likes = 0.7
      const weightedScore = likes.reduce((total, like) => {
        return total + (like.likerType === LikerType.STUDENT ? 1.0 : 0.7);
      }, 0);

      return Math.round(weightedScore * 10) / 10; // Round to 1 decimal place
    } catch (error) {
      this.logger.error(`Failed to get weighted like count: ${error.message}`);
      return 0;
    }
  }

  /**
   * Get detailed like information for a submission
   * @param submissionId ID of the submission
   * @returns Object with like details
   */
  async getLikeDetails(submissionId: string): Promise<{
    totalLikes: number;
    studentLikes: number;
    tutorLikes: number;
    likesWithin24h: number;
    weightedLikes24h: number;
  }> {
    try {
      const [totalLikes, likesWithin24h, weightedLikes24h] = await Promise.all([
        this.getLikeCount(submissionId),
        this.getLikesWithin24Hours(submissionId),
        this.getWeightedLikesWithin24Hours(submissionId)
      ]);

      // Get breakdown by liker type
      const likes = await this.storyMakerLikeRepository.find({
        where: { submissionId }
      });

      const studentLikes = likes.filter(like => like.likerType === LikerType.STUDENT).length;
      const tutorLikes = likes.filter(like => like.likerType === LikerType.TUTOR).length;

      return {
        totalLikes,
        studentLikes,
        tutorLikes,
        likesWithin24h,
        weightedLikes24h
      };
    } catch (error) {
      this.logger.error(`Failed to get like details: ${error.message}`);
      return {
        totalLikes: 0,
        studentLikes: 0,
        tutorLikes: 0,
        likesWithin24h: 0,
        weightedLikes24h: 0
      };
    }
  }

  /**
   * Send notification when someone likes a story
   */
  private async sendLikeNotification(submission: StoryMakerSubmission, likerId: string, likerType: LikerType): Promise<void> {
    try {
      if (!submission.participation?.studentId) {
        return;
      }

      // Get liker information
      const liker = await this.userRepository.findOne({
        where: { id: likerId }
      });

      if (!liker) {
        return;
      }

      const likerName = liker.name || (likerType === LikerType.STUDENT ? 'A student' : 'A tutor');

      await this.notificationHelper.notify(
        submission.participation.studentId,
        NotificationType.STORY_MAKER_LIKED,
        'Someone liked your story! ❤️',
        `${likerName} liked your story. Your popularity score is increasing!`,
        {
          relatedEntityId: submission.id,
          relatedEntityType: 'story_maker_submission',
          sendPush: true,
          sendInApp: true,
          sendMobile: true,
        }
      );
    } catch (error) {
      this.logger.error(`Failed to send like notification: ${error.message}`);
      // Don't throw - notification failure shouldn't break the like functionality
    }
  }
}
