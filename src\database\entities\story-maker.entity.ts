import { Entity, Column, OneToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { StoryMakerParticipation } from './story-maker-participation.entity';

@Entity()
export class StoryMaker extends AuditableBaseEntity {
  @Column()
  title: string;

  @Column({ type: 'text' })
  instruction: string;

  @Column()
  picture: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'deadline', nullable: true })
  deadline: number;

  @Column({ name: 'image_analysis', type: 'jsonb', nullable: true })
  imageAnalysis: {
    objects?: string[];
    scene?: string;
    mood?: string;
    themes?: string[];
    colors?: string[];
    setting?: string;
    characters?: string[];
    emotions?: string[];
    description?: string;
    relevanceKeywords?: string[];
  };

  // Relationships
  @OneToMany(() => StoryMakerParticipation, (participation) => participation.storyMaker)
  participations: StoryMakerParticipation[];
}
