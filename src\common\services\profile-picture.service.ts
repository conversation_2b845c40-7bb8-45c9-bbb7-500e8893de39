import { Injectable, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { ProfilePicture } from '../../database/entities/profile-picture.entity';
import LoggerService from './logger.service';
import { FileRegistryService } from './file-registry.service';
import { FileEntityType } from '../enums/file-entity-type.enum';
import { FileUtilService } from './file-util.service';

@Injectable()
export class ProfilePictureService {
  constructor(
    @InjectRepository(ProfilePicture)
    private readonly profilePictureRepository: Repository<ProfilePicture>,
    @Inject(ConfigService)
    private readonly configService: ConfigService,
    @Inject(LoggerService)
    private readonly logger: LoggerService,
    @Inject(FileRegistryService)
    private readonly fileRegistryService: FileRegistryService,
    @Inject(FileUtilService)
    private readonly fileUtilService: FileUtilService,
  ) {}

  // Removed ensureUploadDirExists as it's now handled by FileUtilService

  /**
   * Upload a profile picture for a user
   * @param file The file to upload
   * @param userId The ID of the user
   * @returns The profile picture entity
   */
  async uploadProfilePicture(file: any, userId: string): Promise<ProfilePicture> {
    try {
      // First check if user already has a profile picture to get its ID
      const existingPicture = await this.profilePictureRepository.findOne({
        where: { userId: userId },
      });

      // Use the registry service to upload the profile picture
      // If user already has a profile picture, pass its ID to update the existing registry
      const result = await this.fileRegistryService.uploadFile(FileEntityType.PROFILE_PICTURE, file, userId, existingPicture ? { entityId: existingPicture.id } : undefined);

      return await this.getProfilePicture(userId);
    } catch (error) {
      this.logger.error(`Failed to upload profile picture for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get the profile picture for a user
   * @param userId The ID of the user
   * @returns The profile picture entity or null if not found
   */
  async getProfilePicture(userId: string): Promise<ProfilePicture | null> {
    try {
      // Use the registry service to get the profile picture
      const profilePicture = await this.fileRegistryService.getFile(FileEntityType.PROFILE_PICTURE, userId);
      if (profilePicture) {
        return profilePicture;
      } else {
        return null;
      }
    } catch (error) {
      this.logger.error(`Failed to get profile picture for user ${userId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Delete the profile picture for a user
   * @param userId The ID of the user
   * @returns True if the profile picture was deleted, false otherwise
   */
  async deleteProfilePicture(userId: string): Promise<boolean> {
    try {
      const profilePicture = await this.profilePictureRepository.findOne({
        where: { userId: userId },
      });

      if (!profilePicture) {
        return false;
      }

      // Delete the file using the file util service
      if (profilePicture.filePath) {
        this.fileUtilService.deleteFile(profilePicture.filePath);
      }

      // Delete the profile picture record
      await this.profilePictureRepository.remove(profilePicture);

      this.logger.info(`Deleted profile picture for user ${userId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete profile picture for user ${userId}: ${error.message}`);
      return false;
    }
  }

  // getProfilePictureUrl method moved below

  // Removed getSignedProfilePictureUrl method as it's no longer used

  /**
   * Get a direct URL for a user's profile picture
   * @param userId User ID
   * @returns Direct URL for accessing the profile picture or null if not found
   */
  async getProfilePictureDirectUrl(userId: string): Promise<string | null> {
    try {
      // Get profile picture entity
      const profilePicture = await this.getProfilePicture(userId);
      if (!profilePicture) {
        return null;
      }

      // Register the profile picture in the registry if not already registered
      await this.fileRegistryService.registerFile(FileEntityType.PROFILE_PICTURE, userId, profilePicture.filePath, profilePicture.fileName, profilePicture.mimeType, profilePicture.fileSize);

      // Return a direct URL to the profile picture
      return this.fileRegistryService.getFileUrl(FileEntityType.PROFILE_PICTURE, userId);
    } catch (error) {
      this.logger.error(`Error generating direct URL for user ${userId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get the URL for the current user's profile picture
   * @param userId The ID of the user
   * @returns The URL of the current user's profile picture
   */
  async getProfilePictureUrl(userId: string): Promise<string> {
    // Use the registry service to get the URL
    return await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.PROFILE_PICTURE, userId);
  }

  /**
   * Check if a user has a profile picture
   * @param userId The ID of the user
   * @returns True if the user has a profile picture, false otherwise
   */
  async hasProfilePicture(userId: string): Promise<boolean> {
    const profilePicture = await this.getProfilePicture(userId);
    return !!profilePicture;
  }

  // Removed deleteProfilePictureFile and validateFile as they're now handled by FileUtilService
}
