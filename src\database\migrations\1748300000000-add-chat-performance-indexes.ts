import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddChatPerformanceIndexes1748300000000 implements MigrationInterface {
  name = 'AddChatPerformanceIndexes1748300000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add index for conversation admin status lookup (used frequently in message mapping)
    await queryRunner.query(`
      CREATE INDEX "IDX_conversation_is_admin_conversation" 
      ON "conversation" ("is_admin_conversation")
    `);

    // Add composite index for admin conversation user lookup
    await queryRunner.query(`
      CREATE INDEX "IDX_conversation_admin_user_status" 
      ON "conversation" ("is_admin_conversation", "admin_conversation_user_id") 
      WHERE "is_admin_conversation" = true
    `);

    // Add index for message conversation lookup (used in batch queries)
    await queryRunner.query(`
      CREATE INDEX "IDX_message_conversation_created" 
      ON "message" ("conversation_id", "created_at" DESC)
    `);

    // Add index for admin conversation participant active status
    await queryRunner.query(`
      CREATE INDEX "IDX_admin_conversation_participant_active" 
      ON "admin_conversation_participant" ("conversation_id", "is_active") 
      WHERE "is_active" = true
    `);

    // Add index for message status and type filtering
    await queryRunner.query(`
      CREATE INDEX "IDX_message_status_type" 
      ON "message" ("status", "type")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes in reverse order
    await queryRunner.query(`DROP INDEX "IDX_message_status_type"`);
    await queryRunner.query(`DROP INDEX "IDX_admin_conversation_participant_active"`);
    await queryRunner.query(`DROP INDEX "IDX_message_conversation_created"`);
    await queryRunner.query(`DROP INDEX "IDX_conversation_admin_user_status"`);
    await queryRunner.query(`DROP INDEX "IDX_conversation_is_admin_conversation"`);
  }
}
