import { MigrationInterface, QueryRunner, Table, TableForeign<PERSON>ey } from 'typeorm';
import { AwardModule } from '../entities/award.entity';
import { ScheduleStatus } from '../entities/award-schedule.entity';

export class CreateAwardSchedule1747000000001 implements MigrationInterface {
  name = 'CreateAwardSchedule1747000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create award schedule table
    await queryRunner.createTable(
      new Table({
        name: 'award_schedule',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            isGenerated: true,
          },
          {
            name: 'module',
            type: 'enum',
            enum: Object.values(AwardModule),
          },
          {
            name: 'schedule_date',
            type: 'date',
          },
          {
            name: 'period_start_date',
            type: 'date',
          },
          {
            name: 'period_end_date',
            type: 'date',
          },
          {
            name: 'status',
            type: 'enum',
            enum: Object.values(ScheduleStatus),
            default: `'${ScheduleStatus.PENDING}'`,
          },
          {
            name: 'error_message',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'retry_count',
            type: 'int',
            default: 0,
          },
          {
            name: 'last_retry_date',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'processing_started_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'processing_completed_at',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'is_active',
            type: 'boolean',
            default: true,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            isNullable: true,
            onUpdate: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'created_by',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
          {
            name: 'updated_by',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('award_schedule', true);
  }
}
