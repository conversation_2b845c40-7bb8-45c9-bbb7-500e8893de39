import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TutorMatchingService } from './tutor-matching.service';
import { AdminTutorMatchingController } from './admin-tutor-matching.controller';
import { StudentTutorMatchingController } from './student-tutor-matching.controller';
import { TutorStudentMatchingController } from './tutor-student-matching.controller';
import { StudentTutorMapping } from '../../database/entities/student-tutor-mapping.entity';
import { User } from '../../database/entities/user.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { Conversation } from '../../database/entities/conversation.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { NotificationModule } from '../notification/notification.module';
import { ChatModule } from '../chat/chat.module';
import { CommonModule } from '../../common/common.module';

@Module({
  imports: [TypeOrmModule.forFeature([StudentTutorMapping, User, PlanFeature, DiaryEntry, Conversation, UserPlan]), NotificationModule, ChatModule, CommonModule],
  controllers: [AdminTutorMatchingController, StudentTutorMatchingController, TutorStudentMatchingController],
  providers: [TutorMatchingService],
  exports: [TutorMatchingService],
})
export class TutorMatchingModule {}
