import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsPositive, IsOptional, IsBoolean, IsEnum, IsIn, IsArray } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { PaginationDto } from '../../../common/models/pagination.dto';

/**
 * DTO for image analysis data structure
 */
export class ImageAnalysisDto {
  @ApiProperty({
    description: 'Objects detected in the image',
    example: ['forest', 'trees', 'path', 'mountains'],
    required: false,
  })
  @IsArray()
  @IsOptional()
  objects?: string[];

  @ApiProperty({
    description: 'Overall scene description',
    example: 'A mystical forest with a winding path leading to mountains',
    required: false,
  })
  @IsString()
  @IsOptional()
  scene?: string;

  @ApiProperty({
    description: 'Mood or atmosphere of the image',
    example: 'mysterious and adventurous',
    required: false,
  })
  @IsString()
  @IsOptional()
  mood?: string;

  @ApiProperty({
    description: 'Themes present in the image',
    example: ['adventure', 'nature', 'discovery', 'journey'],
    required: false,
  })
  @IsArray()
  @IsOptional()
  themes?: string[];

  @ApiProperty({
    description: 'Dominant colors in the image',
    example: ['green', 'brown', 'golden', 'blue'],
    required: false,
  })
  @IsArray()
  @IsOptional()
  colors?: string[];

  @ApiProperty({
    description: 'Setting or location depicted',
    example: 'enchanted forest',
    required: false,
  })
  @IsString()
  @IsOptional()
  setting?: string;

  @ApiProperty({
    description: 'Characters or beings visible in the image',
    example: ['traveler', 'fairy', 'woodland creature'],
    required: false,
  })
  @IsArray()
  @IsOptional()
  characters?: string[];

  @ApiProperty({
    description: 'Emotions evoked by the image',
    example: ['curiosity', 'wonder', 'excitement'],
    required: false,
  })
  @IsArray()
  @IsOptional()
  emotions?: string[];

  @ApiProperty({
    description: 'Detailed description of the image',
    example: 'A magical forest scene with tall ancient trees, a mysterious winding path, and soft golden light filtering through the canopy',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Keywords for relevance checking during story evaluation',
    example: ['forest', 'adventure', 'magical', 'journey', 'nature', 'mystery'],
    required: false,
  })
  @IsArray()
  @IsOptional()
  relevanceKeywords?: string[];
}

/**
 * DTO for querying story makers
 */
export class GetStoriesQueryDto extends PaginationDto {
  @ApiProperty({
    description: 'Search term for story title',
    example: 'Adventure',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Filter by active status',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isActive?: boolean;

  @ApiProperty({
    description: 'Field to sort by',
    example: 'createdAt',
    enum: ['title', 'createdAt'],
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['title', 'createdAt'])
  override sortBy?: string = 'createdAt';
}

/**
 * DTO for creating a new story maker
 */
export class CreateStoryMakerDto {
  @ApiProperty({
    description: 'The title of the story',
    example: 'Adventure in the Forest',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'The instruction text in rich text format',
    example: '<p>Create a story about a magical forest adventure</p>',
  })
  @IsString()
  @IsNotEmpty()
  instruction: string;

  // Note: picture is handled separately as a file upload

  @ApiProperty({
    description: 'The deadline in days for the story (optional)',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  deadline?: number;
}

/**
 * DTO for updating a story maker
 */
export class UpdateStoryMakerDto {
  @ApiProperty({
    description: 'The title of the story',
    example: 'Adventure in the Enchanted Forest',
    required: false,
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: 'The instruction text in rich text format',
    example: '<p>Create a story about a magical forest adventure with fairies</p>',
    required: false,
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  @ApiProperty({
    description: 'URL or path to the picture for the story',
    example: 'https://example.com/images/enchanted-forest.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  picture?: string;

  @ApiProperty({
    description: 'Whether the story is active and available to students',
    example: true,
    required: false,
  })
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    description: 'The deadline in days for the story (optional)',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  deadline?: number;
}

/**
 * DTO for story maker response
 */
export class StoryMakerResponseDto {
  @ApiProperty({
    description: 'The ID of the story',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the story',
    example: 'Adventure in the Forest',
  })
  title: string;

  @ApiProperty({
    description: 'The instruction text in rich text format',
    example: '<p>Create a story about a magical forest adventure</p>',
  })
  instruction: string;

  @ApiProperty({
    description: 'URL or path to the picture for the story',
    example: 'https://example.com/images/forest.jpg',
  })
  picture: string;

  @ApiProperty({
    description: 'Whether the story is active and available to students',
    example: true,
  })
  is_active: boolean;

  @ApiProperty({
    description: 'The deadline in days for the story (if set)',
    example: 2,
    required: false,
  })
  deadline?: number;

  @ApiProperty({
    description: 'When the story was created',
    example: '2023-01-01T00:00:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'When the story was last updated',
    example: '2023-01-02T00:00:00.000Z',
  })
  updated_at: Date;

  @ApiProperty({
    description: 'AI-generated analysis of the story prompt image',
    type: ImageAnalysisDto,
    required: false,
  })
  image_analysis?: ImageAnalysisDto;
}

/**
 * DTO for toggling story active status
 */
export class ToggleStoryStatusDto {
  @ApiProperty({
    description: 'Whether the story should be active',
    example: true,
  })
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  is_active: boolean;
}

/**
 * DTO for evaluation details in game response
 */
export class EvaluationDetailDto {
  @ApiProperty({
    description: 'The ID of the evaluation',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'AI-generated feedback on the submission',
    example: 'Great job on your creative descriptions! Your story shows excellent imagination.',
    required: false,
  })
  ai_feedback?: string;

  @ApiProperty({
    description: 'Creativity score (1-5)',
    example: 4,
  })
  creativity_score: number;

  @ApiProperty({
    description: 'Writing quality score (1-3)',
    example: 2,
  })
  sentence_power_score: number;

  @ApiProperty({
    description: 'Participation score (1-5)',
    example: 4,
  })
  participation_score: number;

  @ApiProperty({
    description: 'Accuracy score (1-3)',
    example: 3,
  })
  accuracy_score: number;

  @ApiProperty({
    description: 'Popularity score (1-5)',
    example: 2,
  })
  popularity_score: number;

  @ApiProperty({
    description: 'Relevance score (1-5) - how well the story relates to the image prompt',
    example: 4,
    required: false,
  })
  relevance_score?: number;

  @ApiProperty({
    description: 'Total score',
    example: 25,
  })
  total_score: number;

  @ApiProperty({
    description: 'When the submission was evaluated',
    example: '2023-01-02T00:00:00.000Z',
  })
  evaluated_at: Date;
}

/**
 * DTO for submission details in game response
 */
export class SubmissionDetailDto {
  @ApiProperty({
    description: 'The ID of the submission',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The content of the submission in rich text format',
    example: '<p>Once upon a time in a magical forest...</p>',
  })
  content: string;

  @ApiProperty({
    description: 'When the submission was submitted',
    example: '2023-01-01T00:00:00.000Z',
  })
  submitted_at: Date;

  @ApiProperty({
    description: 'Whether the submission has been evaluated',
    example: true,
  })
  is_evaluated: boolean;

  @ApiProperty({
    description: 'Word count of the submission',
    example: 125,
  })
  word_count: number;

  @ApiProperty({
    description: 'Character count of the submission',
    example: 650,
  })
  character_count: number;

  @ApiProperty({
    description: 'The evaluation details if the submission has been evaluated',
    type: EvaluationDetailDto,
    required: false,
  })
  evaluation?: EvaluationDetailDto;
}

/**
 * DTO for story maker game details (student view)
 */
export class StoryMakerGameDetailDto {
  @ApiProperty({
    description: 'The ID of the story',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the story',
    example: 'Adventure in the Forest',
  })
  title: string;

  @ApiProperty({
    description: 'The instruction text in rich text format',
    example: '<p>Create a story about a magical forest adventure</p>',
  })
  instruction: string;

  @ApiProperty({
    description: 'URL or path to the picture for the story',
    example: 'https://example.com/images/forest.jpg',
  })
  picture: string;

  @ApiProperty({
    description: 'The deadline in days for the story (if set)',
    example: 2,
    required: false,
  })
  deadline?: number;

  @ApiProperty({
    description: 'Whether the student has played this game',
    example: true,
  })
  is_played: boolean;

  @ApiProperty({
    description: 'The score the student received for this story (if played and evaluated)',
    example: 45,
    required: false,
  })
  participation_score?: number;

  @ApiProperty({
    description: 'The latest submission details (if played)',
    type: SubmissionDetailDto,
    required: false,
  })
  latest_submission?: SubmissionDetailDto;
}
