import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DiaryService } from './diary.service';
import { DiaryAwardPeriod } from '../../database/entities/diary-award.entity';

@Injectable()
export class DiaryAwardsScheduler {
  private readonly logger = new Logger(DiaryAwardsScheduler.name);

  constructor(private readonly diaryService: DiaryService) {}

  // DISABLED: Legacy scheduler - replaced by unified AwardScheduler
  // The modern AwardScheduler handles all award generation through
  // cron-based scheduling with proper error handling and retry logic

  // @Cron(CronExpression.EVERY_WEEK)
  async generateWeeklyAwards() {
    this.logger.warn('Legacy weekly award generation is disabled. Use AwardScheduler instead.');
    // await this.diaryService.generatePeriodAwards(DiaryAwardPeriod.WEEKLY);
  }

  // @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  async generateMonthlyAwards() {
    this.logger.warn('Legacy monthly award generation is disabled. Use AwardScheduleScheduler instead.');
    // await this.diaryService.generatePeriodAwards(DiaryAwardPeriod.MONTHLY);
  }
}
