# HEC Novel Module - Award Calculations

## Overview

The novel module has 6 awards across 3 categories, each with monthly and annual variants.

## Awards

### 1. Best Writer Award
### 2. Best Perfect Award
### 3. Best Performance Award

---

## 1. Best Writer Award

Recognizes students with exceptional writing quality and consistency in novel entries.

### Criteria
- `NOVEL_PERFORMANCE` (novel quality based on tutor evaluations)

### Calculation Formula

```typescript
// Quality Score (70% weight) - based on average novel scores
const qualityScore = Math.min(averageScore * 10, 100);

// Consistency Score (30% weight) - regular submission pattern
const consistencyScore = Math.min(submissionFrequency * 20, 100);

// Final Writer Score
const writerScore = (qualityScore * 0.7) + (consistencyScore * 0.3);
```

### Configuration
- **Monthly**: Min score 7, 2+ novels, 150 points
- **Annual**: Min score 8, 20+ novels, 500 points

### Example Calculation

**Student Profile (Monthly)**:
- 3 novel submissions
- Average score: 8.0 points
- Submission frequency: 3 per month

**Calculation**:
1. **Quality Score**: `min(8.0 * 10, 100) = 80`
2. **Consistency Score**: `min(3 * 20, 100) = 60`
3. **Final Score**: `(80 * 0.7) + (60 * 0.3) = 74`

**Result**: 74/100 - Good writer performance

---

## 2. Best Perfect Award

Recognizes students who excel in all aspects of novel writing - quality, completion, and word count.

### Criteria
- `NOVEL_PERFORMANCE` (comprehensive novel performance)

### Calculation Formula

```typescript
// Quality Score (50% weight) - based on average novel scores
const qualityScore = Math.min(averageScore * 10, 100);

// Completion Rate (30% weight) - percentage of available topics completed
const completionScore = Math.min(completionRate, 100);

// Word Count Achievement (20% weight) - meeting word count targets
const wordCountScore = Math.min((averageWordCount / 500) * 100, 100);

// Final Perfect Score
const perfectScore = (qualityScore * 0.5) + (completionScore * 0.3) + (wordCountScore * 0.2);
```

### Configuration
- **Monthly**: Min score 7, 3+ novels, 80%+ completion, 200 points
- **Annual**: Min score 8, 25+ novels, 75%+ completion, 750 points

### Example Calculation

**Student Profile (Monthly)**:
- 4 novel submissions
- Average score: 8.5 points
- Completion rate: 85%
- Average word count: 600 words

**Calculation**:
1. **Quality Score**: `min(8.5 * 10, 100) = 85`
2. **Completion Score**: `min(85, 100) = 85`
3. **Word Count Score**: `min((600/500) * 100, 100) = 100`
4. **Final Score**: `(85 * 0.5) + (85 * 0.3) + (100 * 0.2) = 88`

**Result**: 88/100 - Excellent all-around performance

---

## 3. Best Performance Award

Recognizes students with outstanding overall performance in the novel module.

### Criteria
- `NOVEL_PERFORMANCE` (comprehensive performance metrics)

### Calculation Formula

```typescript
// Submission Frequency (40% weight) - regular and timely submissions
const frequencyScore = Math.min(submissionFrequency * 25, 100);

// Quality Improvement (30% weight) - score improvement over time
const improvementScore = calculateImprovementScore(entries);

// Engagement Level (30% weight) - word count and participation
const engagementScore = Math.min((averageWordCount / 300) * 100, 100);

// Final Performance Score
const performanceScore = (frequencyScore * 0.4) + (improvementScore * 0.3) + (engagementScore * 0.3);
```

### Configuration
- **Monthly**: Min score 6, 4+ novels, 1+ frequency, 150 points
- **Annual**: Min score 7, 30+ novels, 0.5+ frequency, 500 points

### Example Calculation

**Student Profile (Monthly)**:
- 5 novel submissions
- Submission frequency: 5 per month
- Average word count: 450 words
- Score improvement: 15% increase

**Calculation**:
1. **Frequency Score**: `min(5 * 25, 100) = 100`
2. **Improvement Score**: `50 + (15 * 5) = 125, capped at 100`
3. **Engagement Score**: `min((450/300) * 100, 100) = 100`
4. **Final Score**: `(100 * 0.4) + (100 * 0.3) + (100 * 0.3) = 100`

**Result**: 100/100 - Perfect performance

---

## Award Filtering

### Client Requirements Only
The system only processes awards that match the exact client requirements:
- **Best Writer Award**
- **Best Perfect Award**
- **Best Performance Award**

Any additional awards created outside the seeder are automatically filtered out during calculation to ensure only client-specified awards are considered.

## Scheduler

### Automated Generation (ACTIVE SYSTEM)

The **AwardScheduler** is the award scheduling system:

- **Monthly Awards**: 1st of each month at 02:00 UTC (All modules including Novel)
- **Quarterly Awards**: 1st of each quarter at 01:00 UTC (All modules including Novel)
- **Annual Awards**: January 1st at 03:00 UTC (All modules including Novel)

*Note: Weekly awards are diary-specific only.*

### Implementation
```typescript
@Injectable()
export class AwardScheduler {
  // Monthly awards: 1st of each month at 2:00 AM UTC (All modules)
  @Cron('0 2 1 * *')
  async generateMonthlyAwards() {
    const lastMonth = addMonthsUTC(getCurrentUTCDate(), -1);
    const startDate = getStartOfMonthUTC(lastMonth);
    const endDate = getEndOfMonthUTC(lastMonth);
    await Promise.all([
      this.diaryAwardService.generateAwardsForRange(startDate, endDate),
      this.essayAwardService.generateAwardsForRange(startDate, endDate),
      this.novelAwardService.generateAwardsForRange(startDate, endDate)
    ]);
  }

  // Quarterly awards: 1st of each quarter at 1:00 AM UTC (All modules)
  @Cron('0 1 1 1,4,7,10 *')
  async generateQuarterlyAwards() {
    // ... quarterly logic for all modules including novel
    await Promise.all([
      this.diaryAwardService.generateAwardsForRange(startDate, endDate),
      this.essayAwardService.generateAwardsForRange(startDate, endDate),
      this.novelAwardService.generateAwardsForRange(startDate, endDate)
    ]);
  }

  // Annual awards: January 1st at 3:00 AM UTC (All modules)
  @Cron('0 3 1 1 *')
  async generateAnnualAwards() {
    const lastYear = getCurrentUTCDate().getUTCFullYear() - 1;
    const startDate = new Date(Date.UTC(lastYear, 0, 1));
    const endDate = new Date(Date.UTC(lastYear, 11, 31, 23, 59, 59, 999));
    await Promise.all([
      this.diaryAwardService.generateAwardsForRange(startDate, endDate),
      this.essayAwardService.generateAwardsForRange(startDate, endDate),
      this.novelAwardService.generateAwardsForRange(startDate, endDate)
    ]);
  }
}
```

### Enhanced Status Monitoring
```bash
# Get comprehensive scheduler status
GET /api/award-scheduler/status
```

### Manual Triggers (Enhanced)
```bash
# Monthly awards (all modules including novel)
POST /api/award-scheduler/trigger/monthly?year=2023&month=12

# Quarterly awards (all modules including novel)
POST /api/award-scheduler/trigger/quarterly?year=2024&quarter=1

# Annual awards (all modules including novel)
POST /api/award-scheduler/trigger/annual?year=2023
```

### Legacy System (DEPRECATED)

⚠️ **Note**: The legacy award scheduling system has been removed. Use the AwardScheduler system above.
