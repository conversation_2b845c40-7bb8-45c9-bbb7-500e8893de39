import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { ShopCategory } from './shop-category.entity';

/**
 * Type of shop item
 * @enum {string}
 */
export enum ShopItemType {
  /** Free item */
  FREE = 'free',
  /** In-app purchase */
  IN_APP_PURCHASE = 'in_app_purchase',
}

@Entity()
export class ShopItem extends AuditableBaseEntity {
  @Column({ name: 'item_number' })
  itemNumber: string;

  @Column({ name: 'title' })
  title: string;

  @Column({ name: 'description', type: 'text' })
  description: string;

  @Column({ name: 'category_id' })
  categoryId: string;

  @ManyToOne(() => ShopCategory, (category) => category.shopItems)
  @JoinColumn({ name: 'category_id' })
  category: ShopCategory;

  @Column({
    name: 'type',
    type: 'enum',
    enum: ShopItemType,
    default: ShopItemType.IN_APP_PURCHASE,
  })
  type: ShopItemType;

  @Column({ name: 'price', type: 'decimal', precision: 10, scale: 2, default: 0 })
  price: number;

  @Column({ name: 'is_purchasable_in_rewardpoint', default: false })
  isPurchasableInRewardpoint: boolean;

  @Column({ name: 'file_path', nullable: true })
  filePath: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'is_featured', default: false })
  isFeatured: boolean;

  @Column({ name: 'view_count', default: 0 })
  viewCount: number;

  @Column({ name: 'purchase_count', default: 0 })
  purchaseCount: number;

  @Column({ name: 'promotion_id', nullable: true })
  promotionId: string;

  @Column({ name: 'is_promotion_active', default: true })
  isPromotionActive: boolean;

  @Column({ name: 'discounted_price', type: 'decimal', precision: 10, scale: 2, nullable: true })
  discountedPrice: number;

  @Column({ name: 'metadata', type: 'text', nullable: true })
  metadata: string;

  /**
   * Determine if the item is currently on sale (has a valid promotion and discounted price)
   * @returns {boolean} Whether the item is on sale
   */
  isOnSale(): boolean {
    return this.promotionId != null && this.isPromotionActive && this.discountedPrice != null;
  }

  /**
   * Get the final price of the item, accounting for discounts if applicable
   * @returns {number} The final price
   */
  getFinalPrice(): number {
    if (this.isOnSale()) {
      return Number(this.discountedPrice);
    }
    return Number(this.price);
  }

  /**
   * Calculate the discount percentage if the item is on sale
   * @returns {number} The discount percentage
   */
  getDiscountPercentage(): number {
    if (this.isOnSale()) {
      const originalPrice = Number(this.price);
      const discountedPrice = Number(this.discountedPrice);
      return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100);
    }
    return 0;
  }
}
