import { MigrationInterface, QueryRunner } from "typeorm";

export class EssayEntityNullable1752572216327 implements MigrationInterface {
    name = 'EssayEntityNullable1752572216327'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ALTER COLUMN "submission_id" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ALTER COLUMN "submission_id" SET NOT NULL`);
    }

}
