import { MigrationInterface, QueryRunner } from "typeorm";

export class EssaySubmissionHistoryVersionNumber1752465834351 implements MigrationInterface {
    name = 'EssaySubmissionHistoryVersionNumber1752465834351'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD "version_number" integer NOT NULL DEFAULT '1'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP COLUMN "version_number"`);
    }

}
