import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixDiarySettingsInconsistency1750576722496 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, let's check and log the current state of the problematic entry
    console.log('Checking current state of diary entry settings...');
    
    const problematicEntry = await queryRunner.query(`
      SELECT 
        de.id as entry_id,
        des.level,
        des.word_limit,
        des.min_word_limit,
        des.title as settings_title,
        des.settings_template_id,
        dst.level as template_level,
        dst.word_limit as template_word_limit,
        dst.title as template_title
      FROM diary_entry de
      JOIN diary_entry_settings des ON de.id = des.diary_entry_id
      LEFT JOIN diary_settings_template dst ON des.settings_template_id = dst.id
      WHERE de.id = 'c8948293-7a83-443f-b356-37e9ba68db2a'
    `);
    
    if (problematicEntry.length > 0) {
      console.log('Found problematic entry:', problematicEntry[0]);
    }

    // Find all entries where settings don't match their templates
    const inconsistentEntries = await queryRunner.query(`
      SELECT 
        de.id as entry_id,
        des.id as settings_id,
        des.level as current_level,
        des.word_limit as current_word_limit,
        des.min_word_limit as current_min_word_limit,
        des.settings_template_id,
        dst.level as template_level,
        dst.word_limit as template_word_limit,
        dst.min_word_limit as template_min_word_limit,
        dst.title as template_title
      FROM diary_entry de
      JOIN diary_entry_settings des ON de.id = des.diary_entry_id
      JOIN diary_settings_template dst ON des.settings_template_id = dst.id
      WHERE des.level != dst.level 
         OR des.word_limit != dst.word_limit 
         OR des.min_word_limit != dst.min_word_limit
    `);

    console.log(`Found ${inconsistentEntries.length} entries with settings inconsistencies`);

    // Fix each inconsistent entry by updating settings to match their template
    for (const entry of inconsistentEntries) {
      console.log(`Fixing entry ${entry.entry_id}: updating level from ${entry.current_level} to ${entry.template_level}, word_limit from ${entry.current_word_limit} to ${entry.template_word_limit}`);
      
      await queryRunner.query(`
        UPDATE diary_entry_settings 
        SET 
          level = $1,
          word_limit = $2,
          min_word_limit = $3,
          title = $4,
          updated_at = NOW()
        WHERE id = $5
      `, [
        entry.template_level,
        entry.template_word_limit,
        entry.template_min_word_limit,
        entry.template_title,
        entry.settings_id
      ]);
    }

    // Verify the fix for the specific problematic entry
    const fixedEntry = await queryRunner.query(`
      SELECT 
        de.id as entry_id,
        des.level,
        des.word_limit,
        des.min_word_limit,
        des.title as settings_title
      FROM diary_entry de
      JOIN diary_entry_settings des ON de.id = des.diary_entry_id
      WHERE de.id = 'c8948293-7a83-443f-b356-37e9ba68db2a'
    `);
    
    if (fixedEntry.length > 0) {
      console.log('Entry after fix:', fixedEntry[0]);
    }

    console.log('Diary settings inconsistency fix completed');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // This migration fixes data inconsistencies, so we don't provide a rollback
    // as it would reintroduce the inconsistent state
    console.log('This migration fixes data inconsistencies and cannot be rolled back');
  }
}
