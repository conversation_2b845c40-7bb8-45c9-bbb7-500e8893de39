# KCP Payment Integration Flow

## Overview

Simple guide for integrating KCP payment gateway with your frontend application.

### Payment Flow Summary
```
1. User adds items to cart
2. User clicks checkout
3. Backend creates payment transaction
4. Frontend loads KCP payment page
5. User completes payment via KCP
6. Payment verified and completed
```

### Supported Payment Methods
- **Credit Cards** - Visa, MasterCard, domestic cards
- **Bank Transfer** - Direct bank transfers
- **Virtual Account** - Virtual account payments

## Authentication

### Login and Get JWT Token

**Endpoint:** `POST /auth/login`

**Request:**
```json
{
  "userId": "<EMAIL>",
  "password": "password123",
  "selectedRole": "student"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": { "id": "user-id", "email": "<EMAIL>" }
  }
}
```

**Usage:**
- Store the `access_token` in localStorage
- Include in all API calls: `Authorization: Bearer {token}`

## Shop Item Purchase

### 1. Browse Items

**Endpoint:** `GET /shop/items/available`

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "item-id",
      "title": "Orange Doggo",
      "price": 9.99,
      "imageUrl": "http://...",
      "rewardPoints": 999
    }
  ]
}
```

### 2. Add to Cart

**Endpoint:** `POST /shop/cart/add`

**Request:**
```json
{
  "itemId": "item-id",
  "quantity": 2
}
```

### 3. Checkout

**Endpoint:** `POST /shop/cart/checkout`

**Request:**
```json
{
  "paymentMethod": "kcp_card",
  "returnUrl": "https://yourapp.com/payment/success",
  "cancelUrl": "https://yourapp.com/payment/cancel"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "orderId": "ORDER-123",
    "paymentTransactionId": "TXN-456",
    "paymentUrl": "http://frontend.com/payment/kcp?site_cd=T0000&tno=TXN-456&...",
    "totalAmount": 19.98
  }
}
```

**Next Step:** Redirect user to `paymentUrl`

## Plan Subscription

### 1. Browse Plans

**Endpoint:** `GET /plans`

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "plan-id",
      "name": "Premium Plan",
      "price": 29.99,
      "duration": "monthly",
      "features": ["Feature 1", "Feature 2"]
    }
  ]
}
```

### 2. Subscribe to Plan

**Endpoint:** `POST /plans/subscribe`

**Request:**
```json
{
  "planId": "plan-id",
  "paymentMethod": "kcp_card",
  "returnUrl": "https://yourapp.com/payment/success",
  "cancelUrl": "https://yourapp.com/payment/cancel"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "subscriptionId": "SUB-123",
    "paymentTransactionId": "TXN-456",
    "paymentUrl": "http://frontend.com/payment/kcp?site_cd=T0000&tno=TXN-456&...",
    "amount": 29.99
  }
}
```

**Next Step:** Redirect user to `paymentUrl`

## KCP Payment Integration

### Frontend Payment Page

When user is redirected to `paymentUrl`, your frontend needs to:

1. **Create Payment Page** at `/payment/kcp` route
2. **Extract URL Parameters** (site_cd, tno, ordr_idxx, etc.)
3. **Load KCP SDK** (`https://testspay.kcp.co.kr/plugin/kcp_spay_hub.js`)
4. **Display Payment Form** with KCP integration
5. **Handle Payment Result** via callback function

### Payment Flow

```
1. User clicks "Pay Now" → KCP opens payment window
2. User completes payment → KCP calls callback function
3. Frontend verifies payment → Calls backend verification API
4. Backend updates status → User sees success/failure page
```

### Required Implementation

**Frontend must implement:**
- `/payment/kcp` route
- KCP SDK integration
- `m_Completepayment()` callback function
- Payment verification API call

**See detailed implementation guide:** `KCP_FRONTEND_INTEGRATION_GUIDE.md`

## Payment Verification

### After Payment Completion

When KCP payment is completed, frontend must verify the payment:

**Endpoint:** `POST /payment/kcp/verify`

**Request:**
```json
{
  "tno": "TXN-1750148813331",
  "ordr_idxx": "ORDER-123",
  "res_cd": "0000",
  "res_msg": "SUCCESS",
  "enc_info": "",
  "enc_data": "",
  "tran_cd": "00100000"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "transactionId": "uuid",
    "status": "completed",
    "amount": 19.98,
    "currency": "KRW"
  }
}
```

### Payment Status Check

**Endpoint:** `GET /payment/status/{transactionId}`

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "completed",
    "amount": 19.98,
    "completedAt": "2024-12-17T08:27:37.531Z"
  }
}
```

**Status Values:**
- `pending` - Payment initiated
- `completed` - Payment successful
- `failed` - Payment failed
- `cancelled` - Payment cancelled

## Error Handling

### Common HTTP Status Codes

- **400** - Validation error (fix request data)
- **401** - Authentication failed (login again)
- **403** - Access denied (insufficient permissions)
- **404** - Resource not found
- **500** - Server error (try again later)

### Payment Error Scenarios

- **Payment Failed** - Insufficient funds, card declined
- **Payment Expired** - User took too long to pay
- **Network Error** - Connection issues
- **KCP Error** - Payment gateway issues

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "PAYMENT_FAILED",
    "message": "Payment processing failed",
    "details": "Insufficient funds"
  }
}
```

### Error Handling Best Practices

1. **Show user-friendly messages** (not technical errors)
2. **Provide retry options** for failed operations
3. **Log errors** for debugging (without sensitive data)
4. **Redirect to login** on 401 errors
5. **Offer alternative payment methods** on payment failures

## Quick Setup Guide

### 1. API Configuration

**Base URL:** `http://**************:3010`

**Authentication:** Include JWT token in all requests:
```javascript
headers: {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
}
```

### 2. Required Frontend Implementation

**Must implement:**
- `/payment/kcp` route for KCP payment page
- KCP SDK integration (`https://testspay.kcp.co.kr/plugin/kcp_spay_hub.js`)
- `m_Completepayment()` callback function
- Payment verification API call

**See complete implementation guide:** `KCP_FRONTEND_INTEGRATION_GUIDE.md`

### 3. Testing

**Test with Swagger:** `http://**************:3010/api-docs`

1. Login to get JWT token
2. Add items to cart
3. Call checkout API
4. Test payment URL (should point to your frontend)
5. Implement frontend payment page
6. Test complete payment flow

---

**Backend Status:** ✅ Complete and ready
**Frontend Status:** ⏳ Needs implementation using provided guides