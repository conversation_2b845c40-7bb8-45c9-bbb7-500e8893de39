import { ApiProperty } from '@nestjs/swagger';
import { DiaryEntryResponseDto, DiaryFeedbackResponseDto, PendingReviewEntryDto } from '../../database/models/diary.dto';

/**
 * Base API response wrapper
 */
export class BaseApiResponse {
  @ApiProperty({ example: true, description: 'Indicates if the request was successful' })
  success: boolean;

  @ApiProperty({ description: 'Response message', example: 'Operation completed successfully' })
  message: string;

  @ApiProperty({ description: 'Timestamp of the response', example: '2023-07-25T12:34:56.789Z' })
  timestamp: string;
}

/**
 * API response for pending review entries list
 */
export class PendingReviewEntriesResponse extends BaseApiResponse {
  @ApiProperty({
    description: 'List of diary entries pending review',
    type: [PendingReviewEntryDto],
  })
  data: PendingReviewEntryDto[];
}

/**
 * API response for a diary entry
 */
export class TutorDiaryEntryResponse extends BaseApiResponse {
  @ApiProperty({
    description: 'Diary entry details',
    type: DiaryEntryResponseDto,
  })
  data: DiaryEntryResponseDto;
}

/**
 * API response for diary feedback
 */
export class DiaryFeedbackResponse extends BaseApiResponse {
  @ApiProperty({
    description: 'Diary feedback details',
    type: DiaryFeedbackResponseDto,
  })
  data: DiaryFeedbackResponseDto;
}

/**
 * Error response for validation errors
 */
export class ValidationErrorResponse extends BaseApiResponse {
  @ApiProperty({ example: false, description: 'Indicates that the request failed' })
  success: boolean;

  @ApiProperty({
    example: {
      feedback: ['feedback must not be empty'],
      rating: ['rating must be between 1 and 5'],
    },
    description: 'Validation errors by field',
  })
  error: Record<string, string[]>;
}

/**
 * Error response for general errors
 */
export class GeneralErrorResponse extends BaseApiResponse {
  @ApiProperty({ example: false, description: 'Indicates that the request failed' })
  success: boolean;

  @ApiProperty({ example: 'An error occurred', description: 'Error message' })
  message: string;

  @ApiProperty({ example: 'Internal server error', description: 'Error details', required: false })
  error?: string;
}
