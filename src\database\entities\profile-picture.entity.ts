import { <PERSON><PERSON><PERSON>, Column, OneTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

@Entity('profile_pictures')
export class ProfilePicture extends AuditableBaseEntity {
  // id is inherited from AuditableBaseEntity

  @Column({ name: 'user_id', nullable: true })
  userId: string;

  @Column({ name: 'file_path', nullable: false })
  filePath: string;

  @Column({ name: 'file_name', nullable: false })
  fileName: string;

  @Column({ name: 'mime_type', nullable: false })
  mimeType: string;

  @Column({ name: 'file_size', nullable: false })
  fileSize: number;

  // createdAt and updatedAt are inherited from AuditableBaseEntity

  @OneToOne(() => User, (user) => user.profilePictureEntity, { nullable: true })
  @JoinColumn({ name: 'user_id' })
  user: User;
}
