import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { CategoryService } from './category.service';
import { CreateCategoryDto, UpdateCategoryDto, CategoryResponseDto } from '../../database/models/category.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithArrayType, ApiOkResponseWithPagedListType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { ApiOkResponseWithEmptyData } from 'src/common/decorators/api-empty-response.decorator';

@ApiTags('Categories Management')
@Controller('categories')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, AdminGuard)
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new category' })
  @ApiOkResponseWithType(CategoryResponseDto, 'Category created successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(409, 'Category name already exists')
  async createCategory(@Body() createDto: CreateCategoryDto): Promise<ApiResponse<CategoryResponseDto>> {
    const category = await this.categoryService.createCategory(createDto);
    return ApiResponse.success(category, 'Category created successfully');
  }

  @Get()
  @ApiOperation({ summary: 'Get all categories with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiOkResponseWithPagedListType(CategoryResponseDto, 'Categories retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  async getCategories(@Query() params: any): Promise<ApiResponse<PagedListDto<CategoryResponseDto>>> {
    const paginationDto: PaginationDto = {
      page: params.page ? parseInt(params.page) : 1,
      limit: params.limit ? parseInt(params.limit) : 10,
    };

    const categories = await this.categoryService.getCategories(paginationDto);
    return ApiResponse.success(categories, 'Categories retrieved successfully');
  }

  @Get('/all')
  @ApiOperation({ summary: 'Get all active categories without pagination' })
  @ApiOkResponseWithArrayType(CategoryResponseDto, 'All active categories retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  async getAllActiveCategories(): Promise<ApiResponse<CategoryResponseDto[]>> {
    const categories = await this.categoryService.getAllActiveCategories();
    return ApiResponse.success(categories, 'All active categories retrieved successfully');
  }

  @Get('/:id')
  @ApiOperation({ summary: 'Get a specific category by ID' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiOkResponseWithType(CategoryResponseDto, 'Category retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Category not found')
  async getCategoryById(@Param('id') id: string): Promise<ApiResponse<CategoryResponseDto>> {
    const category = await this.categoryService.getCategoryById(id);
    return ApiResponse.success(category, 'Category retrieved successfully');
  }

  @Put('/:id')
  @ApiOperation({ summary: 'Update a category' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiOkResponseWithType(CategoryResponseDto, 'Category updated successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Category not found')
  @ApiErrorResponse(409, 'Category name already exists')
  async updateCategory(
    @Param('id') id: string,
    @Body() updateDto: UpdateCategoryDto,
  ): Promise<ApiResponse<CategoryResponseDto>> {
    const category = await this.categoryService.updateCategory(id, updateDto);
    return ApiResponse.success(category, 'Category updated successfully');
  }

  @Delete('/:id')
  @ApiOperation({ summary: 'Delete a category (soft delete)' })
  @ApiParam({ name: 'id', description: 'Category ID' })
  @ApiOkResponseWithEmptyData('Category deleted successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Category not found')
  async deleteCategory(@Param('id') id: string): Promise<ApiResponse<null>> {
    await this.categoryService.deleteCategory(id);
    return ApiResponse.success(null, 'Category deleted successfully');
  }
}
