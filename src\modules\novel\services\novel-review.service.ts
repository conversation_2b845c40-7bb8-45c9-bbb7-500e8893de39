import { Injectable, NotFoundException, BadRequestException, ForbiddenException, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { NovelEntry, NovelEntryStatus } from '../../../database/entities/novel-entry.entity';
import { NovelEntryHistory } from '../../../database/entities/novel-entry-history.entity';
import { NovelFeedback } from '../../../database/entities/novel-feedback.entity';
import { NovelCorrection } from '../../../database/entities/novel-correction.entity';
import { StudentTutorMapping, MappingStatus } from '../../../database/entities/student-tutor-mapping.entity';
import { User } from '../../../database/entities/user.entity';
import {
  CreateNovelFeedbackDto,
  CreateNovelCorrectionDto,
  UpdateNovelCorrectionDto,
  CreateNovelReviewDto,
  NovelFeedbackResponseDto,
  NovelCorrectionResponseDto,
  NovelEntryResponseDto,
} from '../../../database/models/novel.dto';
import { DiarySkinResponseDto } from '../../../database/models/diary.dto';
import { NotificationHelperService } from '../../notification/notification-helper.service';
import { AsyncNotificationHelperService } from '../../notification/async-notification-helper.service';
import { NotificationType } from '../../../database/entities/notification.entity';
import { FileRegistryService } from '../../../common/services/file-registry.service';
import { FileEntityType } from '../../../common/enums/file-entity-type.enum';
import { DiarySkinService } from '../../diary/diary-skin.service';
import { TutorMatchingService } from '../../tutor-matching/tutor-matching.service';
import { getCurrentUTCDate } from 'src/common/utils/date-utils';

@Injectable()
export class NovelReviewService {
  private readonly logger = new Logger(NovelReviewService.name);

  constructor(
    @InjectRepository(NovelEntry)
    private readonly novelEntryRepository: Repository<NovelEntry>,
    @InjectRepository(NovelEntryHistory)
    private readonly novelEntryHistoryRepository: Repository<NovelEntryHistory>,
    @InjectRepository(NovelFeedback)
    private readonly novelFeedbackRepository: Repository<NovelFeedback>,
    @InjectRepository(NovelCorrection)
    private readonly novelCorrectionRepository: Repository<NovelCorrection>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => NotificationHelperService))
    private readonly notificationHelper: NotificationHelperService,
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
    private readonly fileRegistryService: FileRegistryService,
    private readonly diarySkinService: DiarySkinService,
    private readonly tutorMatchingService: TutorMatchingService,
  ) {}

  async getTutorEntries(tutorId: string): Promise<NovelEntryResponseDto[]> {
    // Get students assigned to this tutor
    const tutorMappings = await this.studentTutorMappingRepository.find({
      where: { tutorId, status: MappingStatus.ACTIVE },
      select: ['studentId'],
    });

    const studentIds = tutorMappings.map((mapping) => mapping.studentId);

    if (studentIds.length === 0) {
      return [];
    }

    const entries = await this.novelEntryRepository
      .createQueryBuilder('entry')
      .leftJoinAndSelect('entry.topic', 'topic')
      .leftJoinAndSelect('entry.student', 'student')
      .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
      .leftJoinAndSelect('entry.correction', 'correction')
      .leftJoinAndSelect('entry.skin', 'skin')
      .leftJoinAndSelect('entry.originalReviewedVersion', 'originalReviewedVersion')
      .where('entry.studentId IN (:...studentIds)', { studentIds })
      .andWhere('entry.status NOT IN (:...excludedStatuses)', {
        excludedStatuses: [NovelEntryStatus.NEW, NovelEntryStatus.CONFIRMED],
      })
      .andWhere('(entry.resubmissionType IS NULL OR entry.resubmissionType != :afterConfirmation)', {
        afterConfirmation: 'after_confirmation',
      })
      .orderBy('entry.submittedAt', 'DESC')
      .getMany();

    return await Promise.all(entries.map((entry) => this.mapEntryToResponseDto(entry)));
  }

  async getEntryForReview(tutorId: string, entryId: string): Promise<NovelEntryResponseDto> {
    const entry = await this.novelEntryRepository.findOne({
      where: { id: entryId },
      relations: ['topic', 'student', 'feedbacks', 'correction', 'skin', 'originalReviewedVersion'],
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    // Verify tutor has access to this student
    const tutorMapping = await this.studentTutorMappingRepository.findOne({
      where: {
        tutorId,
        studentId: entry.studentId,
        status: MappingStatus.ACTIVE,
      },
    });

    if (!tutorMapping) {
      throw new BadRequestException("You do not have access to this student's entry");
    }

    return await this.mapEntryToResponseDto(entry);
  }

  async submitFeedback(tutorId: string, entryId: string, createFeedbackDto: CreateNovelFeedbackDto): Promise<NovelFeedbackResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Optimized entry loading with tutor access validation
      const entry = await this.novelEntryRepository
        .createQueryBuilder('entry')
        .leftJoinAndSelect('entry.student', 'student')
        .innerJoin('student_tutor_mapping', 'stm', 'stm.studentId = entry.studentId AND stm.tutorId = :tutorId AND stm.status = :status')
        .where('entry.id = :entryId', { entryId })
        .setParameters({ tutorId, status: MappingStatus.ACTIVE })
        .getOne();

      if (!entry) {
        throw new ForbiddenException('Entry not found or you do not have access to provide feedback on this entry');
      }

      // Access validation is already handled in the query above

      if (entry.status === NovelEntryStatus.NEW) {
        throw new BadRequestException('Cannot provide feedback on entry that has not been submitted');
      }

      const feedback = this.novelFeedbackRepository.create({
        entryId,
        tutorId,
        feedback: createFeedbackDto.feedback,
      });

      const savedFeedback = await queryRunner.manager.save(feedback);
      await queryRunner.commitTransaction();

      // Send notification to student
      try {
        await this.sendFeedbackNotification(entry, savedFeedback);
      } catch (notificationError) {
        this.logger.error(`Failed to send notification: ${notificationError.message}`, notificationError.stack);
      }

      this.logger.log(`Tutor ${tutorId} submitted feedback for entry ${entryId}`);
      return this.mapFeedbackToResponseDto(savedFeedback);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error submitting feedback: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async submitCorrection(tutorId: string, entryId: string, createCorrectionDto: CreateNovelCorrectionDto): Promise<NovelCorrectionResponseDto> {
    this.logger.log(`submitCorrection called with tutorId: ${tutorId}, entryId: ${entryId}`);
    this.logger.log(`createCorrectionDto: ${JSON.stringify(createCorrectionDto)}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Optimized entry loading with tutor access validation and existing correction check
      const entry = await queryRunner.manager
        .createQueryBuilder(NovelEntry, 'entry')
        .leftJoinAndSelect('entry.student', 'student')
        .leftJoinAndSelect('entry.correction', 'correction')
        .innerJoin('student_tutor_mapping', 'stm', 'stm.studentId = entry.studentId AND stm.tutorId = :tutorId AND stm.status = :status')
        .where('entry.id = :entryId', { entryId })
        .setParameters({ tutorId, status: MappingStatus.ACTIVE })
        .getOne();

      this.logger.log(`Found entry: ${entry ? entry.id : 'null'}, student: ${entry?.studentId}, has correction: ${!!entry?.correction}`);

      if (!entry) {
        throw new ForbiddenException('Entry not found or you do not have access to provide corrections on this entry');
      }

      // Access validation is already handled in the query above

      if (entry.status === NovelEntryStatus.NEW) {
        throw new BadRequestException('Cannot provide correction on entry that has not been submitted');
      }

      // Check if correction already exists (already loaded in the query above)
      if (entry.correction) {
        throw new BadRequestException('Correction already exists for this entry. Use PUT to update the correction text.');
      }

      // Create new correction without score
      this.logger.log(`Creating correction for entryId: ${entryId}, tutorId: ${tutorId}`);

      // Validate parameters before creating entity
      if (!entryId || typeof entryId !== 'string' || entryId.trim() === '') {
        throw new BadRequestException(`Invalid entryId: ${entryId}`);
      }
      if (!tutorId || typeof tutorId !== 'string' || tutorId.trim() === '') {
        throw new BadRequestException(`Invalid tutorId: ${tutorId}`);
      }

      // Create correction using query runner to ensure transaction consistency
      const correctionData = {
        entryId: entryId,
        tutorId: tutorId,
        correction: createCorrectionDto.correction,
        score: null,
      };

      this.logger.log(`Creating correction with data: ${JSON.stringify(correctionData)}`);

      const correctionRepository = queryRunner.manager.getRepository(NovelCorrection);
      const correction = correctionRepository.create(correctionData);

      this.logger.log(`Created correction entity - entryId: ${correction.entryId}, tutorId: ${correction.tutorId}`);

      const savedCorrection = await queryRunner.manager.save(NovelCorrection, correction);

      this.logger.log(`After save - savedCorrection.entryId: ${savedCorrection.entryId}, savedCorrection.id: ${savedCorrection.id}`);

      // Set original reviewed version if not already set
      await this.setOriginalReviewedVersion(entry, queryRunner);

      await queryRunner.commitTransaction();

      // Update entry status in a separate transaction - PRESERVE resubmission tracking fields
      const updateQueryRunner = this.dataSource.createQueryRunner();
      await updateQueryRunner.connect();
      await updateQueryRunner.startTransaction();

      try {
        // Get the full entry to preserve all fields
        const entryToUpdate = await updateQueryRunner.manager.findOne(NovelEntry, { where: { id: entryId } });
        if (entryToUpdate) {
          // Store resubmission tracking fields before update to preserve them
          const resubmissionType = entryToUpdate.resubmissionType;
          const isResubmission = entryToUpdate.isResubmission;
          const previousReviewCount = entryToUpdate.previousReviewCount;
          const previousConfirmationCount = entryToUpdate.previousConfirmationCount;
          const submittedVersionCount = entryToUpdate.submittedVersionCount;

          // Update status and preserve resubmission fields
          entryToUpdate.status = NovelEntryStatus.REVIEWED;
          entryToUpdate.resubmissionType = resubmissionType;
          entryToUpdate.isResubmission = isResubmission;
          entryToUpdate.previousReviewCount = previousReviewCount;
          entryToUpdate.previousConfirmationCount = previousConfirmationCount;
          entryToUpdate.submittedVersionCount = submittedVersionCount;

          await updateQueryRunner.manager.save(entryToUpdate);
        }
        await updateQueryRunner.commitTransaction();
      } catch (updateError) {
        await updateQueryRunner.rollbackTransaction();
        this.logger.error(`Error updating entry status: ${updateError.message}`, updateError.stack);
      } finally {
        await updateQueryRunner.release();
      }

      this.logger.log(`Tutor ${tutorId} submitted correction for entry ${entryId}`);
      return this.mapCorrectionToResponseDto(savedCorrection);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error submitting correction: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async updateCorrection(tutorId: string, entryId: string, updateCorrectionDto: UpdateNovelCorrectionDto): Promise<NovelCorrectionResponseDto> {
    const entry = await this.novelEntryRepository.findOne({
      where: { id: entryId },
      relations: ['correction'],
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    if (!entry.correction) {
      throw new NotFoundException('No correction found for this entry');
    }

    // Check if correction has already been scored - if so, prevent updates
    if (entry.correction.score !== null) {
      throw new BadRequestException('Cannot update correction after it has been scored. Correction and score can only be given once per entry.');
    }

    // Verify tutor has access and owns the correction
    const tutorMapping = await this.studentTutorMappingRepository.findOne({
      where: {
        tutorId,
        studentId: entry.studentId,
        status: MappingStatus.ACTIVE,
      },
    });

    if (!tutorMapping || entry.correction.tutorId !== tutorId) {
      throw new BadRequestException('You do not have access to update this correction');
    }

    // Only update correction text (only allowed before scoring)
    entry.correction.correction = updateCorrectionDto.correction;
    const updatedCorrection = await this.novelCorrectionRepository.save(entry.correction);

    this.logger.log(`Tutor ${tutorId} updated correction for entry ${entryId}`);
    return this.mapCorrectionToResponseDto(updatedCorrection);
  }

  async submitReview(tutorId: string, entryId: string, createReviewDto: CreateNovelReviewDto): Promise<NovelCorrectionResponseDto> {
    // Validate input parameters
    if (!entryId) {
      throw new BadRequestException('Entry ID is required');
    }
    if (!tutorId) {
      throw new BadRequestException('Tutor ID is required');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const entry = await queryRunner.manager.findOne(NovelEntry, {
        where: { id: entryId },
        relations: ['student'],
      });

      if (!entry) {
        throw new NotFoundException('Entry not found');
      }

      // Verify tutor has access
      const tutorMapping = await queryRunner.manager.findOne(StudentTutorMapping, {
        where: {
          tutorId,
          studentId: entry.studentId,
          status: MappingStatus.ACTIVE,
        },
      });

      if (!tutorMapping) {
        throw new BadRequestException("You do not have access to this student's entry");
      }

      if (entry.status === NovelEntryStatus.NEW) {
        throw new BadRequestException('Cannot provide review on entry that has not been submitted');
      }

      let correction: NovelCorrection;

      // Check if correction already exists
      const existingCorrection = await queryRunner.manager.findOne(NovelCorrection, {
        where: { entryId: entryId },
      });

      if (existingCorrection) {
        // Check if score already exists - if so, prevent any further scoring
        if (existingCorrection.score !== null) {
          throw new BadRequestException('Entry has already been reviewed and scored. Correction and score can only be given once per entry.');
        } else {
          // Update existing correction with score (first time scoring)
          existingCorrection.correction = createReviewDto.correction;
          existingCorrection.score = createReviewDto.score;
          correction = await queryRunner.manager.save(NovelCorrection, existingCorrection);
        }
      } else {
        // Create new correction with score
        this.logger.log(`Creating review correction for entryId: ${entryId}, tutorId: ${tutorId}`);

        // Validate parameters before creating entity
        if (!entryId || typeof entryId !== 'string' || entryId.trim() === '') {
          throw new BadRequestException(`Invalid entryId: ${entryId}`);
        }
        if (!tutorId || typeof tutorId !== 'string' || tutorId.trim() === '') {
          throw new BadRequestException(`Invalid tutorId: ${tutorId}`);
        }

        // Create correction using query runner's repository to ensure transaction consistency
        const correctionData = {
          entryId: entryId,
          tutorId: tutorId,
          correction: createReviewDto.correction,
          score: createReviewDto.score,
        };

        this.logger.log(`Creating correction with data: ${JSON.stringify(correctionData)}`);

        // Use query runner's repository to create and save the entity
        const correctionRepository = queryRunner.manager.getRepository(NovelCorrection);
        correction = correctionRepository.create(correctionData);

        this.logger.log(`Created correction entity - entryId: ${correction.entryId}, tutorId: ${correction.tutorId}`);

        correction = await queryRunner.manager.save(NovelCorrection, correction);

        this.logger.log(`After save - review savedCorrection.entryId: ${correction.entryId}, savedCorrection.id: ${correction.id}`);
      }

      // Set original reviewed version if not already set
      await this.setOriginalReviewedVersion(entry, queryRunner);

      // Commit the correction first
      await queryRunner.commitTransaction();

      // Now update the entry status in a separate transaction
      const updateQueryRunner = this.dataSource.createQueryRunner();
      await updateQueryRunner.connect();
      await updateQueryRunner.startTransaction();

      try {
        // NEW REQUIREMENT: Check if already reviewed (can only review once)
        if (entry.reviewedBy && entry.score !== null && entry.score !== undefined) {
          throw new BadRequestException('Entry has already been reviewed and scored. Only feedback can be added after review.');
        }

        // Set status to REVIEWED since this is the first and only review
        const newStatus = NovelEntryStatus.REVIEWED;
        const updateData: any = {
          status: newStatus,
          reviewedAt: getCurrentUTCDate(),
          reviewedBy: tutorId,
          score: createReviewDto.score,
          // NEW REQUIREMENT: Enable subsequent submissions after review
          lastReviewedAt: getCurrentUTCDate(),
          canSubmitNewVersion: true,
        };

        await updateQueryRunner.manager.update(NovelEntry, { id: entryId }, updateData);
        await updateQueryRunner.commitTransaction();
      } catch (updateError) {
        await updateQueryRunner.rollbackTransaction();
        this.logger.error(`Error updating entry status: ${updateError.message}`, updateError.stack);
        // Don't throw here as the correction was already saved successfully
      } finally {
        await updateQueryRunner.release();
      }

      // Send notification to student
      try {
        await this.sendCorrectionNotification(entry, correction);
      } catch (notificationError) {
        this.logger.error(`Failed to send notification: ${notificationError.message}`, notificationError.stack);
      }

      this.logger.log(`Tutor ${tutorId} submitted review for entry ${entryId}`);
      return this.mapCorrectionToResponseDto(correction);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error submitting review: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // REMOVED: confirmEntry method - confirm stage removed from lifecycle
  // Review submission is now the final state, no additional confirmation needed

  /**
   * Add unlimited feedback to novel entry - NEW UNIFIED LOGIC
   * @param entryId The ID of the novel entry
   * @param tutorId The ID of the tutor adding feedback
   * @param feedbackText The feedback text
   * @returns The created feedback
   */
  async addNovelFeedback(entryId: string, tutorId: string, feedbackText: string): Promise<NovelFeedback> {
    try {
      // Check if the user is a tutor
      const user = await this.userRepository.findOne({
        where: { id: tutorId },
        relations: ['userRoles', 'userRoles.role'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${tutorId} not found`);
      }

      const isTutor = user.userRoles.some((userRole) => userRole.role.name === 'tutor');
      if (!isTutor) {
        throw new ForbiddenException('Only tutors can add feedback');
      }

      // Get the novel entry
      const entry = await this.novelEntryRepository.findOne({
        where: { id: entryId },
        relations: ['student', 'topic'],
      });

      if (!entry) {
        throw new NotFoundException(`Novel entry with ID ${entryId} not found`);
      }

      if (entry.status === NovelEntryStatus.NEW) {
        throw new BadRequestException('Entry must be submitted to receive feedback');
      }

      // Verify tutor has access
      const tutorMapping = await this.studentTutorMappingRepository.findOne({
        where: {
          tutorId,
          studentId: entry.studentId,
          status: MappingStatus.ACTIVE,
        },
      });

      if (!tutorMapping) {
        throw new BadRequestException("You do not have access to this student's entry");
      }

      // Create feedback
      const feedback = this.novelFeedbackRepository.create({
        entryId: entryId,
        tutorId: tutorId,
        feedback: feedbackText,
        createdBy: tutorId,
        updatedBy: tutorId,
      });

      const savedFeedback = await this.novelFeedbackRepository.save(feedback);

      // Send feedback notification
      try {
        await this.sendNovelFeedbackNotification(entry, savedFeedback);
      } catch (notificationError) {
        this.logger.error(`Failed to send feedback notification: ${notificationError.message}`, notificationError.stack);
      }

      return savedFeedback;
    } catch (error) {
      this.logger.error(`Error adding novel feedback: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send feedback notification to student
   */
  private async sendNovelFeedbackNotification(entry: NovelEntry, feedback: NovelFeedback): Promise<void> {
    try {
      // Get tutor details
      const tutor = await this.userRepository.findOne({
        where: { id: feedback.tutorId },
      });

      if (!tutor || !entry.student) {
        this.logger.warn('Missing tutor or student information for feedback notification');
        return;
      }

      await this.notificationHelper.notify(entry.studentId, NotificationType.NOVEL_FEEDBACK, 'New Feedback on Novel Entry', `${tutor.name} has provided feedback on your novel entry.`, {
        relatedEntityId: entry.id,
        relatedEntityType: 'novel_entry',
        sendEmail: true,
        sendPush: true,
        sendInApp: true,
        sendMobile: true,
        sendSms: false,
        sendRealtime: false,
      });

      this.logger.log(`Sent feedback notification to student ${entry.studentId} for novel entry ${entry.id}`);
    } catch (error) {
      this.logger.error(`Failed to send feedback notification: ${error.message}`, error.stack);
      throw error;
    }
  }

  // REMOVED: sendConfirmationNotification method - confirm stage removed from lifecycle
  // Review submission is now the final state, no confirmation notifications needed

  private async sendFeedbackNotification(entry: NovelEntry, feedback: NovelFeedback): Promise<void> {
    try {
      // Send notification asynchronously to avoid blocking the response
      this.asyncNotificationHelper.notifyAsync(
        entry.studentId,
        NotificationType.NOVEL_FEEDBACK,
        'New Novel Feedback',
        `You have received feedback on your novel entry`,
        {
          relatedEntityId: entry.id,
          relatedEntityType: 'novel_entry',
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendMobile: true,
          sendSms: false,
          sendRealtime: false,
        },
        {
          submissionId: entry.id,
          entryType: 'novel_entry',
          priority: 2, // Medium priority for feedback
        }
      ).catch(error => {
        this.logger.error(`Failed to send async notification: ${error?.message || 'Unknown error'}`, error?.stack);
      });
    } catch (error) {
      this.logger.warn(`Failed to prepare feedback notification: ${error.message}`);
    }
  }

  private async sendCorrectionNotification(entry: NovelEntry, correction: NovelCorrection): Promise<void> {
    try {
      // Send notification asynchronously to avoid blocking the response
      this.asyncNotificationHelper.notifyAsync(
        entry.studentId,
        NotificationType.NOVEL_REVIEW,
        'Novel Entry Reviewed',
        `Your novel entry has been reviewed and scored`,
        {
          relatedEntityId: entry.id,
          relatedEntityType: 'novel_entry',
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendMobile: true,
          sendSms: false,
          sendRealtime: false,
        },
        {
          submissionId: entry.id,
          entryType: 'novel_entry',
          priority: 1, // High priority for review completion
        }
      ).catch(error => {
        this.logger.error(`Failed to send async notification: ${error?.message || 'Unknown error'}`, error?.stack);
      });
    } catch (error) {
      this.logger.warn(`Failed to prepare correction notification: ${error.message}`);
    }
  }

  /**
   * Set original reviewed version if not already set
   */
  private async setOriginalReviewedVersion(entry: NovelEntry, queryRunner: any): Promise<void> {
    try {
      if (!entry.originalReviewedVersionId) {
        // Create a version history entry for the current state before review
        const originalVersion = new NovelEntryHistory();
        originalVersion.novelEntryId = entry.id;
        originalVersion.content = entry.content;
        // Calculate next version number based on existing history records
        const existingVersionsCount = await this.novelEntryHistoryRepository.count({
          where: { novelEntryId: entry.id },
        });
        originalVersion.versionNumber = existingVersionsCount + 1;
        originalVersion.isLatest = false; // This is the original reviewed version, not the latest
        originalVersion.wordCount = this.calculateWordCount(entry.content);
        originalVersion.metaData = {
          updateTrigger: 'review' as const,
          significantChange: true,
        };
        originalVersion.createdBy = entry.studentId;
        originalVersion.updatedBy = entry.studentId;

        const savedOriginalVersion = await queryRunner.manager.save(NovelEntryHistory, originalVersion);

        // Update entry to reference this original version
        await queryRunner.manager.update(NovelEntry, entry.id, {
          originalReviewedVersionId: savedOriginalVersion.id,
          totalVersions: originalVersion.versionNumber,
        });

        this.logger.log(`Set original reviewed version for novel entry ${entry.id}`);
      }
    } catch (error) {
      this.logger.error(`Error setting original reviewed version for novel entry ${entry.id}: ${error.message}`, error.stack);
    }
  }

  private calculateWordCount(content: string): number {
    if (!content || content.trim().length === 0) {
      return 0;
    }
    return content.trim().split(/\s+/).length;
  }

  /**
   * Calculate edit history count from actual history table records
   */
  private async calculateEditHistoryCount(entryId: string): Promise<number> {
    try {
      const count = await this.novelEntryHistoryRepository.count({
        where: { novelEntryId: entryId },
      });
      return count;
    } catch (error) {
      this.logger.warn(`Failed to calculate edit history count for novel entry ${entryId}: ${error.message}`);
      return 0;
    }
  }

  private async mapEntryToResponseDto(entry: NovelEntry): Promise<NovelEntryResponseDto> {
    // Get skin information - if entry has a skin, use it; otherwise use user's default novel skin
    let skinInfo: DiarySkinResponseDto | undefined = undefined;
    let effectiveSkinId: string | undefined = undefined;

    if (entry.skinId) {
      // Entry has a specific skin assigned
      effectiveSkinId = entry.skinId;

      if (entry.skin) {
        // Skin relation is loaded
        try {
          const skinUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, entry.skinId);

          skinInfo = {
            id: entry.skin.id,
            name: entry.skin.name,
            description: entry.skin.description,
            previewImagePath: skinUrl,
            isActive: entry.skin.isActive,
            isGlobal: entry.skin.isGlobal,
            createdById: entry.skin.createdById,
            templateContent: entry.skin.templateContent || '<div>{{content}}</div>',
            isUsedIn: true,
          };
        } catch (error) {
          this.logger.warn(`Failed to load skin URL for entry ${entry.id}: ${error.message}`);
        }
      } else {
        // Skin relation not loaded, load it separately
        try {
          const skinDto = await this.diarySkinService.getDiarySkinById(entry.skinId);

          skinInfo = {
            id: skinDto.id,
            name: skinDto.name,
            description: skinDto.description,
            previewImagePath: skinDto.previewImagePath,
            isActive: skinDto.isActive,
            isGlobal: skinDto.isGlobal,
            createdById: skinDto.createdById,
            templateContent: skinDto.templateContent || '<div>{{content}}</div>',
            isUsedIn: skinDto.isUsedIn || true,
          };
        } catch (error) {
          this.logger.warn(`Failed to load skin information for entry ${entry.id}: ${error.message}`);
        }
      }
    } else if (entry.studentId) {
      // No specific skin assigned, get user's default novel skin
      try {
        const user = await this.userRepository.findOne({
          where: { id: entry.studentId },
          relations: ['defaultNovelSkin'],
          select: ['id', 'defaultNovelSkinId'],
        });

        if (user?.defaultNovelSkin) {
          const skinUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, user.defaultNovelSkin.id);

          skinInfo = {
            id: user.defaultNovelSkin.id,
            name: user.defaultNovelSkin.name,
            description: user.defaultNovelSkin.description,
            previewImagePath: skinUrl,
            isActive: user.defaultNovelSkin.isActive,
            isGlobal: user.defaultNovelSkin.isGlobal,
            createdById: user.defaultNovelSkin.createdById,
            templateContent: user.defaultNovelSkin.templateContent || '<div>{{content}}</div>',
            isUsedIn: true,
            isUserDefaultNovel: true,
            isUserDefaultDiary: false,
          };

          // Set effectiveSkinId to the default skin ID
          effectiveSkinId = user.defaultNovelSkin.id;
        } else if (user?.defaultNovelSkinId) {
          // User has defaultNovelSkinId but relation not loaded
          effectiveSkinId = user.defaultNovelSkinId;

          try {
            const skinDto = await this.diarySkinService.getDiarySkinById(user.defaultNovelSkinId);
            skinInfo = {
              id: skinDto.id,
              name: skinDto.name,
              description: skinDto.description,
              previewImagePath: skinDto.previewImagePath,
              isActive: skinDto.isActive,
              isGlobal: skinDto.isGlobal,
              createdById: skinDto.createdById,
              templateContent: skinDto.templateContent || '<div>{{content}}</div>',
              isUsedIn: skinDto.isUsedIn || true,
              isUserDefaultNovel: true,
              isUserDefaultDiary: false,
            };
          } catch (skinError) {
            this.logger.warn(`Failed to load skin details for default novel skin ${user.defaultNovelSkinId}: ${skinError.message}`);
          }
        }
      } catch (error) {
        this.logger.warn(`Failed to load default novel skin for student ${entry.studentId}: ${error.message}`);
      }
    }

    // Get original reviewed version if it exists
    let originalReviewedVersion = undefined;
    if (entry.originalReviewedVersion) {
      // Use the loaded relation if available
      originalReviewedVersion = {
        id: entry.originalReviewedVersion.id,
        content: entry.originalReviewedVersion.content,
        versionNumber: entry.originalReviewedVersion.versionNumber,
        wordCount: entry.originalReviewedVersion.wordCount,
        createdAt: entry.originalReviewedVersion.createdAt,
        metaData: entry.originalReviewedVersion.metaData,
        isLatest: entry.originalReviewedVersion.isLatest,
      };
    } else if (entry.originalReviewedVersionId) {
      // Fallback to database query if relation not loaded
      try {
        const originalVersion = await this.novelEntryHistoryRepository.findOne({
          where: { id: entry.originalReviewedVersionId },
        });
        if (originalVersion) {
          originalReviewedVersion = {
            id: originalVersion.id,
            content: originalVersion.content,
            versionNumber: originalVersion.versionNumber,
            wordCount: originalVersion.wordCount,
            createdAt: originalVersion.createdAt,
            metaData: originalVersion.metaData,
            isLatest: originalVersion.isLatest,
          };
        }
      } catch (error) {
        this.logger.warn(`Failed to load original reviewed version for entry ${entry.id}: ${error.message}`);
      }
    }

    // Debug logging
    this.logger.debug(`Entry ${entry.id}: original skinId=${entry.skinId}, effective skinId=${effectiveSkinId}, has skin info=${!!skinInfo}`);

    return {
      id: entry.id,
      topicId: entry.topicId,
      studentId: entry.studentId,
      studentName: entry.student?.name,
      content: entry.content,
      wordCount: entry.wordCount,
      status: entry.status,
      skinId: effectiveSkinId, // Use effective skin ID (entry's skin or default skin)
      backgroundColor: entry.backgroundColor,
      submittedAt: entry.submittedAt,
      reviewedAt: entry.reviewedAt,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt,
      topic: entry.topic
        ? {
            id: entry.topic.id,
            title: entry.topic.title,
            sequenceTitle: entry.topic.sequenceTitle,
            category: entry.topic.category,
            instruction: entry.topic.instruction,
            isActive: entry.topic.isActive,
            minWordCount: entry.topic.minWordCount,
            maxWordCount: entry.topic.maxWordCount,
            createdAt: entry.topic.createdAt,
            updatedAt: entry.topic.updatedAt,
          }
        : undefined,
      feedbacks: entry.feedbacks?.map((feedback) => this.mapFeedbackToResponseDto(feedback)),
      correction: entry.correction ? this.mapCorrectionToResponseDto(entry.correction) : undefined,
      skin: skinInfo,
      totalEditHistory: await this.calculateEditHistoryCount(entry.id),
      originalReviewedVersion: originalReviewedVersion,

      // NEW: Submission tracking fields
      submittedVersionCount: entry.submittedVersionCount || 0,
      canSubmitNewVersion: entry.canSubmitNewVersion ?? true,
      lastSubmittedAt: entry.lastSubmittedAt,
      lastReviewedAt: entry.lastReviewedAt,

      // NEW: Resubmission tracking fields
      isResubmission: entry.isResubmission || false,
      resubmissionType: entry.resubmissionType,
      previousReviewCount: entry.previousReviewCount || 0,
      previousConfirmationCount: entry.previousConfirmationCount || 0,
    };
  }

  private mapFeedbackToResponseDto(feedback: NovelFeedback): NovelFeedbackResponseDto {
    return {
      id: feedback.id,
      entryId: feedback.entryId,
      tutorId: feedback.tutorId,
      feedback: feedback.feedback,
      createdAt: feedback.createdAt,
      updatedAt: feedback.updatedAt,
    };
  }

  private mapCorrectionToResponseDto(correction: NovelCorrection): NovelCorrectionResponseDto {
    return {
      id: correction.id,
      entryId: correction.entryId,
      tutorId: correction.tutorId,
      correction: correction.correction,
      score: correction.score || undefined,
      createdAt: correction.createdAt,
      updatedAt: correction.updatedAt,
    };
  }

  /**
   * Get all feedbacks for a novel entry
   * @param tutorId The ID of the tutor requesting the feedbacks
   * @param entryId The ID of the novel entry
   * @returns Array of feedback response DTOs
   */
  async getFeedbacks(tutorId: string, entryId: string): Promise<NovelFeedbackResponseDto[]> {
    try {
      // Verify the tutor exists and has tutor role
      const tutor = await this.userRepository.findOne({
        where: { id: tutorId },
        relations: ['userRoles', 'userRoles.role'],
      });

      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      const isTutor = tutor.userRoles.some((userRole) => userRole.role.name === 'tutor');
      if (!isTutor) {
        throw new ForbiddenException('Only tutors can view feedbacks on novel entries');
      }

      // Find the novel entry
      const entry = await this.novelEntryRepository.findOne({
        where: { id: entryId },
        relations: ['student', 'feedbacks', 'feedbacks.tutor'],
      });

      if (!entry) {
        throw new NotFoundException(`Novel entry with ID ${entryId} not found`);
      }

      // Check if the tutor has access to this student
      const studentId = entry.studentId;
      const hasAccess = await this.tutorMatchingService.hasTutorStudentAccess(tutorId, studentId);
      if (!hasAccess) {
        throw new ForbiddenException("You do not have access to this student's novel entries");
      }

      // Map feedbacks to response DTOs
      const feedbacks = entry.feedbacks?.map((feedback) => this.mapFeedbackToResponseDto(feedback)) || [];

      this.logger.log(`Retrieved ${feedbacks.length} feedbacks for novel entry ${entryId}`);
      return feedbacks;
    } catch (error) {
      this.logger.error(`Error getting feedbacks for novel entry ${entryId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
