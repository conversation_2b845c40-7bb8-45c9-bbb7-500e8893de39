import { Injectable, NotFoundException, ForbiddenException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { NovelEntryHistory } from '../../../database/entities/novel-entry-history.entity';
import { NovelEntry } from '../../../database/entities/novel-entry.entity';
import { NovelEntryHistoryResponseDto, NovelEntryVersionDto } from '../../../database/models/novel.dto';

@Injectable()
export class NovelEntryHistoryService {
  private readonly logger = new Logger(NovelEntryHistoryService.name);

  constructor(
    @InjectRepository(NovelEntryHistory)
    private readonly novelEntryHistoryRepository: Repository<NovelEntryHistory>,
    @InjectRepository(NovelEntry)
    private readonly novelEntryRepository: Repository<NovelEntry>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Create a new version during novel entry update
   */
  async createVersionFromUpdate(novelEntryId: string, oldData: { content: string }, newData: { content?: string }, userId: string, request?: any): Promise<NovelEntryHistory> {
    try {
      // Get current entry to determine next version number
      const entry = await this.novelEntryRepository.findOne({
        where: { id: novelEntryId },
        relations: ['student'],
      });

      if (!entry) {
        throw new NotFoundException(`Novel entry with ID ${novelEntryId} not found`);
      }

      // Check ownership
      if (entry.studentId !== userId) {
        throw new ForbiddenException('You do not have permission to create versions for this novel entry');
      }

      // Mark all existing versions as not latest
      await this.novelEntryHistoryRepository.update({ novelEntryId: novelEntryId }, { isLatest: false });

      // Prepare new version data
      const newContent = newData.content !== undefined ? newData.content : oldData.content;

      // Calculate next version number based on existing history records
      const existingVersionsCount = await this.novelEntryHistoryRepository.count({
        where: { novelEntryId: novelEntryId },
      });
      const nextVersionNumber = existingVersionsCount + 1;

      // Create new version
      const newVersion = this.novelEntryHistoryRepository.create({
        novelEntryId: novelEntryId,
        content: newContent,
        versionNumber: nextVersionNumber,
        isLatest: true,
        wordCount: this.calculateWordCount(newContent),
        metaData: this.generateImplicitMetadata(request, oldData, { content: newContent }),
        createdBy: userId,
        updatedBy: userId,
      });

      const savedVersion = await this.novelEntryHistoryRepository.save(newVersion);

      // Update entry's current version (totalEditHistory will be calculated from actual records)
      await this.novelEntryRepository.update(novelEntryId, {
        currentVersionId: savedVersion.id,
      });

      this.logger.log(`Created version ${nextVersionNumber} for novel entry ${novelEntryId}`);
      return savedVersion;
    } catch (error) {
      this.logger.error(`Error creating version for novel entry ${novelEntryId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Calculate word count
   */
  private calculateWordCount(content: string): number {
    if (!content || content.trim().length === 0) {
      return 0;
    }
    return content.trim().split(/\s+/).length;
  }

  /**
   * Generate metadata for version tracking
   */
  private generateImplicitMetadata(request: any, oldData: any, newData: any): any {
    const oldContent = oldData.content || '';
    const newContent = newData.content || '';

    const metadata = {
      updateTrigger: 'update' as const,
      significantChange: this.isSignificantChange(oldContent, newContent),
      wordCountChange: this.calculateWordCount(newContent) - this.calculateWordCount(oldContent),
      timestamp: new Date().toISOString(),
    };

    // Add request metadata if available
    if (request) {
      metadata['userAgent'] = request.headers?.['user-agent'];
      metadata['ip'] = request.ip;
    }

    return metadata;
  }

  /**
   * Determine if the change is significant
   */
  private isSignificantChange(oldContent: string, newContent: string): boolean {
    const oldWordCount = this.calculateWordCount(oldContent);
    const newWordCount = this.calculateWordCount(newContent);

    // Consider significant if word count changes by more than 10% or 5 words, whichever is greater
    const threshold = Math.max(Math.floor(oldWordCount * 0.1), 5);
    return Math.abs(newWordCount - oldWordCount) >= threshold;
  }

  /**
   * Get version history for a novel entry
   */
  async getVersionHistory(novelEntryId: string, userId: string): Promise<NovelEntryHistoryResponseDto> {
    try {
      // Verify ownership
      const entry = await this.novelEntryRepository.findOne({
        where: { id: novelEntryId },
        relations: ['student'],
      });

      if (!entry) {
        throw new NotFoundException(`Novel entry with ID ${novelEntryId} not found`);
      }

      if (entry.studentId !== userId) {
        throw new ForbiddenException('You do not have permission to view history for this novel entry');
      }

      // Get all versions
      const versions = await this.novelEntryHistoryRepository.find({
        where: { novelEntryId },
        order: { versionNumber: 'DESC' },
      });

      // Get the latest entry data to ensure we have current version info
      const latestEntry = await this.novelEntryRepository.findOne({
        where: { id: novelEntryId },
      });

      return {
        entryId: novelEntryId,
        totalEditHistory: versions.length,
        currentVersionId: latestEntry?.currentVersionId || (versions.length > 0 ? versions[0].id : ''),
        versions: versions.map((version) => this.mapVersionToDto(version)),
      };
    } catch (error) {
      this.logger.error(`Error getting version history for novel entry ${novelEntryId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a specific version
   */
  async getVersion(versionId: string, userId: string): Promise<NovelEntryVersionDto> {
    try {
      const version = await this.novelEntryHistoryRepository.findOne({
        where: { id: versionId },
        relations: ['novelEntry', 'novelEntry.student'],
      });

      if (!version) {
        throw new NotFoundException(`Version with ID ${versionId} not found`);
      }

      if (version.novelEntry.studentId !== userId) {
        throw new ForbiddenException('You do not have permission to view this version');
      }

      return this.mapVersionToDto(version);
    } catch (error) {
      this.logger.error(`Error getting version ${versionId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Set a version as the latest (restore functionality)
   */
  async setLatestVersion(novelEntryId: string, versionId: string, userId: string): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Verify the entry exists and user has permission
      const entry = await queryRunner.manager.findOne(NovelEntry, {
        where: { id: novelEntryId },
        relations: ['student'],
      });

      if (!entry) {
        throw new NotFoundException(`Novel entry with ID ${novelEntryId} not found`);
      }

      if (entry.studentId !== userId) {
        throw new ForbiddenException('You do not have permission to modify this novel entry');
      }

      // Verify the version exists and belongs to this entry
      const versionToRestore = await queryRunner.manager.findOne(NovelEntryHistory, {
        where: { id: versionId, novelEntryId: novelEntryId },
      });

      if (!versionToRestore) {
        throw new NotFoundException(`Version with ID ${versionId} not found for this novel entry`);
      }

      // Mark all versions as not latest
      await queryRunner.manager.update(NovelEntryHistory, { novelEntryId: novelEntryId }, { isLatest: false });

      // Mark the selected version as latest
      await queryRunner.manager.update(NovelEntryHistory, { id: versionId }, { isLatest: true });

      // Update the main entry with the restored content
      await queryRunner.manager.update(
        NovelEntry,
        { id: novelEntryId },
        {
          content: versionToRestore.content,
          currentVersionId: versionId,
          updatedBy: userId,
        },
      );

      await queryRunner.commitTransaction();
      this.logger.log(`Restored version ${versionToRestore.versionNumber} for novel entry ${novelEntryId}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error restoring version ${versionId} for novel entry ${novelEntryId}: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Map version entity to DTO
   */
  private mapVersionToDto(version: NovelEntryHistory): NovelEntryVersionDto {
    return {
      id: version.id,
      content: version.content,
      versionNumber: version.versionNumber,
      wordCount: version.wordCount,
      createdAt: version.createdAt,
      metaData: version.metaData,
      isLatest: version.isLatest,
    };
  }
}
