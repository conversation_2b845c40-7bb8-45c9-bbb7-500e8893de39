import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThanOrEqual } from 'typeorm';
import { MissionDiaryEntry } from '../../database/entities/mission-diary-entry.entity';
import { AwardsService } from '../awards/awards.service';
import { AwardModule, AwardFrequency } from '../../database/entities/award.entity';

@Injectable()
export class MissionDiaryAwardService {
  private readonly logger = new Logger(MissionDiaryAwardService.name);

  constructor(
    @InjectRepository(MissionDiaryEntry)
    private readonly missionDiaryEntryRepository: Repository<MissionDiaryEntry>,
    private readonly awardsService: AwardsService,
  ) {}

  /**
   * Generate awards for mission diary entries in a specific date range
   */
  async generateAwardsForRange(startDate: Date, endDate: Date): Promise<void> {
    try {
      this.logger.log(`Generating mission diary awards for period: ${startDate.toISOString()} to ${endDate.toISOString()}`);

      // Get available awards for the diary module (mission diaries use diary module awards)
      const availableAwards = await this.awardsService.getAllAwards(
        AwardModule.DIARY,
        false, // only active awards
        undefined,
        undefined,
        this.determineFrequency(startDate, endDate),
      );

      if (availableAwards.items.length === 0) {
        this.logger.log('No active awards found for mission diary module');
        return;
      }

      // Get mission diary entry data for the period
      const studentData = await this.getMissionDiaryStudentData(startDate, endDate);

      if (studentData.length === 0) {
        this.logger.log('No eligible mission diary entries found for the period');
        return;
      }

      // Generate awards for each available award
      for (const award of availableAwards.items) {
        await this.generateAwardForStudents(award, studentData, startDate, endDate);
      }

      this.logger.log('Mission diary awards generated successfully');
    } catch (error) {
      this.logger.error(`Error generating mission diary awards: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get mission diary student data for award calculations
   */
  private async getMissionDiaryStudentData(startDate: Date, endDate: Date) {
    // Get all mission diary entries within the date range that have been reviewed
    const entries = await this.missionDiaryEntryRepository
      .createQueryBuilder('entry')
      .leftJoin('entry.student', 'student')
      .leftJoin('entry.mission', 'mission')
      .leftJoin('entry.correction', 'correction')
      .select([
        'student.id as userId',
        'student.name as userName',
        'COUNT(entry.id) as totalEntries',
        'AVG(COALESCE(correction.score, 0)) as averageScore',
        'SUM(COALESCE(correction.score, 0)) as totalScore',
      ])
      .where('entry.submittedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere('entry.status IN (:...statuses)', { statuses: ['SUBMITTED', 'REVIEWED'] })
      .groupBy('student.id, student.name')
      .having('COUNT(entry.id) > 0')
      .getRawMany();

    return entries.map(entry => ({
      userId: entry.userId,
      userName: entry.userName,
      totalEntries: parseInt(entry.totalEntries) || 0,
      averageScore: parseFloat(entry.averageScore) || 0,
      totalScore: parseFloat(entry.totalScore) || 0,
    }));
  }

  /**
   * Generate award for students based on award criteria
   */
  private async generateAwardForStudents(award: any, studentData: any[], startDate: Date, endDate: Date) {
    try {
      // Calculate scores for each student
      const scoredStudents = studentData.map(student => ({
        ...student,
        awardScore: this.calculateAwardScore(student, award),
      }));

      // Filter students who meet minimum criteria
      const eligibleStudents = scoredStudents.filter(student => 
        student.awardScore > 0 && 
        student.totalEntries >= (award.criteriaConfig?.entriesRequired || 1)
      );

      if (eligibleStudents.length === 0) {
        this.logger.log(`No eligible students found for mission diary award: ${award.name}`);
        return;
      }

      // Sort by score and select winners
      eligibleStudents.sort((a, b) => b.awardScore - a.awardScore);
      const maxWinners = award.criteriaConfig?.maxWinners || 1;
      const winners = eligibleStudents.slice(0, maxWinners);

      // Create award records
      for (const winner of winners) {
        await this.awardsService.createAwardWinner({
          userId: winner.userId,
          awardId: award.id,
          awardDate: endDate.toISOString().split('T')[0],
          awardReason: `Achieved ${winner.awardScore.toFixed(1)} points in mission diary ${award.name}`,
          metadata: {
            totalEntries: winner.totalEntries,
            averageScore: winner.averageScore,
            totalScore: winner.totalScore,
            awardScore: winner.awardScore,
            calculationPeriod: {
              startDate: startDate.toISOString(),
              endDate: endDate.toISOString(),
            },
          },
        });

        this.logger.log(`Created mission diary award winner: User ${winner.userId} for award ${award.name} with score ${winner.awardScore.toFixed(1)}`);
      }
    } catch (error) {
      this.logger.error(`Error generating award for students: ${error.message}`, error.stack);
    }
  }

  /**
   * Calculate award score for a student
   */
  private calculateAwardScore(student: any, award: any): number {
    const minScore = award.criteriaConfig?.minScore || 0;
    
    // Check if student meets minimum requirements
    if (student.averageScore < minScore) {
      return 0;
    }

    // Simple scoring based on average score and total entries
    const qualityScore = Math.min(student.averageScore * 10, 100); // Convert to 0-100 scale
    const quantityScore = Math.min(student.totalEntries * 10, 100); // Reward more entries
    
    // Weighted combination (70% quality, 30% quantity)
    return qualityScore * 0.7 + quantityScore * 0.3;
  }

  /**
   * Determine award frequency based on date range
   */
  private determineFrequency(startDate: Date, endDate: Date): AwardFrequency {
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    if (daysDiff <= 7) return AwardFrequency.WEEKLY;
    if (daysDiff <= 31) return AwardFrequency.MONTHLY;
    if (daysDiff <= 93) return AwardFrequency.QUARTERLY;
    return AwardFrequency.YEARLY;
  }
}
