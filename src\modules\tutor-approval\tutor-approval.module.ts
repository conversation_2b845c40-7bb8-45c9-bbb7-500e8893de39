import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { <PERSON>torApprovalController } from './tutor-approval.controller';
import { TutorApprovalService } from './tutor-approval.service';
import { <PERSON>torApproval } from '../../database/entities/tutor-approval.entity';
import { User } from '../../database/entities/user.entity';
import { EmailModule } from '../email/email.module';

@Module({
  imports: [TypeOrmModule.forFeature([TutorApproval, User]), EmailModule],
  controllers: [TutorApprovalController],
  providers: [TutorApprovalService],
  exports: [TutorApprovalService],
})
export class TutorApprovalModule {}
