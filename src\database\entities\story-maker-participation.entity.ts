import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Check, OneToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { StoryMaker } from './story-maker.entity';
import { StoryMakerSubmission } from './story-maker-submission.entity';

@Entity()
@Check('score > 0') // Ensure score is greater than 0 when provided
export class StoryMakerParticipation extends AuditableBaseEntity {
  @Column({ name: 'student_id', type: 'uuid' })
  studentId: string;

  @Column({ name: 'story_maker_id', type: 'uuid' })
  storyMakerId: string;

  @Column({ name: 'first_submitted_at', nullable: true })
  firstSubmittedAt: Date;

  @Column({ name: 'is_evaluated', default: false })
  isEvaluated: boolean;

  @Column({ name: 'score', nullable: true, type: 'integer' })
  score: number;

  @Column({ name: 'evaluated_at', nullable: true })
  evaluatedAt: Date;

  @Column({ name: 'evaluated_by', nullable: true, type: 'uuid' })
  evaluatedBy: string;

  // Relationships
  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  @ManyToOne(() => StoryMaker)
  @JoinColumn({ name: 'story_maker_id' })
  storyMaker: StoryMaker;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'evaluated_by' })
  evaluator: User;

  @OneToMany(() => StoryMakerSubmission, (submission) => submission.participation)
  submissions: StoryMakerSubmission[];
}
