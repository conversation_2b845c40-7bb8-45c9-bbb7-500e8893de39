import { Injectable, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, <PERSON>Than, MoreThan } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { Cron, CronExpression } from '@nestjs/schedule';
import { createHash } from 'crypto';
import { TokenBlacklist } from '../../database/entities/token-blacklist.entity';
import LoggerService from './logger.service';

@Injectable()
export class TokenBlacklistService {
  constructor(
    @InjectRepository(TokenBlacklist)
    private tokenBlacklistRepository: Repository<TokenBlacklist>,
    @Inject(JwtService) private jwtService: JwtService,
    private logger: LoggerService,
  ) {}

  /**
   * Add a token to the blacklist
   * @param token JWT token to blacklist
   * @param userId User ID who owns the token
   * @param reason Reason for blacklisting
   */
  async blacklistToken(token: string, userId: string, reason: string = 'logout'): Promise<void> {
    try {
      // Decode token to get expiration
      const decoded = this.jwtService.decode(token) as any;
      if (!decoded || !decoded.exp) {
        this.logger.warn(`Cannot blacklist token: Invalid token format`);
        return;
      }

      const expiresAt = new Date(decoded.exp * 1000);
      
      // Only blacklist if token hasn't expired yet
      if (expiresAt <= new Date()) {
        this.logger.debug(`Token already expired, skipping blacklist`);
        return;
      }

      // Create hash of token for security (don't store actual token)
      const tokenHash = this.hashToken(token);

      // Check if token is already blacklisted
      const existing = await this.tokenBlacklistRepository.findOne({
        where: { tokenHash }
      });

      if (existing) {
        this.logger.debug(`Token already blacklisted`);
        return;
      }

      // Add to blacklist
      const blacklistEntry = this.tokenBlacklistRepository.create({
        tokenHash,
        userId,
        expiresAt,
        reason,
      });

      await this.tokenBlacklistRepository.save(blacklistEntry);
      this.logger.log(`Token blacklisted for user ${userId}, reason: ${reason}`);
    } catch (error) {
      this.logger.error(`Failed to blacklist token: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check if a token is blacklisted
   * @param token JWT token to check
   * @returns true if token is blacklisted
   */
  async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const tokenHash = this.hashToken(token);
      
      const blacklistEntry = await this.tokenBlacklistRepository.findOne({
        where: {
          tokenHash,
          expiresAt: MoreThan(new Date()) // Only check non-expired blacklist entries
        }
      });

      return !!blacklistEntry;
    } catch (error) {
      this.logger.error(`Failed to check token blacklist: ${error.message}`, error.stack);
      // In case of error, assume token is not blacklisted to avoid blocking valid users
      return false;
    }
  }

  /**
   * Blacklist all tokens for a user (useful for password changes, security breaches)
   * @param userId User ID
   * @param reason Reason for blacklisting
   */
  async blacklistAllUserTokens(userId: string, reason: string = 'security'): Promise<void> {
    try {
      // Note: This is a simplified approach. In a real-world scenario, you might want to
      // track all issued tokens per user, but that would require significant changes.
      // For now, we'll just log this action and rely on short token expiry times.
      this.logger.log(`Request to blacklist all tokens for user ${userId}, reason: ${reason}`);
      
      // You could implement a user-level blacklist here if needed
      // For example, add a "tokens_invalidated_at" timestamp to the user entity
    } catch (error) {
      this.logger.error(`Failed to blacklist all user tokens: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Clean up expired blacklist entries (runs daily)
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async cleanupExpiredEntries(): Promise<void> {
    try {
      const result = await this.tokenBlacklistRepository.delete({
        expiresAt: LessThan(new Date())
      });

      if (result.affected && result.affected > 0) {
        this.logger.log(`Cleaned up ${result.affected} expired blacklist entries`);
      }
    } catch (error) {
      this.logger.error(`Failed to cleanup expired blacklist entries: ${error.message}`, error.stack);
    }
  }

  /**
   * Create SHA-256 hash of token for secure storage
   * @param token JWT token
   * @returns SHA-256 hash
   */
  private hashToken(token: string): string {
    return createHash('sha256').update(token).digest('hex');
  }
}
