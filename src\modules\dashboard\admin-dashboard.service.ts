import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserType } from '../../database/entities/user.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { Plan, PlanType } from '../../database/entities/plan.entity';
import { PlanFeature, FeatureType } from '../../database/entities/plan-feature.entity';
import { StudentTutorMapping, MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { DiaryEntryAttendance, AttendanceStatus } from '../../database/entities/diary-entry-attendance.entity';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import { MissionDiaryEntry, MissionEntryStatus } from '../../database/entities/mission-diary-entry.entity';
import { NovelEntry, NovelEntryStatus } from '../../database/entities/novel-entry.entity';
import { QATaskSubmissions, QASubmissionStatus } from '../../database/entities/qa-task-submissions.entity';
import { QAMissionTasks } from '../../database/entities/qa-mission-tasks.entity';
import { EssayTaskSubmissions } from '../../database/entities/essay-task-submissions.entity';
import {SubmissionStatus as EssaySubmissionStatus} from '../../constants/submission.enum';
import { EssayMissionTasks } from '../../database/entities/essay-mission-tasks.entity';
import { BlockGameAttempt } from '../../database/entities/block-game-attempt.entity';
import { StoryMakerParticipation } from '../../database/entities/story-maker-participation.entity';
import {
  AdminStudentCountDto,
  AdminAttendanceStatsDto,
  AdminSubmissionStatsDto,
  AdminTutorCountDto,
  AdminSubscriptionStatusDto,
  AdminModuleCompletionRatesDto,
  ModuleSubmissionStatsDto,
  SubscriptionStatusBreakdownDto,
  UserModuleCompletionDto,
  StudentDailyActivityResponseDto,
  YearlyActivityStatsDto,
  MonthlyActivityStatsDto,
} from '../../database/models/dashboard.dto';

@Injectable()
export class AdminDashboardService {
  private readonly logger = new Logger(AdminDashboardService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserPlan)
    private readonly userPlanRepository: Repository<UserPlan>,
    @InjectRepository(Plan)
    private readonly planRepository: Repository<Plan>,
    @InjectRepository(PlanFeature)
    private readonly planFeatureRepository: Repository<PlanFeature>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(DiaryEntryAttendance)
    private readonly diaryEntryAttendanceRepository: Repository<DiaryEntryAttendance>,
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(MissionDiaryEntry)
    private readonly missionDiaryEntryRepository: Repository<MissionDiaryEntry>,
    @InjectRepository(NovelEntry)
    private readonly novelEntryRepository: Repository<NovelEntry>,
    @InjectRepository(QATaskSubmissions)
    private readonly qaTaskSubmissionsRepository: Repository<QATaskSubmissions>,
    @InjectRepository(QAMissionTasks)
    private readonly qaMissionTasksRepository: Repository<QAMissionTasks>,
    @InjectRepository(EssayTaskSubmissions)
    private readonly essayTaskSubmissionsRepository: Repository<EssayTaskSubmissions>,
    @InjectRepository(EssayMissionTasks)
    private readonly essayMissionTasksRepository: Repository<EssayMissionTasks>,
    @InjectRepository(BlockGameAttempt)
    private readonly blockGameAttemptRepository: Repository<BlockGameAttempt>,
    @InjectRepository(StoryMakerParticipation)
    private readonly storyMakerParticipationRepository: Repository<StoryMakerParticipation>,
  ) {}

  /**
   * Get total count of active students
   */
  async getActiveStudentCount(): Promise<AdminStudentCountDto> {
    try {
      const totalActiveStudents = await this.userRepository
        .createQueryBuilder('user')
        .leftJoin('user.userPlans', 'userPlan')
        .leftJoin('userPlan.plan', 'plan')
        .where('user.type = :userType', { userType: UserType.STUDENT })
        .andWhere('userPlan.isActive = :isActive', { isActive: true })
        .andWhere('plan.isActive = :planActive', { planActive: true })
        .getCount();

      return { totalActiveStudents };
    } catch (error) {
      this.logger.error(`Error getting active student count: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get active student count');
    }
  }

  /**
   * Get today's attendance statistics
   */
  async getTodayAttendanceStats(): Promise<AdminAttendanceStatsDto> {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // First, get all active students (same logic as getActiveStudentCount)
      const totalActiveStudents = await this.userRepository
        .createQueryBuilder('user')
        .leftJoin('user.userPlans', 'userPlan')
        .leftJoin('userPlan.plan', 'plan')
        .where('user.type = :userType', { userType: UserType.STUDENT })
        .andWhere('userPlan.isActive = :isActive', { isActive: true })
        .andWhere('plan.isActive = :planActive', { planActive: true })
        .getCount();

      // Get attendance stats for today (only for active students)
      const todayEnd = new Date(today);
      todayEnd.setHours(23, 59, 59, 999);

      const attendanceStats = await this.diaryEntryAttendanceRepository
        .createQueryBuilder('attendance')
        .leftJoin('attendance.student', 'student')
        .leftJoin('student.userPlans', 'userPlan')
        .leftJoin('userPlan.plan', 'plan')
        .select('attendance.status', 'status')
        .addSelect('COUNT(*)', 'count')
        .where('attendance.entryDate >= :todayStart AND attendance.entryDate <= :todayEnd', {
          todayStart: today,
          todayEnd: todayEnd
        })
        .andWhere('student.type = :userType', { userType: UserType.STUDENT })
        .andWhere('userPlan.isActive = :isActive', { isActive: true })
        .andWhere('plan.isActive = :planActive', { planActive: true })
        .groupBy('attendance.status')
        .getRawMany();

      let presentCount = 0;
      let absentCount = 0;

      attendanceStats.forEach((stat) => {
        const count = parseInt(stat.count, 10);
        if (stat.status === AttendanceStatus.PRESENT) {
          presentCount = count;
        } else if (stat.status === AttendanceStatus.ABSENT) {
          absentCount = count;
        }
      });

      // Calculate students with no attendance record for today
      const studentsWithAttendance = presentCount + absentCount;
      const studentsWithoutAttendance = totalActiveStudents - studentsWithAttendance;

      // For attendance percentage calculation, use total active students as denominator
      const attendancePercentage = totalActiveStudents > 0 ? (presentCount / totalActiveStudents) * 100 : 0;

      return {
        presentCount,
        absentCount: absentCount + studentsWithoutAttendance, // Include students without attendance as absent
        totalStudents: totalActiveStudents,
        attendancePercentage: Math.round(attendancePercentage * 100) / 100,
      };
    } catch (error) {
      this.logger.error(`Error getting today's attendance stats: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get attendance statistics');
    }
  }

  /**
   * Get total submission statistics across all modules
   */
  async getTotalSubmissionStats(): Promise<AdminSubmissionStatsDto> {
    try {
      const modules = await this.planFeatureRepository.find({
        where: [
          { type: FeatureType.HEC_USER_DIARY },
          { type: FeatureType.ENGLISH_NOVEL },
          { type: FeatureType.ENGLISH_QA_WRITING },
          { type: FeatureType.ENGLISH_ESSAY },
          { type: FeatureType.HEC_PLAY },
        ],
      });

      const moduleBreakdown: ModuleSubmissionStatsDto[] = [];
      let totalSubmissions = 0;
      let totalPendingSubmissions = 0;

      for (const module of modules) {
        const stats = await this.getModuleSubmissionStats(module);
        moduleBreakdown.push(stats);
        totalSubmissions += stats.totalSubmissions;
        totalPendingSubmissions += stats.pendingSubmissions;
      }

      return {
        totalSubmissions,
        totalPendingSubmissions,
        moduleBreakdown,
      };
    } catch (error) {
      this.logger.error(`Error getting total submission stats: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get submission statistics');
    }
  }

  /**
   * Get submission statistics for a specific module
   */
  private async getModuleSubmissionStats(module: PlanFeature): Promise<ModuleSubmissionStatsDto> {
    let totalSubmissions = 0;
    let pendingSubmissions = 0;

    switch (module.type) {
      case FeatureType.HEC_USER_DIARY:
        const diaryStats = await this.getDiarySubmissionStats();
        totalSubmissions = diaryStats.total;
        pendingSubmissions = diaryStats.pending;
        break;

      case FeatureType.ENGLISH_NOVEL:
        const novelStats = await this.getNovelSubmissionStats();
        totalSubmissions = novelStats.total;
        pendingSubmissions = novelStats.pending;
        break;

      case FeatureType.ENGLISH_QA_WRITING:
        const qaStats = await this.getQASubmissionStats();
        totalSubmissions = qaStats.total;
        pendingSubmissions = qaStats.pending;
        break;

      case FeatureType.ENGLISH_ESSAY:
        const essayStats = await this.getEssaySubmissionStats();
        totalSubmissions = essayStats.total;
        pendingSubmissions = essayStats.pending;
        break;

      case FeatureType.HEC_PLAY:
        // HECplay might not have traditional submissions
        totalSubmissions = 0;
        pendingSubmissions = 0;
        break;
    }

    return {
      moduleType: module.type,
      moduleName: module.name,
      totalSubmissions,
      pendingSubmissions,
    };
  }

  /**
   * Get diary submission statistics
   */
  private async getDiarySubmissionStats(): Promise<{ total: number; pending: number }> {
    // Regular diary entries
    const diaryTotal = await this.diaryEntryRepository.count();
    const diaryPending = await this.diaryEntryRepository.count({
      where: { status: DiaryEntryStatus.SUBMIT },
    });

    // Mission diary entries
    const missionTotal = await this.missionDiaryEntryRepository.count();
    const missionPending = await this.missionDiaryEntryRepository.count({
      where: { status: MissionEntryStatus.SUBMITTED },
    });

    return {
      total: diaryTotal + missionTotal,
      pending: diaryPending + missionPending,
    };
  }

  /**
   * Get novel submission statistics
   */
  private async getNovelSubmissionStats(): Promise<{ total: number; pending: number }> {
    const total = await this.novelEntryRepository.count();
    const pending = await this.novelEntryRepository.count({
      where: { status: NovelEntryStatus.SUBMITTED },
    });

    return { total, pending };
  }

  /**
   * Get QA submission statistics
   */
  private async getQASubmissionStats(): Promise<{ total: number; pending: number }> {
    const total = await this.qaTaskSubmissionsRepository.count();
    const pending = await this.qaTaskSubmissionsRepository.count({
      where: { status: QASubmissionStatus.SUBMITTED },
    });

    return { total, pending };
  }

  /**
   * Get essay submission statistics
   */
  private async getEssaySubmissionStats(): Promise<{ total: number; pending: number }> {
    const total = await this.essayTaskSubmissionsRepository.count();
    const pending = await this.essayTaskSubmissionsRepository.count({
      where: { status: EssaySubmissionStatus.SUBMITTED },
    });

    return { total, pending };
  }

  /**
   * Get total count of assigned tutors
   */
  async getAssignedTutorCount(): Promise<AdminTutorCountDto> {
    try {
      // Get total unique tutors who have active assignments
      const activeTutors = await this.studentTutorMappingRepository
        .createQueryBuilder('mapping')
        .select('COUNT(DISTINCT mapping.tutorId)', 'count')
        .where('mapping.status = :status', { status: MappingStatus.ACTIVE })
        .getRawOne();

      // Get total tutors in the system
      const totalAssignedTutors = await this.userRepository.count({
        where: { type: UserType.TUTOR },
      });

      return {
        totalAssignedTutors,
        activeTutors: parseInt(activeTutors.count, 10),
      };
    } catch (error) {
      this.logger.error(`Error getting assigned tutor count: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get tutor count');
    }
  }

  /**
   * Get subscription status breakdown for all students
   */
  async getSubscriptionStatusBreakdown(): Promise<AdminSubscriptionStatusDto> {
    try {
      const subscriptionStats = await this.userPlanRepository
        .createQueryBuilder('userPlan')
        .leftJoin('userPlan.plan', 'plan')
        .leftJoin('userPlan.user', 'user')
        .select('plan.type', 'planType')
        .addSelect('plan.name', 'planName')
        .addSelect('COUNT(*)', 'studentCount')
        .where('userPlan.isActive = :isActive', { isActive: true })
        .andWhere('user.type = :userType', { userType: UserType.STUDENT })
        .andWhere('plan.isActive = :planActive', { planActive: true })
        .groupBy('plan.type, plan.name')
        .getRawMany();

      const totalStudents = subscriptionStats.reduce((sum, stat) => sum + parseInt(stat.studentCount, 10), 0);

      const planBreakdown: SubscriptionStatusBreakdownDto[] = subscriptionStats.map((stat) => {
        const studentCount = parseInt(stat.studentCount, 10);
        const percentage = totalStudents > 0 ? (studentCount / totalStudents) * 100 : 0;

        return {
          planType: stat.planType,
          planName: stat.planName,
          studentCount,
          percentage: Math.round(percentage * 100) / 100,
        };
      });

      return {
        totalStudents,
        planBreakdown,
      };
    } catch (error) {
      this.logger.error(`Error getting subscription status breakdown: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get subscription status breakdown');
    }
  }

  /**
   * Get module completion rates for all users
   */
  async getModuleCompletionRates(): Promise<AdminModuleCompletionRatesDto> {
    try {
      const modules = await this.planFeatureRepository.find({
        where: [{ type: FeatureType.HEC_USER_DIARY }, { type: FeatureType.ENGLISH_NOVEL }, { type: FeatureType.ENGLISH_QA_WRITING }, { type: FeatureType.ENGLISH_ESSAY }],
      });

      const activeStudents = await this.userRepository
        .createQueryBuilder('user')
        .leftJoin('user.userPlans', 'userPlan')
        .leftJoin('userPlan.plan', 'plan')
        .where('user.type = :userType', { userType: UserType.STUDENT })
        .andWhere('userPlan.isActive = :isActive', { isActive: true })
        .andWhere('plan.isActive = :planActive', { planActive: true })
        .getMany();

      const userModuleCompletions: UserModuleCompletionDto[] = [];

      for (const student of activeStudents) {
        for (const module of modules) {
          const completionData = await this.getUserModuleCompletion(student, module);
          if (completionData.totalSubmissions > 0) {
            userModuleCompletions.push(completionData);
          }
        }
      }

      return {
        userModuleCompletions,
        totalStudents: activeStudents.length,
      };
    } catch (error) {
      this.logger.error(`Error getting module completion rates: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get module completion rates');
    }
  }

  /**
   * Get completion data for a specific user and module
   */
  private async getUserModuleCompletion(user: User, module: PlanFeature): Promise<UserModuleCompletionDto> {
    let totalSubmissions = 0;
    let confirmedSubmissions = 0;

    switch (module.type) {
      case FeatureType.HEC_USER_DIARY:
        const diaryData = await this.getUserDiaryCompletion(user.id);
        totalSubmissions = diaryData.total;
        confirmedSubmissions = diaryData.confirmed;
        break;

      case FeatureType.ENGLISH_NOVEL:
        const novelData = await this.getUserNovelCompletion(user.id);
        totalSubmissions = novelData.total;
        confirmedSubmissions = novelData.confirmed;
        break;

      case FeatureType.ENGLISH_QA_WRITING:
        const qaData = await this.getUserQACompletion(user.id);
        totalSubmissions = qaData.total;
        confirmedSubmissions = qaData.confirmed;
        break;

      case FeatureType.ENGLISH_ESSAY:
        const essayData = await this.getUserEssayCompletion(user.id);
        totalSubmissions = essayData.total;
        confirmedSubmissions = essayData.confirmed;
        break;
    }

    const completionRate = totalSubmissions > 0 ? (confirmedSubmissions / totalSubmissions) * 100 : 0;

    return {
      userId: user.id,
      userName: user.name,
      userEmail: user.email,
      moduleType: module.type,
      moduleName: module.name,
      totalSubmissions,
      confirmedSubmissions,
      completionRate: Math.round(completionRate * 100) / 100,
    };
  }

  /**
   * Get user's diary completion data
   */
  private async getUserDiaryCompletion(userId: string): Promise<{ total: number; confirmed: number }> {
    // Regular diary entries
    const diaryTotal = await this.diaryEntryRepository.count({
      where: { diary: { userId } },
    });
    const diaryConfirmed = await this.diaryEntryRepository.count({
      where: { diary: { userId }, status: DiaryEntryStatus.CONFIRM },
    });

    // Mission diary entries
    const missionTotal = await this.missionDiaryEntryRepository.count({
      where: { studentId: userId },
    });
    const missionConfirmed = await this.missionDiaryEntryRepository.count({
      where: { studentId: userId, status: MissionEntryStatus.CONFIRMED },
    });

    return {
      total: diaryTotal + missionTotal,
      confirmed: diaryConfirmed + missionConfirmed,
    };
  }

  /**
   * Get user's novel completion data
   */
  private async getUserNovelCompletion(userId: string): Promise<{ total: number; confirmed: number }> {
    const total = await this.novelEntryRepository.count({
      where: { studentId: userId },
    });
    const confirmed = await this.novelEntryRepository.count({
      where: { studentId: userId, status: NovelEntryStatus.CONFIRMED },
    });

    return { total, confirmed };
  }

  /**
   * Get user's QA completion data
   */
  private async getUserQACompletion(userId: string): Promise<{ total: number; confirmed: number }> {
    const total = await this.qaTaskSubmissionsRepository.createQueryBuilder('submission').where('submission.createdBy = :userId', { userId }).getCount();

    const confirmed = await this.qaTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .where('submission.createdBy = :userId', { userId })
      .andWhere('submission.status = :status', { status: QASubmissionStatus.REVIEWED })
      .getCount();

    return { total, confirmed };
  }

  /**
   * Get user's essay completion data
   */
  private async getUserEssayCompletion(userId: string): Promise<{ total: number; confirmed: number }> {
    const total = await this.essayTaskSubmissionsRepository.createQueryBuilder('submission').where('submission.createdBy = :userId', { userId }).getCount();

    const confirmed = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .where('submission.createdBy = :userId', { userId })
      .andWhere('submission.status = :status', { status: EssaySubmissionStatus.REVIEWED })
      .getCount();

    return { total, confirmed };
  }

  /**
   * Get student daily activity performance for current year only
   */
  async getStudentDailyActivity(): Promise<StudentDailyActivityResponseDto> {
    try {
      // Get current year only
      const currentYear = new Date().getFullYear();
      this.logger.log(`Fetching student daily activity for current year: ${currentYear}`);

      // Get stats for current year only
      const yearStats = await this.getYearlyActivityStats(currentYear);
      const yearlyStats: YearlyActivityStatsDto[] = [yearStats];

      return {
        yearlyStats,
        totalYears: 1, // Always 1 since we only return current year
        grandTotalActivities: yearStats.totalActivities,
      };
    } catch (error) {
      this.logger.error(`Error getting student daily activity: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get years that have activity data
   */
  private async getYearsWithActivityData(): Promise<number[]> {
    const years = new Set<number>();

    // Get years from diary entries
    const diaryYears = await this.diaryEntryRepository
      .createQueryBuilder('entry')
      .select('EXTRACT(YEAR FROM entry.createdAt)', 'year')
      .distinct(true)
      .getRawMany();
    diaryYears.forEach(row => years.add(parseInt(row.year)));

    // Get years from QA submissions
    const qaYears = await this.qaTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .select('EXTRACT(YEAR FROM submission.createdAt)', 'year')
      .distinct(true)
      .getRawMany();
    qaYears.forEach(row => years.add(parseInt(row.year)));

    // Get years from essay submissions
    const essayYears = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .select('EXTRACT(YEAR FROM submission.createdAt)', 'year')
      .distinct(true)
      .getRawMany();
    essayYears.forEach(row => years.add(parseInt(row.year)));

    // Get years from novel entries
    const novelYears = await this.novelEntryRepository
      .createQueryBuilder('entry')
      .select('EXTRACT(YEAR FROM entry.createdAt)', 'year')
      .distinct(true)
      .getRawMany();
    novelYears.forEach(row => years.add(parseInt(row.year)));

    // Get years from block game attempts
    const gameYears = await this.blockGameAttemptRepository
      .createQueryBuilder('attempt')
      .select('EXTRACT(YEAR FROM attempt.submittedAt)', 'year')
      .distinct(true)
      .getRawMany();
    gameYears.forEach(row => years.add(parseInt(row.year)));

    // Get years from story maker participations
    const storyYears = await this.storyMakerParticipationRepository
      .createQueryBuilder('participation')
      .select('EXTRACT(YEAR FROM participation.createdAt)', 'year')
      .distinct(true)
      .getRawMany();
    storyYears.forEach(row => years.add(parseInt(row.year)));

    return Array.from(years).sort((a, b) => b - a); // Sort descending (newest first)
  }

  /**
   * Get activity stats for a specific year
   */
  private async getYearlyActivityStats(year: number): Promise<YearlyActivityStatsDto> {
    const monthlyStats: MonthlyActivityStatsDto[] = [];
    let totalDiaryEntries = 0;
    let totalQaSubmissions = 0;
    let totalEssaySubmissions = 0;
    let totalNovelEntries = 0;
    let totalGameAttempts = 0;
    let totalStoryMakerParticipations = 0;
    const activeStudentsSet = new Set<string>();

    // Get stats for each month
    for (let month = 1; month <= 12; month++) {
      const monthStats = await this.getMonthlyActivityStats(year, month);
      monthlyStats.push(monthStats);

      totalDiaryEntries += monthStats.diaryEntries;
      totalQaSubmissions += monthStats.qaSubmissions;
      totalEssaySubmissions += monthStats.essaySubmissions;
      totalNovelEntries += monthStats.novelEntries;
      totalGameAttempts += monthStats.gameAttempts;
      totalStoryMakerParticipations += monthStats.storyMakerParticipations;
    }

    // Get unique active students for the year
    const yearActiveStudents = await this.getYearlyActiveStudents(year);

    return {
      year,
      monthlyStats,
      totalDiaryEntries,
      totalQaSubmissions,
      totalEssaySubmissions,
      totalNovelEntries,
      totalGameAttempts,
      totalStoryMakerParticipations,
      totalActivities: totalDiaryEntries + totalQaSubmissions + totalEssaySubmissions +
                     totalNovelEntries + totalGameAttempts + totalStoryMakerParticipations,
      totalActiveStudents: yearActiveStudents,
    };
  }

  /**
   * Get activity stats for a specific month
   */
  private async getMonthlyActivityStats(year: number, month: number): Promise<MonthlyActivityStatsDto> {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0, 23, 59, 59, 999);

    // Get diary entries count
    const diaryEntries = await this.diaryEntryRepository
      .createQueryBuilder('entry')
      .where('entry.createdAt >= :startDate AND entry.createdAt <= :endDate', { startDate, endDate })
      .getCount();

    // Get QA submissions count
    const qaSubmissions = await this.qaTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .where('submission.createdAt >= :startDate AND submission.createdAt <= :endDate', { startDate, endDate })
      .getCount();

    // Get essay submissions count
    const essaySubmissions = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .where('submission.createdAt >= :startDate AND submission.createdAt <= :endDate', { startDate, endDate })
      .getCount();

    // Get novel entries count
    const novelEntries = await this.novelEntryRepository
      .createQueryBuilder('entry')
      .where('entry.createdAt >= :startDate AND entry.createdAt <= :endDate', { startDate, endDate })
      .getCount();

    // Get game attempts count
    const gameAttempts = await this.blockGameAttemptRepository
      .createQueryBuilder('attempt')
      .where('attempt.submittedAt >= :startDate AND attempt.submittedAt <= :endDate', { startDate, endDate })
      .getCount();

    // Get story maker participations count
    const storyMakerParticipations = await this.storyMakerParticipationRepository
      .createQueryBuilder('participation')
      .where('participation.createdAt >= :startDate AND participation.createdAt <= :endDate', { startDate, endDate })
      .getCount();

    // Get unique active students for the month
    const activeStudents = await this.getMonthlyActiveStudents(year, month);

    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    return {
      month,
      monthName: monthNames[month - 1],
      diaryEntries,
      qaSubmissions,
      essaySubmissions,
      novelEntries,
      gameAttempts,
      storyMakerParticipations,
      totalActivities: diaryEntries + qaSubmissions + essaySubmissions +
                      novelEntries + gameAttempts + storyMakerParticipations,
      activeStudents,
    };
  }

  /**
   * Get unique active students for a specific month
   */
  private async getMonthlyActiveStudents(year: number, month: number): Promise<number> {
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0, 23, 59, 59, 999);
    const activeStudentsSet = new Set<string>();

    // Get students from diary entries
    const diaryStudents = await this.diaryEntryRepository
      .createQueryBuilder('entry')
      .select('entry.createdBy', 'studentId')
      .where('entry.createdAt >= :startDate AND entry.createdAt <= :endDate', { startDate, endDate })
      .distinct(true)
      .getRawMany();
    diaryStudents.forEach(row => activeStudentsSet.add(row.studentId));

    // Get students from QA submissions
    const qaStudents = await this.qaTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .select('submission.createdBy', 'studentId')
      .where('submission.createdAt >= :startDate AND submission.createdAt <= :endDate', { startDate, endDate })
      .distinct(true)
      .getRawMany();
    qaStudents.forEach(row => activeStudentsSet.add(row.studentId));

    // Get students from essay submissions
    const essayStudents = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .select('submission.createdBy', 'studentId')
      .where('submission.createdAt >= :startDate AND submission.createdAt <= :endDate', { startDate, endDate })
      .distinct(true)
      .getRawMany();
    essayStudents.forEach(row => activeStudentsSet.add(row.studentId));

    // Get students from novel entries
    const novelStudents = await this.novelEntryRepository
      .createQueryBuilder('entry')
      .select('entry.createdBy', 'studentId')
      .where('entry.createdAt >= :startDate AND entry.createdAt <= :endDate', { startDate, endDate })
      .distinct(true)
      .getRawMany();
    novelStudents.forEach(row => activeStudentsSet.add(row.studentId));

    // Get students from game attempts
    const gameStudents = await this.blockGameAttemptRepository
      .createQueryBuilder('attempt')
      .select('attempt.studentId', 'studentId')
      .where('attempt.submittedAt >= :startDate AND attempt.submittedAt <= :endDate', { startDate, endDate })
      .distinct(true)
      .getRawMany();
    gameStudents.forEach(row => activeStudentsSet.add(row.studentId));

    // Get students from story maker participations
    const storyStudents = await this.storyMakerParticipationRepository
      .createQueryBuilder('participation')
      .select('participation.studentId', 'studentId')
      .where('participation.createdAt >= :startDate AND participation.createdAt <= :endDate', { startDate, endDate })
      .distinct(true)
      .getRawMany();
    storyStudents.forEach(row => activeStudentsSet.add(row.studentId));

    return activeStudentsSet.size;
  }

  /**
   * Get unique active students for a specific year
   */
  private async getYearlyActiveStudents(year: number): Promise<number> {
    const startDate = new Date(year, 0, 1);
    const endDate = new Date(year, 11, 31, 23, 59, 59, 999);
    const activeStudentsSet = new Set<string>();

    // Get students from all activity types for the year
    const diaryStudents = await this.diaryEntryRepository
      .createQueryBuilder('entry')
      .select('entry.createdBy', 'studentId')
      .where('entry.createdAt >= :startDate AND entry.createdAt <= :endDate', { startDate, endDate })
      .distinct(true)
      .getRawMany();
    diaryStudents.forEach(row => activeStudentsSet.add(row.studentId));

    const qaStudents = await this.qaTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .select('submission.createdBy', 'studentId')
      .where('submission.createdAt >= :startDate AND submission.createdAt <= :endDate', { startDate, endDate })
      .distinct(true)
      .getRawMany();
    qaStudents.forEach(row => activeStudentsSet.add(row.studentId));

    const essayStudents = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .select('submission.createdBy', 'studentId')
      .where('submission.createdAt >= :startDate AND submission.createdAt <= :endDate', { startDate, endDate })
      .distinct(true)
      .getRawMany();
    essayStudents.forEach(row => activeStudentsSet.add(row.studentId));

    const novelStudents = await this.novelEntryRepository
      .createQueryBuilder('entry')
      .select('entry.createdBy', 'studentId')
      .where('entry.createdAt >= :startDate AND entry.createdAt <= :endDate', { startDate, endDate })
      .distinct(true)
      .getRawMany();
    novelStudents.forEach(row => activeStudentsSet.add(row.studentId));

    const gameStudents = await this.blockGameAttemptRepository
      .createQueryBuilder('attempt')
      .select('attempt.studentId', 'studentId')
      .where('attempt.submittedAt >= :startDate AND attempt.submittedAt <= :endDate', { startDate, endDate })
      .distinct(true)
      .getRawMany();
    gameStudents.forEach(row => activeStudentsSet.add(row.studentId));

    const storyStudents = await this.storyMakerParticipationRepository
      .createQueryBuilder('participation')
      .select('participation.studentId', 'studentId')
      .where('participation.createdAt >= :startDate AND participation.createdAt <= :endDate', { startDate, endDate })
      .distinct(true)
      .getRawMany();
    storyStudents.forEach(row => activeStudentsSet.add(row.studentId));

    return activeStudentsSet.size;
  }
}
