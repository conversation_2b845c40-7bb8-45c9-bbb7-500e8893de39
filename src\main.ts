import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { initializeDatabase } from './config/database-initializer';
import { ValidationPipe, BadRequestException, ValidationError } from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as fs from 'fs';
import * as path from 'path';
import LoggerService from './common/services/logger.service';
import { AllExceptionsFilter } from './common/filters/all-exceptions.filter';
import { ApiResponse, ErrorDetail } from './common/dto/api-response.dto';
import { ResponseTransformerInterceptor } from './common/interceptors/response-transformer.interceptor';

async function bootstrap() {
  // Add global unhandled promise rejection handler
  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Promise Rejection at:', promise, 'reason:', reason);
    // Log the error but don't exit the process in production
    if (process.env.NODE_ENV !== 'production') {
      console.error('Stack trace:', reason instanceof Error ? reason.stack : 'No stack trace available');
    }
  });

  // Add global uncaught exception handler
  process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    console.error('Stack trace:', error.stack);
    // Exit the process for uncaught exceptions
    process.exit(1);
  });

  // Create logs directory if it doesn't exist
  const logDirectory = process.env.LOG_PATH || './logs';
  if (!fs.existsSync(logDirectory)) {
    fs.mkdirSync(logDirectory, { recursive: true });
  }

  // Create uploads directory if it doesn't exist
  const uploadsDirectory = process.env.UPLOAD_DIR || 'uploads';
  if (!fs.existsSync(uploadsDirectory)) {
    fs.mkdirSync(uploadsDirectory, { recursive: true });
  }

  // Create profile pictures directory if it doesn't exist
  const profilePicturesDir = path.join(uploadsDirectory, 'profile-pictures');
  if (!fs.existsSync(profilePicturesDir)) {
    fs.mkdirSync(profilePicturesDir, { recursive: true });
  }

  // Create diary covers directory if it doesn't exist
  const diaryCoversDir = path.join(uploadsDirectory, 'diary-covers');
  if (!fs.existsSync(diaryCoversDir)) {
    fs.mkdirSync(diaryCoversDir, { recursive: true });
  }

  // Create a custom logger instance
  const logger = new LoggerService();

  // Initialize the database
  await initializeDatabase();

  // Create the application with the custom logger
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger,
    bufferLogs: true,
  });

  // Configure static file serving
  app.useStaticAssets(path.join(__dirname, '..', process.env.UPLOAD_DIR || 'uploads'), {
    prefix: '/uploads/',
  });

  // Configure static file serving for documentation
  app.useStaticAssets(path.join(__dirname, '..', 'docs'), {
    prefix: '/docs/',
    index: 'index.html', // Use index.html as the default file
  });

  // Apply global validation pipe with detailed error messages
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // Strip properties that do not have any decorators
      forbidNonWhitelisted: true, // Throw errors if non-whitelisted properties are present
      transform: true, // Automatically transform payloads to be objects typed according to their DTO classes
      transformOptions: {
        enableImplicitConversion: true, // Automatically convert primitive types
      },
      exceptionFactory: (errors: ValidationError[]) => {
        const formattedErrors = buildErrorMap(errors);

        return new BadRequestException({
          message: 'Validation failed',
          validationErrors: formattedErrors,
        });
      },
    }),
  );
  function buildErrorMap(errors: ValidationError[], parentPath = '', result: Record<string, string[]> = {}): Record<string, string[]> {
    for (const error of errors) {
      const path = parentPath ? (/^\d+$/.test(error.property) ? `${parentPath}[${error.property}]` : `${parentPath}.${error.property}`) : error.property;
      if (error.constraints) {
        result[path] = Object.values(error.constraints);
      }

      if (error.children && error.children.length > 0) {
        buildErrorMap(error.children, path, result);
      }
    }

    return result;
  }
  // Apply global exception filter for consistent error responses
  app.useGlobalFilters(new AllExceptionsFilter());

  // Apply global interceptor to transform successful responses
  app.useGlobalInterceptors(new ResponseTransformerInterceptor());

  // Enable CORS for specific HTTP origins

  const allowedOrigins = ['http://**************:3012', 'http://**************:3011', 'http://localhost:3012', 'http://localhost:3011', 'localhost', 'http://127.0.0.1:3012', 'http://127.0.0.1:3011'];

  // For development, use a more permissive CORS configuration
  app.enableCors({
    origin: true, // Allow all origins for development
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    credentials: true,
    allowedHeaders: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization', 'Access-Control-Allow-Origin'],
    exposedHeaders: ['Content-Disposition'],
  });

  // Log CORS configuration
  logger.log('CORS enabled for specific origins');
  logger.log(`Configured origins (for reference): ${allowedOrigins.join(', ')}`);

  // OpenAPI Configuration
  const config = new DocumentBuilder()
    .setTitle('HEC API')
    .setDescription('API Documentation for HEC Project. This API provides endpoints for authentication, user management, subscription plans, and the HEC Diary module for students and tutors.')
    .setVersion('1.0')
    .setContact('HEC Support', 'http://**************:1080', '<EMAIL>')
    .addServer(process.env.API_URL || 'http://**************:3012', 'Development Server')
    .addServer(process.env.API_URL_DEV_LOCAL || 'http://localhost:3010', 'Local Dev Server')
    .addServer(process.env.API_URL_LOCAL || 'http://localhost:3012', 'Local StagingServer')
    // .addServer(process.env.API_URL_VS_CODE || 'http://localhost:3012', 'VS Code Server')
    .addTag('auth', 'Authentication endpoints for registration, login, and password management')
    .addTag('Users', 'User management endpoints for creating and managing users')
    .addTag('plan-features', 'Subscription plan features management endpoints')
    .addTag('plans', 'Subscription plan management endpoints')
    .addTag('diary', 'Student diary management endpoints for creating, updating, and sharing diary entries')
    .addTag('tutor-diary', 'Tutor diary management endpoints for reviewing and providing feedback on student diary entries')
    .addTag('Tutor Approval', 'Admin-only endpoints for managing tutor approval requests. Only administrators can approve or reject tutor registrations.')
    .addTag('admin-essay', 'Essay mission management endpoints for creating, updating, and deleting essay missions')
    .addTag('student-essay', 'Student essay management endpoints for starting and submitting essay tasks')
    .addTag('tutor-essay', 'Tutor essay management endpoints for marking and reviewing student essays')
    .addTag('chat', 'Chat endpoints for realtime messaging between students and tutors')
    .addTag('admin-chat', 'Admin chat management endpoints')
    .addTag('Hall of Fame', 'Hall of Fame endpoints for viewing award winners, statistics, and ongoing achievements across all modules')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: 'Enter JWT token with Bearer prefix',
        in: 'header',
      },
      'JWT-auth',
    )
    .build();

  // Import necessary DTOs for Swagger
  const { UserPlanResponseDto } = require('./database/models/plans.dto');
  const { PlanFeatureResponseDto } = require('./database/models/plan-features.dto');
  const { SimplifiedPlanDto, SimplifiedFeatureDto } = require('./database/models/simplified-plan.dto');
  const { LoginResponseDto } = require('./database/models/auth-response.dto');
  const { ConversationDto, MessageDto, CreateMessageDto, PagedConversationListDto, PagedMessageListDto, ConversationParticipantDto, ChatFileUploadResponseDto } = require('./database/models/chat.dto');
  const { HallOfFameResponseDto, OngoingAwardsResponseDto, HallOfFameStatsDto, ModuleHallOfFameDto, HallOfFameWinnerDto, AwardTypeDto } = require('./database/models/hall-of-fame.dto');

  // Create Swagger document
  const document = SwaggerModule.createDocument(app, config, {
    extraModels: [
      ApiResponse,
      ErrorDetail,
      UserPlanResponseDto,
      PlanFeatureResponseDto,
      SimplifiedPlanDto,
      SimplifiedFeatureDto,
      LoginResponseDto,
      HallOfFameResponseDto,
      OngoingAwardsResponseDto,
      HallOfFameStatsDto,
      ModuleHallOfFameDto,
      HallOfFameWinnerDto,
    ],
  });

  // Setup Swagger UI with enhanced security and CORS handling
  SwaggerModule.setup('api-docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
      docExpansion: 'none',
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
      tryItOutEnabled: true,
      displayRequestDuration: true,
      securityDefinitions: {
        'JWT-auth': {
          type: 'apiKey',
          name: 'Authorization',
          in: 'header',
          description: 'Enter JWT token with Bearer prefix',
        },
      },
      withCredentials: false, // Disable for HTTP-only setup to avoid CORS issues
      supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch', 'options'],
      onComplete: (_swaggerApi: any, _swaggerUi: any) => {
        // Log when Swagger UI is fully loaded
        console.log('Swagger UI loaded successfully');
      },
    },
    customSiteTitle: 'HEC API Documentation',
    customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .auth-wrapper .authorize {
        background-color: #49cc90;
        border-color: #49cc90;
        color: #fff;
        font-weight: bold;
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(73, 204, 144, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(73, 204, 144, 0); }
        100% { box-shadow: 0 0 0 0 rgba(73, 204, 144, 0); }
      }
    `,
    customfavIcon: 'https://nestjs.com/img/logo_text.svg',
  });

  // Log application startup with detailed information
  const port = process.env.PORT || 3012 || 3014;
  const apiUrl = process.env.API_URL || `http://localhost:${port}`;

  logger.log('=== HEC API Server Configuration ===');
  logger.log(`Port: ${port}`);
  logger.log(`API URL: ${apiUrl}`);
  logger.log(`Swagger documentation: ${apiUrl}/api-docs`);
  logger.log(`Markdown documentation: ${apiUrl}/docs/view-markdown.html`);
  logger.log(`JWT Secret configured: ${!!process.env.JWT_SECRET}`);
  logger.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  logger.log('====================================');

  await app.listen(port, '0.0.0.0', () => {
    logger.log(`Server is running and listening on port ${port}`);
  });
}

bootstrap().catch((error) => {
  // Create a logger instance for error handling
  const errorLogger = new LoggerService();
  errorLogger.error('Application failed to start', error.stack, 'Bootstrap');

  // Also log to console for visibility
  console.error('Application failed to start:', error);

  // Exit with error code
  process.exit(1);
});
