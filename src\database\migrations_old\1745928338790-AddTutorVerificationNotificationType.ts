import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTutorVerificationNotificationType1745928338790 implements MigrationInterface {
  name = 'AddTutorVerificationNotificationType1745928338790';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" RENAME TO "notification_type_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."notification_type_enum" AS ENUM('diary_submission', 'diary_review', 'diary_feedback', 'tutor_assignment', 'tutor_verification', 'system')`);
    await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "type" TYPE "public"."notification_type_enum" USING "type"::"text"::"public"."notification_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."notification_type_enum_old"`);
    await queryRunner.query(`ALTER TABLE "user_notification_preference" DROP CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2"`);
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" RENAME TO "user_notification_preference_notification_type_enum_old"`);
    await queryRunner.query(
      `CREATE TYPE "public"."user_notification_preference_notification_type_enum" AS ENUM('diary_submission', 'diary_review', 'diary_feedback', 'tutor_assignment', 'tutor_verification', 'system')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_notification_preference" ALTER COLUMN "notification_type" TYPE "public"."user_notification_preference_notification_type_enum" USING "notification_type"::"text"::"public"."user_notification_preference_notification_type_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."user_notification_preference_notification_type_enum_old"`);
    await queryRunner.query(`ALTER TABLE "user_notification_preference" ADD CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2" UNIQUE ("user_id", "notification_type", "channel")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user_notification_preference" DROP CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2"`);
    await queryRunner.query(
      `CREATE TYPE "public"."user_notification_preference_notification_type_enum_old" AS ENUM('diary_submission', 'diary_review', 'diary_feedback', 'tutor_assignment', 'system')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_notification_preference" ALTER COLUMN "notification_type" TYPE "public"."user_notification_preference_notification_type_enum_old" USING "notification_type"::"text"::"public"."user_notification_preference_notification_type_enum_old"`,
    );
    await queryRunner.query(`DROP TYPE "public"."user_notification_preference_notification_type_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum_old" RENAME TO "user_notification_preference_notification_type_enum"`);
    await queryRunner.query(`ALTER TABLE "user_notification_preference" ADD CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2" UNIQUE ("user_id", "notification_type", "channel")`);
    await queryRunner.query(`CREATE TYPE "public"."notification_type_enum_old" AS ENUM('diary_submission', 'diary_review', 'diary_feedback', 'tutor_assignment', 'system')`);
    await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "type" TYPE "public"."notification_type_enum_old" USING "type"::"text"::"public"."notification_type_enum_old"`);
    await queryRunner.query(`DROP TYPE "public"."notification_type_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum_old" RENAME TO "notification_type_enum"`);
  }
}
