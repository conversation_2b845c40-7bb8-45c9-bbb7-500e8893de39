import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { UserType } from '../../database/entities/user.entity';
import { Messages } from '../../constants/messages';

@Injectable()
export class StudentTutorGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException(Messages.UNAUTHORIZED);
    }

    // Check if user is student or tutor
    const isStudent = user.type === UserType.STUDENT || (user.roles && Array.isArray(user.roles) && user.roles.includes('student'));
    const isTutor = user.type === UserType.TUTOR || (user.roles && Array.isArray(user.roles) && user.roles.includes('tutor'));
    const isAdmin = user.type === UserType.ADMIN || (user.roles && Array.isArray(user.roles) && user.roles.includes('admin'));

    if (isStudent || isTutor || isAdmin) {
      return true;
    }

    throw new ForbiddenException(Messages.FORBIDDEN);
  }
}
