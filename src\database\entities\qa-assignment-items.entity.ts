import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, Join<PERSON><PERSON>umn, OneToOne } from 'typeorm';
import { QAAssignmentSets } from './qa-assignment-sets.entity';
import { AuditableBaseEntity } from './base-entity';
import { QAQuestion } from './qa-question.entity';
import { User } from './user.entity';
import { QAAssignmentStatus } from './qa-assignment.entity';
import { QASubmission } from './qa-submission.entity';

@Entity('qa-assignment-items')
export class QAAssignmentItems extends AuditableBaseEntity {
  @Column({ name: 'question_id' })
  questionId: string;

  @ManyToOne(() => QAQuestion)
  @JoinColumn({ name: 'question_id' })
  question: QAQuestion;

  @Column({ name: 'student_id' })
  studentId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  @Column({ nullable: true })
  score: number;

  @Column({ type: 'timestamp', nullable: true })
  deadline: Date;

  @Column()
  setSequence: number;

  @ManyToOne(() => QAAssignmentSets, { eager: true })
  @JoinColumn({ name: 'setSequence', referencedColumnName: 'setSequence' })
  assignmentSet: QAAssignmentSets;

  @Column({
    type: 'enum',
    enum: QAAssignmentStatus,
    default: QAAssignmentStatus.ASSIGNED,
  })
  status: QAAssignmentStatus;

  @Column({ name: 'assigned_date', type: 'timestamp' })
  assignedDate: Date;

  @OneToOne(() => QASubmission, (submission) => submission.assignment)
  submission: QASubmission;
}
