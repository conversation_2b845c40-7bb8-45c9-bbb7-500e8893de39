import { Injectable, NotFoundException, ConflictException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TutorPermission } from '../../database/entities/tutor-permission.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { CreateTutorPermissionDto, UpdateTutorPermissionDto, TutorPermissionResponseDto, TutorPermissionPaginationDto } from '../../database/models/tutor-permission.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';

@Injectable()
export class TutorPermissionService {
  private readonly logger = new Logger(TutorPermissionService.name);

  constructor(
    @InjectRepository(TutorPermission)
    private tutorPermissionRepository: Repository<TutorPermission>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(PlanFeature)
    private planFeatureRepository: Repository<PlanFeature>,
  ) {}

  /**
   * Create a new tutor permission
   * @param createTutorPermissionDto DTO with tutor ID, plan feature ID, and optional notes
   * @param adminId ID of the admin creating the permission
   * @returns The created tutor permission
   */
  async create(createTutorPermissionDto: CreateTutorPermissionDto, adminId: string): Promise<TutorPermissionResponseDto> {
    this.logger.log(`Creating tutor permission for tutor ${createTutorPermissionDto.tutorId} for plan feature ${createTutorPermissionDto.planFeatureId} by admin ${adminId}`);
    const { tutorId, planFeatureId, notes } = createTutorPermissionDto;

    // Check if tutor exists and is actually a tutor
    const tutor = await this.userRepository.findOne({
      where: { id: tutorId },
    });

    if (!tutor) {
      this.logger.warn(`Tutor with ID ${tutorId} not found`);
      throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
    }

    // Check if user is a tutor
    if (tutor.type !== UserType.TUTOR) {
      this.logger.warn(`User with ID ${tutorId} is not a tutor`);
      throw new BadRequestException(`User with ID ${tutorId} is not a tutor`);
    }

    // Check if plan feature exists
    const planFeature = await this.planFeatureRepository.findOne({
      where: { id: planFeatureId },
    });

    if (!planFeature) {
      this.logger.warn(`Plan feature with ID ${planFeatureId} not found`);
      throw new NotFoundException(`Plan feature with ID ${planFeatureId} not found`);
    }

    // Check if permission already exists
    const existingPermission = await this.tutorPermissionRepository.findOne({
      where: { tutorId, planFeatureId },
    });

    if (existingPermission) {
      // If permission exists but is inactive, reactivate it
      if (!existingPermission.isActive) {
        existingPermission.isActive = true;
        existingPermission.notes = notes || existingPermission.notes;
        existingPermission.grantedBy = adminId;

        // Get admin user information
        const admin = await this.userRepository.findOne({
          where: { id: adminId },
        });

        if (!admin) {
          this.logger.warn(`Admin with ID ${adminId} not found`);
        }

        const savedPermission = await this.tutorPermissionRepository.save(existingPermission);
        return this.transformToResponseDto(savedPermission, tutor, planFeature, admin);
      }

      throw new ConflictException(`Tutor with ID ${tutorId} already has permission for plan feature with ID ${planFeatureId}`);
    }

    // Get admin user information
    const admin = await this.userRepository.findOne({
      where: { id: adminId },
    });

    if (!admin) {
      this.logger.warn(`Admin with ID ${adminId} not found`);
    }

    // Create new permission
    const permission = this.tutorPermissionRepository.create({
      tutorId,
      planFeatureId,
      grantedBy: adminId,
      notes,
      isActive: true,
    });

    const savedPermission = await this.tutorPermissionRepository.save(permission);
    return this.transformToResponseDto(savedPermission, tutor, planFeature, admin);
  }

  /**
   * Update an existing tutor permission
   * @param id Permission ID
   * @param updateTutorPermissionDto DTO with updated fields
   * @returns The updated tutor permission
   */
  async update(id: string, updateTutorPermissionDto: UpdateTutorPermissionDto): Promise<TutorPermissionResponseDto> {
    const permission = await this.tutorPermissionRepository.findOne({
      where: { id },
      relations: ['tutor', 'planFeature'],
    });

    if (!permission) {
      throw new NotFoundException(`Tutor permission with ID ${id} not found`);
    }

    // Update fields
    if (updateTutorPermissionDto.isActive !== undefined) {
      permission.isActive = updateTutorPermissionDto.isActive;
    }

    if (updateTutorPermissionDto.notes !== undefined) {
      permission.notes = updateTutorPermissionDto.notes;
    }

    const savedPermission = await this.tutorPermissionRepository.save(permission);

    return this.transformToResponseDto(savedPermission, permission.tutor, permission.planFeature);
  }

  /**
   * Get all tutor permissions with pagination
   * @param paginationDto Pagination parameters
   * @returns Paged list of tutor permissions
   */
  async findAll(paginationDto?: TutorPermissionPaginationDto): Promise<PagedListDto<TutorPermissionResponseDto>> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC', isActive, planFeatureId, searchTerm } = paginationDto || {};

    // Build query
    const queryBuilder = this.tutorPermissionRepository
      .createQueryBuilder('permission')
      .leftJoinAndSelect('permission.tutor', 'tutor')
      .leftJoinAndSelect('permission.planFeature', 'planFeature')
      .leftJoinAndSelect('permission.admin', 'admin');

    // Apply filters
    if (isActive !== undefined) {
      queryBuilder.andWhere('permission.isActive = :isActive', { isActive });
    }

    if (planFeatureId) {
      queryBuilder.andWhere('permission.planFeatureId = :planFeatureId', { planFeatureId });
    }

    if (searchTerm) {
      queryBuilder.andWhere('(tutor.name ILIKE :searchTerm OR tutor.email ILIKE :searchTerm)', { searchTerm: `%${searchTerm}%` });
    }

    // Apply sorting
    queryBuilder.orderBy(`permission.${sortBy}`, sortDirection);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Execute query
    const [permissions, totalItems] = await queryBuilder.getManyAndCount();

    // Transform to response DTOs
    const items = permissions.map((permission) => this.transformToResponseDto(permission, permission.tutor, permission.planFeature, permission.admin));

    return new PagedListDto(items, totalItems, page, limit);
  }

  /**
   * Get a specific tutor permission by ID
   * @param id Permission ID
   * @returns The tutor permission
   */
  async findOne(id: string): Promise<TutorPermissionResponseDto> {
    const permission = await this.tutorPermissionRepository.findOne({
      where: { id },
      relations: ['tutor', 'planFeature', 'admin'],
    });

    if (!permission) {
      throw new NotFoundException(`Tutor permission with ID ${id} not found`);
    }

    return this.transformToResponseDto(permission, permission.tutor, permission.planFeature, permission.admin);
  }

  /**
   * Delete a tutor permission
   * @param id Permission ID
   */
  async remove(id: string): Promise<void> {
    const permission = await this.tutorPermissionRepository.findOne({
      where: { id },
    });

    if (!permission) {
      throw new NotFoundException(`Tutor permission with ID ${id} not found`);
    }

    await this.tutorPermissionRepository.remove(permission);
  }

  /**
   * Check if a tutor has permission for a specific plan feature
   * @param tutorId Tutor ID
   * @param planFeatureId Plan feature ID
   * @returns True if the tutor has permission, false otherwise
   */
  async hasTutorPermission(tutorId: string, planFeatureId: string): Promise<boolean> {
    const permission = await this.tutorPermissionRepository.findOne({
      where: {
        tutorId,
        planFeatureId,
        isActive: true,
      },
    });

    return !!permission;
  }

  /**
   * Transform a tutor permission entity to a response DTO
   * @param permission Tutor permission entity
   * @param tutor Tutor entity
   * @param planFeature Plan feature entity
   * @param admin Admin entity
   * @returns Tutor permission response DTO
   */
  private transformToResponseDto(permission: TutorPermission, tutor?: User, planFeature?: PlanFeature, admin?: User): TutorPermissionResponseDto {
    return {
      id: permission.id,
      tutorId: permission.tutorId,
      tutorName: tutor?.name || 'Unknown',
      tutorEmail: tutor?.email || 'Unknown',
      planFeatureId: permission.planFeatureId,
      planFeatureName: planFeature?.name || 'Unknown',
      grantedBy: permission.grantedBy,
      grantedByName: admin?.name || 'Unknown',
      isActive: permission.isActive,
      notes: permission.notes,
      createdAt: permission.createdAt,
      updatedAt: permission.updatedAt,
    };
  }
}
