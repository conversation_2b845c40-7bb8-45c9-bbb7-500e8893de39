import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminDashboardService } from './admin-dashboard.service';
import { TutorDashboardService } from './tutor-dashboard.service';
import { User, UserType } from '../../database/entities/user.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { Plan } from '../../database/entities/plan.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { StudentTutorMapping } from '../../database/entities/student-tutor-mapping.entity';
import { DiaryEntryAttendance } from '../../database/entities/diary-entry-attendance.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { MissionDiaryEntry } from '../../database/entities/mission-diary-entry.entity';
import { NovelEntry } from '../../database/entities/novel-entry.entity';
import { QATaskSubmissions } from '../../database/entities/qa-task-submissions.entity';
import { QAMissionTasks } from '../../database/entities/qa-mission-tasks.entity';
import { EssayTaskSubmissions } from '../../database/entities/essay-task-submissions.entity';
import { EssayMissionTasks } from '../../database/entities/essay-mission-tasks.entity';
import { DiaryFeedback } from '../../database/entities/diary-feedback.entity';
import { DiaryCorrection } from '../../database/entities/diary-correction.entity';
import { MissionDiaryEntryFeedback } from '../../database/entities/mission-diary-entry-feedback.entity';
import { NovelFeedback } from '../../database/entities/novel-feedback.entity';
import { NovelCorrection } from '../../database/entities/novel-correction.entity';
import { QATaskSubmissionMarking } from '../../database/entities/qa-task-submission-marking.entity';
import { EssayTaskSubmissionMarking } from '../../database/entities/essay-task-submission-marking.entity';

describe('Dashboard Services', () => {
  let adminDashboardService: AdminDashboardService;
  let tutorDashboardService: TutorDashboardService;

  // Mock repositories
  const createMockQueryBuilder = () => ({
    select: jest.fn().mockReturnThis(),
    addSelect: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    groupBy: jest.fn().mockReturnThis(),
    getRawMany: jest.fn(),
    getRawOne: jest.fn(),
    getCount: jest.fn(),
    getMany: jest.fn(),
  });

  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    count: jest.fn(),
    createQueryBuilder: jest.fn(() => createMockQueryBuilder()),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminDashboardService,
        TutorDashboardService,
        {
          provide: getRepositoryToken(User),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(UserPlan),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(Plan),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(PlanFeature),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(StudentTutorMapping),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(DiaryEntryAttendance),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(DiaryEntry),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(MissionDiaryEntry),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(NovelEntry),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(QATaskSubmissions),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(QAMissionTasks),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(EssayTaskSubmissions),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(EssayMissionTasks),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(DiaryFeedback),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(DiaryCorrection),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(MissionDiaryEntryFeedback),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(NovelFeedback),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(NovelCorrection),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(QATaskSubmissionMarking),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(EssayTaskSubmissionMarking),
          useValue: mockRepository,
        },
      ],
    }).compile();

    adminDashboardService = module.get<AdminDashboardService>(AdminDashboardService);
    tutorDashboardService = module.get<TutorDashboardService>(TutorDashboardService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('AdminDashboardService', () => {
    it('should be defined', () => {
      expect(adminDashboardService).toBeDefined();
    });

    it('should get active student count', async () => {
      // Mock the repository to return the query builder
      const mockQueryBuilder = createMockQueryBuilder();
      mockQueryBuilder.getCount.mockResolvedValue(1250);
      jest.spyOn(mockRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder);

      const result = await adminDashboardService.getActiveStudentCount();

      expect(result).toEqual({ totalActiveStudents: 1250 });
    });

    it('should get today attendance stats', async () => {
      // Mock user repository for total active students
      const userQueryBuilder = createMockQueryBuilder();
      userQueryBuilder.getCount.mockResolvedValue(1250);

      // Mock attendance repository for attendance stats
      const attendanceQueryBuilder = createMockQueryBuilder();
      attendanceQueryBuilder.getRawMany.mockResolvedValue([
        { status: 'present', count: '850' },
        { status: 'absent', count: '200' }, // Only 200 have attendance records as absent
      ]);

      // Set up the mocks to return different query builders for different calls
      jest.spyOn(mockRepository, 'createQueryBuilder')
        .mockReturnValueOnce(userQueryBuilder)  // First call for user count
        .mockReturnValueOnce(attendanceQueryBuilder); // Second call for attendance

      const result = await adminDashboardService.getTodayAttendanceStats();

      expect(result.presentCount).toBe(850);
      expect(result.absentCount).toBe(400); // 200 marked absent + 250 without attendance (1250 - 850 - 200)
      expect(result.totalStudents).toBe(1250);
      expect(result.attendancePercentage).toBe(68); // 850/1250 * 100
    });
  });

  describe('TutorDashboardService', () => {
    it('should be defined', () => {
      expect(tutorDashboardService).toBeDefined();
    });

    it('should get assigned student count for tutor', async () => {
      // Mock the repository to return the query builder
      const mockQueryBuilder = createMockQueryBuilder();
      mockQueryBuilder.getRawOne.mockResolvedValue({ count: '25' });
      jest.spyOn(mockRepository, 'createQueryBuilder').mockReturnValue(mockQueryBuilder);

      const result = await tutorDashboardService.getAssignedStudentCount('tutor-id');

      expect(result).toEqual({ totalAssignedStudents: 25 });
    });
  });
});
