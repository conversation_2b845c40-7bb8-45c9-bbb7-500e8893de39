import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { RewardPointSetting } from '../../database/entities/reward-point-setting.entity';
import { CreateRewardPointSettingDto, UpdateRewardPointSettingDto, RewardPointSettingResponseDto } from '../../database/models/reward-point-setting.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';

@Injectable()
export class RewardPointSettingService {
  private readonly logger = new Logger(RewardPointSettingService.name);

  constructor(
    @InjectRepository(RewardPointSetting)
    private readonly rewardPointSettingRepository: Repository<RewardPointSetting>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Create a new reward point setting
   * @param createRewardPointSettingDto Reward point setting creation data
   * @returns Created reward point setting
   */
  async createRewardPointSetting(createRewardPointSettingDto: CreateRewardPointSettingDto): Promise<RewardPointSettingResponseDto> {
    try {
      // Log the raw DTO for debugging
      this.logger.log(`Raw DTO received: ${JSON.stringify(createRewardPointSettingDto)}`);

      // Validate required fields
      if (!createRewardPointSettingDto.name) {
        this.logger.error('Name is required but not provided');
        throw new BadRequestException('Name is required');
      }

      if (createRewardPointSettingDto.conversionRate === undefined || createRewardPointSettingDto.conversionRate === null) {
        this.logger.error('Conversion rate is required but not provided');
        throw new BadRequestException('Conversion rate is required');
      }

      // Ensure conversionRate is a number
      let conversionRate: number;
      if (typeof createRewardPointSettingDto.conversionRate === 'string') {
        conversionRate = parseFloat(createRewardPointSettingDto.conversionRate);
        if (isNaN(conversionRate)) {
          this.logger.error(`Invalid conversion rate: ${createRewardPointSettingDto.conversionRate}`);
          throw new BadRequestException('Conversion rate must be a valid number');
        }
      } else {
        conversionRate = createRewardPointSettingDto.conversionRate;
      }

      // Ensure isActive is a boolean
      let isActive: boolean = false;
      if (createRewardPointSettingDto.isActive !== undefined) {
        if (typeof createRewardPointSettingDto.isActive === 'string') {
          const isActiveStr = String(createRewardPointSettingDto.isActive).toLowerCase();
          isActive = isActiveStr === 'true';
        } else {
          isActive = !!createRewardPointSettingDto.isActive;
        }
      }

      // Log the processed data
      this.logger.log(`Processed data: name=${createRewardPointSettingDto.name}, description=${createRewardPointSettingDto.description}, conversionRate=${conversionRate}, isActive=${isActive}`);

      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Create the reward point setting
        const rewardPointSetting = new RewardPointSetting();
        rewardPointSetting.name = createRewardPointSettingDto.name;
        rewardPointSetting.description = createRewardPointSettingDto.description || null;
        rewardPointSetting.conversionRate = conversionRate;
        rewardPointSetting.isActive = isActive;
        rewardPointSetting.createdAt = new Date();
        rewardPointSetting.updatedAt = new Date();

        this.logger.log(`Entity created: ${JSON.stringify(rewardPointSetting)}`);

        // If this setting is active, deactivate all other settings
        if (rewardPointSetting.isActive) {
          this.logger.log('Setting is active, deactivating all other settings');
          await queryRunner.manager.update(RewardPointSetting, {}, { isActive: false });
        }

        // Save the reward point setting
        this.logger.log('Attempting to save reward point setting');
        const savedRewardPointSetting = await queryRunner.manager.save(RewardPointSetting, rewardPointSetting);
        this.logger.log(`Saved reward point setting with ID: ${savedRewardPointSetting.id}`);

        // Commit the transaction
        await queryRunner.commitTransaction();
        this.logger.log('Transaction committed successfully');

        const result = this.mapRewardPointSettingToDto(savedRewardPointSetting);
        this.logger.log(`Returning result: ${JSON.stringify(result)}`);
        return result;
      } catch (error) {
        // Rollback the transaction in case of error
        await queryRunner.rollbackTransaction();
        this.logger.error(`Transaction rolled back due to error: ${error.message}`, error.stack);
        throw error;
      } finally {
        // Release the query runner
        await queryRunner.release();
        this.logger.log('Query runner released');
      }
    } catch (error) {
      this.logger.error(`Error creating reward point setting: ${error.message}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Failed to create reward point setting: ${error.message}`);
    }
  }

  /**
   * Update a reward point setting
   * @param id Reward point setting ID
   * @param updateRewardPointSettingDto Reward point setting update data
   * @returns Updated reward point setting
   */
  async updateRewardPointSetting(id: string, updateRewardPointSettingDto: UpdateRewardPointSettingDto): Promise<RewardPointSettingResponseDto> {
    try {
      // Find the reward point setting
      const rewardPointSetting = await this.rewardPointSettingRepository.findOne({
        where: { id },
      });

      if (!rewardPointSetting) {
        throw new NotFoundException(`Reward point setting with ID ${id} not found`);
      }

      // Update the reward point setting
      const updatedRewardPointSetting = this.rewardPointSettingRepository.create({
        ...rewardPointSetting,
        ...updateRewardPointSettingDto,
      });

      // Save the updated reward point setting
      const savedRewardPointSetting = await this.rewardPointSettingRepository.save(updatedRewardPointSetting);

      return this.mapRewardPointSettingToDto(savedRewardPointSetting);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error updating reward point setting: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to update reward point setting');
    }
  }

  /**
   * Set a reward point setting as active
   * @param id Reward point setting ID
   * @returns Activated reward point setting
   */
  async setRewardPointSettingActive(id: string): Promise<RewardPointSettingResponseDto> {
    try {
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Find the reward point setting
        const rewardPointSetting = await queryRunner.manager.findOne(RewardPointSetting, {
          where: { id },
        });

        if (!rewardPointSetting) {
          throw new NotFoundException(`Reward point setting with ID ${id} not found`);
        }

        // Deactivate all reward point settings
        await queryRunner.manager.update(RewardPointSetting, {}, { isActive: false });

        // Activate the specified reward point setting
        rewardPointSetting.isActive = true;
        const savedRewardPointSetting = await queryRunner.manager.save(rewardPointSetting);

        // Commit the transaction
        await queryRunner.commitTransaction();

        return this.mapRewardPointSettingToDto(savedRewardPointSetting);
      } catch (error) {
        // Rollback the transaction in case of error
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        // Release the query runner
        await queryRunner.release();
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error setting reward point setting as active: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to set reward point setting as active');
    }
  }

  /**
   * Get all reward point settings
   * @param paginationDto Pagination options
   * @returns Paged list of reward point settings
   */
  async getRewardPointSettings(paginationDto: PaginationDto): Promise<PagedListDto<RewardPointSettingResponseDto>> {
    try {
      // Ensure pagination parameters are valid
      const page = paginationDto?.page || 1;
      const limit = paginationDto?.limit || 10;
      const sortBy = paginationDto?.sortBy || 'createdAt';
      const sortDirection = paginationDto?.sortDirection || 'DESC';

      // Log pagination parameters for debugging
      this.logger.log(`Getting reward point settings with pagination: page=${page}, limit=${limit}, sortBy=${sortBy}, sortDirection=${sortDirection}`);

      const skip = (page - 1) * limit;

      // Get reward point settings with pagination
      const orderOptions = {};

      // Only apply sortBy if it's a valid property of RewardPointSetting
      if (sortBy && this.isValidSortField(sortBy)) {
        orderOptions[sortBy] = sortDirection;
      } else {
        // Default sort by createdAt if sortBy is not provided or invalid
        orderOptions['createdAt'] = 'DESC';
      }

      try {
        // Check if the table exists first
        const tableExists = await this.dataSource.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = 'reward_point_setting'
          );
        `);

        if (!tableExists[0].exists) {
          this.logger.error('Reward point setting table does not exist');
          return new PagedListDto([], 0, page, limit);
        }

        // Use try-catch specifically for the database query
        const [rewardPointSettings, totalItems] = await this.rewardPointSettingRepository.findAndCount({
          order: orderOptions,
          skip,
          take: limit,
        });

        this.logger.log(`Found ${rewardPointSettings.length} reward point settings out of ${totalItems} total`);

        // Map reward point settings to DTOs
        const items = rewardPointSettings.map((setting) => this.mapRewardPointSettingToDto(setting));

        // Return the paged list
        return new PagedListDto(items, totalItems, page, limit);
      } catch (dbError) {
        this.logger.error(`Database error getting reward point settings: ${dbError.message}`, dbError.stack);
        // Return empty result instead of throwing an error
        return new PagedListDto([], 0, page, limit);
      }
    } catch (error) {
      this.logger.error(`Error getting reward point settings: ${error.message}`, error.stack);
      // Return empty result instead of throwing an error
      return new PagedListDto([], 0, 1, 10);
    }
  }

  /**
   * Get a reward point setting by ID
   * @param id Reward point setting ID
   * @returns Reward point setting
   */
  async getRewardPointSettingById(id: string): Promise<RewardPointSettingResponseDto> {
    try {
      const rewardPointSetting = await this.rewardPointSettingRepository.findOne({
        where: { id },
      });

      if (!rewardPointSetting) {
        throw new NotFoundException(`Reward point setting with ID ${id} not found`);
      }

      return this.mapRewardPointSettingToDto(rewardPointSetting);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error getting reward point setting: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get reward point setting');
    }
  }

  /**
   * Get the active reward point setting
   * @returns Active reward point setting
   */
  async getActiveRewardPointSetting(): Promise<RewardPointSettingResponseDto> {
    try {
      const rewardPointSetting = await this.rewardPointSettingRepository.findOne({
        where: { isActive: true },
      });

      if (!rewardPointSetting) {
        throw new NotFoundException('No active reward point setting found');
      }

      return this.mapRewardPointSettingToDto(rewardPointSetting);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error getting active reward point setting: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get active reward point setting');
    }
  }

  /**
   * Map reward point setting entity to DTO
   * @param rewardPointSetting Reward point setting entity
   * @returns Reward point setting DTO
   */
  private mapRewardPointSettingToDto(rewardPointSetting: RewardPointSetting): RewardPointSettingResponseDto {
    return {
      id: rewardPointSetting.id,
      name: rewardPointSetting.name,
      description: rewardPointSetting.description,
      conversionRate: Number(rewardPointSetting.conversionRate),
      isActive: rewardPointSetting.isActive,
      createdAt: rewardPointSetting.createdAt,
      updatedAt: rewardPointSetting.updatedAt,
    };
  }

  /**
   * Check if a field is valid for sorting
   * @param field Field to check
   * @returns True if the field is valid for sorting
   */
  private isValidSortField(field: string): boolean {
    const validFields = ['id', 'name', 'description', 'conversionRate', 'isActive', 'createdAt', 'updatedAt'];
    return validFields.includes(field);
  }
}
