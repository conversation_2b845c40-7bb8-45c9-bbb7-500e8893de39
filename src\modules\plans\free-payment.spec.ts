import { Test, TestingModule } from '@nestjs/testing';
import { PlansService } from './plans.service';
import { PlansController } from './plans.controller';
import { FreeSubscriptionDto } from './dto/free-subscription.dto';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Plan } from '../../database/entities/plan.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { User } from '../../database/entities/user.entity';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

describe('Free Payment Integration', () => {
  let service: PlansService;
  let controller: PlansController;

  const mockPlanRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
  };

  const mockUserPlanRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PlansController],
      providers: [
        PlansService,
        {
          provide: getRepositoryToken(Plan),
          useValue: mockPlanRepository,
        },
        {
          provide: getRepositoryToken(UserPlan),
          useValue: mockUserPlanRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        // Add other required dependencies as mocks
        {
          provide: 'DiaryService',
          useValue: { getOrCreateDiary: jest.fn() },
        },
        {
          provide: 'PaymentService',
          useValue: {},
        },
        {
          provide: 'PromotionsService',
          useValue: {},
        },
        {
          provide: 'TutorMatchingService',
          useValue: {},
        },
        {
          provide: 'NotificationHelperService',
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<PlansService>(PlansService);
    controller = module.get<PlansController>(PlansController);
  });

  describe('Free Payment Method Validation', () => {
    it('should validate FreeSubscriptionDto structure', () => {
      const dto: FreeSubscriptionDto = {
        planId: '123e4567-e89b-12d3-a456-************',
        autoRenew: false,
        reason: 'Development testing',
      };

      expect(dto.planId).toBeDefined();
      expect(typeof dto.autoRenew).toBe('boolean');
      expect(typeof dto.reason).toBe('string');
    });

    it('should have required fields in FreeSubscriptionDto', () => {
      const dto: FreeSubscriptionDto = {
        planId: '123e4567-e89b-12d3-a456-************',
      };

      expect(dto.planId).toBeDefined();
      expect(dto.autoRenew).toBeUndefined(); // Optional field
      expect(dto.reason).toBeUndefined(); // Optional field
    });
  });

  describe('Payment Method Integration', () => {
    it('should include free in valid payment methods', () => {
      const validMethods = ['kcp_card', 'kcp_bank', 'kcp_virtual_account', 'kcp_mobile', 'reward_points', 'free'];

      expect(validMethods).toContain('free');
      expect(validMethods.length).toBe(6);
    });

    it('should validate payment method enum includes free', () => {
      // This would be validated by class-validator in real scenario
      const paymentMethod = 'free';
      const validMethods = ['kcp_card', 'kcp_bank', 'kcp_virtual_account', 'kcp_mobile', 'reward_points', 'free'];

      expect(validMethods).toContain(paymentMethod);
    });
  });

  describe('Environment Protection', () => {
    it('should respect NODE_ENV environment variable', () => {
      // Test that environment protection logic exists
      const originalEnv = process.env.NODE_ENV;

      process.env.NODE_ENV = 'production';
      expect(process.env.NODE_ENV).toBe('production');

      process.env.NODE_ENV = 'development';
      expect(process.env.NODE_ENV).toBe('development');

      // Restore original environment
      process.env.NODE_ENV = originalEnv;
    });

    it('should respect ENABLE_FREE_PAYMENT environment variable', () => {
      const originalValue = process.env.ENABLE_FREE_PAYMENT;

      process.env.ENABLE_FREE_PAYMENT = 'true';
      expect(process.env.ENABLE_FREE_PAYMENT).toBe('true');

      process.env.ENABLE_FREE_PAYMENT = 'false';
      expect(process.env.ENABLE_FREE_PAYMENT).toBe('false');

      // Restore original value
      process.env.ENABLE_FREE_PAYMENT = originalValue;
    });
  });
});
