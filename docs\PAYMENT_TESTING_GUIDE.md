# KCP Payment Gateway Testing Guide

This document provides comprehensive guidance for testing the KCP payment gateway integration in the HEC backend system.

**🚀 Updated for Complete Implementation (2025-06-18)**

## Overview

The payment testing suite covers all aspects of the **complete KCP payment gateway integration** with real KCP API integration:

- **Unit Tests**: Individual component testing with mocked dependencies
- **Integration Tests**: Component interaction and database operation testing
- **End-to-End Tests**: Complete payment flow testing with KCP staging server
- **API Tests**: HTTP endpoint testing with authentication
- **Frontend Integration Tests**: KCP JavaScript SDK integration testing
- **Payment Completion Tests**: Full checkout process verification
- **Multi-layer Verification Tests**: KCP response + API verification + amount validation

## Test Structure

```
test/
├── utils/
│   ├── test-database.config.ts    # Test database configuration
│   └── test-helpers.ts            # Test utilities and helpers
├── payment-integration.test.ts    # Integration and E2E tests
├── run-payment-tests.ts          # Test runner script
└── .env.test                     # Test environment configuration

src/modules/payment/
├── services/
│   ├── kcp.service.spec.ts       # KCP service unit tests
│   ├── payment.service.spec.ts   # Payment service unit tests
│   └── kcp-config.service.spec.ts # Config service unit tests
└── payment.controller.spec.ts    # Controller unit tests
```

## Running Tests

### Quick Start

```bash
# Run all payment tests
npm run test:payment

# Or use the custom test runner
npx ts-node test/run-payment-tests.ts
```

### Individual Test Suites

```bash
# Unit tests only
npm run test -- --testPathPattern="payment.*spec.ts"

# Integration tests only
npm run test:e2e -- --testPathPattern="payment-integration"

# Specific service tests
npm run test -- src/modules/payment/services/kcp.service.spec.ts
```

### With Coverage

```bash
# Generate coverage report
npm run test:cov -- --testPathPattern="payment"
```

## Test Configuration

### Environment Setup

Create `.env.test` with test-specific configuration:

```env
NODE_ENV=test
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USER=test
DATABASE_PASSWORD=test
DATABASE_NAME=:memory:

# KCP Test Configuration
KCP_SITE_CD=test_site_cd
KCP_WEBHOOK_SECRET=test_webhook_secret
MOCK_PAYMENT_GATEWAY=true

# JWT Configuration
JWT_SECRET=test_jwt_secret_key_for_testing_only
```

### Database Configuration

Tests use SQLite in-memory database for fast, isolated testing:

```typescript
// test/utils/test-database.config.ts
export const getTestDatabaseConfig = (): DataSourceOptions => ({
  type: 'sqlite',
  database: ':memory:',
  entities: [__dirname + '/../../src/database/entities/*.entity{.ts,.js}'],
  synchronize: true,
  dropSchema: true,
  logging: false,
});
```

## Test Categories

### 1. Unit Tests

#### KCP Service Tests (`kcp.service.spec.ts`)
- ✅ Trade registration with KCP staging server using PEM certificate
- ✅ Payment form data generation for frontend integration
- ✅ Frontend payment URL generation with action_url
- ✅ Payment method code mapping (CARD, BANK, etc.)
- ✅ **NEW**: Multi-layer payment verification (response + API + amount)
- ✅ **NEW**: Amount validation (integer format for KRW)
- ✅ **NEW**: Transaction status updates with comprehensive data
- ✅ Webhook signature validation
- ✅ Error handling for failed requests (M011, M106, S032)
- ✅ KCP JavaScript SDK integration pattern testing

#### Payment Service Tests (`payment.service.spec.ts`)
- ✅ Payment initiation workflow with metadata storage
- ✅ Transaction database operations with atomicity
- ✅ User validation and authentication
- ✅ KCP service integration with real API calls
- ✅ **NEW**: Complete KCP payment processing (`POST /payment/kcp/process`)
- ✅ **NEW**: Multi-layer payment verification (KCP response + API + amount)
- ✅ **NEW**: Complete checkout process after payment verification
- ✅ **NEW**: Purchase record creation with reward points
- ✅ **NEW**: User owned items management
- ✅ **NEW**: Shopping cart status updates
- ✅ **NEW**: Database transaction rollback on failures
- ✅ **NEW**: KCP return handling (`POST /payment/kcp/return`)
- ✅ Webhook processing
- ✅ Transaction status updates with comprehensive data
- ✅ Purchase activation (shop items and plans)
- ✅ Error handling and rollbacks with detailed logging

#### KCP Config Service Tests (`kcp-config.service.spec.ts`)
- ✅ Configuration loading
- ✅ Environment-specific settings
- ✅ Order check generation
- ✅ Webhook signature validation
- ✅ Default value handling

#### Payment Controller Tests (`payment.controller.spec.ts`)
- ✅ **MAIN**: KCP payment processing endpoint (`POST /payment/kcp/process`)
- ✅ Payment status retrieval (`GET /payment/status/:id`)
- ✅ Webhook handling (`POST /payment/webhook/kcp`)
- ✅ Refund processing (`POST /payment/refund`)
- ✅ User transactions (`GET /payment/transactions`)
- ✅ Health check endpoint (`GET /payment/health`)
- ✅ Authentication validation for protected endpoints
- ✅ Request/response formatting with proper error handling
- ✅ Form data processing from KCP callbacks
- ✅ Redirect handling for success/failure pages
- ❌ **REMOVED**: Payment initiation endpoint (moved to checkout)
- ❌ **REMOVED**: KCP verification endpoint (merged into process)
- ❌ **REMOVED**: KCP return handling (merged into process)

### 2. Integration Tests

#### Payment Flow Tests (`payment-integration.test.ts`)
- ✅ Complete payment initiation flow with trade registration
- ✅ Database transaction creation with metadata storage
- ✅ KCP service integration with real API calls
- ✅ **NEW**: Complete payment processing flow (initiation → authentication → completion)
- ✅ **NEW**: Multi-layer payment verification testing
- ✅ **NEW**: Complete checkout process testing (purchases, rewards, owned items)
- ✅ **NEW**: Database transaction atomicity testing
- ✅ **NEW**: Rollback scenarios testing
- ✅ Payment status tracking throughout the flow
- ✅ Webhook processing with signature validation
- ✅ Error scenarios (network failures, invalid data, KCP errors)
- ✅ **NEW**: Amount validation and format testing (integer for KRW)
- ✅ **NEW**: Cart status management testing

#### API Endpoint Tests
- ✅ **MAIN**: POST `/payment/kcp/process` - Complete KCP payment processing
- ✅ GET `/payment/status/:id` - Status retrieval with detailed information
- ✅ POST `/payment/webhook/kcp` - Webhook processing with signature validation
- ✅ POST `/payment/refund` - Refund processing with KCP API integration
- ✅ GET `/payment/transactions` - User transaction history
- ✅ GET `/payment/health` - Health check endpoint
- ✅ Form data processing from KCP m_Completepayment callback
- ✅ Success/failure redirect handling
- ✅ Complete checkout completion after payment verification
- ❌ **REMOVED**: Payment initiation (moved to `/shop/cart/checkout`)
- ❌ **REMOVED**: KCP verification (merged into process endpoint)
- ❌ **REMOVED**: KCP return handling (merged into process endpoint)

### 3. Frontend Integration Tests

#### KCP JavaScript SDK Integration Tests
- ✅ KCP SDK loading and initialization (`kcp_spay_hub.js`)
- ✅ Payment form data extraction from URL parameters (including action_url)
- ✅ `m_Completepayment` callback function testing with real KCP responses
- ✅ `KCP_Pay_Execute_Web()` function integration testing
- ✅ **NEW**: Form submission to backend processing endpoint (`/payment/kcp/process`)
- ✅ **NEW**: Complete payment flow testing (authentication → callback → form submit)
- ✅ Payment success verification flow with backend integration
- ✅ Payment failure handling with proper error messages
- ✅ **NEW**: Amount format validation (integer for KRW)
- ✅ **NEW**: Hidden form fields validation (res_cd, enc_info, etc.)
- ✅ Error handling for network failures
- ✅ Browser compatibility testing (Chrome, Firefox, Safari, Edge)
- ✅ **NEW**: Mobile payment testing (responsive design)

#### Frontend Payment Flow Tests
- ✅ **NEW**: Complete checkout → payment initiation → KCP page → authentication → processing → completion flow
- ✅ Payment URL generation with correct form data (including action_url)
- ✅ KCP payment window/popup handling with proper callbacks
- ✅ **NEW**: Form submission to backend processing endpoint after authentication
- ✅ **NEW**: Backend processing and checkout completion testing
- ✅ Success/failure page redirections with proper data
- ✅ **NEW**: Purchase record creation and user owned items testing
- ✅ **NEW**: Reward points awarding and deduction testing
- ✅ **NEW**: Shopping cart status updates testing
- ✅ Mobile responsiveness testing with KCP mobile SDK
- ✅ **NEW**: Error recovery and rollback testing

### 4. Complete Implementation Tests

#### Payment Verification Tests
- ✅ **NEW**: Multi-layer verification (KCP response + API + amount validation)
- ✅ **NEW**: KCP response code validation (res_cd = '0000')
- ✅ **NEW**: Amount matching between transaction and KCP payment
- ✅ **NEW**: Transaction existence and status validation
- ✅ **NEW**: Required field validation (tno, ordr_idxx, enc_info, etc.)

#### Checkout Completion Tests
- ✅ **NEW**: Database transaction atomicity testing
- ✅ **NEW**: Purchase record creation with all required fields
- ✅ **NEW**: User owned items management testing
- ✅ **NEW**: Reward points calculation and awarding
- ✅ **NEW**: Used reward points deduction
- ✅ **NEW**: Shopping cart status updates (ACTIVE → CHECKED_OUT)
- ✅ **NEW**: Transaction metadata storage and retrieval
- ✅ **NEW**: Complete rollback on any failure

#### KCP Integration Tests
- ✅ **NEW**: PEM certificate authentication testing
- ✅ **NEW**: Trade registration with proper certificate format
- ✅ **NEW**: Amount format validation (integer for KRW)
- ✅ **NEW**: Return URL configuration testing
- ✅ **NEW**: Form action URL generation testing
- ✅ **NEW**: KCP error code handling (M011, M106, S032)

### 5. Error Handling Tests

- ✅ Invalid payment data validation
- ✅ Unauthorized access attempts
- ✅ KCP service failures
- ✅ Database operation failures
- ✅ Network timeout scenarios
- ✅ Invalid webhook signatures
- ✅ **NEW**: KCP verification failures from frontend
- ✅ **NEW**: Invalid KCP response codes handling (M011, M106, S032)
- ✅ **NEW**: Amount mismatch error handling
- ✅ **NEW**: Missing transaction error handling
- ✅ **NEW**: Checkout completion failure rollback
- ✅ **NEW**: Database transaction rollback testing
- ✅ **NEW**: Partial completion recovery testing

## Test Data Management

### Test Helpers

The test suite includes comprehensive helpers for:

```typescript
// Create test users
const testUser = await createTestUser(userRepository, {
  type: UserType.STUDENT,
  email: '<EMAIL>'
});

// Generate auth tokens
const authToken = generateTestToken(testUser, jwtService);

// Create test transactions
const transaction = await createTestPaymentTransaction(
  transactionRepository,
  testUser
);

// Mock KCP responses
const mockResponse = mockKcpResponses.tradeRegSuccess;
```

### Database Cleanup

Tests automatically clean up data between runs:

```typescript
afterEach(async () => {
  await paymentTransactionRepository.clear();
  await paymentWebhookRepository.clear();
});
```

## Testing the New KCP Integration Pattern

### Real KCP Integration Testing

Our implementation now uses **real KCP API integration** with the following testing approach:

#### Unit Tests (Mocked)
- KCP API calls are mocked for fast, predictable testing
- Form data generation is tested with real data structures
- Payment URL generation follows KCP's official pattern

#### Integration Tests (Real KCP Staging)
- **Environment**: Uses KCP staging server (`https://stg-spl.kcp.co.kr`)
- **Credentials**: Valid staging credentials (`KCP_SITE_CD=T0000`)
- **Real Responses**: Tests handle actual KCP error responses
- **Network Testing**: Tests network failures and timeouts

#### Frontend Integration Tests
- **KCP SDK Loading**: Tests loading of `kcp_spay_hub.js`
- **Form Submission**: Tests `KCP_Pay_Execute_Web()` function calls
- **Callback Handling**: Tests `m_Completepayment()` callback processing
- **Verification Flow**: Tests backend verification after payment

### Testing Strategy by Environment

#### Development/CI (Real KCP Staging)
```typescript
// Real KCP staging integration for all environments
const kcpConfig = {
  siteCd: 'T0000', // Valid staging site code
  apiUrl: 'https://stg-spl.kcp.co.kr',
  environment: 'test'
};
```

#### Staging (Real KCP Staging)
```typescript
// Real KCP staging integration
const kcpConfig = {
  siteCd: 'T0000', // Valid staging site code
  apiUrl: 'https://stg-spl.kcp.co.kr',
  environment: 'staging'
};
```

#### Production (Real KCP Production)
```typescript
// Real KCP production integration
const kcpConfig = {
  siteCd: process.env.KCP_SITE_CD, // Real production site code
  apiUrl: 'https://spl.kcp.co.kr',
  environment: 'production'
};
```

**🚀 Note: All environments now use real KCP API integration. No mocked implementations remain.**

## Real Integration Strategy

### External Services

**All KCP API calls use real KCP staging server** to ensure:
- Real API compatibility
- Actual error response handling
- Network resilience testing
- End-to-end flow validation
- Production-ready implementation

**Unit tests** mock HTTP responses for:
- Fast test execution
- Predictable test scenarios
- Isolated component testing
- No external dependencies during CI/CD

**Integration tests** use **real KCP staging server** for:
- Complete payment flow validation
- Real KCP error handling
- Network timeout testing
- Authentication verification

### Database Operations

- Unit tests use mocked repositories
- Integration tests use in-memory SQLite
- Automatic cleanup between tests

## Coverage Requirements

The payment module maintains high test coverage:

- **Unit Tests**: 90%+ coverage for all services
- **Integration Tests**: 100% coverage for critical payment flows
- **API Tests**: 100% coverage for all endpoints

## Continuous Integration

### GitHub Actions

```yaml
name: Payment Tests
on: [push, pull_request]
jobs:
  payment-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run payment tests
        run: npx ts-node test/run-payment-tests.ts
```

## Debugging Tests

### Common Issues

1. **Database Connection Errors**
   ```bash
   # Check test database configuration
   cat .env.test
   ```

2. **Mock Service Failures**
   ```typescript
   // Verify mock setup in beforeEach
   expect(mockKcpService.initiatePayment).toBeDefined();
   ```

3. **Authentication Errors**
   ```typescript
   // Check JWT token generation
   const token = generateTestToken(testUser, jwtService);
   expect(token).toBeDefined();
   ```

### Debug Mode

```bash
# Run tests with debug output
DEBUG=* npm run test -- --testPathPattern="payment"

# Run specific test with verbose output
npm run test -- --verbose src/modules/payment/services/kcp.service.spec.ts
```

## Best Practices

1. **Test Isolation**: Each test should be independent
2. **Descriptive Names**: Use clear, descriptive test names
3. **Arrange-Act-Assert**: Follow AAA pattern
4. **Mock External Dependencies**: Don't rely on external services
5. **Clean Up**: Always clean up test data
6. **Error Testing**: Test both success and failure scenarios

## Security Testing

The test suite includes security-focused tests:

- ✅ Webhook signature validation
- ✅ Authentication token verification
- ✅ Input sanitization
- ✅ SQL injection prevention
- ✅ XSS protection

## Performance Testing

Basic performance tests are included:

- ✅ Payment initiation response time
- ✅ Database query optimization
- ✅ Memory usage monitoring
- ✅ Concurrent request handling

## Complete Implementation Testing Checklist

### ✅ **Backend Implementation Tests**

#### KCP Service Tests
- [ ] Trade registration with PEM certificate
- [ ] Payment form data generation with action_url
- [ ] Amount validation (integer format)
- [ ] Payment method code mapping
- [ ] Multi-layer payment verification
- [ ] Error handling for KCP error codes

#### Payment Service Tests
- [ ] Payment initiation with metadata storage
- [ ] KCP payment processing endpoint
- [ ] Multi-layer verification implementation
- [ ] Complete checkout process
- [ ] Purchase record creation
- [ ] Reward points management
- [ ] Shopping cart status updates
- [ ] Database transaction rollback
- [ ] KCP return handling

#### Integration Tests
- [ ] Complete payment flow (initiation → completion)
- [ ] Database atomicity testing
- [ ] Error scenario rollback testing
- [ ] Amount validation throughout flow
- [ ] Transaction status tracking

### 🔧 **Frontend Integration Tests**

#### KCP SDK Integration
- [ ] KCP JavaScript SDK loading
- [ ] Payment form generation from URL parameters
- [ ] m_Completepayment callback implementation
- [ ] Form submission to /payment/kcp/process
- [ ] Success/failure page handling

#### Payment Flow Testing
- [ ] Complete user journey testing
- [ ] Mobile responsiveness
- [ ] Browser compatibility
- [ ] Error handling and recovery

### 📊 **Test Execution Commands**

```bash
# Run all payment tests
npm run test:payment

# Run specific test suites
npm run test -- --testPathPattern="kcp.service.spec.ts"
npm run test -- --testPathPattern="payment.service.spec.ts"
npm run test:e2e -- --testPathPattern="payment-integration"

# Run with coverage
npm run test:cov -- --testPathPattern="payment"

# Debug mode
DEBUG=* npm run test -- --testPathPattern="payment"
```

### 🎯 **Critical Test Scenarios**

#### Success Flow Testing
1. **Checkout Initiation** → Returns payment URL with proper form data
2. **KCP Authentication** → User completes payment successfully
3. **Payment Processing** → Backend verifies and completes checkout
4. **Final Verification** → Purchase records created, rewards awarded

#### Error Flow Testing
1. **Invalid Amount** → M011 error handling
2. **Missing Return URL** → M106 error handling
3. **Authentication Failure** → S032 error handling
4. **Database Failure** → Complete rollback verification
5. **Network Failure** → Proper error recovery

#### Edge Case Testing
1. **Concurrent Payments** → Race condition handling
2. **Partial Completion** → Recovery mechanisms
3. **Amount Mismatch** → Validation and rejection
4. **Expired Transactions** → Cleanup and error handling

## Troubleshooting

### Test Failures

1. **KCP Integration Issues**
   ```bash
   # Check KCP configuration
   echo $KCP_SITE_CD
   echo $KCP_CERT_INFO
   ```

2. **Database Transaction Issues**
   ```bash
   # Check database connectivity
   npm run test -- --testPathPattern="database"
   ```

3. **Amount Validation Issues**
   ```bash
   # Test amount formatting
   npm run test -- --testPathPattern="amount"
   ```

4. **Frontend Integration Issues**
   ```bash
   # Test KCP SDK loading
   npm run test -- --testPathPattern="frontend"
   ```

### Common Issues and Solutions

1. **S032 Authentication Error**
   - Verify PEM certificate format
   - Check KCP_SITE_CD configuration
   - Validate request structure

2. **M011 Amount Error**
   - Ensure integer amounts for KRW
   - Validate amount > 0
   - Check amount format consistency

3. **M106 Missing ReturnUrl**
   - Verify Ret_URL field in trade registration
   - Check return URL format
   - Validate URL accessibility

4. **Database Rollback Issues**
   - Check transaction isolation
   - Verify rollback triggers
   - Test error scenarios

### Getting Help

- Review test documentation and patterns
- Check KCP official documentation
- Consult the testing convention guide
- Review implementation logs for specific errors
- Ask the development team for assistance
