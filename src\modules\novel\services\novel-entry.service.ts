import { Injectable, NotFoundException, BadRequestException, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Like, Between } from 'typeorm';
import { NovelEntry, NovelEntryStatus } from '../../../database/entities/novel-entry.entity';
import { NovelEntryHistory } from '../../../database/entities/novel-entry-history.entity';
import { NovelTopic } from '../../../database/entities/novel-topic.entity';
import { Novel } from '../../../database/entities/novel.entity';
import { PagedListDto } from '../../../common/models/paged-list.dto';
import { PaginationDto } from '../../../common/models/pagination.dto';
import { NovelModuleSkinPreference } from '../../../database/entities/novel-module-skin-preference.entity';
import { StudentTutorMapping, MappingStatus } from '../../../database/entities/student-tutor-mapping.entity';
import { User } from '../../../database/entities/user.entity';

import {
  UpdateNovelEntryDto,
  SubmitNovelEntryDto,
  NovelEntryResponseDto,
  SetNovelSkinPreferenceDto,
  NovelSkinPreferenceResponseDto,
  NovelEntryHistoryResponseDto,
  NovelEntryVersionDto,
} from '../../../database/models/novel.dto';
import { NotificationHelperService } from '../../notification/notification-helper.service';
import { AsyncNotificationHelperService } from '../../notification/async-notification-helper.service';
import { NotificationType } from '../../../database/entities/notification.entity';
import { DiarySkinService } from '../../diary/diary-skin.service';
import { FileRegistryService } from '../../../common/services/file-registry.service';
import { FileEntityType } from '../../../common/enums/file-entity-type.enum';
import { NovelService } from './novel.service';
import { NovelEntryHistoryService } from './novel-entry-history.service';
import { NovelTutorGreetingRequiredException, NovelDefaultSkinRequiredException } from '../../../common/exceptions/novel.exceptions';
import { NovelEntryFilterDto } from 'src/database/models/novel-search.dto';

@Injectable()
export class NovelEntryService {
  private readonly logger = new Logger(NovelEntryService.name);

  constructor(
    @InjectRepository(NovelEntry)
    private readonly novelEntryRepository: Repository<NovelEntry>,
    @InjectRepository(NovelEntryHistory)
    private readonly novelEntryHistoryRepository: Repository<NovelEntryHistory>,
    @InjectRepository(NovelTopic)
    private readonly novelTopicRepository: Repository<NovelTopic>,
    @InjectRepository(Novel)
    private readonly novelRepository: Repository<Novel>,
    @InjectRepository(NovelModuleSkinPreference)
    private readonly skinPreferenceRepository: Repository<NovelModuleSkinPreference>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => NotificationHelperService))
    private readonly notificationHelper: NotificationHelperService,
    @Inject(forwardRef(() => AsyncNotificationHelperService))
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
    @Inject(forwardRef(() => DiarySkinService))
    private readonly diarySkinService: DiarySkinService,
    private readonly fileRegistryService: FileRegistryService,
    @Inject(forwardRef(() => NovelService))
    private readonly novelService: NovelService,
    @Inject(forwardRef(() => NovelEntryHistoryService))
    private readonly novelEntryHistoryService: NovelEntryHistoryService,
  ) {}

  async updateEntry(studentId: string, entryId: string, updateEntryDto: UpdateNovelEntryDto, request?: any): Promise<NovelEntryResponseDto> {
    // Check if student has set tutor greeting before allowing updates
    const hasGreeting = await this.novelService.hasSetTutorGreeting(studentId);
    if (!hasGreeting) {
      throw new NovelTutorGreetingRequiredException('Please set a greeting message for your tutor before updating novel entries. This is required once for the Novel module.');
    }

    const entry = await this.novelEntryRepository.findOne({
      where: { id: entryId, studentId },
      relations: ['topic'],
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    // Allow updates even after review - students can update unlimited times
    // Only the first qualifying submission will notify the tutor

    // Validate word count against topic limits if content is being updated - only check maximum for update (drafting behavior)
    if (updateEntryDto.content && entry.topic) {
      const wordCount = this.calculateWordCount(updateEntryDto.content);

      // Check maximum word count only - update API should allow drafts below minimum
      if (entry.topic.maxWordCount && wordCount > entry.topic.maxWordCount) {
        throw new BadRequestException(`Content cannot exceed ${entry.topic.maxWordCount} words`);
      }
    }

    // Store old data for version history
    const oldData = {
      content: entry.content,
    };

    // NEW REQUIREMENT: Update API should NOT create versions - this is "save as draft"
    this.logger.log(`Updating novel entry ${entryId} as draft (no version created)`);

    // Update word count if content changed
    if (updateEntryDto.content) {
      entry.wordCount = this.calculateWordCount(updateEntryDto.content);
    }

    Object.assign(entry, updateEntryDto);

    // NEW REQUIREMENT: Mark as draft
    entry.isDraft = true;

    const updatedEntry = await this.novelEntryRepository.save(entry);

    // Note: Regular updates don't trigger notifications
    // Only submissions through submitEntry trigger notifications
    this.logger.log(`Updated novel entry ${entryId} for student ${studentId} as draft (no notification sent)`);
    return await this.mapEntryToResponseDto(updatedEntry);
  }

  async submitEntry(studentId: string, submitEntryDto: SubmitNovelEntryDto): Promise<NovelEntryResponseDto> {
    // Check if student has set tutor greeting before allowing submission
    const hasGreeting = await this.novelService.hasSetTutorGreeting(studentId);
    if (!hasGreeting) {
      throw new NovelTutorGreetingRequiredException('Please set a greeting message for your tutor before submitting novel entries. This is required once for the Novel module.');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let isTransactionActive = true;

    try {
      // Optimized: Get entry with minimal relations and validate ownership
      const entry = await this.novelEntryRepository.findOne({
        where: { id: submitEntryDto.entryId, studentId },
        relations: ['topic'],
        select: {
          id: true,
          content: true,
          wordCount: true,
          status: true,
          studentId: true,
          isDraft: true,
          canSubmitNewVersion: true,
          submittedVersionCount: true,
          isResubmission: true,
          resubmissionType: true,
          previousReviewCount: true,
          lastReviewedAt: true,
          topic: {
            id: true,
            title: true,
            minWordCount: true,
            maxWordCount: true,
          },
        },
      });

      if (!entry) {
        await queryRunner.rollbackTransaction();
        isTransactionActive = false;
        throw new NotFoundException('Entry not found');
      }

      // NEW REQUIREMENT: Check if user can submit a new version
      // Allow submissions after review or if canSubmitNewVersion is true
      // Also allow resubmissions (entries that have been reviewed before)
      const hasBeenReviewed = entry.lastReviewedAt || entry.previousReviewCount > 0;
      const canSubmit =
        entry.canSubmitNewVersion ||
        entry.status === NovelEntryStatus.CONFIRMED || // Legacy confirmed entries
        entry.status === NovelEntryStatus.REVIEWED || // New final state
        hasBeenReviewed; // Allow resubmissions after any previous review

      if (!canSubmit) {
        await queryRunner.rollbackTransaction();
        isTransactionActive = false;

        // Provide more specific error message based on current status
        let errorMessage = 'Cannot submit new version.';

        if (entry.status === NovelEntryStatus.SUBMITTED) {
          errorMessage += ' Previous submission is still pending review by tutor.';
        } else {
          errorMessage += ' Previous submission must be reviewed by tutor first.';
        }

        throw new BadRequestException(errorMessage);
      }

      // Validate word count against topic limits if content is being updated
      if (submitEntryDto.content !== undefined && entry.topic) {
        const wordCount = this.calculateWordCount(submitEntryDto.content);

        // Check minimum word count
        if (entry.topic.minWordCount && wordCount < entry.topic.minWordCount) {
          await queryRunner.rollbackTransaction();
          isTransactionActive = false;
          throw new BadRequestException(`Content must contain at least ${entry.topic.minWordCount} words before submission`);
        }

        // Check maximum word count
        if (entry.topic.maxWordCount && wordCount > entry.topic.maxWordCount) {
          await queryRunner.rollbackTransaction();
          isTransactionActive = false;
          throw new BadRequestException(`Content cannot exceed ${entry.topic.maxWordCount} words`);
        }
      }

      // Update content if provided
      if (submitEntryDto.content !== undefined) {
        entry.content = submitEntryDto.content;
        entry.wordCount = this.calculateWordCount(submitEntryDto.content);
      }

      // Update skin if provided
      if (submitEntryDto.skinId !== undefined) {
        entry.skinId = submitEntryDto.skinId;
      }

      // Update background color if provided
      if (submitEntryDto.backgroundColor !== undefined) {
        entry.backgroundColor = submitEntryDto.backgroundColor;
      }

      // NEW REQUIREMENT: Create a submitted version in history with resubmission tracking
      const submissionNumber = entry.submittedVersionCount + 1;

      // Determine if this is a resubmission and what type
      const isResubmission = entry.submittedVersionCount > 0;
      let resubmissionType: 'after_review' | null = null;
      let previousStatus: string | null = null;

      if (isResubmission) {
        previousStatus = entry.status;
        if (entry.status === NovelEntryStatus.REVIEWED || entry.status === NovelEntryStatus.CONFIRMED) {
          resubmissionType = 'after_review'; // Both REVIEWED and legacy CONFIRMED map to after_review
        }
      }

      // Mark all existing versions as not latest before creating new submission
      await queryRunner.manager.update('novel_entry_history', { novelEntryId: submitEntryDto.entryId }, { isLatest: false });

      // Create submitted version with resubmission tracking
      const submittedVersion = await queryRunner.manager.save(
        queryRunner.manager.create('novel_entry_history', {
          novelEntryId: submitEntryDto.entryId,
          content: entry.content,
          versionNumber: submissionNumber,
          isLatest: true,
          isSubmittedVersion: true,
          submissionNumber: submissionNumber,
          submittedAt: new Date(),
          wordCount: entry.wordCount,
          isResubmission: isResubmission,
          resubmissionType: resubmissionType,
          previousStatus: previousStatus,
          metaData: {
            submissionType: isResubmission ? 'resubmission' : 'initial_submit',
            resubmissionType: resubmissionType,
            previousStatus: previousStatus,
            browserInfo: 'N/A',
            ipAddress: 'N/A',
          },
        }),
      );

      // Update status and submission time
      // Simplified status progression: NEW -> SUBMITTED
      // After review: entries can be resubmitted but keep SUBMITTED status
      let newStatus = entry.status;

      if (entry.status === NovelEntryStatus.NEW) {
        newStatus = NovelEntryStatus.SUBMITTED;
      } else {
        // For SUBMITTED, REVIEWED, CONFIRMED - keep current status for resubmissions
        // Legacy statuses (UPDATED, CORRECTION_GIVEN, UNDER_REVIEW) also keep current status
        newStatus = entry.status;
      }

      entry.status = newStatus;
      entry.submittedAt = new Date();

      // NEW REQUIREMENT: Update submission tracking fields
      entry.isDraft = false;
      entry.lastSubmittedAt = new Date();
      entry.canSubmitNewVersion = false; // Will be set to true when reviewed
      entry.submittedVersionCount = submissionNumber;
      entry.currentSubmittedVersionId = (submittedVersion as any).id;

      // Update resubmission tracking fields
      entry.isResubmission = isResubmission;
      entry.resubmissionType = resubmissionType;
      if (resubmissionType === 'after_review') {
        entry.previousReviewCount = (entry.previousReviewCount || 0) + 1;
      } else if (resubmissionType === 'after_confirmation') {
        entry.previousConfirmationCount = (entry.previousConfirmationCount || 0) + 1;
      }

      const updatedEntry = await queryRunner.manager.save(entry);
      await queryRunner.commitTransaction();
      isTransactionActive = false;

      // NEW REQUIREMENT: Always send notification for submissions (unlimited submissions allowed)
      try {
        const submissionType = isResubmission ? `resubmission (${resubmissionType})` : 'initial submission';
        this.logger.log(`Sending submission notification for novel entry: ${updatedEntry.id} (submission #${submissionNumber}, ${submissionType})`);
        // Send notification asynchronously to avoid blocking submission
        this.sendSubmissionNotificationAsync(updatedEntry, newStatus, submissionNumber, isResubmission, resubmissionType);
      } catch (notificationError) {
        this.logger.error(`Failed to send notification: ${notificationError.message}`, notificationError.stack);
      }

      this.logger.log(`Submitted novel entry ${submitEntryDto.entryId} for student ${studentId}`);
      return await this.mapEntryToResponseDto(updatedEntry);
    } catch (error) {
      // Only rollback if transaction is still active
      if (isTransactionActive) {
        try {
          await queryRunner.rollbackTransaction();
        } catch (rollbackError) {
          this.logger.error(`Error rolling back transaction: ${rollbackError.message}`, rollbackError.stack);
        }
      }
      this.logger.error(`Error submitting novel entry: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getStudentEntries(studentId: string, category?: string): Promise<NovelEntryResponseDto[]> {
    // Check if student has set tutor greeting before allowing access to entries
    const hasGreeting = await this.novelService.hasSetTutorGreeting(studentId);
    if (!hasGreeting) {
      throw new NovelTutorGreetingRequiredException('Please set a greeting message for your tutor before accessing novel entries. This is required once for the Novel module.');
    }

    const queryBuilder = this.novelEntryRepository
      .createQueryBuilder('entry')
      .leftJoinAndSelect('entry.topic', 'topic')
      .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
      .leftJoinAndSelect('entry.correction', 'correction')
      .leftJoinAndSelect('entry.skin', 'skin')
      .leftJoinAndSelect('entry.originalReviewedVersion', 'originalReviewedVersion')
      .where('entry.studentId = :studentId', { studentId });

    if (category) {
      queryBuilder.andWhere('topic.category = :category', { category });
    }

    queryBuilder.orderBy('entry.createdAt', 'DESC');

    const entries = await queryBuilder.getMany();
    return Promise.all(entries.map((entry) => this.mapEntryToResponseDto(entry)));
  }

  async getEntryById(entryId: string, studentId?: string): Promise<NovelEntryResponseDto> {
    // If studentId is provided, check if student has set tutor greeting
    if (studentId) {
      const hasGreeting = await this.novelService.hasSetTutorGreeting(studentId);
      if (!hasGreeting) {
        throw new NovelTutorGreetingRequiredException('Please set a greeting message for your tutor before accessing novel entries. This is required once for the Novel module.');
      }
    }

    const whereCondition: any = { id: entryId };
    if (studentId) {
      whereCondition.studentId = studentId;
    }

    const entry = await this.novelEntryRepository.findOne({
      where: whereCondition,
      relations: ['topic', 'feedbacks', 'correction', 'student', 'skin', 'originalReviewedVersion'],
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    return await this.mapEntryToResponseDto(entry);
  }

  async getOrCreateEntryByTopicId(studentId: string, topicId: string): Promise<NovelEntryResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let isTransactionActive = true;

    try {
      // Check if entry already exists for this student and topic
      let entry = await this.novelEntryRepository.findOne({
        where: { topicId, studentId },
        relations: ['topic', 'feedbacks', 'correction', 'skin', 'originalReviewedVersion'],
      });

      if (entry) {
        // Entry exists, but still check if student has set tutor greeting
        const hasGreeting = await this.novelService.hasSetTutorGreeting(studentId);
        if (!hasGreeting) {
          await queryRunner.rollbackTransaction();
          isTransactionActive = false;
          throw new NovelTutorGreetingRequiredException('Please set a greeting message for your tutor before accessing novel entries. This is required once for the Novel module.');
        }

        // Entry exists and greeting is set, return it
        await queryRunner.commitTransaction();
        isTransactionActive = false;
        this.logger.log(`Found existing entry for student ${studentId} on topic ${topicId}`);
        return await this.mapEntryToResponseDto(entry);
      }

      // Before creating a new entry, check if student has set tutor greeting
      // This is required for ALL students before creating any novel entry
      const hasGreeting = await this.novelService.hasSetTutorGreeting(studentId);
      if (!hasGreeting) {
        await queryRunner.rollbackTransaction();
        isTransactionActive = false;
        throw new NovelTutorGreetingRequiredException('Please set a greeting message for your tutor before creating novel entries. This is required once for the Novel module.');
      }

      // Entry doesn't exist, create a new one
      // First verify topic exists and is active
      const topic = await this.novelTopicRepository.findOne({
        where: { id: topicId, isActive: true },
      });

      if (!topic) {
        await queryRunner.rollbackTransaction();
        isTransactionActive = false;
        throw new NotFoundException('Topic not found or inactive');
      }

      // Get user's default novel skin, fall back to old preference system if needed
      let defaultSkinId: string | null = null;

      // First try to get from user entity
      const user = await this.userRepository.findOne({
        where: { id: studentId },
        select: ['defaultNovelSkinId'],
      });
      defaultSkinId = user?.defaultNovelSkinId;

      // If no default novel skin in user entity, check old preference system
      if (!defaultSkinId) {
        const skinPreference = await this.skinPreferenceRepository.findOne({
          where: { studentId },
        });
        defaultSkinId = skinPreference?.defaultSkinId;
      }

      if (!defaultSkinId) {
        await queryRunner.rollbackTransaction();
        isTransactionActive = false;
        throw new NovelDefaultSkinRequiredException('Please set a default skin for the Novel module before creating an entry');
      }

      // Create new entry with default content and default skin
      entry = this.novelEntryRepository.create({
        topicId,
        studentId,
        content: '', // Start with empty content
        wordCount: 0,
        skinId: defaultSkinId,
        status: NovelEntryStatus.NEW,
      });

      const savedEntry = await queryRunner.manager.save(entry);

      // Load the topic relation for the response
      savedEntry.topic = topic;

      await queryRunner.commitTransaction();
      isTransactionActive = false;

      this.logger.log(`Created new entry for student ${studentId} on topic ${topicId}`);
      return await this.mapEntryToResponseDto(savedEntry);
    } catch (error) {
      // Only rollback if transaction is still active
      if (isTransactionActive) {
        try {
          await queryRunner.rollbackTransaction();
        } catch (rollbackError) {
          this.logger.error(`Error rolling back transaction: ${rollbackError.message}`, rollbackError.stack);
        }
      }
      this.logger.error(`Error getting or creating entry: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async setDefaultSkin(studentId: string, setSkinDto: SetNovelSkinPreferenceDto): Promise<NovelSkinPreferenceResponseDto> {
    let preference = await this.skinPreferenceRepository.findOne({
      where: { studentId },
    });

    if (preference) {
      preference.defaultSkinId = setSkinDto.defaultSkinId;
    } else {
      preference = this.skinPreferenceRepository.create({
        studentId,
        defaultSkinId: setSkinDto.defaultSkinId,
      });
    }

    const savedPreference = await this.skinPreferenceRepository.save(preference);
    this.logger.log(`Set default skin for student ${studentId}`);

    return {
      id: savedPreference.id,
      studentId: savedPreference.studentId,
      defaultSkinId: savedPreference.defaultSkinId,
      createdAt: savedPreference.createdAt,
      updatedAt: savedPreference.updatedAt,
    };
  }

  async getDefaultSkin(studentId: string): Promise<NovelSkinPreferenceResponseDto | null> {
    const preference = await this.skinPreferenceRepository.findOne({
      where: { studentId },
    });

    if (!preference) {
      return null;
    }

    return {
      id: preference.id,
      studentId: preference.studentId,
      defaultSkinId: preference.defaultSkinId,
      createdAt: preference.createdAt,
      updatedAt: preference.updatedAt,
    };
  }

  /**
   * Set default novel skin for a user using the new user entity approach
   * @param userId User ID
   * @param skinId Skin ID
   */
  async setDefaultNovelSkin(userId: string, skinId: string): Promise<void> {
    // Validate that the user has access to this skin (use diary skin service validation)
    await this.diarySkinService.validateStudentSkinAccess(userId, skinId);

    // Update user's default novel skin
    await this.userRepository.update(userId, {
      defaultNovelSkinId: skinId,
    });

    this.logger.log(`Updated default novel skin for user ${userId} to ${skinId}`);
  }

  /**
   * Get default novel skin for a user using the new user entity approach
   * @param userId User ID
   * @returns Default novel skin or null if not set
   */
  async getDefaultNovelSkin(userId: string): Promise<any | null> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['defaultNovelSkin'],
    });

    if (!user?.defaultNovelSkin) {
      return null;
    }

    // Return a simplified skin response (novel uses diary skins)
    return {
      id: user.defaultNovelSkin.id,
      name: user.defaultNovelSkin.name,
      description: user.defaultNovelSkin.description,
      isActive: user.defaultNovelSkin.isActive,
      isGlobal: true,
      templateContent: user.defaultNovelSkin.templateContent,
      isUserDefaultNovel: true,
      isUserDefaultDiary: false,
    };
  }

  /**
   * Search novel entries with filtering and pagination
   * @param studentId The ID of the student
   * @param filterDto Filter parameters
   * @param paginationDto Pagination parameters
   * @returns A paged list of filtered novel entries
   */
  async searchNovelEntries(studentId: string, filterDto: NovelEntryFilterDto): Promise<PagedListDto<NovelEntryResponseDto>> {
    try {
      // Default pagination values
      const page = 1;
      const limit = 10;
      const skip = (page - 1) * limit;

      // Create query builder
      const queryBuilder = this.novelEntryRepository
        .createQueryBuilder('entry')
        .leftJoinAndSelect('entry.topic', 'topic')
        .leftJoinAndSelect('entry.student', 'student')
        .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
        .leftJoinAndSelect('feedbacks.tutor', 'feedbackTutor')
        .leftJoinAndSelect('entry.correction', 'correction')
        .leftJoinAndSelect('correction.tutor', 'correctionTutor')
        .leftJoinAndSelect('entry.skin', 'skin')
        .where('entry.studentId = :studentId', { studentId });

      // Apply filters
      if (filterDto.date) {
        const dateStart = new Date(filterDto.date);
        dateStart.setUTCHours(0, 0, 0, 0);
        const dateEnd = new Date(filterDto.date);
        dateEnd.setUTCHours(23, 59, 59, 999);

        queryBuilder.andWhere('entry.createdAt BETWEEN :dateStart AND :dateEnd', {
          dateStart,
          dateEnd,
        });
      }

      if (filterDto.sequence) {
        queryBuilder.andWhere('LOWER(topic.sequenceTitle) LIKE LOWER(:sequence)', {
          sequence: `%${filterDto.sequence}%`,
        });
      }

      if (filterDto.title) {
        queryBuilder.andWhere('LOWER(topic.title) LIKE LOWER(:title)', {
          title: `%${filterDto.title}%`,
        });
      }

      // Add order by
      queryBuilder.orderBy('entry.createdAt', 'DESC').skip(skip).take(limit);

      // Get entries and count
      const [entries, totalCount] = await queryBuilder.getManyAndCount();

      // Map entries to response DTOs
      const mappedEntries = await Promise.all(entries.map((entry) => this.mapEntryToResponseDto(entry)));

      return new PagedListDto(mappedEntries, totalCount, page, limit);
    } catch (error) {
      this.logger.error(`Error searching novel entries: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async sendSubmissionNotification(
    entry: NovelEntry,
    status: NovelEntryStatus,
    submissionNumber?: number,
    isResubmission?: boolean,
    resubmissionType?: 'after_review' | 'after_confirmation' | null,
  ): Promise<void> {
    try {
      // Find assigned tutor
      const tutorMapping = await this.studentTutorMappingRepository.findOne({
        where: {
          studentId: entry.studentId,
          status: MappingStatus.ACTIVE,
        },
        relations: ['tutor'],
      });

      if (tutorMapping) {
        // Use NOVEL_SUBMISSION for all submissions (initial and resubmissions)
        const notificationType = NotificationType.NOVEL_SUBMISSION;

        // Create notification title and message based on submission type
        let title: string;
        let message: string;

        title = isResubmission ? 'Novel Entry Resubmitted' : 'New Novel Submission';
        message = isResubmission
          ? `${entry.student.name} has resubmitted a novel entry for topic "${entry.topic.sequenceTitle}" (Submission #${submissionNumber}, after review)`
          : `${entry.student.name} has submitted a novel entry for topic "${entry.topic.sequenceTitle}" (Submission #${submissionNumber})`;

        await this.notificationHelper.notify(tutorMapping.tutorId, notificationType, title, message, {
          relatedEntityId: entry.id,
          relatedEntityType: 'novel_entry',
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendMobile: true,
          sendSms: false,
          sendRealtime: false,
        });
      }
    } catch (error) {
      this.logger.warn(`Failed to send notification: ${error.message}`);
    }
  }

  /**
   * Check if tutor should be notified for this submission
   * Only notify on first submission when word count target is met
   */
  private async shouldNotifyTutorForSubmission(entry: NovelEntry): Promise<boolean> {
    try {
      // Check if already notified for first submission
      if (entry.firstSubmissionNotified) {
        this.logger.log(`Novel entry ${entry.id} already has first submission notification sent`);
        return false;
      }

      // Check if word count meets the minimum target (if set)
      const wordCount = this.calculateWordCount(entry.content);
      const minWordCount = entry.topic?.minWordCount || 50; // Default minimum

      if (wordCount < minWordCount) {
        this.logger.log(`Novel entry ${entry.id} word count (${wordCount}) does not meet minimum target (${minWordCount})`);
        return false;
      }

      // All conditions met - should notify tutor
      this.logger.log(`Novel entry ${entry.id} qualifies for tutor notification`);
      return true;
    } catch (error) {
      this.logger.error(`Error checking notification eligibility for novel entry ${entry.id}: ${error.message}`, error.stack);
      // Default to not notifying on error to prevent spam
      return false;
    }
  }

  private calculateWordCount(content: string): number {
    return content
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0).length;
  }

  private async mapEntryToResponseDto(entry: NovelEntry): Promise<NovelEntryResponseDto> {
    // Get tutor greeting for the student
    let tutorGreeting: string | undefined = undefined;
    try {
      const novel = await this.novelRepository.findOne({
        where: { userId: entry.studentId },
        select: ['tutorGreeting'],
      });
      tutorGreeting = novel?.tutorGreeting;
    } catch (error) {
      this.logger.warn(`Failed to load tutor greeting for student ${entry.studentId}: ${error.message}`);
    }

    // Get skin information if skinId exists
    let skinInfo = undefined;
    if (entry.skinId) {
      try {
        // If skin relation is not loaded, load it separately using DiarySkinService
        let skinDto = null;

        if (entry.skin) {
          // Use the loaded skin relation
          const skinUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, entry.skinId);

          skinInfo = {
            id: entry.skin.id,
            name: entry.skin.name,
            description: entry.skin.description,
            previewImagePath: skinUrl,
            isActive: entry.skin.isActive,
            isGlobal: entry.skin.isGlobal,
            createdById: entry.skin.createdById,
            templateContent: entry.skin.templateContent || '<div>{{content}}</div>',
            isUsedIn: true,
          };
        } else {
          // Load skin data using DiarySkinService
          skinDto = await this.diarySkinService.getDiarySkinById(entry.skinId);

          skinInfo = {
            id: skinDto.id,
            name: skinDto.name,
            description: skinDto.description,
            previewImagePath: skinDto.previewImagePath, // This already includes the URL
            isActive: skinDto.isActive,
            isGlobal: skinDto.isGlobal,
            createdById: skinDto.createdById,
            templateContent: skinDto.templateContent || '<div>{{content}}</div>',
            isUsedIn: skinDto.isUsedIn || true,
          };
        }
      } catch (error) {
        this.logger.warn(`Failed to load skin information for entry ${entry.id}: ${error.message}`);
        // Provide fallback skin info
        const skinUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, entry.skinId);
        skinInfo = {
          id: entry.skinId,
          name: 'Default Skin',
          description: 'Skin details could not be loaded',
          previewImagePath: skinUrl,
          isActive: true,
          isGlobal: true,
          createdById: null,
          templateContent: '<div>{{content}}</div>',
          isUsedIn: true,
        };
      }
    }

    // Get original reviewed version if it exists
    let originalReviewedVersion = undefined;
    if (entry.originalReviewedVersion) {
      // Use the loaded relation if available
      originalReviewedVersion = {
        id: entry.originalReviewedVersion.id,
        content: entry.originalReviewedVersion.content,
        versionNumber: entry.originalReviewedVersion.versionNumber,
        wordCount: entry.originalReviewedVersion.wordCount,
        createdAt: entry.originalReviewedVersion.createdAt,
        metaData: entry.originalReviewedVersion.metaData,
        isLatest: entry.originalReviewedVersion.isLatest,
      };
    } else if (entry.originalReviewedVersionId) {
      // Fallback to database query if relation not loaded
      try {
        const originalVersion = await this.novelEntryHistoryRepository.findOne({
          where: { id: entry.originalReviewedVersionId },
        });
        if (originalVersion) {
          originalReviewedVersion = {
            id: originalVersion.id,
            content: originalVersion.content,
            versionNumber: originalVersion.versionNumber,
            wordCount: originalVersion.wordCount,
            createdAt: originalVersion.createdAt,
            metaData: originalVersion.metaData,
            isLatest: originalVersion.isLatest,
          };
        }
      } catch (error) {
        this.logger.warn(`Failed to load original reviewed version for entry ${entry.id}: ${error.message}`);
      }
    }

    return {
      id: entry.id,
      topicId: entry.topicId,
      studentId: entry.studentId,
      content: entry.content,
      wordCount: entry.wordCount,
      status: entry.status,
      skinId: entry.skinId,
      backgroundColor: entry.backgroundColor,
      submittedAt: entry.submittedAt,
      reviewedAt: entry.reviewedAt,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt,
      topic: entry.topic
        ? {
            id: entry.topic.id,
            title: entry.topic.title,
            sequenceTitle: entry.topic.sequenceTitle,
            category: entry.topic.category,
            instruction: entry.topic.instruction,
            isActive: entry.topic.isActive,
            minWordCount: entry.topic.minWordCount,
            maxWordCount: entry.topic.maxWordCount,
            createdAt: entry.topic.createdAt,
            updatedAt: entry.topic.updatedAt,
          }
        : undefined,
      feedbacks: entry.feedbacks?.map((feedback) => ({
        id: feedback.id,
        entryId: feedback.entryId,
        tutorId: feedback.tutorId,
        feedback: feedback.feedback,
        createdAt: feedback.createdAt,
        updatedAt: feedback.updatedAt,
      })),
      correction: entry.correction
        ? {
            id: entry.correction.id,
            entryId: entry.correction.entryId,
            tutorId: entry.correction.tutorId,
            correction: entry.correction.correction,
            score: entry.correction.score,
            createdAt: entry.correction.createdAt,
            updatedAt: entry.correction.updatedAt,
          }
        : undefined,
      skin: skinInfo,
      tutorGreeting: tutorGreeting,
      totalEditHistory: await this.calculateEditHistoryCount(entry.id),
      originalReviewedVersion: originalReviewedVersion,

      // NEW: Submission tracking fields
      submittedVersionCount: entry.submittedVersionCount || 0,
      canSubmitNewVersion: entry.canSubmitNewVersion ?? true,
      lastSubmittedAt: entry.lastSubmittedAt,
      lastReviewedAt: entry.lastReviewedAt,

      // NEW: Resubmission tracking fields
      isResubmission: entry.isResubmission || false,
      resubmissionType: entry.resubmissionType,
      previousReviewCount: entry.previousReviewCount || 0,
      previousConfirmationCount: entry.previousConfirmationCount || 0,
    };
  }

  // Version History Methods
  async getNovelEntryHistory(entryId: string, userId: string): Promise<NovelEntryHistoryResponseDto> {
    return this.novelEntryHistoryService.getVersionHistory(entryId, userId);
  }

  async getNovelEntryVersion(versionId: string, userId: string): Promise<NovelEntryVersionDto> {
    return this.novelEntryHistoryService.getVersion(versionId, userId);
  }

  async restoreNovelEntryVersion(entryId: string, versionId: string, userId: string): Promise<NovelEntryResponseDto> {
    try {
      // Restore the version using the history service
      await this.novelEntryHistoryService.setLatestVersion(entryId, versionId, userId);

      // Return the updated entry
      return this.getEntryById(entryId, userId);
    } catch (error) {
      this.logger.error(`Error restoring novel entry version ${versionId} for entry ${entryId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Calculate edit history count from actual history table records
   */
  private async calculateEditHistoryCount(entryId: string): Promise<number> {
    try {
      const count = await this.novelEntryHistoryRepository.count({
        where: { novelEntryId: entryId },
      });
      return count;
    } catch (error) {
      this.logger.warn(`Failed to calculate edit history count for novel entry ${entryId}: ${error.message}`);
      return 0;
    }
  }

  /**
   * Send submission notification asynchronously (non-blocking)
   */
  private sendSubmissionNotificationAsync(
    entry: NovelEntry,
    status: NovelEntryStatus,
    submissionNumber?: number,
    isResubmission?: boolean,
    resubmissionType?: 'after_review' | 'after_confirmation' | null,
  ): void {
    // Fire and forget - don't await this to avoid blocking submission
    this.sendSubmissionNotificationAsyncInternal(
      entry,
      status,
      submissionNumber,
      isResubmission,
      resubmissionType,
    ).catch(error => {
      this.logger.error(`Failed to send async novel submission notification: ${error.message}`, error.stack);
    });
  }

  /**
   * Internal async notification method
   */
  private async sendSubmissionNotificationAsyncInternal(
    entry: NovelEntry,
    status: NovelEntryStatus,
    submissionNumber?: number,
    isResubmission?: boolean,
    resubmissionType?: 'after_review' | 'after_confirmation' | null,
  ): Promise<void> {
    try {
      // Find assigned tutor
      const tutorMapping = await this.studentTutorMappingRepository.findOne({
        where: {
          studentId: entry.studentId,
          status: MappingStatus.ACTIVE,
        },
        relations: ['tutor'],
      });

      if (!tutorMapping || !tutorMapping.tutor) {
        this.logger.warn(`No assigned tutor found for student ${entry.studentId}`);
        return;
      }

      const tutor = tutorMapping.tutor;

      // Determine notification title and message based on submission type
      let title = 'New Novel Entry Submission';
      let message = `A student has submitted a novel entry for review.`;

      if (isResubmission) {
        if (resubmissionType === 'after_review') {
          title = 'Novel Entry Resubmitted After Review';
          message = `A student has resubmitted a novel entry after your review (Submission #${submissionNumber}).`;
        } else if (resubmissionType === 'after_confirmation') {
          title = 'Novel Entry Resubmitted After Confirmation';
          message = `A student has resubmitted a novel entry after confirmation (Submission #${submissionNumber}).`;
        }
      }

      // Create HTML content for the notification
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: #333;">${title}</h2>
          </div>
          <div style="margin-bottom: 20px;">
            <p>Hello ${tutor.name},</p>
            <p>${message}</p>
            <p><strong>Student:</strong> ${entry.studentId}</p>
            <p><strong>Word Count:</strong> ${entry.wordCount || 0} words</p>
            ${submissionNumber ? `<p><strong>Submission Number:</strong> ${submissionNumber}</p>` : ''}
          </div>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
            <p>This is an automated message from the HEC system.</p>
            <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
          </div>
        </div>
      `;

      // Send notification asynchronously
      await this.asyncNotificationHelper.notifyAsync(
        tutor.id,
        NotificationType.NOVEL_SUBMISSION,
        title,
        message,
        {
          relatedEntityId: entry.id,
          relatedEntityType: 'novel_entry',
          htmlContent: htmlContent,
          sendEmail: true,
          sendPush: true,
          sendInApp: true,
          sendMobile: true,
          sendSms: false,
          sendRealtime: false,
        },
      );

      this.logger.log(`Queued novel submission notification for tutor ${tutor.id}: ${title}`);
    } catch (error) {
      this.logger.error(`Error sending async novel submission notification: ${error.message}`, error.stack);
      throw error;
    }
  }
}
