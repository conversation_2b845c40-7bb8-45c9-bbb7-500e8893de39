import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NovelEntry, NovelEntryStatus } from '../../database/entities/novel-entry.entity';
import { NovelCorrection } from '../../database/entities/novel-correction.entity';
import { NovelTopic } from '../../database/entities/novel-topic.entity';
import { AwardCriteria, AwardFrequency } from '../../database/entities/award.entity';
import { AwardsService } from '../awards/awards.service';

@Injectable()
export class NovelAwardService {
  private readonly logger = new Logger(NovelAwardService.name);

  constructor(
    @InjectRepository(NovelEntry)
    private novelEntryRepository: Repository<NovelEntry>,
    @InjectRepository(NovelCorrection)
    private novelCorrectionRepository: Repository<NovelCorrection>,
    @InjectRepository(NovelTopic)
    private novelTopicRepository: Repository<NovelTopic>,
    @Inject(forwardRef(() => AwardsService))
    private readonly awardsService: AwardsService,
  ) {}

  // Client-required award names for novel module
  private readonly CLIENT_AWARD_NAMES = ['Best Writer Award', 'Best Perfect Award', 'Best Performance Award'];

  /**
   * Generate novel awards for a specific date range
   * @param startDate Start date of the period
   * @param endDate End date of the period
   */
  async generateAwardsForRange(startDate: Date, endDate: Date): Promise<void> {
    this.logger.log(`Generating novel awards for period: ${startDate.toISOString()} to ${endDate.toISOString()}`);

    try {
      // Get all active novel awards that match client requirements
      const allAwardsResponse = await this.awardsService.getAllAwards(
        'novel' as any,
        false, // only active awards
        undefined,
        undefined,
        this.determineFrequency(startDate, endDate),
      );
      const awards = allAwardsResponse.items.filter((award) => this.CLIENT_AWARD_NAMES.includes(award.name));

      if (awards.length === 0) {
        this.logger.log('No client-required novel awards found');
        return;
      }

      this.logger.log(`Found ${awards.length} client-required novel awards: ${awards.map((a) => a.name).join(', ')}`);

      // Filter out any non-client awards
      const filteredCount = allAwardsResponse.items.length - awards.length;
      if (filteredCount > 0) {
        this.logger.log(`Filtered out ${filteredCount} non-client awards from calculation`);
      }

      // Get all students who submitted novels in the period
      const studentSubmissions = await this.getStudentSubmissionsInPeriod(startDate, endDate);

      if (studentSubmissions.length === 0) {
        this.logger.log('No novel submissions found in the period');
        return;
      }

      // Process each award
      for (const award of awards) {
        await this.processAward(award, studentSubmissions, startDate, endDate);
      }

      this.logger.log('Novel award generation completed successfully');
    } catch (error) {
      this.logger.error(`Error generating novel awards: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Determine award frequency based on date range
   */
  private determineFrequency(startDate: Date, endDate: Date): AwardFrequency {
    const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    if (days <= 7) return AwardFrequency.WEEKLY;
    if (days <= 31) return AwardFrequency.MONTHLY;
    if (days <= 93) return AwardFrequency.QUARTERLY;
    return AwardFrequency.YEARLY;
  }

  /**
   * Process a specific award
   */
  private async processAward(
    award: any, // Use any to accept both Award and AwardResponseDto
    studentSubmissions: any[],
    startDate: Date,
    endDate: Date,
  ): Promise<void> {
    this.logger.log(`Processing award: ${award.name}`);

    const scorers = [];

    for (const studentData of studentSubmissions) {
      const userId = studentData.userId;

      // Skip if userId is missing
      if (!userId) {
        this.logger.warn(`Skipping student data - missing userId in award processing`);
        continue;
      }

      let totalScore = 0;
      let meetsMinimumCriteria = true;

      // Calculate scores for each criterion
      for (const criterion of award.criteria) {
        switch (criterion) {
          case AwardCriteria.NOVEL_PERFORMANCE: {
            const novelScore = await this.calculateNovelPerformanceScore(userId, studentData, award.criteriaConfig, award.name);

            const minScore = award.criteriaConfig?.minScore || 0;
            if (novelScore < minScore) {
              meetsMinimumCriteria = false;
              break;
            }

            totalScore += novelScore;
            break;
          }
        }

        if (!meetsMinimumCriteria) break;
      }

      if (meetsMinimumCriteria && totalScore > 0) {
        scorers.push({
          userId,
          totalScore,
          metrics: {
            totalNovels: studentData.entries.length,
            averageScore: studentData.averageScore,
            completionRate: studentData.completionRate,
            averageWordCount: studentData.averageWordCount,
            submissionFrequency: studentData.submissionFrequency,
          },
        });
      }
    }

    // Sort by score and select winners
    scorers.sort((a, b) => b.totalScore - a.totalScore);
    const maxWinners = award.criteriaConfig?.maxWinners || 1;
    const winners = scorers.slice(0, maxWinners);

    // Create award records
    for (const winner of winners) {
      // Final validation before creating award winner
      if (!winner.userId) {
        this.logger.error(`Cannot create award winner - missing userId for award ${award.name}`);
        continue;
      }

      if (!award.id) {
        this.logger.error(`Cannot create award winner - missing awardId for award ${award.name}`);
        continue;
      }

      await this.awardsService.createAwardWinner({
        userId: winner.userId,
        awardId: award.id,
        awardDate: endDate.toISOString().split('T')[0], // Convert to YYYY-MM-DD format
        awardReason: `Achieved ${winner.totalScore.toFixed(1)} points in ${award.name}`,
        metadata: {
          ...winner.metrics,
          calculationPeriod: {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          },
          totalScore: winner.totalScore,
        },
      });
    }

    this.logger.log(`Award ${award.name} processed: ${winners.length} winners selected from ${scorers.length} candidates`);
  }

  /**
   * Calculate novel performance score based on award type
   */
  private async calculateNovelPerformanceScore(userId: string, studentData: any, criteriaConfig: any, awardName: string): Promise<number> {
    try {
      const entries = studentData.entries;
      const averageScore = studentData.averageScore;
      const completionRate = studentData.completionRate;
      const averageWordCount = studentData.averageWordCount;
      const submissionFrequency = studentData.submissionFrequency;

      let combinedScore = 0;

      if (awardName.includes('Best Writer')) {
        // Best Writer Award: Focus on most likes/engagement (for novels, use word count and quality as proxy)
        const engagementScore = Math.min((averageWordCount / 500) * 100, 100); // Higher word count shows engagement
        const qualityScore = Math.min(averageScore * 10, 100); // Convert to 0-100 scale
        combinedScore = engagementScore * 0.6 + qualityScore * 0.4; // Prioritize engagement
      } else if (awardName.includes('Best Perfect')) {
        // Best Perfect Award: Focus on fewest errors (highest accuracy/quality)
        const qualityScore = Math.min(averageScore * 10, 100); // Primary focus on quality
        const completionScore = Math.min(completionRate, 100); // Consistency in completion
        const consistencyScore = Math.min(submissionFrequency * 20, 100); // Regular submissions
        combinedScore = qualityScore * 0.7 + completionScore * 0.2 + consistencyScore * 0.1; // Heavily weighted toward quality
      } else if (awardName.includes('Best Performance')) {
        // Best Performance Award: Comprehensive performance
        const frequencyScore = Math.min(submissionFrequency * 25, 100);
        const improvementScore = this.calculateImprovementScore(entries);
        const engagementScore = Math.min((averageWordCount / 300) * 100, 100); // Engagement through word count
        combinedScore = frequencyScore * 0.4 + improvementScore * 0.3 + engagementScore * 0.3;
      }

      this.logger.log(`Novel performance score for user ${userId} (${awardName}): ${combinedScore.toFixed(1)}
        (Novels: ${entries.length}, Avg Score: ${averageScore.toFixed(1)},
        Completion: ${completionRate.toFixed(1)}%, Avg Words: ${averageWordCount})`);

      return Math.round(combinedScore);
    } catch (error) {
      this.logger.error(`Error calculating novel performance score for user ${userId}: ${error.message}`);
      return 0;
    }
  }

  /**
   * Calculate improvement score based on score progression
   */
  private calculateImprovementScore(entries: any[]): number {
    if (entries.length < 2) return 50; // Default score for insufficient data

    // Sort entries by submission date
    const sortedEntries = entries.sort((a, b) => new Date(a.submittedAt).getTime() - new Date(b.submittedAt).getTime());

    const firstHalf = sortedEntries.slice(0, Math.ceil(sortedEntries.length / 2));
    const secondHalf = sortedEntries.slice(Math.ceil(sortedEntries.length / 2));

    const firstHalfAvg = firstHalf.reduce((sum, entry) => sum + (entry.score || 0), 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, entry) => sum + (entry.score || 0), 0) / secondHalf.length;

    const improvement = ((secondHalfAvg - firstHalfAvg) / Math.max(firstHalfAvg, 1)) * 100;

    // Convert improvement to 0-100 score (0% improvement = 50 points, positive improvement gets bonus)
    return Math.min(Math.max(50 + improvement * 5, 0), 100);
  }

  /**
   * Get student submissions in the specified period
   */
  private async getStudentSubmissionsInPeriod(startDate: Date, endDate: Date): Promise<any[]> {
    const entries = await this.novelEntryRepository
      .createQueryBuilder('entry')
      .leftJoin('entry.correction', 'correction')
      .where('entry.status = :status', {
        status: NovelEntryStatus.REVIEWED,
      })
      .andWhere('entry.submittedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere('entry.studentId IS NOT NULL') // Exclude entries without studentId
      .select(['entry.studentId as userId', 'COUNT(entry.id) as entryCount', 'AVG(correction.score) as averageScore', 'AVG(entry.wordCount) as averageWordCount'])
      .groupBy('entry.studentId')
      .getRawMany();

    // Get detailed entry data for each student
    const studentData = [];
    for (const entry of entries) {
      const userId = entry.userId;

      // Skip entries without valid user ID
      if (!userId) {
        this.logger.warn(`Skipping novel entry - missing studentId`);
        continue;
      }

      // Get all entries for this student in the period
      const userEntries = await this.novelEntryRepository
        .createQueryBuilder('entry')
        .leftJoin('entry.correction', 'correction')
        .where('entry.studentId = :userId', { userId })
        .andWhere('entry.status = :status', {
          status: NovelEntryStatus.REVIEWED,
        })
        .andWhere('entry.submittedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
        .select(['entry.id', 'entry.submittedAt', 'entry.wordCount', 'correction.score'])
        .getRawMany();

      // Get total available topics in the period
      const availableTopics = await this.novelTopicRepository
        .createQueryBuilder('topic')
        .where('topic.createdAt <= :endDate', { endDate })
        .andWhere('topic.isActive = :isActive', { isActive: true })
        .getCount();

      const completionRate = availableTopics > 0 ? (userEntries.length / availableTopics) * 100 : 0;

      // Calculate submission frequency (entries per week)
      const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const submissionFrequency = userEntries.length / Math.max(periodDays / 7, 1);

      studentData.push({
        userId,
        entries: userEntries,
        averageScore: parseFloat(entry.averageScore) || 0,
        averageWordCount: parseFloat(entry.averageWordCount) || 0,
        completionRate: Math.min(completionRate, 100),
        submissionFrequency,
      });
    }

    return studentData;
  }
}
