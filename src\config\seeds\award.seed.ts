import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Award, AwardModule, AwardCriteria, AwardFrequency } from '../../database/entities/award.entity';

/**
 * Unified Award Seeder
 * 
 * This seeder handles all awards from all modules (Diary, Essay, Novel).
 * It creates new awards and updates existing ones completely.
 */
@Injectable()
export class AwardSeed {
  private readonly logger = new Logger(AwardSeed.name);

  constructor(
    @InjectRepository(Award)
    private readonly awardRepository: Repository<Award>,
  ) {}

  async seed(): Promise<void> {
    this.logger.log('Starting unified award seeding...');

    // Define all awards from all modules in one place
    const allAwards = [
      // === DIARY AWARDS ===
      
      // HEC Client Required Awards - Monthly
      {
        name: 'Best Writer Award',
        description: 'Awarded monthly to the student with exceptional writing quality and consistency in diary entries',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 150,
        isActive: true,
        criteriaConfig: {
          minScore: 1,
          entriesRequired: 1,
          targetEntries: 1,
          maxWinners: 5,
        },
      },
      {
        name: 'Best Designer Award',
        description: 'Awarded monthly to the student with the most creative and engaging diary designs',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_DECORATION],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 150,
        isActive: true,
        criteriaConfig: {
          minStudentLikes: 0,
          minDesignVariety: 0,
          minSkinChanges: 0,
          minDecorationScore: 0,
          maxWinners: 5,
        },
      },
      {
        name: 'Best Perfect Award',
        description: 'Awarded monthly to the student who excels in all aspects of diary keeping',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE, AwardCriteria.ATTENDANCE, AwardCriteria.DIARY_DECORATION],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 200,
        isActive: true,
        criteriaConfig: {
          minScore: 1,
          entriesRequired: 1,
          daysRequired: 1,
          minStudentLikes: 0,
          minDesignVariety: 0,
          minDecorationScore: 0,
          maxWinners: 5,
        },
      },
      {
        name: 'Best Performance Award',
        description: 'Awarded monthly to the student with the best overall diary performance',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 150,
        isActive: true,
        criteriaConfig: {
          minScore: 1,
          entriesRequired: 1,
          maxWinners: 5,
        },
      },

      // HEC Client Required Awards - Yearly
      {
        name: 'Best Writer Award',
        description: 'Awarded yearly to the student with exceptional writing quality and consistency in diary entries',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 1500,
        isActive: true,
        criteriaConfig: {
          minScore: 80,
          entriesRequired: 100,
          targetEntries: 200,
          maxWinners: 1,
        },
      },
      {
        name: 'Best Designer Award',
        description: 'Awarded yearly to the student with the most creative and engaging diary designs',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_DECORATION],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 1500,
        isActive: true,
        criteriaConfig: {
          minStudentLikes: 50,
          minDesignVariety: 3,
          minSkinChanges: 10,
          minDecorationScore: 50,
          maxWinners: 1,
        },
      },
      {
        name: 'Best Perfect Award',
        description: 'Awarded yearly to the student who excels in all aspects of diary keeping',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE, AwardCriteria.ATTENDANCE, AwardCriteria.DIARY_DECORATION],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 2000,
        isActive: true,
        criteriaConfig: {
          minScore: 80,
          entriesRequired: 150,
          daysRequired: 250,
          minStudentLikes: 100,
          minDesignVariety: 5,
          minDecorationScore: 60,
          maxWinners: 1,
        },
      },
      {
        name: 'Best Performance Award',
        description: 'Awarded yearly to the student with the best overall diary performance',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 1500,
        isActive: true,
        criteriaConfig: {
          minScore: 80,
          entriesRequired: 100,
          maxWinners: 1,
        },
      },
      {
        name: 'Best Friendship Award',
        description: 'Awarded monthly to the student with the best social engagement and friendship building',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_FRIENDSHIP],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 150,
        isActive: true,
        criteriaConfig: {
          minFriendships: 1,
          minShares: 0,
          minLikesGiven: 0,
          minLikesReceived: 0,
          maxWinners: 5,
        },
      },
      {
        name: 'Best Friendship Award',
        description: 'Awarded yearly to the student with the best social engagement and friendship building',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_FRIENDSHIP],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 1500,
        isActive: true,
        criteriaConfig: {
          minFriendships: 5,
          minShares: 10,
          minLikesGiven: 20,
          minLikesReceived: 20,
          maxWinners: 1,
        },
      },

      // === ESSAY AWARDS ===
      
      // HEC Client Required Awards - Monthly
      {
        name: 'Best Writer Award',
        description: 'Awarded monthly to the student with exceptional writing quality and consistency in essay submissions',
        module: AwardModule.ESSAY,
        criteria: [AwardCriteria.ESSAY_PERFORMANCE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 150,
        isActive: true,
        criteriaConfig: {
          minScore: 1,
          minEssays: 1,
          maxWinners: 5,
        },
      },
      {
        name: 'Best Perfect Award',
        description: 'Awarded monthly to the student who excels in all aspects of essay writing',
        module: AwardModule.ESSAY,
        criteria: [AwardCriteria.ESSAY_PERFORMANCE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 200,
        isActive: true,
        criteriaConfig: {
          minScore: 7,
          minEssays: 5,
          minCompletionRate: 90,
          maxWinners: 1,
        },
      },

      // HEC Client Required Awards - Yearly
      {
        name: 'Best Writer Award',
        description: 'Awarded yearly to the student with exceptional writing quality and consistency in essay submissions',
        module: AwardModule.ESSAY,
        criteria: [AwardCriteria.ESSAY_PERFORMANCE],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 1500,
        isActive: true,
        criteriaConfig: {
          minScore: 8,
          minEssays: 30,
          maxWinners: 1,
        },
      },
      {
        name: 'Best Perfect Award',
        description: 'Awarded yearly to the student who excels in all aspects of essay writing',
        module: AwardModule.ESSAY,
        criteria: [AwardCriteria.ESSAY_PERFORMANCE],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 2000,
        isActive: true,
        criteriaConfig: {
          minScore: 8,
          minEssays: 40,
          minCompletionRate: 95,
          maxWinners: 1,
        },
      },

      // === NOVEL AWARDS ===
      
      // HEC Client Required Awards - Monthly
      {
        name: 'Best Writer Award',
        description: 'Awarded monthly to the student with exceptional writing quality and consistency in novel entries',
        module: AwardModule.NOVEL,
        criteria: [AwardCriteria.NOVEL_PERFORMANCE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 150,
        isActive: true,
        criteriaConfig: {
          minScore: 1,
          minNovels: 1,
          maxWinners: 5,
        },
      },
      {
        name: 'Best Perfect Award',
        description: 'Awarded monthly to the student who excels in all aspects of novel writing',
        module: AwardModule.NOVEL,
        criteria: [AwardCriteria.NOVEL_PERFORMANCE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 200,
        isActive: true,
        criteriaConfig: {
          minScore: 7,
          minNovels: 3,
          minCompletionRate: 80,
          maxWinners: 1,
        },
      },

      // HEC Client Required Awards - Yearly
      {
        name: 'Best Writer Award',
        description: 'Awarded yearly to the student with exceptional writing quality and consistency in novel entries',
        module: AwardModule.NOVEL,
        criteria: [AwardCriteria.NOVEL_PERFORMANCE],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 1500,
        isActive: true,
        criteriaConfig: {
          minScore: 8,
          minNovels: 20,
          maxWinners: 1,
        },
      },
      {
        name: 'Best Perfect Award',
        description: 'Awarded yearly to the student who excels in all aspects of novel writing',
        module: AwardModule.NOVEL,
        criteria: [AwardCriteria.NOVEL_PERFORMANCE],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 2000,
        isActive: true,
        criteriaConfig: {
          minScore: 8,
          minNovels: 30,
          minCompletionRate: 95,
          maxWinners: 1,
        },
      },
    ];

    let createdCount = 0;
    let updatedCount = 0;
    let skippedCount = 0;

    // Process each award
    for (const awardData of allAwards) {
      try {
        // Find existing award by name, module, and frequency (unique combination)
        const existingAward = await this.awardRepository.findOne({
          where: {
            name: awardData.name,
            module: awardData.module,
            frequency: awardData.frequency,
          },
        });

        if (existingAward) {
          // Update existing award completely
          existingAward.description = awardData.description;
          existingAward.criteria = awardData.criteria;
          existingAward.criteriaConfig = awardData.criteriaConfig;
          existingAward.isActive = awardData.isActive;
          existingAward.rewardPoints = awardData.rewardPoints;
          
          await this.awardRepository.save(existingAward);
          
          this.logger.log(`Updated: ${awardData.name} (${awardData.module} ${awardData.frequency})`);
          updatedCount++;
        } else {
          // Create new award
          const award = this.awardRepository.create(awardData);
          await this.awardRepository.save(award);
          this.logger.log(`Created: ${awardData.name} (${awardData.module} ${awardData.frequency})`);
          createdCount++;
        }
      } catch (error) {
        this.logger.error(`Error processing award ${awardData.name}: ${error.message}`);
        skippedCount++;
      }
    }

    this.logger.log(`Unified award seeding completed:`);
    this.logger.log(`   Created: ${createdCount} awards`);
    this.logger.log(`   Updated: ${updatedCount} awards`);
    this.logger.log(`   Skipped: ${skippedCount} awards`);
    this.logger.log(`   Total processed: ${allAwards.length} awards`);
  }


}
