import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsInt, IsOptional, IsBoolean, Min, Max, IsUUID, ValidateIf, registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

// Custom validator to ensure minWordLimit <= wordLimit
export function IsMinWordLimitValid(relatedPropertyName: string, validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isMinWordLimitValid',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [relatedPropertyName],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];
          return typeof value === 'number' && typeof relatedValue === 'number' && value <= relatedValue;
        },
        defaultMessage(args: ValidationArguments) {
          return 'Minimum word limit must be less than or equal to maximum word limit';
        },
      },
    });
  };
}

// Request DTOs
export class CreateDiarySettingsTemplateDto {
  @ApiProperty({ example: 'Beginner Level', description: 'Title of the diary settings template' })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({ example: 1, description: 'Level of difficulty (1-10)' })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  @Max(10)
  level: number;

  @ApiProperty({ example: 100, description: 'Maximum word limit for the diary entry' })
  @IsNotEmpty()
  @IsInt()
  @Min(10)
  wordLimit: number;

  @ApiProperty({ example: 20, description: 'Minimum word limit for the diary entry' })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  @IsMinWordLimitValid('wordLimit')
  minWordLimit: number;

  @ApiProperty({ example: 'Settings for beginners with basic vocabulary', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ example: true, default: true, description: 'Whether the settings template is active' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdateDiarySettingsTemplateDto {
  @ApiProperty({ example: 'Advanced Level', required: false })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ example: 5, required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(10)
  level?: number;

  @ApiProperty({ example: 500, required: false })
  @IsOptional()
  @IsInt()
  @Min(10)
  wordLimit?: number;

  @ApiProperty({ example: 30, required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  @ValidateIf((o) => o.minWordLimit !== undefined && o.wordLimit !== undefined)
  @IsMinWordLimitValid('wordLimit')
  minWordLimit?: number;

  @ApiProperty({ example: 'Settings for advanced students', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ example: true, required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

// Response DTOs
export class DiarySettingsTemplateResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  level: number;

  @ApiProperty()
  wordLimit: number;

  @ApiProperty()
  minWordLimit: number;

  @ApiProperty({ required: false })
  description?: string;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class DiaryEntrySettingsResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  settingsTemplateId: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  level: number;

  @ApiProperty()
  wordLimit: number;

  @ApiProperty()
  minWordLimit: number;

  @ApiProperty({ required: false })
  description?: string;

  @ApiProperty({ required: false })
  isActive?: boolean;
}
