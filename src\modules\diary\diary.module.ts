import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { DiaryController } from './diary.controller';
import { DiaryService } from './diary.service';
import { TutorDiaryController } from './tutor-diary.controller';
import { AdminDiaryController } from './admin-diary.controller';
import { AdminDiarySettingsController } from './admin-diary-settings.controller';
import { DiarySettingsController } from './diary-settings.controller';
import { DiarySettingsInitializer } from './diary-settings.initializer';

import { Diary } from '../../database/entities/diary.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { DiaryEntryHistory } from '../../database/entities/diary-entry-history.entity';
import { DiarySkin } from '../../database/entities/diary-skin.entity';
import { DiarySkinRegistry } from '../../database/entities/diary-skin-registry.entity';
import { DiaryQrRegistry } from '../../database/entities/diary-qr-registry.entity';
import { StudentDiarySkin } from '../../database/entities/student-diary-skin.entity';
import { StudentDiarySkinRegistry } from '../../database/entities/student-diary-skin-registry.entity';
import { StudentFriendship } from '../../database/entities/student-friendship.entity';
import { DiaryFollowRequest } from '../../database/entities/diary-follow-request.entity';
import { AwardsModule } from '../awards/awards.module';
import { DiaryFeedback } from '../../database/entities/diary-feedback.entity';
import { DiaryShare } from '../../database/entities/diary-share.entity';
import { DiaryAward } from '../../database/entities/diary-award.entity';
import { DiarySettingsTemplate } from '../../database/entities/diary-settings-template.entity';
import { DiaryEntrySettings } from '../../database/entities/diary-entry-settings.entity';
import { DiaryCorrection } from '../../database/entities/diary-correction.entity';
import { DiaryEntryLike } from '../../database/entities/diary-entry-like.entity';
import { DiaryEntryFriendShare } from '../../database/entities/diary-entry-friend-share.entity';
import { User } from '../../database/entities/user.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { JwtService } from '@nestjs/jwt';
import { CommonModule } from '../../common/common.module';
import { ShopModule } from '../shop/shop.module';
import { AuthModule } from '../auth/auth.module';
import { NotificationModule } from '../notification/notification.module';

import { TutorMatchingModule } from '../tutor-matching/tutor-matching.module';
import { PlansModule } from '../plans/plans.module';
import { ChatModule } from '../chat/chat.module';
import { DiarySettingsService } from './diary-settings.service';
import { DiaryReviewService } from './diary-review.service';
import { DiaryEntryService } from './diary-entry.service';
import { DiaryEntryHistoryService } from './diary-entry-history.service';
import { DiarySkinService } from './diary-skin.service';
import { DiaryShareService } from './diary-share.service';
import { DiaryAwardService } from './diary-award.service';
import { DeeplinkModule } from '../../common/utils/deeplink.module';
import { SubscriptionFeatureGuard } from '../../common/guards/subscription-feature.guard';
import { DiaryMapperService } from './diary-mapper.service';
import { DiaryLikeService } from './diary-like.service';
import { DiaryFriendShareService } from './diary-friend-share.service';
// Mission management imports
import { DiaryMission } from '../../database/entities/diary-mission.entity';
import { MissionDiaryEntry } from '../../database/entities/mission-diary-entry.entity';
import { MissionDiaryEntryHistory } from '../../database/entities/mission-diary-entry-history.entity';
import { Category } from '../../database/entities/category.entity';
import { MissionDiaryEntryFeedback } from '../../database/entities/mission-diary-entry-feedback.entity';
import { DiaryMissionService } from './diary-mission.service';
import { MissionDiaryEntryService } from './mission-diary-entry.service';
import { MissionDiaryEntryHistoryService } from './mission-diary-entry-history.service';
import { StudentMissionController } from './student-mission.controller';
import { TutorMissionController } from './tutor-mission.controller';
import { AdminDiaryMissionController } from './admin-mission.controller';
import { DiaryEntryAttendanceController } from './diary-entry-attendance.controller';
import { DiaryEntryAttendance } from '../../database/entities/diary-entry-attendance.entity';
import { StudentTutorMapping } from '../../database/entities/student-tutor-mapping.entity';
import { DiaryTutorService } from './diary-tutor.service';
import { ShopSkinMapping } from '../../database/entities/shop-skin-mapping.entity';
import { ShopItem } from '../../database/entities/shop-item.entity';
import { StudentOwnedItem } from '../../database/entities/student-owned-item.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Diary,
      DiaryEntry,
      DiaryEntryHistory,
      DiarySkin,
      DiarySkinRegistry,
      DiaryQrRegistry,
      StudentDiarySkin,
      StudentDiarySkinRegistry,
      StudentFriendship,
      DiaryFollowRequest,
      DiaryFeedback,
      DiaryShare,
      DiaryAward,
      DiarySettingsTemplate,
      DiaryEntrySettings,
      DiaryCorrection,
      DiaryEntryLike,
      DiaryEntryFriendShare,
      User,
      PlanFeature,
      DiaryMission,
      MissionDiaryEntry,
      MissionDiaryEntryHistory,
      MissionDiaryEntryFeedback,
      DiaryEntryAttendance,
      StudentTutorMapping,
      ShopSkinMapping,
      ShopItem,
      StudentOwnedItem,
      Category,
    ]),
    CommonModule,
    forwardRef(() => AwardsModule),
    forwardRef(() => ShopModule),
    forwardRef(() => AuthModule),
    forwardRef(() => TutorMatchingModule),
    forwardRef(() => PlansModule),
    forwardRef(() => NotificationModule),
    forwardRef(() => ChatModule),
    DeeplinkModule,
    MulterModule.register({
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
  ],
  controllers: [
    DiaryController,
    AdminDiaryController,
    AdminDiarySettingsController,
    DiarySettingsController,
    StudentMissionController,
    AdminDiaryMissionController,
    TutorMissionController,
    TutorDiaryController,
    DiaryEntryAttendanceController,
  ],
  providers: [
    {
      provide: DiaryService,
      useClass: DiaryService,
    },
    {
      provide: DiaryAwardService,
      useClass: DiaryAwardService,
    },
    {
      provide: DiaryEntryService,
      useClass: DiaryEntryService,
    },
    {
      provide: DiaryEntryHistoryService,
      useClass: DiaryEntryHistoryService,
    },
    {
      provide: DiaryReviewService,
      useClass: DiaryReviewService,
    },
    {
      provide: DiarySettingsService,
      useClass: DiarySettingsService,
    },
    {
      provide: DiarySkinService,
      useClass: DiarySkinService,
    },
    {
      provide: DiaryShareService,
      useClass: DiaryShareService,
    },
    {
      provide: DiaryMapperService,
      useClass: DiaryMapperService,
    },
    {
      provide: DiaryLikeService,
      useClass: DiaryLikeService,
    },
    {
      provide: DiaryFriendShareService,
      useClass: DiaryFriendShareService,
    },
    {
      provide: DiaryMissionService,
      useClass: DiaryMissionService,
    },
    {
      provide: MissionDiaryEntryService,
      useClass: MissionDiaryEntryService,
    },
    {
      provide: MissionDiaryEntryHistoryService,
      useClass: MissionDiaryEntryHistoryService,
    },
    {
      provide: DiaryTutorService,
      useClass: DiaryTutorService,
    },
    SubscriptionFeatureGuard,
    JwtService,
  ],
  exports: [DiaryService, DiaryAwardService, DiaryEntryService, DiarySettingsService, DiaryShareService, DiaryMissionService, DiaryTutorService, DiarySkinService],
})
export class DiaryModule {}
