import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { NovelEntry } from './novel-entry.entity';
import { User } from './user.entity';

@Entity('novel_correction')
export class NovelCorrection extends AuditableBaseEntity {
  @Column({ name: 'entry_id', type: 'uuid', nullable: false })
  entryId: string;

  @OneToOne(() => NovelEntry, (entry) => entry.correction)
  @JoinColumn({ name: 'entry_id' })
  entry: NovelEntry;

  @Column({ name: 'tutor_id', type: 'uuid', nullable: false })
  tutorId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'tutor_id' })
  tutor: User;

  @Column({ name: 'correction', type: 'text', nullable: false })
  correction: string;

  @Column({ name: 'score', type: 'integer', nullable: true })
  score: number;
}
