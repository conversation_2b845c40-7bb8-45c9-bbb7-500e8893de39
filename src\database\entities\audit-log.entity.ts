import { Entity, Column } from 'typeorm';
import { NonAuditableBaseEntity } from './base-entity';

export enum AuditAction {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  LOGIN = 'login',
  LOGOUT = 'logout',
  REGISTER = 'register',
  VERIFY = 'verify',
  RESET_PASSWORD = 'reset_password',
  CHANGE_PASSWORD = 'change_password',
  SUBSCRIBE = 'subscribe',
  UNSUBSCRIBE = 'unsubscribe',
}

@Entity()
export class AuditLog extends NonAuditableBaseEntity {
  @Column({ nullable: true })
  userId: string;

  @Column()
  action: string;

  @Column()
  resource: string;

  @Column({ type: 'json', nullable: true })
  details: Record<string, any>;

  @Column()
  ipAddress: string;

  @Column()
  userAgent: string;

  @Column()
  timestamp: Date;

  @Column({ nullable: true })
  status: string;

  @Column({ nullable: true })
  errorMessage: string;
}
