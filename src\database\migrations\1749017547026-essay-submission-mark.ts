import { MigrationInterface, QueryRunner } from 'typeorm';

export class EssaySubmissionMark1749017547026 implements MigrationInterface {
  name = 'EssaySubmissionMark1749017547026';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_task_submission_marking" ALTER COLUMN "points" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_task_submission_marking" ALTER COLUMN "points" SET NOT NULL`);
  }
}
