import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAdminConversationSupport1748200000000 implements MigrationInterface {
  name = 'AddAdminConversationSupport1748200000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new columns to conversation table
    await queryRunner.query(`
      ALTER TABLE "conversation"
      ADD COLUMN "is_admin_conversation" boolean NOT NULL DEFAULT false,
      ADD COLUMN "admin_conversation_user_id" uuid NULL
    `);

    // Add foreign key constraint for admin_conversation_user_id
    await queryRunner.query(`
      ALTER TABLE "conversation"
      ADD CONSTRAINT "FK_conversation_admin_conversation_user_id"
      FOREIGN KEY ("admin_conversation_user_id")
      REFERENCES "user"("id")
      ON DELETE SET NULL
    `);

    // Add new columns to message table
    await queryRunner.query(`
      ALTER TABLE "message"
      ADD COLUMN "actual_sender_id" uuid NULL
    `);

    // Add foreign key constraint for actual_sender_id
    await queryRunner.query(`
      ALTER TABLE "message"
      ADD CONSTRAINT "FK_message_actual_sender_id"
      FOREIGN KEY ("actual_sender_id")
      REFERENCES "user"("id")
      ON DELETE SET NULL
    `);

    // Create admin_conversation_participant table
    await queryRunner.query(`
      CREATE TABLE "admin_conversation_participant" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" timestamp NOT NULL DEFAULT now(),
        "updated_at" timestamp DEFAULT now(),
        "created_by" varchar(36),
        "updated_by" varchar(36),
        "conversation_id" uuid NOT NULL,
        "admin_id" uuid NOT NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "last_accessed_at" timestamp,
        "unread_count" integer NOT NULL DEFAULT 0,
        CONSTRAINT "PK_admin_conversation_participant" PRIMARY KEY ("id"),
        CONSTRAINT "FK_admin_conversation_participant_conversation_id"
          FOREIGN KEY ("conversation_id") REFERENCES "conversation"("id") ON DELETE CASCADE,
        CONSTRAINT "FK_admin_conversation_participant_admin_id"
          FOREIGN KEY ("admin_id") REFERENCES "user"("id") ON DELETE CASCADE
      )
    `);

    // Create unique index for conversation_id and admin_id
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_admin_conversation_participant_conversation_admin"
      ON "admin_conversation_participant" ("conversation_id", "admin_id")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop admin_conversation_participant table
    await queryRunner.query(`DROP TABLE "admin_conversation_participant"`);

    // Remove foreign key constraints and columns from message table
    await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_message_actual_sender_id"`);
    await queryRunner.query(`ALTER TABLE "message" DROP COLUMN "actual_sender_id"`);

    // Remove foreign key constraints and columns from conversation table
    await queryRunner.query(`ALTER TABLE "conversation" DROP CONSTRAINT "FK_conversation_admin_conversation_user_id"`);
    await queryRunner.query(`ALTER TABLE "conversation" DROP COLUMN "admin_conversation_user_id"`);
    await queryRunner.query(`ALTER TABLE "conversation" DROP COLUMN "is_admin_conversation"`);
  }
}
