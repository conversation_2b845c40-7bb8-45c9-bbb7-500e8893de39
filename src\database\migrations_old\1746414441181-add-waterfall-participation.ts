import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddWaterfallParticipation1746414441181 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create waterfall_participation table
    await queryRunner.query(`
            CREATE TABLE "waterfall_participation" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP DEFAULT now(),
                "created_by" character varying(36),
                "updated_by" character varying(36),
                "student_id" uuid NOT NULL,
                "set_id" uuid NOT NULL,
                "total_correct_answers" integer NOT NULL,
                "total_questions" integer NOT NULL,
                "score" float NOT NULL,
                CONSTRAINT "PK_waterfall_participation" PRIMARY KEY ("id")
            )
        `);

    // Create waterfall_answer table
    await queryRunner.query(`
            CREATE TABLE "waterfall_answer" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP DEFAULT now(),
                "created_by" character varying(36),
                "updated_by" character varying(36),
                "participation_id" uuid NOT NULL,
                "question_id" uuid NOT NULL,
                "submitted_answers" text NOT NULL,
                "is_correct" boolean NOT NULL,
                CONSTRAINT "PK_waterfall_answer" PRIMARY KEY ("id")
            )
        `);

    // Add foreign key constraints
    await queryRunner.query(`
            ALTER TABLE "waterfall_participation"
            ADD CONSTRAINT "FK_waterfall_participation_student"
            FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);

    await queryRunner.query(`
            ALTER TABLE "waterfall_participation"
            ADD CONSTRAINT "FK_waterfall_participation_set"
            FOREIGN KEY ("set_id") REFERENCES "waterfall_set"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);

    await queryRunner.query(`
            ALTER TABLE "waterfall_answer"
            ADD CONSTRAINT "FK_waterfall_answer_participation"
            FOREIGN KEY ("participation_id") REFERENCES "waterfall_participation"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);

    await queryRunner.query(`
            ALTER TABLE "waterfall_answer"
            ADD CONSTRAINT "FK_waterfall_answer_question"
            FOREIGN KEY ("question_id") REFERENCES "waterfall_question"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints first
    await queryRunner.query(`ALTER TABLE "waterfall_answer" DROP CONSTRAINT "FK_waterfall_answer_question"`);
    await queryRunner.query(`ALTER TABLE "waterfall_answer" DROP CONSTRAINT "FK_waterfall_answer_participation"`);
    await queryRunner.query(`ALTER TABLE "waterfall_participation" DROP CONSTRAINT "FK_waterfall_participation_set"`);
    await queryRunner.query(`ALTER TABLE "waterfall_participation" DROP CONSTRAINT "FK_waterfall_participation_student"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "waterfall_answer"`);
    await queryRunner.query(`DROP TABLE "waterfall_participation"`);
  }
}
