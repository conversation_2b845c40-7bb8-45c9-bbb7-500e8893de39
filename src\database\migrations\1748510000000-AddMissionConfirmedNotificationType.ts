import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMissionConfirmedNotificationType1748510000000 implements MigrationInterface {
  name = 'AddMissionConfirmedNotificationType1748510000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the new notification type to the enum
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'mission_confirmed'`);

    // Also add it to the user notification preference enum
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'mission_confirmed'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: PostgreSQL doesn't support removing enum values directly
    // This would require recreating the enum type, which is complex
    // For now, we'll leave the enum value in place
    console.log('Cannot remove enum values in PostgreSQL. The mission_confirmed value will remain.');
  }
}
