import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { KcpConfig } from '../interfaces/kcp.interface';

@Injectable()
export class KcpConfigService {
  private readonly logger = new Logger(KcpConfigService.name);
  private readonly config: KcpConfig;

  constructor(private configService: ConfigService) {
    this.config = this.loadConfig();
    this.validateConfig();
  }

  private loadConfig(): KcpConfig {
    return {
      siteCd: this.configService.get<string>('KCP_SITE_CD'),
      siteKey: this.configService.get<string>('KCP_SITE_KEY'),
      apiUrl: this.configService.get<string>('KCP_API_URL', 'https://stg-spl.kcp.co.kr'),
      tradeRegUrl: this.configService.get<string>('KCP_TRADE_REG_URL', '/std/tradeReg/register'),
      paymentUrl: this.configService.get<string>('KCP_PAYMENT_URL', '/gw/enc/v1/payment'),
      webhookSecret: this.configService.get<string>('KCP_WEBHOOK_SECRET'),
      timeout: this.configService.get<number>('KCP_TIMEOUT', 30000),
      retryAttempts: this.configService.get<number>('KCP_RETRY_ATTEMPTS', 3),
      environment: this.configService.get<'development' | 'staging' | 'production'>('NODE_ENV', 'development'),
    };
  }

  private validateConfig(): void {
    const requiredFields = ['siteCd', 'siteKey', 'webhookSecret'];
    const missingFields = requiredFields.filter((field) => !this.config[field]);

    if (missingFields.length > 0) {
      const errorMessage = `Missing required KCP configuration: ${missingFields.join(', ')}`;
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    this.logger.log('KCP configuration loaded successfully');
    this.logger.log(`Environment: ${this.config.environment}`);
    this.logger.log(`API URL: ${this.config.apiUrl}`);
    this.logger.log(`Site CD: ${this.config.siteCd}`);
  }

  getConfig(): KcpConfig {
    return { ...this.config };
  }

  getSiteCd(): string {
    return this.config.siteCd;
  }

  getSiteKey(): string {
    return this.config.siteKey;
  }

  getApiUrl(): string {
    return this.config.apiUrl;
  }

  getTradeRegUrl(): string {
    return `${this.config.apiUrl}${this.config.tradeRegUrl}`;
  }

  getPaymentUrl(): string {
    return `${this.config.apiUrl}${this.config.paymentUrl}`;
  }

  getWebhookSecret(): string {
    return this.config.webhookSecret;
  }

  getTimeout(): number {
    return this.config.timeout;
  }

  getRetryAttempts(): number {
    return this.config.retryAttempts;
  }

  isProduction(): boolean {
    return this.config.environment === 'production';
  }

  isStaging(): boolean {
    return this.config.environment === 'staging';
  }

  isDevelopment(): boolean {
    return this.config.environment === 'development';
  }

  /**
   * Get KCP certificate info (PEM certificate for API authentication)
   */
  getKcpCertInfo(): string {
    try {
      const fs = require('fs');
      const path = require('path');

      // Get certificate path from environment or use default
      const certPath = this.configService.get<string>('KCP_CERT_PATH', 'certificates/splCert.pem');

      // Resolve path relative to project root
      const fullCertPath = path.resolve(process.cwd(), certPath);

      this.logger.log(`Loading KCP certificate from: ${fullCertPath}`);

      // Read certificate file
      const certContent = fs.readFileSync(fullCertPath, 'utf8');

      // Validate certificate format
      if (!certContent.includes('-----BEGIN CERTIFICATE-----') || !certContent.includes('-----END CERTIFICATE-----')) {
        throw new Error('Invalid certificate format - must be PEM format');
      }

      this.logger.log('KCP certificate loaded successfully');
      return certContent.trim();
    } catch (error) {
      this.logger.error(`Failed to load KCP certificate: ${error.message}`);

      // In development/staging, provide fallback
      if (this.config.environment === 'staging') {
        this.logger.warn('Using fallback certificate for staging environment');
        return this.getFallbackCertificate();
      }

      throw new Error(`KCP certificate loading failed: ${error.message}`);
    }
  }

  /**
   * Get fallback certificate for development/test environments
   */
  private getFallbackCertificate(): string {
    return `-----BEGIN CERTIFICATE-----
MIIDgTCCAmmgAwIBAgIHBy4lYNG7ojANBgkqhkiG9w0BAQsFADBzMQswCQYDVQQG
EwJLUjEOMAwGA1UECAwFU2VvdWwxEDAOBgNVBAcMB0d1cm8tZ3UxFTATBgNVBAoM
DE5ITktDUCBDb3JwLjETMBEGA1UECwwKSVQgQ2VudGVyLjEWMBQGA1UEAwwNc3Bs
LmtjcC5jby5rcjAeFw0yMTA2MjkwMDM0MzdaFw0yNjA2MjgwMDM0MzdaMHAxCzAJ
BgNVBAYTAktSMQ4wDAYDVQQIDAVTZW91bDEQMA4GA1UEBwwHR3Vyby1ndTERMA8G
A1UECgwITG9jYWxXZWIxETAPBgNVBAsMCERFVlBHV0VCMRkwFwYDVQQDDBAyMDIx
MDYyOTEwMDAwMDI0MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAppkV
QkU4SwNTYbIUaNDVhu2w1uvG4qip0U7h9n90cLfKymIRKDiebLhLIVFctuhTmgY7
tkE7yQTNkD+jXHYufQ/qj06ukwf1BtqUVru9mqa7ysU298B6l9v0Fv8h3ztTYvfH
EBmpB6AoZDBChMEua7Or/L3C2vYtU/6lWLjBT1xwXVLvNN/7XpQokuWq0rnjSRTh
cXrDpWMbqYYUt/CL7YHosfBazAXLoN5JvTd1O9C3FPxLxwcIAI9H8SbWIQKhap7J
eA/IUP1Vk4K/o3Yiytl6Aqh3U1egHfEdWNqwpaiHPuM/jsDkVzuS9FV4RCdcBEsR
PnAWHz10w8CX7e7zdwIDAQABox0wGzAOBgNVHQ8BAf8EBAMCB4AwCQYDVR0TBAIw
ADANBgkqhkiG9w0BAQsFAAOCAQEAg9lYy+dM/8Dnz4COc+XIjEwr4FeC9ExnWaax
H6GlWjJbB94O2L26arrjT2hGl9jUzwd+BdvTGdNCpEjOz3KEq8yJhcu5mFxMskLn
HNo1lg5qtydIID6eSgew3vm6d7b3O6pYd+NHdHQsuMw5S5z1m+0TbBQkb6A9RKE1
md5/Yw+NymDy+c4NaKsbxepw+HtSOnma/R7TErQ/8qVioIthEpwbqyjgIoGzgOdE
FsF9mfkt/5k6rR0WX8xzcro5XSB3T+oecMS54j0+nHyoS96/llRLqFDBUfWn5Cay
7pJNWXCnw4jIiBsTBa3q95RVRyMEcDgPwugMXPXGBwNoMOOpuQ==
-----END CERTIFICATE-----`;
  }

  /**
   * Get request timeout for HTTP calls
   */
  getRequestTimeout(): number {
    return this.config.timeout;
  }

  /**
   * Get maximum retry attempts for failed requests
   */
  getMaxRetryAttempts(): number {
    return this.config.retryAttempts;
  }

  /**
   * Get environment-specific configuration
   */
  getEnvironmentConfig(): {
    apiUrl: string;
    environment: string;
    timeout: number;
    retryAttempts: number;
  } {
    return {
      apiUrl: this.config.apiUrl,
      environment: this.config.environment,
      timeout: this.config.timeout,
      retryAttempts: this.config.retryAttempts,
    };
  }

  /**
   * Validate webhook signature
   */
  validateWebhookSignature(payload: string, signature: string): boolean {
    try {
      // Implement signature validation logic here
      // This is a placeholder - actual implementation would use HMAC
      const crypto = require('crypto');
      const expectedSignature = crypto.createHmac('sha256', this.config.webhookSecret).update(payload).digest('hex');

      return signature === expectedSignature;
    } catch (error) {
      this.logger.error(`Error validating webhook signature: ${error.message}`);
      return false;
    }
  }

  /**
   * Generate order check value for KCP
   */
  generateOrderCheck(orderId: string, amount: number): string {
    try {
      const crypto = require('crypto');
      const data = `${orderId}${amount}${this.config.siteKey}`;
      return crypto.createHash('sha256').update(data).digest('hex');
    } catch (error) {
      this.logger.error(`Error generating order check: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get payment method configuration
   */
  getPaymentMethodConfig(paymentMethod: string): any {
    const baseConfig = {
      site_cd: this.config.siteCd,
      kcp_cert_info: this.getKcpCertInfo(),
      currency: 'KRW',
    };

    switch (paymentMethod) {
      case 'CARD':
        return {
          ...baseConfig,
          pay_method: '************',
          escw_used: 'N',
        };
      case 'BANK':
        return {
          ...baseConfig,
          pay_method: '************',
          escw_used: 'N',
        };
      case 'MOBX':
        return {
          ...baseConfig,
          pay_method: '************',
          escw_used: 'N',
        };
      case 'VCNT':
        return {
          ...baseConfig,
          pay_method: '************',
          escw_used: 'N',
        };
      default:
        throw new Error(`Unsupported payment method: ${paymentMethod}`);
    }
  }
}
