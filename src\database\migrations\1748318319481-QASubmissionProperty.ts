import { MigrationInterface, QueryRunner } from 'typeorm';

export class QASubmissionProperty1748318319481 implements MigrationInterface {
  name = 'QASubmissionProperty1748318319481';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" RENAME COLUMN "points" TO "score"`);
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" ALTER COLUMN "score" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" ALTER COLUMN "score" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" RENAME COLUMN "score" TO "points"`);
  }
}
