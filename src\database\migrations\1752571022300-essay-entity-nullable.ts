import { MigrationInterface, QueryRunner } from "typeorm";

export class EssayEntityNullable1752571022300 implements MigrationInterface {
    name = 'EssayEntityNullable1752571022300'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_090783a2cd92909ecf3f68fb2e"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_marking" ALTER COLUMN "submission_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_marking" ALTER COLUMN "submission_history_id" DROP NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_090783a2cd92909ecf3f68fb2e" ON "essay_task_submission_marking" ("submission_id", "submission_history_id") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submission_marking" ALTER COLUMN "submission_history_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_marking" ALTER COLUMN "submission_id" SET NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_090783a2cd92909ecf3f68fb2e" ON "essay_task_submission_marking" ("submission_id", "submission_history_id") `);
    }

}
