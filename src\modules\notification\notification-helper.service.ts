import { Injectable, Logger } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { NotificationChannel } from '../../database/entities/notification-delivery.entity';

/**
 * Helper service to simplify notification sending to a single line
 */
@Injectable()
export class NotificationHelperService {
  private readonly logger = new Logger(NotificationHelperService.name);

  constructor(private readonly notificationService: NotificationService) {}

  /**
   * Send a notification in a single line
   *
   * @param userId User ID to send notification to
   * @param type Notification type
   * @param title Notification title
   * @param message Notification message
   * @param options Additional options (relatedEntityId, relatedEntityType, channels, htmlContent, channel controls)
   * @returns Promise that resolves when notification is sent
   */
  async notify(
    userId: string,
    type: NotificationType,
    title: string,
    message: string,
    options?: {
      relatedEntityId?: string;
      relatedEntityType?: string;
      channels?: NotificationChannel[];
      htmlContent?: string; // HTML version of the message for rich notifications
      webLink?: string; // Web URL for the notification
      deepLink?: string; // Deep link for the notification
      // Channel control parameters
      sendEmail?: boolean;
      sendPush?: boolean;
      sendInApp?: boolean;
      sendMobile?: boolean;
      sendSms?: boolean;
      sendRealtime?: boolean;
    },
  ): Promise<void> {
    try {
      // Determine which channels to use based on the options
      let finalChannels = options?.channels;

      // If any channel control parameters are provided, override the channels array
      if (
        options?.sendEmail !== undefined ||
        options?.sendPush !== undefined ||
        options?.sendInApp !== undefined ||
        options?.sendMobile !== undefined ||
        options?.sendSms !== undefined ||
        options?.sendRealtime !== undefined
      ) {
        // Start with all channels
        const selectedChannels: NotificationChannel[] = [];

        // Add channels based on the control parameters - explicitly check for true
        if (options?.sendInApp === true) {
          this.logger.log(`Adding IN_APP channel for user ${userId}`);
          selectedChannels.push(NotificationChannel.IN_APP);
        }

        if (options?.sendEmail === true) {
          this.logger.log(`Adding EMAIL channel for user ${userId}`);
          selectedChannels.push(NotificationChannel.EMAIL);
        }

        if (options?.sendPush === true) {
          this.logger.log(`Adding PUSH channel for user ${userId}`);
          selectedChannels.push(NotificationChannel.PUSH);
        }

        if (options?.sendMobile === true) {
          this.logger.log(`Adding MOBILE channel for user ${userId}`);
          selectedChannels.push(NotificationChannel.MOBILE);
        }

        if (options?.sendSms === true) {
          this.logger.log(`Adding SMS channel for user ${userId}`);
          selectedChannels.push(NotificationChannel.SMS);
        }

        if (options?.sendRealtime === true) {
          this.logger.log(`Adding REALTIME_MESSAGE channel for user ${userId}`);
          selectedChannels.push(NotificationChannel.REALTIME_MESSAGE);
        }

        finalChannels = selectedChannels.length > 0 ? selectedChannels : undefined;
      }

      // Log the channels being used
      if (finalChannels && finalChannels.length > 0) {
        this.logger.log(`Sending notification to user ${userId} via channels: ${finalChannels.join(', ')}`);
      } else {
        this.logger.log(`Sending notification to user ${userId} via default channels`);
      }

      await this.notificationService.notify({
        userId,
        type,
        title,
        message,
        relatedEntityId: options?.relatedEntityId,
        relatedEntityType: options?.relatedEntityType,
        channels: finalChannels,
        htmlContent: options?.htmlContent,
      });

      this.logger.log(`Notification sent to user ${userId}: ${title}`);
    } catch (error) {
      this.logger.error(`Failed to send notification to user ${userId}: ${error.message}`, error.stack);
      // Don't rethrow the error to prevent disrupting the main flow
    }
  }

  /**
   * Send a notification to multiple users
   * @param userIds Array of user IDs
   * @param type Notification type
   * @param title Notification title
   * @param message Notification message
   * @param options Additional options (relatedEntityId, relatedEntityType, channels, htmlContent, channel controls)
   */
  async notifyMany(
    userIds: string[],
    type: NotificationType,
    title: string,
    message: string,
    options?: {
      relatedEntityId?: string;
      relatedEntityType?: string;
      channels?: NotificationChannel[];
      htmlContent?: string;
      webLink?: string; // Web URL for the notification
      deepLink?: string; // Deep link for the notification
      // Channel control parameters
      sendEmail?: boolean;
      sendPush?: boolean;
      sendInApp?: boolean;
      sendMobile?: boolean;
      sendSms?: boolean;
      sendRealtime?: boolean;
    },
  ): Promise<void> {
    try {
      // Send notifications in parallel
      await Promise.all(userIds.map((userId) => this.notify(userId, type, title, message, options)));

      this.logger.log(`Notifications sent to ${userIds.length} users: ${title}`);
    } catch (error) {
      this.logger.error(`Failed to send notifications to multiple users: ${error.message}`, error.stack);
      // Don't rethrow the error to prevent disrupting the main flow
    }
  }

  /**
   * Send a push notification to a user
   * @param userId User ID
   * @param type Notification type
   * @param title Notification title
   * @param message Notification message
   * @param options Additional options (relatedEntityId, relatedEntityType, htmlContent)
   */
  async notifyPush(
    userId: string,
    type: NotificationType,
    title: string,
    message: string,
    options?: {
      relatedEntityId?: string;
      relatedEntityType?: string;
      htmlContent?: string;
      webLink?: string;
      deepLink?: string;
    },
  ): Promise<void> {
    await this.notify(userId, type, title, message, {
      ...options,
      sendEmail: false,
      sendInApp: false,
      sendMobile: false,
      sendSms: false,
      sendRealtime: false,
      sendPush: true,
    });
  }

  /**
   * Send an email notification to a user
   * @param userId User ID
   * @param type Notification type
   * @param title Notification title
   * @param message Notification message
   * @param options Additional options (relatedEntityId, relatedEntityType, htmlContent)
   */
  async notifyEmail(
    userId: string,
    type: NotificationType,
    title: string,
    message: string,
    options?: {
      relatedEntityId?: string;
      relatedEntityType?: string;
      htmlContent?: string;
      webLink?: string;
      deepLink?: string;
    },
  ): Promise<void> {
    await this.notify(userId, type, title, message, {
      ...options,
      sendPush: false,
      sendInApp: false,
      sendMobile: false,
      sendSms: false,
      sendRealtime: false,
      sendEmail: true,
    });
  }

  /**
   * Send an in-app notification to a user
   * @param userId User ID
   * @param type Notification type
   * @param title Notification title
   * @param message Notification message
   * @param options Additional options (relatedEntityId, relatedEntityType)
   */
  async notifyInApp(
    userId: string,
    type: NotificationType,
    title: string,
    message: string,
    options?: {
      relatedEntityId?: string;
      relatedEntityType?: string;
      webLink?: string;
      deepLink?: string;
    },
  ): Promise<void> {
    await this.notify(userId, type, title, message, {
      ...options,
      sendEmail: false,
      sendPush: false,
      sendMobile: false,
      sendSms: false,
      sendRealtime: false,
      sendInApp: true,
    });
  }
}
