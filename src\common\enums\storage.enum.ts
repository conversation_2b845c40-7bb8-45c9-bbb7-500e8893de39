/**
 * Storage provider types
 */
export enum StorageProvider {
  LOCAL = 'local',
  S3 = 's3',
}

/**
 * Storage health status
 */
export enum StorageHealthStatus {
  HEALTHY = 'healthy',
  UNHEALTHY = 'unhealthy',
  UNKNOWN = 'unknown',
  MAINTENANCE = 'maintenance',
}

/**
 * S3 storage classes
 */
export enum S3StorageClass {
  STANDARD = 'STANDARD',
  REDUCED_REDUNDANCY = 'REDUCED_REDUNDANCY',
  STANDARD_IA = 'STANDARD_IA',
  ONEZONE_IA = 'ONEZONE_IA',
  INTELLIGENT_TIERING = 'INTELLIGENT_TIERING',
  GLACIER = 'GLACIER',
  DEEP_ARCHIVE = 'DEEP_ARCHIVE',
  GLACIER_IR = 'GLACIER_IR',
}

/**
 * S3 server-side encryption types
 */
export enum S3ServerSideEncryption {
  AES256 = 'AES256',
  AWS_KMS = 'aws:kms',
  AWS_KMS_DSSE = 'aws:kms:dsse',
}

/**
 * S3 ACL (Access Control List) types
 */
export enum S3ACL {
  PRIVATE = 'private',
  PUBLIC_READ = 'public-read',
  PUBLIC_READ_WRITE = 'public-read-write',
  AUTHENTICATED_READ = 'authenticated-read',
  AWS_EXEC_READ = 'aws-exec-read',
  BUCKET_OWNER_READ = 'bucket-owner-read',
  BUCKET_OWNER_FULL_CONTROL = 'bucket-owner-full-control',
}

/**
 * File entity types for registry management
 */
export enum FileEntityType {
  PROFILE_PICTURE = 'profile_picture',
  SHOP_ITEM = 'shop_item',
  DIARY_SKIN = 'diary_skin',
  DIARY_QR = 'diary_qr',
  MESSAGE_ATTACHMENT = 'message_attachment',
  STORY_MAKER = 'story_maker',
}

/**
 * Storage operation types for logging and monitoring
 */
export enum StorageOperation {
  UPLOAD = 'upload',
  DOWNLOAD = 'download',
  DELETE = 'delete',
  COPY = 'copy',
  MOVE = 'move',
  MIGRATE = 'migrate',
  HEALTH_CHECK = 'health_check',
}

/**
 * Storage configuration keys
 */
export enum StorageConfigKey {
  // AWS S3 Configuration
  AWS_REGION = 'AWS_REGION',
  AWS_ACCESS_KEY_ID = 'AWS_ACCESS_KEY_ID',
  AWS_SECRET_ACCESS_KEY = 'AWS_SECRET_ACCESS_KEY',
  AWS_S3_BUCKET_NAME = 'AWS_S3_BUCKET_NAME',
  AWS_S3_BUCKET_REGION = 'AWS_S3_BUCKET_REGION',
  AWS_CLOUDFRONT_DOMAIN = 'AWS_CLOUDFRONT_DOMAIN',
  AWS_S3_ACL = 'AWS_S3_ACL',
  AWS_S3_STORAGE_CLASS = 'AWS_S3_STORAGE_CLASS',
  AWS_S3_SERVER_SIDE_ENCRYPTION = 'AWS_S3_SERVER_SIDE_ENCRYPTION',
  AWS_S3_PRESIGNED_URL_EXPIRY = 'AWS_S3_PRESIGNED_URL_EXPIRY',

  // Storage Provider Configuration
  STORAGE_PROVIDER = 'STORAGE_PROVIDER',
  STORAGE_FALLBACK_ENABLED = 'STORAGE_FALLBACK_ENABLED',

  // Local Storage Configuration
  UPLOAD_DIR = 'UPLOAD_DIR',
  MAX_FILE_SIZE = 'MAX_FILE_SIZE',
  ALLOWED_MIME_TYPES = 'ALLOWED_MIME_TYPES',

  // Migration Configuration
  ENABLE_STORAGE_MIGRATION = 'ENABLE_STORAGE_MIGRATION',
  MIGRATION_BATCH_SIZE = 'MIGRATION_BATCH_SIZE',
}

/**
 * Default values for storage configuration
 */
export const StorageDefaults = {
  STORAGE_PROVIDER: StorageProvider.LOCAL,
  STORAGE_FALLBACK_ENABLED: true,
  UPLOAD_DIR: 'uploads',
  MAX_FILE_SIZE: 5242880, // 5MB
  ALLOWED_MIME_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  AWS_S3_ACL: S3ACL.PRIVATE,
  AWS_S3_STORAGE_CLASS: S3StorageClass.STANDARD,
  AWS_S3_SERVER_SIDE_ENCRYPTION: S3ServerSideEncryption.AES256,
  AWS_S3_PRESIGNED_URL_EXPIRY: 3600, // 1 hour
  MIGRATION_BATCH_SIZE: 100,
  ENABLE_STORAGE_MIGRATION: false,
} as const;

/**
 * Storage provider capabilities
 */
export interface StorageCapabilities {
  supportsPresignedUrls: boolean;
  supportsCDN: boolean;
  supportsVersioning: boolean;
  supportsEncryption: boolean;
  supportsMetadata: boolean;
  supportsBatchOperations: boolean;
  maxFileSize?: number;
  allowedMimeTypes?: string[];
}

/**
 * Storage provider capabilities mapping
 */
export const StorageProviderCapabilities: Record<StorageProvider, StorageCapabilities> = {
  [StorageProvider.LOCAL]: {
    supportsPresignedUrls: true, // Via JWT tokens
    supportsCDN: false,
    supportsVersioning: false,
    supportsEncryption: false,
    supportsMetadata: true,
    supportsBatchOperations: true,
    maxFileSize: StorageDefaults.MAX_FILE_SIZE,
    allowedMimeTypes: [...StorageDefaults.ALLOWED_MIME_TYPES],
  },
  [StorageProvider.S3]: {
    supportsPresignedUrls: true,
    supportsCDN: true,
    supportsVersioning: true,
    supportsEncryption: true,
    supportsMetadata: true,
    supportsBatchOperations: true,
    maxFileSize: 5 * 1024 * 1024 * 1024, // 5GB
    allowedMimeTypes: undefined, // S3 supports all MIME types
  },
};
