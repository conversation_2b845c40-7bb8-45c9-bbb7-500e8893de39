import { Controller, Get, Query, UseGuards, Req } from '@nestjs/common';
import { DiaryEntryAttendance, AttendanceStatus } from '../../database/entities/diary-entry-attendance.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThanOrEqual, LessThanOrEqual } from 'typeorm';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { UserType } from 'src/database/entities/user.entity';
import { Request } from 'express';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiQuery, ApiProperty } from '@nestjs/swagger';
import { DiaryEntrySettings } from '../../database/entities/diary-entry-settings.entity';

// Extend Request to include user property
interface AuthRequest extends Request {
  user?: any;
}

// DTO for attendance response with progress
export class DiaryAttendanceResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  diaryEntryId: string;

  @ApiProperty()
  studentId: string;

  @ApiProperty()
  entryDate: Date;

  @ApiProperty({ enum: AttendanceStatus })
  status: AttendanceStatus;

  @ApiProperty()
  wordCount: number;

  @ApiProperty({ description: 'Progress in decimal format (0.00 to 1.00) based on word count vs word limit' })
  progress: number;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  diaryTitle: string;

  @ApiProperty({ required: false })
  studentName?: string;
}

@ApiTags('Diary Attendance')
@Controller('diary-attendance')
@ApiBearerAuth('JWT-auth')
export class DiaryEntryAttendanceController {
  constructor(
    @InjectRepository(DiaryEntryAttendance)
    private readonly diaryEntryAttendanceRepository: Repository<DiaryEntryAttendance>,
    @InjectRepository(DiaryEntrySettings)
    private readonly diaryEntrySettingsRepository: Repository<DiaryEntrySettings>,
  ) {}

  /**
   * Calculate progress in decimal format based on word count vs word limit
   */
  private async calculateProgress(attendance: DiaryEntryAttendance): Promise<number> {
    try {
      // Get the diary entry settings to find the word limit
      const settings = await this.diaryEntrySettingsRepository.findOne({
        where: { diaryEntryId: attendance.diaryEntryId },
      });

      if (!settings || !settings.wordLimit || settings.wordLimit <= 0) {
        return 0; // No word limit set, return 0
      }

      const progress = Math.min(attendance.wordCount / settings.wordLimit, 1);
      return Math.round(progress * 100) / 100; // Round to 2 decimal places (0.00 to 1.00)
    } catch (error) {
      console.error('Error calculating progress:', error);
      return 0;
    }
  }

  @Get('admin')
  @UseGuards(RolesGuard)
  @Roles(UserType.ADMIN)
  @ApiOperation({ summary: "Get all students' attendance" })
  @ApiQuery({ name: 'from', required: false, type: String, description: 'Start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'to', required: false, type: String, description: 'End date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'studentId', required: false, type: String, description: 'Filter by student ID' })
  @ApiQuery({ name: 'status', required: false, enum: AttendanceStatus, description: 'Filter by attendance status' })
  async getAllAttendance(@Query('from') from?: string, @Query('to') to?: string, @Query('studentId') studentId?: string, @Query('status') status?: AttendanceStatus) {
    try {
      const where: any = {};

      // Date range filtering
      if (from && to) {
        const fromDate = new Date(from);
        const toDate = new Date(to);

        if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
          return ApiResponse.error('Invalid date format. Use YYYY-MM-DD format.', 400);
        }

        where.entryDate = Between(fromDate, toDate);
      } else if (from) {
        const fromDate = new Date(from);
        if (isNaN(fromDate.getTime())) {
          return ApiResponse.error('Invalid from date format. Use YYYY-MM-DD format.', 400);
        }
        where.entryDate = MoreThanOrEqual(fromDate);
      } else if (to) {
        const toDate = new Date(to);
        if (isNaN(toDate.getTime())) {
          return ApiResponse.error('Invalid to date format. Use YYYY-MM-DD format.', 400);
        }
        where.entryDate = LessThanOrEqual(toDate);
      }

      // Student ID filtering
      if (studentId) {
        where.studentId = studentId;
      }

      // Status filtering
      if (status) {
        where.status = status;
      }

      const data = await this.diaryEntryAttendanceRepository.find({
        where,
        relations: ['diaryEntry', 'student'],
        order: { entryDate: 'DESC' },
      });

      const formattedData = await Promise.all(
        data.map(async (attendance) => {
          const progress = await this.calculateProgress(attendance);
          return {
            ...attendance,
            diaryTitle: attendance.diaryEntry?.title || 'N/A',
            studentName: attendance.student?.name || 'N/A',
            progress,
          };
        }),
      );

      return ApiResponse.success(formattedData, 'Attendance records retrieved successfully');
    } catch (error) {
      return ApiResponse.error('Failed to retrieve attendance records', 500, error);
    }
  }

  @Get('tutor')
  @UseGuards(RolesGuard)
  @Roles(UserType.TUTOR)
  @ApiOperation({ summary: 'Get attendance for all assigned students' })
  @ApiQuery({ name: 'from', required: false, type: String, description: 'Start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'to', required: false, type: String, description: 'End date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'status', required: false, enum: AttendanceStatus, description: 'Filter by attendance status' })
  async getTutorStudentsAttendance(@Req() req: AuthRequest, @Query('from') from?: string, @Query('to') to?: string, @Query('status') status?: AttendanceStatus) {
    try {
      const tutorId = req.user?.id;
      if (!tutorId) {
        return ApiResponse.error('Tutor not authenticated', 401);
      }

      const mappings = await this.diaryEntryAttendanceRepository.manager.query(
        `
        SELECT DISTINCT student_id FROM student_tutor_mapping WHERE tutor_id = $1 AND status = 'active'
      `,
        [tutorId],
      );

      const studentIds = mappings.map((m: any) => m.student_id);
      if (!studentIds.length) return ApiResponse.success([], 'No students assigned');

      const where: any = { studentId: studentIds };

      // Date range filtering
      if (from && to) {
        const fromDate = new Date(from);
        const toDate = new Date(to);

        if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
          return ApiResponse.error('Invalid date format. Use YYYY-MM-DD format.', 400);
        }

        where.entryDate = Between(fromDate, toDate);
      } else if (from) {
        const fromDate = new Date(from);
        if (isNaN(fromDate.getTime())) {
          return ApiResponse.error('Invalid from date format. Use YYYY-MM-DD format.', 400);
        }
        where.entryDate = MoreThanOrEqual(fromDate);
      } else if (to) {
        const toDate = new Date(to);
        if (isNaN(toDate.getTime())) {
          return ApiResponse.error('Invalid to date format. Use YYYY-MM-DD format.', 400);
        }
        where.entryDate = LessThanOrEqual(toDate);
      }

      // Status filtering
      if (status) {
        where.status = status;
      }

      const data = await this.diaryEntryAttendanceRepository.find({
        where,
        relations: ['diaryEntry', 'student'],
        order: { entryDate: 'DESC' },
      });

      const formattedData = await Promise.all(
        data.map(async (attendance) => {
          const progress = await this.calculateProgress(attendance);
          return {
            ...attendance,
            diaryTitle: attendance.diaryEntry?.title || 'N/A',
            studentName: attendance.student?.name || 'N/A',
            progress,
          };
        }),
      );

      return ApiResponse.success(formattedData, 'Attendance records retrieved successfully');
    } catch (error) {
      return ApiResponse.error('Failed to retrieve attendance records', 500, error);
    }
  }

  @Get('student')
  @UseGuards(RolesGuard)
  @Roles(UserType.STUDENT)
  @ApiOperation({ summary: 'Get own attendance' })
  @ApiQuery({ name: 'from', required: false, type: String, description: 'Start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'to', required: false, type: String, description: 'End date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'status', required: false, enum: AttendanceStatus, description: 'Filter by attendance status' })
  async getStudentAttendanceHistory(@Req() req: AuthRequest, @Query('from') from?: string, @Query('to') to?: string, @Query('status') status?: AttendanceStatus) {
    try {
      const studentId = req.user?.id;
      if (!studentId) {
        return ApiResponse.error('Student not authenticated', 401);
      }

      const where: any = { studentId };

      // Date range filtering
      if (from && to) {
        const fromDate = new Date(from);
        const toDate = new Date(to);

        if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
          return ApiResponse.error('Invalid date format. Use YYYY-MM-DD format.', 400);
        }

        where.entryDate = Between(fromDate, toDate);
      } else if (from) {
        const fromDate = new Date(from);
        if (isNaN(fromDate.getTime())) {
          return ApiResponse.error('Invalid from date format. Use YYYY-MM-DD format.', 400);
        }
        where.entryDate = MoreThanOrEqual(fromDate);
      } else if (to) {
        const toDate = new Date(to);
        if (isNaN(toDate.getTime())) {
          return ApiResponse.error('Invalid to date format. Use YYYY-MM-DD format.', 400);
        }
        where.entryDate = LessThanOrEqual(toDate);
      }

      // Status filtering
      if (status) {
        where.status = status;
      }

      const data = await this.diaryEntryAttendanceRepository.find({
        where,
        relations: ['diaryEntry'],
        order: { entryDate: 'DESC' },
      });

      const formattedData = await Promise.all(
        data.map(async (attendance) => {
          const progress = await this.calculateProgress(attendance);
          return {
            ...attendance,
            diaryTitle: attendance.diaryEntry?.title || 'N/A',
            progress,
          };
        }),
      );

      return ApiResponse.success(formattedData, 'Attendance records retrieved successfully');
    } catch (error) {
      return ApiResponse.error('Failed to retrieve attendance records', 500, error);
    }
  }
}
