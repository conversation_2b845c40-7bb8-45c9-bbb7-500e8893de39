import { Enti<PERSON>, Column } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';

@Entity('email_verifications')
export class EmailVerification extends AuditableBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'token' })
  token: string;

  @Column({ name: 'expiration_time', type: 'timestamp' })
  expirationTime: Date;

  @Column({ name: 'is_used', default: false })
  isUsed: boolean;

  // createdAt and updatedAt are inherited from AuditableBaseEntity
}
