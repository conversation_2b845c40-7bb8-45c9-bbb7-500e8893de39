import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID, IsArray, ArrayMinSize } from 'class-validator';

/**
 * DTO for applying a promotion to plans
 */
export class ApplyPromotionToPlanDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'ID of the promotion to apply',
  })
  @IsNotEmpty()
  @IsUUID('4')
  promotionId: string;

  @ApiProperty({
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    description: 'IDs of the plans to apply the promotion to',
    type: [String],
  })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  @IsUUID('4', { each: true })
  planIds: string[];
}
