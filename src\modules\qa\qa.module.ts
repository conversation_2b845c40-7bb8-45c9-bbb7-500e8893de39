import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminQAController } from './admin-qa.controller';
import { QAService } from './qa.service';
import { TutorPermission } from '../../database/entities/tutor-permission.entity';
import { QAQuestion } from '../../database/entities/qa-question.entity';
import { QAAssignment } from '../../database/entities/qa-assignment.entity';
import { QASubmission } from '../../database/entities/qa-submission.entity';
import { QASubscription } from '../../database/entities/qa-subscription.entity';
import { User } from '../../database/entities/user.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { CommonModule } from '../../common/common.module';
import { PermissionsModule } from '../permissions/permissions.module';
import { QAController } from './qa.controller';
import { TutorQAController } from './tutor-qa.controller';
import { TutorQAService } from './tutor-qa.service';
import { StudentTutorMapping } from '../../database/entities/student-tutor-mapping.entity';
import { TutorQAAdministrationController } from './tutor-qa-admin-task.controller';
import { QAAssignmentSets } from '../../database/entities/qa-assignment-sets.entity';
import { QAAssignmentItems } from '../../database/entities/qa-assignment-items.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([QAQuestion, QAAssignment, QASubmission, QAAssignmentItems, QAAssignmentSets, QASubscription, TutorPermission, StudentTutorMapping, User, PlanFeature]),
    CommonModule,
    PermissionsModule,
  ],
  controllers: [QAController, TutorQAController, AdminQAController, TutorQAAdministrationController],
  providers: [QAService, TutorQAService],
  exports: [QAService, TutorQAService],
})
export class QAModule {}
