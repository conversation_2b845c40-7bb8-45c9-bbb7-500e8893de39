import { MigrationInterface, QueryRunner } from 'typeorm';

export class QAMissionPropertymigrate1747217369139 implements MigrationInterface {
  name = 'QAMissionPropertymigrate1747217369139';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_mission" ADD "sequence_number" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_mission" DROP COLUMN "sequence_number"`);
  }
}
