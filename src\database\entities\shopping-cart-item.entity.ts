import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { ShoppingCart } from './shopping-cart.entity';
import { ShopItem } from './shop-item.entity';

@Entity()
export class ShoppingCartItem extends AuditableBaseEntity {
  @Column({ name: 'cart_id' })
  cartId: string;

  @ManyToOne(() => ShoppingCart, (cart) => cart.items, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'cart_id' })
  cart: ShoppingCart;

  @Column({ name: 'shop_item_id' })
  shopItemId: string;

  @ManyToOne(() => ShopItem)
  @JoinColumn({ name: 'shop_item_id' })
  shopItem: ShopItem;

  @Column({ name: 'quantity', default: 1 })
  quantity: number;

  @Column({ name: 'price', type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ name: 'reward_points', type: 'decimal', precision: 10, scale: 2 })
  rewardPoints: number;

  /**
   * Calculate the total price for this item (quantity * price)
   * @returns The total price
   */
  getTotalPrice(): number {
    return this.quantity * Number(this.price);
  }

  /**
   * Calculate the total reward points for this item (quantity * rewardPoints)
   * @returns The total reward points
   */
  getTotalRewardPoints(): number {
    return this.quantity * this.rewardPoints;
  }
}
