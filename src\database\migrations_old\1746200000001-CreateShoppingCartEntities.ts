import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateShoppingCartEntities1746200000001 implements MigrationInterface {
  name = 'CreateShoppingCartEntities1746200000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create shopping cart status enum
    await queryRunner.query(`CREATE TYPE "public"."shopping_cart_status_enum" AS ENUM('active', 'checked_out', 'abandoned')`);

    // Create shopping cart table
    await queryRunner.query(`
            CREATE TABLE "shopping_cart" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP DEFAULT now(),
                "created_by" character varying(36),
                "updated_by" character varying(36),
                "user_id" uuid NOT NULL,
                "status" "public"."shopping_cart_status_enum" NOT NULL DEFAULT 'active',
                "last_activity" TIMESTAMP NOT NULL DEFAULT now(),
                CONSTRAINT "PK_shopping_cart" PRIMARY KEY ("id")
            )
        `);

    // Create shopping cart item table
    await queryRunner.query(`
            CREATE TABLE "shopping_cart_item" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP DEFAULT now(),
                "created_by" character varying(36),
                "updated_by" character varying(36),
                "cart_id" uuid NOT NULL,
                "shop_item_id" uuid NOT NULL,
                "quantity" integer NOT NULL DEFAULT 1,
                "price" decimal(10,2) NOT NULL,
                "reward_points" decimal(10,2) NOT NULL,
                CONSTRAINT "PK_shopping_cart_item" PRIMARY KEY ("id")
            )
        `);

    // Add foreign key constraints
    await queryRunner.query(`
            ALTER TABLE "shopping_cart"
            ADD CONSTRAINT "FK_shopping_cart_user"
            FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);

    await queryRunner.query(`
            ALTER TABLE "shopping_cart_item"
            ADD CONSTRAINT "FK_shopping_cart_item_cart"
            FOREIGN KEY ("cart_id") REFERENCES "shopping_cart"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);

    await queryRunner.query(`
            ALTER TABLE "shopping_cart_item"
            ADD CONSTRAINT "FK_shopping_cart_item_shop_item"
            FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "shopping_cart_item" DROP CONSTRAINT "FK_shopping_cart_item_shop_item"`);
    await queryRunner.query(`ALTER TABLE "shopping_cart_item" DROP CONSTRAINT "FK_shopping_cart_item_cart"`);
    await queryRunner.query(`ALTER TABLE "shopping_cart" DROP CONSTRAINT "FK_shopping_cart_user"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "shopping_cart_item"`);
    await queryRunner.query(`DROP TABLE "shopping_cart"`);

    // Drop enum
    await queryRunner.query(`DROP TYPE "public"."shopping_cart_status_enum"`);
  }
}
