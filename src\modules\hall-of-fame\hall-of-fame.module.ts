import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HallOfFameController } from './hall-of-fame.controller';
import { HallOfFameService } from './hall-of-fame.service';
import { AwardWinner } from '../../database/entities/award-winner.entity';
import { Award } from '../../database/entities/award.entity';
@Module({
  imports: [TypeOrmModule.forFeature([AwardWinner, Award])],
  controllers: [HallOfFameController],
  providers: [HallOfFameService],
  exports: [HallOfFameService],
})
export class HallOfFameModule {}
