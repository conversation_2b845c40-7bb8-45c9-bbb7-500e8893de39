import { ApiProperty } from '@nestjs/swagger';
import { LikerType } from '../../entities/story-maker-like.entity';

/**
 * Response DTO for story maker like
 */
export class StoryMakerLikeResponseDto {
  @ApiProperty({
    description: 'The ID of the like',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The ID of the story submission',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  submissionId: string;

  @ApiProperty({
    description: 'The ID of the user who liked the submission',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  likerId: string;

  @ApiProperty({
    description: 'The type of user who liked the submission',
    enum: LikerType,
    example: LikerType.STUDENT,
  })
  likerType: LikerType;

  @ApiProperty({
    description: 'When the like was created',
    example: '2025-05-21T10:00:00.000Z',
  })
  createdAt: Date;
}

/**
 * Response DTO for story maker like count and user like status
 */
export class StoryMakerLikeCountResponseDto {
  @ApiProperty({
    description: 'The total number of likes for the story submission',
    example: 42,
  })
  count: number;

  @ApiProperty({
    description: 'Whether the current user has liked this submission',
    example: true,
  })
  hasLiked: boolean;
}

/**
 * Response DTO for detailed like information
 */
export class StoryMakerLikeDetailsResponseDto {
  @ApiProperty({
    description: 'Total number of likes',
    example: 45,
  })
  totalLikes: number;

  @ApiProperty({
    description: 'Number of student likes',
    example: 30,
  })
  studentLikes: number;

  @ApiProperty({
    description: 'Number of tutor likes',
    example: 15,
  })
  tutorLikes: number;

  @ApiProperty({
    description: 'Number of likes within 24 hours',
    example: 25,
  })
  likesWithin24h: number;

  @ApiProperty({
    description: 'Current popularity score (1-5) based on simple like counts',
    example: 4,
  })
  popularityScore: number;
}

/**
 * Response DTO for story maker share
 */
export class StoryMakerShareResponseDto {
  @ApiProperty({
    description: 'The ID of the share',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The ID of the story submission',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  submissionId: string;

  @ApiProperty({
    description: 'The share token for public access',
    example: 'abc123def456ghi789',
  })
  shareToken: string;

  @ApiProperty({
    description: 'The public share URL',
    example: 'https://app.hecacademy.com/shared/story/abc123def456ghi789',
  })
  shareUrl: string;

  @ApiProperty({
    description: 'When the share expires (null for no expiry)',
    example: '2025-06-21T10:00:00.000Z',
    required: false,
  })
  expiryDate?: Date;

  @ApiProperty({
    description: 'Whether the share is active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'When the share was created',
    example: '2025-05-21T10:00:00.000Z',
  })
  createdAt: Date;
}

/**
 * DTO for sharing a story submission
 */
export class ShareStoryMakerSubmissionDto {
  @ApiProperty({
    description: 'Optional expiry date for the share (ISO string)',
    example: '2025-06-21T10:00:00.000Z',
    required: false,
  })
  expiryDate?: string;
}

/**
 * DTO for story maker friend share details
 */
export class StoryMakerFriendShareDetailsDto {
  @ApiProperty({
    description: 'Share ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Share token',
    example: 'abc123def456',
  })
  shareToken: string;

  @ApiProperty({
    description: 'Whether the share is active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'When the share was created',
    example: '2024-01-15T10:30:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Chat message ID (if sent via chat)',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  chatMessageId?: string;
}

/**
 * Response DTO for story maker friend share
 */
export class StoryMakerFriendShareResponseDto {
  @ApiProperty({
    description: 'Share details',
    example: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      shareToken: 'abc123def456',
      isActive: true,
      createdAt: '2024-01-15T10:30:00.000Z',
    },
  })
  shareDetails: StoryMakerFriendShareDetailsDto;

  @ApiProperty({
    description: 'Deep link for the shared submission',
    example: 'hec://story-maker/friend-shared/abc123def456',
  })
  deepLink: string;

  @ApiProperty({
    description: 'Chat message sent to friend',
    type: Object, // MessageDto would be imported from chat module
  })
  chatMessage: any;

  @ApiProperty({
    description: 'Whether notification was sent successfully',
    example: true,
  })
  notificationSent: boolean;
}

/**
 * DTO for sharing a story submission with a friend
 */
export class ShareStoryMakerWithFriendDto {
  @ApiProperty({
    description: 'Target friend user ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  targetUserId: string;

  @ApiProperty({
    description: 'Optional message to include with the share',
    example: 'Check out my amazing story!',
    required: false,
  })
  message?: string;
}

/**
 * DTO for friend shared story submission view
 */
export class FriendSharedStoryMakerSubmissionDto {
  @ApiProperty({ description: 'Submission ID' })
  id: string;

  @ApiProperty({ description: 'Story content' })
  content: string;

  @ApiProperty({ description: 'Story maker game title' })
  gameTitle: string;

  @ApiProperty({ description: 'Submission date' })
  submittedAt: Date;

  @ApiProperty({ description: 'Sharer name' })
  sharedByName: string;

  @ApiProperty({ description: 'Sharing message', required: false })
  sharingMessage?: string;

  @ApiProperty({ description: 'When it was shared' })
  sharedAt: Date;

  @ApiProperty({ description: 'Deep link to the submission' })
  deepLink: string;

  @ApiProperty({ description: 'AI evaluation results', required: false })
  evaluation?: {
    totalScore: number;
    creativityScore: number;
    sentencePowerScore: number;
    participationScore: number;
    accuracyScore: number;
    popularityScore: number;
    relevanceScore?: number;
    aiFeedback: string;
  };

  @ApiProperty({ description: 'Like information' })
  likeInfo: {
    count: number;
    hasLiked: boolean;
  };
}
