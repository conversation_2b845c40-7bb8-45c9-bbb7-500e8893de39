# Novel Module

The Novel Module provides functionality for students to write creative novels based on admin-created topics, receive feedback from tutors, and suggest new topics. **Now follows the unified writing entry lifecycle** for consistent behavior across all writing modules.

## Key Features (UNIFIED SYSTEM)

- **Unified Lifecycle**: Follows the same `DRAFT` → `SUBMITTED` → `REVIEWED` → `CONFIRMED` progression as all writing modules
- **Unlimited Submissions**: Students can submit unlimited times after each review
- **Draft Mode**: Updates don't create versions or send notifications (save as draft)
- **Unified Review API**: Score required, correction optional, one review per submission, unlimited feedback

## Features

### Admin Features
- Create novel topics with unique sequence titles
- Manage topic categories (Monthly/Quarterly)
- View student topic suggestions
- Update and delete topics

### Student Features
- View active topics by category (Monthly/Quarterly tabs)
- Set default skin preference for the module
- Create and update novel entries
- Submit entries for tutor review
- Change skin per entry
- Submit topic suggestions

### Tutor Features (UNIFIED REVIEW SYSTEM)
- Review submitted novel entries with required score and optional correction
- Submit review only once per submission (unified rule)
- Provide unlimited feedback comments per entry (even after review)
- Unified notification system for reviews and feedback

## API Endpoints

### Admin Endpoints
- `POST /admin/novel/topics` - Create a new topic
- `GET /admin/novel/topics` - Get all topics
- `GET /admin/novel/topics/:id` - Get specific topic
- `PUT /admin/novel/topics/:id` - Update topic
- `DELETE /admin/novel/topics/:id` - Delete topic
- `GET /admin/novel/suggestions` - View student suggestions
- `GET /admin/novel/categories` - Get available topic categories

### Student Endpoints
- `GET /student/novel/topics` - Get active topics (with category filter)
- `GET /student/novel/topics/:id` - Get specific topic
- `PUT /student/novel/entries/:id` - Update novel entry
- `POST /student/novel/entries/submit` - Submit entry for review (with optional content/skin updates)
- `GET /student/novel/entries` - Get student entries (with category filter)
- `GET /student/novel/entries/:id` - Get specific entry
- `GET /student/novel/entries/topic/:topicId` - Get or create entry by topic ID
- `POST /student/novel/suggestions` - Create topic suggestion
- `GET /student/novel/suggestions` - Get student suggestions
- `POST /student/novel/skin-preference` - Set default skin
- `GET /student/novel/skin-preference` - Get default skin
- `GET /student/novel/skins` - Get all available skins (with pagination)

### Tutor Endpoints (UNIFIED REVIEW SYSTEM)
- `GET /tutor/novel/entries` - Get entries for review
- `GET /tutor/novel/entries/:id` - Get specific entry for review
- `POST /tutor/novel/entries/:id/review` - **Submit review with required score and optional correction (NEW UNIFIED API)**
- `POST /tutor/novel/entries/:id/feedback` - **Add unlimited feedback (NEW UNIFIED API)**
- ~~`POST /tutor/novel/entries/:id/confirm`~~ - **REMOVED: Confirm stage eliminated from lifecycle**

#### Submit Novel Review (NEW UNIFIED API)
**Endpoint:** `POST /tutor/novel/entries/{entryId}/review`

**Description:** Submit review for novel entry with required score and optional correction. Tutor can only review once per submission.

**Request Body:**
```json
{
  "score": 90,           // Required: Score for the entry (0-100)
  "correction": "Excellent character development! Consider expanding the dialogue in chapter 2."  // Optional: Correction text
}
```

**Response:**
```json
{
  "id": "entry-uuid",
  "status": "REVIEWED",
  "score": 90,
  "reviewedBy": "tutor-uuid",
  "reviewedAt": "2024-01-15T10:30:00Z",
  "canSubmitNewVersion": true,
  "lastReviewedAt": "2024-01-15T10:30:00Z"
}
```

#### Add Novel Feedback (NEW UNIFIED API)
**Endpoint:** `POST /tutor/novel/entries/{entryId}/feedback`

**Description:** Add unlimited feedback to novel entry. Can be used even after review is completed.

**Request Body:**
```json
{
  "feedback": "I love how you developed the main character's personality throughout the story!"
}
```

**Response:**
```json
{
  "id": "feedback-uuid",
  "entryId": "entry-uuid",
  "tutorId": "tutor-uuid",
  "feedback": "I love how you developed the main character's personality throughout the story!",
  "createdAt": "2024-01-15T10:35:00Z"
}
```

## Workflow

### Student Workflow
1. **View Topics**: Get available topics using `GET /student/novel/topics`
2. **Create/Get Entry**: Use `GET /student/novel/entries/topic/{topicId}` to get or create entry
3. **Update Entry**: Use `PUT /student/novel/entries/{id}` to update content
4. **Submit Entry**: Use `POST /student/novel/entries/submit` to submit for review

### Tutor Review Workflow

#### Option 1: Direct Review (Correction + Score)
1. **Get Entries**: Use `GET /tutor/novel/entries` to see submitted entries
2. **Review Entry**: Use `GET /tutor/novel/entries/{id}` to view specific entry
3. **Submit Review**: Use `POST /tutor/novel/entries/{id}/review` to provide correction and score
   - Entry status changes to `REVIEWED`
   - Student receives notification
   - Score cannot be modified after this point

#### Option 2: Gradual Review (Correction First, Score Later)
1. **Get Entries**: Use `GET /tutor/novel/entries` to see submitted entries
2. **Review Entry**: Use `GET /tutor/novel/entries/{id}` to view specific entry
3. **Submit Correction**: Use `POST /tutor/novel/entries/{id}/correction` to provide initial correction (without score)
   - Can only be done if no correction exists yet
   - Entry status changes to `CORRECTION_GIVEN`
4. **Update Correction**: Use `PUT /tutor/novel/entries/{id}/correction` to update correction text
   - Can be done multiple times as long as no score is set
   - Score cannot be modified if it exists (unless entry is `UNDER_REVIEW`)
5. **Final Review**: Use `POST /tutor/novel/entries/{id}/review` to provide final correction and score
   - Entry status changes to `REVIEWED`
   - Student receives notification

#### Option 3: Further Review (After Initial Review)
1. **Re-review Reviewed Entry**: Use `POST /tutor/novel/entries/{id}/review` on a `REVIEWED` entry
   - Entry status changes to `UNDER_REVIEW`
   - Allows further corrections and score updates
2. **Update Correction**: Use `PUT /tutor/novel/entries/{id}/correction` to update correction text
   - Allowed when entry is in `UNDER_REVIEW` status

### Important Rules
- **POST /correction**: Only works if no correction exists yet, changes status to `CORRECTION_GIVEN`
- **PUT /correction**: Only updates correction text, cannot modify score once set (unless `UNDER_REVIEW`)
- **POST /review**: Sets the final score and marks entry as `REVIEWED` or `UNDER_REVIEW`
- **Status Flow**: `NEW` → `SUBMITTED`/`UPDATED` → `CORRECTION_GIVEN` → `REVIEWED` → `UNDER_REVIEW`
- **Further Reviews**: Reviewed entries can be re-reviewed, changing status to `UNDER_REVIEW`
- **Correction Updates**: Correction text can be updated when entry is `CORRECTION_GIVEN` or `UNDER_REVIEW`
1. **Admin creates topics** with unique sequence titles and categories
2. **Student sets default skin** for the Novel module (required before creating entries)
3. **Student selects a topic** and creates a novel entry (status: `DRAFT`)
4. **Student can update** the entry while it's in `DRAFT` status (save as draft - no versions, no notifications)
5. **Student submits** the entry for review (status: `SUBMITTED`, creates submitted version)
6. **System notifies assigned tutor** about the submission (with submission number)
7. **Tutor submits review** with required score and optional correction (once per submission)
8. **System notifies student** about the review completion (status: `REVIEWED`)
9. **Student can resubmit** unlimited times after review (creates new submitted versions)
10. **Tutor can add unlimited feedback** even after review completion

### GetOrCreate Workflow
1. **Student browses topics** by category (Monthly/Quarterly)
2. **Student clicks on a topic** to start writing
3. **System automatically creates entry** with default skin if it doesn't exist
4. **Student writes content** and can change skin if needed
5. **Student submits with final content/skin** in a single API call
6. **Entry moves to SUBMITTED status** and tutor is notified

### Enhanced Submit Process
- Students can now update content and skin during submission
- Submit API accepts optional `content` and `skinId` fields
- Eliminates need for separate update calls before submission
- Word count is automatically recalculated if content is provided

## Entry Status Flow
- `NEW` → `SUBMITTED` → `REVIEWED` → `CONFIRMED`
- `SUBMITTED` → `UPDATED` → `REVIEWED` (if student updates after submission)

## Key Features

### Topic Categories
- Two predefined categories: `monthly` and `quarterly`
- Categories are defined as constants for consistency
- Admin API endpoint to retrieve available categories
- Category validation ensures only valid values are accepted

### Unique Sequence Titles
- Topic sequence titles must be unique across all topics
- Prevents duplicate topic naming

### Skin Integration
- Reuses existing DiarySkin system
- Students can fetch all available skins via API
- Students must set default skin before creating entries
- Can override skin per individual entry
- Supports both global skins (admin-created) and student-specific skins
- Includes pagination support for large skin collections

### Notification System
- Automatic notifications for submissions, updates, feedback, and reviews
- Integrates with existing notification infrastructure

### Awards Integration
- Novel-specific award criteria (NOVEL_PERFORMANCE)
- Supports all award frequencies (weekly, monthly, quarterly, yearly)

### Suggestion System
- Students can suggest topics to admins
- Admin can view all suggestions (read-only)
- No automatic conversion from suggestions to topics

## Database Schema

### Tables Created
- `novel_topic` - Admin-created topics
- `novel_entry` - Student novel submissions
- `novel_feedback` - Tutor feedback on entries
- `novel_correction` - Tutor corrections and scores
- `novel_suggestion` - Student topic suggestions
- `novel_module_skin_preference` - Student default skin preferences

### Key Constraints
- Unique sequence titles for topics
- One correction per entry
- Multiple feedback entries allowed per entry
- Foreign key relationships with users and diary skins

## Integration Points

- **Diary Module**: Skin system integration
- **Notification Module**: Submission and review notifications
- **Awards Module**: Novel-specific awards
- **Tutor Matching**: Automatic tutor assignment for reviews
- **Authentication**: Role-based access control (Admin/Student/Tutor)
