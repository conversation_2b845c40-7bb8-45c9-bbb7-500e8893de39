import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateWaterfallTables1745321878828 implements MigrationInterface {
  name = 'CreateWaterfallTables1745321878828';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create waterfall_set table
    await queryRunner.query(`
            CREATE TABLE "waterfall_set" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP DEFAULT now(),
                "created_by" character varying(36),
                "updated_by" character varying(36),
                "title" character varying NOT NULL,
                "total_score" integer NOT NULL,
                "total_questions" integer NOT NULL,
                CONSTRAINT "PK_waterfall_set" PRIMARY KEY ("id")
            )
        `);

    // Create waterfall_question table
    await queryRunner.query(`
            CREATE TABLE "waterfall_question" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP DEFAULT now(),
                "created_by" character varying(36),
                "updated_by" character varying(36),
                "question_text" text NOT NULL,
                "correct_answers" text array NOT NULL,
                "options" text array NOT NULL,
                "set_id" uuid NOT NULL,
                CONSTRAINT "PK_waterfall_question" PRIMARY KEY ("id")
            )
        `);

    // Add foreign key constraint
    await queryRunner.query(`
            ALTER TABLE "waterfall_question"
            ADD CONSTRAINT "FK_waterfall_question_set"
            FOREIGN KEY ("set_id")
            REFERENCES "waterfall_set"("id")
            ON DELETE CASCADE
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraint
    await queryRunner.query(`
            ALTER TABLE "waterfall_question"
            DROP CONSTRAINT "FK_waterfall_question_set"
        `);

    // Drop tables
    await queryRunner.query(`DROP TABLE "waterfall_question"`);
    await queryRunner.query(`DROP TABLE "waterfall_set"`);
  }
}
