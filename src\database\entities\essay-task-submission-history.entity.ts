import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, Index, OneToOne } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { IsUUID } from 'class-validator';
import { EssayTaskSubmissions } from './essay-task-submissions.entity';
import type { EssayTaskSubmissionMarking } from './essay-task-submission-marking.entity';
import { SubmissionStatus } from '../../constants/submission.enum';

@Entity()
@Index(['submission', 'sequenceNumber'])
@Index(['submissionDate'])
export class EssayTaskSubmissionHistory extends AuditableBaseEntity {
  @Column({
    name: 'content',
    type: 'text',
  })
  content: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: SubmissionStatus,
    default: SubmissionStatus.DRAFT,
  })
  status: SubmissionStatus;

  @Column({
    name: 'version_number',
    type: 'int',
    default: 1
  })
  versionNumber: number;

  @Column({
    name: 'is_submitted',
    type: 'boolean',
    default: false,
  })
  isSubmitted: boolean;

  @Column({
    name: 'word_count',
    type: 'int',
  })
  wordCount: number;

  @Column({
    name: 'submission_date',
    type: 'timestamp',
  })
  submissionDate: Date;

  @Column({
    name: 'sequence_number',
    type: 'int',
  })
  sequenceNumber: number;

  @Column({
    name: 'meta_data',
    type: 'json',
    nullable: true,
  })
  metaData?: {
    browserInfo?: string;
    ipAddress?: string;
    submissionAttempts?: number;
    lastDraftSavedAt?: Date;
    timeSpent?: number;
    wordCountDiff?: number;
    contentChanges?: {
      paragraphsAdded?: number;
      paragraphsRemoved?: number;
      significantChanges?: boolean;
    };
    reviewerNotes?: string;
    aiDetectionScore?: number;
  };

  @ManyToOne(() => EssayTaskSubmissions, (submission) => submission.submissionHistory, { nullable: true })
  @JoinColumn({ name: 'submission' })
  submission: EssayTaskSubmissions;

  @Column({
    name: 'submission_id',
    type: 'uuid',
    nullable: true,
  })
  @IsUUID()
  submissionId: string;

  @Column({
    name: 'previous_revision_id',
    type: 'uuid',
    nullable: true,
  })
  previousRevisionId?: string;

  @OneToOne('EssayTaskSubmissionMarking', (marking: EssayTaskSubmissionMarking) => marking.submissionHistory, { nullable: true })
  @JoinColumn({ name: 'submission_mark_id' })
  submissionMark: EssayTaskSubmissionMarking;
}
