import { MigrationInterface, QueryRunner } from 'typeorm';

export class EssayPreferences1747675257104 implements MigrationInterface {
  name = 'EssayPreferences1747675257104';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."essay_module_skin_preference_scope_type_enum" AS ENUM('module_default', 'task_specific')`);
    await queryRunner.query(
      `CREATE TABLE "essay_module_skin_preference" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "skin_id" uuid NOT NULL, "scope_type" "public"."essay_module_skin_preference_scope_type_enum" NOT NULL, "task_id" uuid, "is_active" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_d6ad28a43aca68421c50cfd2f96" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_b459468a889b4e58df2199560f" ON "essay_module_skin_preference" ("created_by", "task_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_12c59662b2bdfe38b1222aa7ee" ON "essay_module_skin_preference" ("created_by", "scope_type") `);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD "submission_skin_id" uuid`);
    await queryRunner.query(
      `ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_fac44bb71949efc3e4ce2eac6f7" FOREIGN KEY ("submission_skin_id") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "essay_module_skin_preference" ADD CONSTRAINT "FK_3a54381f8049ae135e0bf604a92" FOREIGN KEY ("skin_id") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "essay_module_skin_preference" ADD CONSTRAINT "FK_ac235b23221d9af3ce1fbceef96" FOREIGN KEY ("task_id") REFERENCES "essay_mission_tasks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" DROP CONSTRAINT "FK_ac235b23221d9af3ce1fbceef96"`);
    await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" DROP CONSTRAINT "FK_3a54381f8049ae135e0bf604a92"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_fac44bb71949efc3e4ce2eac6f7"`);
    await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP COLUMN "submission_skin_id"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_12c59662b2bdfe38b1222aa7ee"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b459468a889b4e58df2199560f"`);
    await queryRunner.query(`DROP TABLE "essay_module_skin_preference"`);
    await queryRunner.query(`DROP TYPE "public"."essay_module_skin_preference_scope_type_enum"`);
  }
}
