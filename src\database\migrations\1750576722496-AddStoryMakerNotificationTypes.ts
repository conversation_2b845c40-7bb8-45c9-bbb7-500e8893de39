import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStoryMakerNotificationTypes1750576722496 implements MigrationInterface {
  name = 'AddStoryMakerNotificationTypes1750576722496';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add the new Story Maker notification types to the notification_type_enum
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_submitted'`);
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_evaluated'`);
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_liked'`);
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_shared'`);
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_achievement'`);
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_popularity_update'`);
    await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_hall_of_fame'`);

    // Also add them to the user notification preference enum
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_submitted'`);
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_evaluated'`);
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_liked'`);
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_shared'`);
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_achievement'`);
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_popularity_update'`);
    await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" ADD VALUE IF NOT EXISTS 'story_maker_hall_of_fame'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Note: PostgreSQL doesn't support removing enum values directly
    // This would require recreating the enum, which is complex and risky
    // For now, we'll leave the enum values in place
    // If you need to remove them, you'll need to:
    // 1. Create a new enum without these values
    // 2. Update all tables to use the new enum
    // 3. Drop the old enum
    // 4. Rename the new enum to the original name
    
    console.log('Warning: Cannot remove enum values in PostgreSQL. Story Maker notification types will remain in the enum.');
  }
}
