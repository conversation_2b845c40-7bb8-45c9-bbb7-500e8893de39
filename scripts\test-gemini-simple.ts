import { ConfigService } from '@nestjs/config';
import { GeminiAiService } from '../src/common/services/gemini-ai.service';
import { Logger } from '@nestjs/common';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const logger = new Logger('GeminiSimpleTest');

async function testGeminiSimple() {
  logger.log('🚀 Starting Simple Gemini AI Test...');

  try {
    // Create a mock ConfigService
    const configService = {
      get: (key: string) => process.env[key]
    } as ConfigService;

    // Check if API key is configured
    const apiKey = process.env.GOOGLE_GEMINI_API_KEY;
    if (!apiKey || apiKey === 'your_google_gemini_api_key_here') {
      logger.error('❌ GOOGLE_GEMINI_API_KEY is not configured properly');
      logger.error('Please set your actual Google Gemini API key in the .env file');
      return;
    }

    logger.log('✅ API key found, initializing Gemini service...');

    // Initialize the Gemini service
    const geminiService = new GeminiAiService(configService);

    // Test 1: Connection Test
    logger.log('\n📡 Test 1: Testing Gemini AI Connection...');
    const connectionTest = await geminiService.testConnection();
    logger.log(`Connection test result: ${connectionTest ? '✅ SUCCESS' : '❌ FAILED'}`);

    if (!connectionTest) {
      logger.error('❌ Gemini AI connection failed. Please check your API key.');
      return;
    }

    // Test 2: Sentence Counting
    logger.log('\n📝 Test 2: Testing Sentence Counting...');
    const testTexts = [
      'Hello world.',
      'This is a test. It has two sentences.',
      'One! Two? Three.',
      'Complex sentence with, commas and; semicolons. Another sentence!',
      ''
    ];

    for (const text of testTexts) {
      const result = geminiService.countSentences(text);
      logger.log(`Text: "${text}" → ${result.sentenceCount} sentences`);
      logger.log(`Sentences: [${result.sentences.join(', ')}]`);
    }

    // Test 3: AI Story Evaluation
    logger.log('\n🤖 Test 3: Testing AI Story Evaluation...');
    const testStories = [
      {
        title: 'Simple Story',
        content: 'Once upon a time, there was a cat. The cat was very happy. It played all day.'
      },
      {
        title: 'Creative Story',
        content: 'In the mystical realm of Eldoria, where dragons soared through crystalline skies and magic flowed like rivers of starlight, a young apprentice named Luna discovered an ancient tome that would change her destiny forever. The book whispered secrets of forgotten spells, each page turning itself as if guided by an invisible hand. As she read the incantations aloud, the very air around her shimmered with ethereal energy, and she realized that she was not just reading magic—she was becoming it.'
      },
      {
        title: 'Story with Errors',
        content: 'Their was a dog who like to run. He run very fast and he dont stop. The dog are happy when he running.'
      }
    ];

    for (const story of testStories) {
      logger.log(`\n📖 Evaluating: ${story.title}`);
      logger.log(`Content: ${story.content.substring(0, 100)}...`);
      
      try {
        const evaluation = await geminiService.evaluateStory(story.content);
        
        logger.log('🎯 Evaluation Results:');
        logger.log(`  Creativity: ${evaluation.creativityScore}/5 - ${evaluation.creativityExplanation}`);
        logger.log(`  Sentence Power: ${evaluation.sentencePowerScore}/3 - ${evaluation.sentencePowerExplanation}`);
        logger.log(`  Grammar Errors: ${evaluation.grammarErrorCount}`);
        logger.log(`  Accuracy Score: ${evaluation.accuracyScore}/3`);
        logger.log(`  Processing Time: ${evaluation.processingTime}ms`);
        logger.log(`  AI Feedback: ${evaluation.overallFeedback}`);
        
        if (evaluation.grammarErrors.length > 0) {
          logger.log(`  Grammar Issues: [${evaluation.grammarErrors.join(', ')}]`);
        }
        
      } catch (error) {
        logger.error(`❌ Evaluation failed for ${story.title}: ${error.message}`);
      }
    }

    // Test 4: Scoring Algorithm Test
    logger.log('\n🏆 Test 4: Testing Scoring Algorithm Components...');
    
    const testContent = 'In a magical forest filled with talking animals and glowing flowers, a brave little rabbit named Whiskers embarked on an incredible adventure to find the legendary Crystal of Harmony. Along the way, he met a wise old owl who taught him that true courage comes from helping others, not from being fearless. Together, they solved riddles, crossed dangerous bridges, and finally discovered that the real treasure was the friendship they had built during their journey.';
    
    // Test sentence counting
    const sentenceResult = geminiService.countSentences(testContent);
    logger.log(`📝 Sentence Count: ${sentenceResult.sentenceCount} sentences`);
    logger.log(`📝 Sentence Score: ${sentenceResult.sentenceCount} points`);
    
    // Test word counting
    const wordCount = testContent.trim().split(/\s+/).filter(word => word.length > 0).length;
    logger.log(`📊 Word Count: ${wordCount} words`);
    
    // Test participation scoring
    let participationScore = 1;
    if (wordCount <= 30) participationScore = 1;
    else if (wordCount <= 60) participationScore = 2;
    else if (wordCount <= 100) participationScore = 3;
    else if (wordCount <= 150) participationScore = 4;
    else if (wordCount >= 200) participationScore = 5;
    else participationScore = 4; // 151-199 words
    
    logger.log(`📊 Participation Score: ${participationScore}/5 points`);
    
    // Test AI evaluation
    try {
      const evaluation = await geminiService.evaluateStory(testContent);
      
      // Test accuracy multiplier
      let finalAccuracyScore = evaluation.accuracyScore;
      if (participationScore >= 3 && wordCount >= 100) {
        finalAccuracyScore = evaluation.accuracyScore * 2;
        logger.log(`✅ Accuracy Multiplier Applied: ${evaluation.accuracyScore} x 2 = ${finalAccuracyScore}`);
      }
      
      const totalScore = sentenceResult.sentenceCount + 
                        evaluation.creativityScore + 
                        evaluation.sentencePowerScore + 
                        participationScore + 
                        finalAccuracyScore + 
                        0; // popularity starts at 0
      
      logger.log('\n🎯 Complete Scoring Results:');
      logger.log(`📝 Sentence Score: ${sentenceResult.sentenceCount} points`);
      logger.log(`🎨 Creativity: ${evaluation.creativityScore}/5 points`);
      logger.log(`✍️  Writing Quality: ${evaluation.sentencePowerScore}/3 points`);
      logger.log(`📊 Participation: ${participationScore}/5 points`);
      logger.log(`✅ Accuracy: ${finalAccuracyScore}/3 points (${evaluation.grammarErrorCount} errors)`);
      logger.log(`❤️  Popularity: 0/5 points (starts at 0)`);
      logger.log(`🏆 TOTAL SCORE: ${totalScore} points`);
      logger.log(`⏱️  Processing Time: ${evaluation.processingTime}ms`);
      logger.log(`💬 AI Feedback: ${evaluation.overallFeedback}`);
      
    } catch (error) {
      logger.error(`❌ AI evaluation failed: ${error.message}`);
    }

    // Test 5: Edge Cases
    logger.log('\n🔍 Test 5: Testing Edge Cases...');
    
    const edgeCases = [
      { name: 'Empty Content', content: '' },
      { name: 'Single Word', content: 'Hello' },
      { name: 'No Punctuation', content: 'This is a story without any punctuation marks' },
      { name: 'Only Punctuation', content: '!!! ??? ...' },
    ];

    for (const testCase of edgeCases) {
      logger.log(`\n🧪 Testing: ${testCase.name}`);
      try {
        const sentenceResult = geminiService.countSentences(testCase.content);
        logger.log(`  Sentences: ${sentenceResult.sentenceCount}`);
        
        if (testCase.content.length > 0) {
          const evaluation = await geminiService.evaluateStory(testCase.content);
          logger.log(`  Creativity: ${evaluation.creativityScore}/5`);
          logger.log(`  Processing: ${evaluation.processingTime}ms`);
        }
      } catch (error) {
        logger.error(`  ❌ Failed: ${error.message}`);
      }
    }

    logger.log('\n🎉 All tests completed successfully!');
    logger.log('\n📋 Test Summary:');
    logger.log('✅ Gemini AI Service initialized');
    logger.log('✅ Connection test passed');
    logger.log('✅ Sentence counting working');
    logger.log('✅ AI evaluation working');
    logger.log('✅ Scoring algorithm components working');
    logger.log('✅ Edge cases handled');

  } catch (error) {
    logger.error('❌ Test failed with error:', error.message);
    logger.error(error.stack);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testGeminiSimple()
    .then(() => {
      logger.log('🏁 Simple test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Simple test failed:', error);
      process.exit(1);
    });
}

export { testGeminiSimple };
