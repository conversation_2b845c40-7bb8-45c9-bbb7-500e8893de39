import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrateExistingSubmissionData1750576722496 implements MigrationInterface {
  name = 'MigrateExistingSubmissionData1750576722496';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('Starting migration of existing submission data...');

    // Migrate diary entries
    console.log('Migrating diary entries...');
    await this.migrateDiaryEntries(queryRunner);

    // Migrate mission diary entries
    console.log('Migrating mission diary entries...');
    await this.migrateMissionDiaryEntries(queryRunner);

    // Migrate novel entries
    console.log('Migrating novel entries...');
    await this.migrateNovelEntries(queryRunner);

    console.log('Data migration completed successfully');
  }

  private async migrateDiaryEntries(queryRunner: QueryRunner): Promise<void> {
    // Find all diary entries that have been submitted (status != 'new')
    const submittedEntries = await queryRunner.query(`
      SELECT id, status, evaluated_at, title, content, created_at, updated_at
      FROM "diary_entry"
      WHERE status != 'new'
    `);

    console.log(`Found ${submittedEntries.length} submitted diary entries to migrate`);

    for (const entry of submittedEntries) {
      // Create a submitted version from the current entry data
      const submittedVersionResult = await queryRunner.query(
        `
        INSERT INTO "diary_entry_history"
        (diary_entry_id, title, content, version_number, is_latest, is_submitted_version,
         submission_number, submitted_at, word_count, is_resubmission, resubmission_type,
         previous_status, created_at, updated_at)
        VALUES ($1, $2, $3, 1, true, true, 1, $4, $5, false, null, null, NOW(), NOW())
        RETURNING id
      `,
        [entry.id, entry.title, entry.content, entry.created_at || new Date(), entry.word_count || 0],
      );

      const submittedVersionId = submittedVersionResult[0].id;

      // Update the diary entry with new fields including resubmission tracking
      await queryRunner.query(
        `
        UPDATE "diary_entry"
        SET
          is_draft = false,
          last_submitted_at = $2,
          last_reviewed_at = $3,
          can_submit_new_version = $4,
          submitted_version_count = 1,
          current_submitted_version_id = $5,
          is_resubmission = false,
          resubmission_type = null,
          previous_review_count = 0,
          previous_confirmation_count = 0
        WHERE id = $1
      `,
        [
          entry.id,
          entry.created_at || new Date(),
          entry.evaluated_at,
          entry.status === 'reviewed' || entry.status === 'confirm', // Can submit new version if reviewed
          submittedVersionId,
        ],
      );
    }

    // Set draft entries as drafts with resubmission tracking
    await queryRunner.query(`
      UPDATE "diary_entry"
      SET
        is_draft = true,
        can_submit_new_version = true,
        is_resubmission = false,
        resubmission_type = null,
        previous_review_count = 0,
        previous_confirmation_count = 0
      WHERE status = 'new'
    `);
  }

  private async migrateMissionDiaryEntries(queryRunner: QueryRunner): Promise<void> {
    // First, check what columns exist in the mission_diary_entry table
    const columns = await queryRunner.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'mission_diary_entry' AND table_schema = 'public'
    `);

    console.log(
      'Available columns in mission_diary_entry:',
      columns.map((c: any) => c.column_name),
    );

    // Find all mission diary entries that have been submitted
    // Use only columns that exist in the current schema
    const submittedEntries = await queryRunner.query(`
      SELECT id, status, content, created_at, updated_at
      FROM "mission_diary_entry"
      WHERE status IN ('SUBMITTED', 'REVIEWED', 'CONFIRMED', 'submit', 'reviewed', 'confirm')
    `);

    console.log(`Found ${submittedEntries.length} submitted mission diary entries to migrate`);

    for (const entry of submittedEntries) {
      // Create a submitted version from the current entry data
      const submittedVersionResult = await queryRunner.query(
        `
        INSERT INTO "mission_diary_entry_history"
        (mission_entry_id, content, version_number, is_latest, is_submitted_version,
         submission_number, submitted_at, word_count, is_resubmission, resubmission_type,
         previous_status, created_at, updated_at)
        VALUES ($1, $2, 1, true, true, 1, $3, $4, false, null, null, NOW(), NOW())
        RETURNING id
      `,
        [
          entry.id,
          entry.content,
          entry.created_at || new Date(),
          0, // word_count default
        ],
      );

      const submittedVersionId = submittedVersionResult[0].id;

      // Update the mission diary entry with new fields including resubmission tracking
      const isReviewed = ['REVIEWED', 'CONFIRMED', 'reviewed', 'confirm'].includes(entry.status);
      await queryRunner.query(
        `
        UPDATE "mission_diary_entry"
        SET
          is_draft = false,
          last_submitted_at = $2,
          last_reviewed_at = $3,
          can_submit_new_version = $4,
          submitted_version_count = 1,
          current_submitted_version_id = $5,
          is_resubmission = false,
          resubmission_type = null,
          previous_review_count = 0,
          previous_confirmation_count = 0
        WHERE id = $1
      `,
        [
          entry.id,
          entry.created_at || new Date(),
          isReviewed ? entry.updated_at || new Date() : null,
          isReviewed, // Can submit new version if reviewed
          submittedVersionId,
        ],
      );
    }

    // Set draft entries as drafts with resubmission tracking
    await queryRunner.query(`
      UPDATE "mission_diary_entry"
      SET
        is_draft = true,
        can_submit_new_version = true,
        is_resubmission = false,
        resubmission_type = null,
        previous_review_count = 0,
        previous_confirmation_count = 0
      WHERE status IN ('NEW', 'new')
    `);
  }

  private async migrateNovelEntries(queryRunner: QueryRunner): Promise<void> {
    // First, check what columns exist in the novel_entry table
    const columns = await queryRunner.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'novel_entry' AND table_schema = 'public'
    `);

    console.log(
      'Available columns in novel_entry:',
      columns.map((c: any) => c.column_name),
    );

    // Find all novel entries that have been submitted
    // Check if submitted_at and reviewed_at exist, otherwise use created_at/updated_at
    const hasSubmittedAt = columns.some((c: any) => c.column_name === 'submitted_at');
    const hasReviewedAt = columns.some((c: any) => c.column_name === 'reviewed_at');

    const submittedEntries = await queryRunner.query(`
      SELECT id, status, ${hasSubmittedAt ? 'submitted_at' : 'created_at'} as submitted_at,
             ${hasReviewedAt ? 'reviewed_at' : 'updated_at'} as reviewed_at, content,
             word_count, created_at, updated_at
      FROM "novel_entry"
      WHERE status != 'new'
    `);

    console.log(`Found ${submittedEntries.length} submitted novel entries to migrate`);

    for (const entry of submittedEntries) {
      // Create a submitted version from the current entry data
      const submittedVersionResult = await queryRunner.query(
        `
        INSERT INTO "novel_entry_history"
        (novel_entry_id, content, version_number, is_latest, is_submitted_version,
         submission_number, submitted_at, word_count, is_resubmission, resubmission_type,
         previous_status, created_at, updated_at)
        VALUES ($1, $2, 1, true, true, 1, $3, $4, false, null, null, NOW(), NOW())
        RETURNING id
      `,
        [entry.id, entry.content, entry.submitted_at || new Date(), entry.word_count || 0],
      );

      const submittedVersionId = submittedVersionResult[0].id;

      // Update the novel entry with new fields including resubmission tracking
      const isReviewed = ['reviewed', 'confirmed', 'correction_given'].includes(entry.status);
      await queryRunner.query(
        `
        UPDATE "novel_entry"
        SET
          is_draft = false,
          last_submitted_at = $2,
          last_reviewed_at = $3,
          can_submit_new_version = $4,
          submitted_version_count = 1,
          current_submitted_version_id = $5,
          is_resubmission = false,
          resubmission_type = null,
          previous_review_count = 0,
          previous_confirmation_count = 0
        WHERE id = $1
      `,
        [
          entry.id,
          entry.submitted_at || new Date(),
          isReviewed ? entry.reviewed_at || new Date() : null,
          isReviewed, // Can submit new version if reviewed
          submittedVersionId,
        ],
      );
    }

    // Set draft entries as drafts with resubmission tracking
    await queryRunner.query(`
      UPDATE "novel_entry"
      SET
        is_draft = true,
        can_submit_new_version = true,
        is_resubmission = false,
        resubmission_type = null,
        previous_review_count = 0,
        previous_confirmation_count = 0
      WHERE status = 'new'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('Rolling back data migration...');

    // Remove all submitted versions created by this migration
    await queryRunner.query(`DELETE FROM "diary_entry_history" WHERE is_submitted_version = true`);
    await queryRunner.query(`DELETE FROM "mission_diary_entry_history" WHERE is_submitted_version = true`);
    await queryRunner.query(`DELETE FROM "novel_entry_history" WHERE is_submitted_version = true`);

    console.log('Data migration rollback completed');
  }
}
