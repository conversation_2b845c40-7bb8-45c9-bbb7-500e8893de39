import { Repository, DataSource } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { User } from '../../src/database/entities/user.entity';
import { UserType } from '../../src/database/entities/user.entity';
import { PaymentTransaction, PaymentTransactionStatus } from '../../src/database/entities/payment-transaction.entity';
import { KcpPaymentMethod, PurchaseType } from '../../src/modules/payment/interfaces/kcp.interface';
import { PaymentWebhook } from '../../src/database/entities/payment-webhook.entity';
import { ShopItemPurchase } from '../../src/database/entities/shop-item-purchase.entity';
import { UserPlan } from '../../src/database/entities/user-plan.entity';
// import * as bcrypt from 'bcrypt'; // Not available in test environment

/**
 * Create a test user for testing purposes
 */
export const createTestUser = async (userRepository: Repository<User>, overrides: Partial<User> = {}): Promise<User> => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  const defaultUser = {
    userId: `test-user-${timestamp}-${random}`,
    email: `test-${timestamp}-${random}@example.com`,
    password: 'Test@123', // Plain text for testing
    name: 'Test User',
    phoneNumber: '010-1234-5678', // Required field
    gender: 'male', // Required field
    type: UserType.STUDENT,
    isActive: true,
    isConfirmed: true,
    agreedToTerms: true,
    ...overrides,
  };

  const user = userRepository.create(defaultUser);
  return await userRepository.save(user);
};

/**
 * Generate a test JWT token for authentication
 */
export const generateTestToken = (user: User, jwtService: JwtService): string => {
  const payload = {
    sub: user.id,
    userId: user.userId,
    email: user.email,
    type: user.type,
  };
  return jwtService.sign(payload);
};

/**
 * Create a test payment transaction
 */
export const createTestPaymentTransaction = async (transactionRepository: Repository<PaymentTransaction>, user: User, overrides: Partial<PaymentTransaction> = {}): Promise<PaymentTransaction> => {
  const defaultTransaction = {
    transactionId: `TEST-TXN-${Date.now()}`,
    orderId: `TEST-ORDER-${Date.now()}`,
    amount: 10000,
    currency: 'KRW',
    paymentMethod: KcpPaymentMethod.CARD,
    status: PaymentTransactionStatus.INITIATED,
    purchaseType: PurchaseType.SHOP_ITEM,
    referenceId: 'test-reference',
    productName: 'Test Product',
    buyerName: user.name,
    buyerEmail: user.email,
    buyerPhone: '010-1234-5678',
    user: user,
    ...overrides,
  };

  const transaction = transactionRepository.create(defaultTransaction);
  return await transactionRepository.save(transaction);
};

/**
 * Create a test webhook record
 */
export const createTestWebhook = async (webhookRepository: Repository<PaymentWebhook>, transaction: PaymentTransaction, overrides: Partial<PaymentWebhook> = {}): Promise<PaymentWebhook> => {
  const defaultWebhook = {
    webhookId: `WEBHOOK-${Date.now()}`,
    orderId: transaction.orderId,
    transactionId: transaction.transactionId,
    eventType: 'payment.completed',
    payload: {
      res_cd: '0000',
      res_msg: 'SUCCESS',
      tno: transaction.kcpTransactionId || `TXN-${Date.now()}`,
      amount: transaction.amount.toString(),
    },
    sourceIp: '127.0.0.1',
    isProcessed: false,
    ...overrides,
  };

  const webhook = webhookRepository.create(defaultWebhook);
  return await webhookRepository.save(webhook);
};

/**
 * Mock KCP API responses
 */
export const mockKcpResponses = {
  tradeRegSuccess: {
    res_cd: '0000',
    res_msg: 'SUCCESS',
    tno: `TXN-${Date.now()}`,
    amount: '10000',
    pnt_issue: '0',
    trace_no: `TRACE-${Date.now()}`,
    PayUrl: 'https://stg-spl.kcp.co.kr/gw/enc/v1/payment',
    ordr_chk: 'test-order-check',
    kcp_sign_data: 'test-sign-data',
  },

  paymentSuccess: {
    res_cd: '0000',
    res_msg: 'SUCCESS',
    tno: `TXN-${Date.now()}`,
    amount: '10000',
    pnt_issue: '0',
    trace_no: `TRACE-${Date.now()}`,
    app_time: new Date().toISOString(),
    app_no: `APP-${Date.now()}`,
    card_cd: '01',
    card_name: 'Test Card',
  },

  tradeRegFailure: {
    res_cd: '9999',
    res_msg: 'TRADE_REG_FAILED',
  },

  paymentFailure: {
    res_cd: '9999',
    res_msg: 'PAYMENT_FAILED',
  },
};

/**
 * Clean up test database - safer approach for PostgreSQL with foreign keys
 */
export const cleanupTestDatabase = async (dataSource: DataSource): Promise<void> => {
  // For integration tests, we'll just clean up the specific tables we use
  // in the correct order to avoid foreign key constraint issues
  const tablesToClean = [
    'payment_webhook',
    'payment_transaction',
    'shop_item_purchase',
    'user_plan',
    'user_role',
    // Don't clean users table as it might be referenced by other tests
  ];

  for (const tableName of tablesToClean) {
    try {
      await dataSource.query(`DELETE FROM ${tableName} WHERE 1=1`);
    } catch (error) {
      // Ignore errors for tables that don't exist or have constraints
      console.warn(`Could not clean table ${tableName}:`, error.message);
    }
  }
};

/**
 * Seed test database with essential data
 */
export const seedTestDatabase = async (dataSource: DataSource): Promise<void> => {
  // Add any essential test data seeding here
  // For now, we'll keep it minimal since tests should be isolated
};

/**
 * Mock repository factory for unit tests
 */
export type MockType<T> = {
  [P in keyof T]?: jest.Mock<any>;
};

export const mockRepository = <T = any>(): MockType<Repository<T>> => ({
  findOne: jest.fn(),
  find: jest.fn(),
  save: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  remove: jest.fn(),
  createQueryBuilder: jest.fn(() => ({
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getOne: jest.fn(),
    getMany: jest.fn(),
    getCount: jest.fn(),
  })),
});
