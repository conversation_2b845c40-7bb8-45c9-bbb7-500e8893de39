import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Award, AwardModule, AwardCriteria, AwardFrequency } from '../../database/entities/award.entity';

@Injectable()
export class NovelAwardsSeed {
  private readonly logger = new Logger(NovelAwardsSeed.name);

  constructor(
    @InjectRepository(Award)
    private awardRepository: Repository<Award>,
  ) {}

  async seed(): Promise<void> {
    this.logger.log('Seeding Novel Awards...');

    // Define the novel awards as requested by client
    const novelAwards = [
      // Monthly Awards
      {
        name: 'Best Writer Award',
        description: 'Awarded monthly to the student with exceptional writing quality and consistency in novel entries',
        module: AwardModule.NOVEL,
        criteria: [AwardCriteria.NOVEL_PERFORMANCE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 150,
        isActive: true,
        criteriaConfig: {
          minScore: 1, // Very low for testing
          minNovels: 1, // Just 1 novel for testing
          maxWinners: 5, // More winners for testing
        },
      },
      {
        name: 'Best Perfect Award',
        description: 'Awarded monthly to the student who excels in all aspects of novel writing - quality, completion, and word count',
        module: AwardModule.NOVEL,
        criteria: [AwardCriteria.NOVEL_PERFORMANCE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 200,
        isActive: true,
        criteriaConfig: {
          minScore: 7,
          minNovels: 3,
          minCompletionRate: 80,
          maxWinners: 1,
        },
      },
      {
        name: 'Best Performance Award',
        description: 'Awarded monthly to the student with outstanding overall performance in the novel module',
        module: AwardModule.NOVEL,
        criteria: [AwardCriteria.NOVEL_PERFORMANCE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 150,
        isActive: true,
        criteriaConfig: {
          minScore: 6,
          minNovels: 4,
          minSubmissionFrequency: 1,
          maxWinners: 1,
        },
      },

      // Annual Awards
      {
        name: 'Best Writer Award',
        description: 'Awarded annually to the student with exceptional writing quality and consistency throughout the year',
        module: AwardModule.NOVEL,
        criteria: [AwardCriteria.NOVEL_PERFORMANCE],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 500,
        isActive: true,
        criteriaConfig: {
          minScore: 8,
          minNovels: 20,
          maxWinners: 1,
        },
      },
      {
        name: 'Best Perfect Award',
        description: 'Awarded annually to the student who excels in all aspects of novel writing throughout the year',
        module: AwardModule.NOVEL,
        criteria: [AwardCriteria.NOVEL_PERFORMANCE],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 750,
        isActive: true,
        criteriaConfig: {
          minScore: 8,
          minNovels: 25,
          minCompletionRate: 75,
          maxWinners: 1,
        },
      },
      {
        name: 'Best Performance Award',
        description: 'Awarded annually to the student with outstanding overall performance in the novel module throughout the year',
        module: AwardModule.NOVEL,
        criteria: [AwardCriteria.NOVEL_PERFORMANCE],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 500,
        isActive: true,
        criteriaConfig: {
          minScore: 7,
          minNovels: 30,
          minSubmissionFrequency: 0.5,
          maxWinners: 1,
        },
      },
    ];

    // Check and create awards
    for (const awardData of novelAwards) {
      try {
        // Check if award already exists (by name, module, and frequency)
        const existingAward = await this.awardRepository.findOne({
          where: {
            name: awardData.name,
            module: awardData.module,
            frequency: awardData.frequency,
          },
        });

        if (existingAward) {
          this.logger.log(`Award "${awardData.name}" (${awardData.frequency}) already exists, skipping...`);
          continue;
        }

        // Create new award
        const award = this.awardRepository.create(awardData);
        await this.awardRepository.save(award);

        this.logger.log(`Created award: "${awardData.name}" (${awardData.frequency})`);
      } catch (error) {
        this.logger.error(`Error creating award "${awardData.name}": ${error.message}`);
      }
    }

    this.logger.log('Novel Awards seeding completed');
  }
}
