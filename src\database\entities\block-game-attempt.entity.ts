import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { NonAuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { BlockGame } from './block-game.entity';

@Entity()
export class BlockGameAttempt extends NonAuditableBaseEntity {
  @Column({ name: 'student_id', type: 'uuid' })
  studentId: string;

  @Column({ name: 'block_game_id', type: 'uuid' })
  blockGameId: string;

  @Column({ type: 'float' })
  score: number;

  @Column({ name: 'total_score', type: 'float' })
  totalScore: number;

  @Column({ name: 'sentence_constructions', type: 'json' })
  sentenceConstructions: {
    startingSentences: string[];
    expandingSentences: string[];
    completedSentences: Array<{
      starting: string;
      expanding: string;
      isCorrect: boolean;
    }>;
  };

  @Column({ name: 'submitted_at' })
  submittedAt: Date;

  // Relationships
  @ManyToOne(() => User)
  @JoinC<PERSON>umn({ name: 'student_id' })
  student: User;

  @ManyToOne(() => BlockGame)
  @JoinColumn({ name: 'block_game_id' })
  blockGame: BlockGame;
}
