import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { StoryMakerSubmission } from './story-maker-submission.entity';
import { User } from './user.entity';

/**
 * Type of user who liked the story submission
 */
export enum LikerType {
  STUDENT = 'student',
  TUTOR = 'tutor',
}

@Entity()
@Unique(['submissionId', 'likerId']) // Prevent duplicate likes
export class StoryMakerLike extends AuditableBaseEntity {
  @Column({ name: 'submission_id' })
  submissionId: string;

  @ManyToOne(() => StoryMakerSubmission, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'submission_id' })
  submission: StoryMakerSubmission;

  @Column({ name: 'liker_id' })
  likerId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'liker_id' })
  liker: User;

  @Column({
    name: 'liker_type',
    type: 'enum',
    enum: LikerType,
  })
  likerType: LikerType;

  /**
   * Convert to DTO for API responses
   */
  toDto(): any {
    return {
      id: this.id,
      submission_id: this.submissionId,
      liker_id: this.likerId,
      liker_type: this.likerType,
      liker_name: this.liker?.name,
      liker_profile_picture: this.liker?.profilePicture,
      created_at: this.createdAt,
    };
  }
}
