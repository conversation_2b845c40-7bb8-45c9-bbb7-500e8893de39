import { DataSourceOptions } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as dotenv from 'dotenv';
import { DataSource } from 'typeorm';
import { SnakeNamingStrategy } from '../common/strategies/snake-naming.strategy';

dotenv.config();

export const getTypeOrmConfig = (configService: ConfigService): DataSourceOptions => ({
  type: 'postgres' as const,
  host: configService.get<string>('DATABASE_HOST'),
  port: configService.get<number>('DATABASE_PORT'),
  username: configService.get<string>('DATABASE_USER'),
  password: configService.get<string>('DATABASE_PASSWORD'),
  database: configService.get<string>('DATABASE_NAME'),
  entities: [__dirname + '/../database/entities/*.entity{.ts,.js}'],
  migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
  synchronize: false,
  migrationsRun: true,
  migrationsTableName: 'migrations',
  namingStrategy: new SnakeNamingStrategy(),
  ssl: { rejectUnauthorized: false },
  // Connection pool configuration for better performance under load
  extra: {
    max: configService.get<number>('DATABASE_POOL_MAX') || 30, // Maximum number of connections in the pool
    min: configService.get<number>('DATABASE_POOL_MIN') || 5,  // Minimum number of connections in the pool
    idle: configService.get<number>('DATABASE_POOL_IDLE') || 10000, // Maximum time (ms) that a connection can be idle before being released
    acquire: configService.get<number>('DATABASE_POOL_ACQUIRE') || 30000, // Maximum time (ms) to try getting connection from pool
    evict: configService.get<number>('DATABASE_POOL_EVICT') || 1000, // How often to run eviction (ms)
    handleDisconnects: true, // Automatically handle disconnects
    // Additional PostgreSQL specific settings
    statement_timeout: configService.get<number>('DATABASE_STATEMENT_TIMEOUT') || 30000, // 30 seconds
    query_timeout: configService.get<number>('DATABASE_QUERY_TIMEOUT') || 30000, // 30 seconds
    connectionTimeoutMillis: configService.get<number>('DATABASE_CONNECTION_TIMEOUT') || 5000, // 5 seconds
    idleTimeoutMillis: configService.get<number>('DATABASE_IDLE_TIMEOUT') || 10000, // 10 seconds
  },
  // Additional TypeORM performance settings
  maxQueryExecutionTime: configService.get<number>('DATABASE_MAX_QUERY_TIME') || 5000, // Log slow queries (5 seconds)
  logging: configService.get<string>('NODE_ENV') === 'development' ? ['error', 'warn', 'migration'] : ['error', 'warn'],
});

export const AppDataSource = new DataSource(getTypeOrmConfig(new ConfigService()));
