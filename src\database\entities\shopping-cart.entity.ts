import { <PERSON><PERSON>ty, Column, OneToMany, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { ShoppingCartItem } from './shopping-cart-item.entity';

/**
 * Status of the shopping cart
 * @enum {string}
 */
export enum ShoppingCartStatus {
  /** <PERSON><PERSON> is active and can be modified */
  ACTIVE = 'active',
  /** <PERSON>t has been checked out */
  CHECKED_OUT = 'checked_out',
  /** Cart has been abandoned */
  ABANDONED = 'abandoned',
}

@Entity()
export class ShoppingCart extends AuditableBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({
    name: 'status',
    type: 'enum',
    enum: ShoppingCartStatus,
    default: ShoppingCartStatus.ACTIVE,
  })
  status: ShoppingCartStatus;

  @Column({ name: 'last_activity', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  lastActivity: Date;

  @OneToMany(() => ShoppingCartItem, (item) => item.cart, { cascade: true })
  items: ShoppingCartItem[];

  /**
   * Calculate the total price of all items in the cart
   * @returns The total price
   */
  getTotalPrice(): number {
    if (!this.items || this.items.length === 0) {
      return 0;
    }

    return this.items.reduce((total, item) => {
      return total + item.quantity * Number(item.price);
    }, 0);
  }

  /**
   * Calculate the total reward points required for all items in the cart
   * @returns The total reward points
   */
  getTotalRewardPoints(): number {
    if (!this.items || this.items.length === 0) {
      return 0;
    }

    return this.items.reduce((total, item) => {
      return total + item.quantity * item.rewardPoints;
    }, 0);
  }
}
