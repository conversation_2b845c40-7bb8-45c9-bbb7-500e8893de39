import { Injectable } from '@nestjs/common';
import { AsyncLocalStorage } from 'async_hooks';

/**
 * Service to track the current user throughout the request lifecycle
 * This uses AsyncLocalStorage to maintain context across async operations
 */
@Injectable()
export class CurrentUserService {
  private readonly storage = new AsyncLocalStorage<{ userId: string }>();

  /**
   * Set the current user ID for the current execution context
   * @param userId The ID of the current user
   * @param callback The function to execute within this context
   * @returns The result of the callback function
   */
  runWithUser<T>(userId: string, callback: () => T): T {
    return this.storage.run({ userId }, callback);
  }

  /**
   * Get the current user ID from the current execution context
   * @returns The current user ID or null if not set
   */
  getCurrentUserId(): string | null {
    const store = this.storage.getStore();
    return store?.userId || null;
  }
}
