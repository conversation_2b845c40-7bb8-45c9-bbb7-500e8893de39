import { Logger, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { WritingEntryStatus, WritingEntryStatusHelper } from '../enums/writing-entry-status.enum';
import { IWritingEntry, IWritingEntryHistory, ISubmitWritingEntryDto, IUpdateWritingEntryDto, IWritingEntryResponseDto } from '../interfaces/writing-entry.interface';

/**
 * Base service class for all writing entries (diary, mission diary, novel)
 * Implements unified submission/review/confirmation lifecycle
 */
export abstract class WritingEntryBaseService<
  TEntry extends IWritingEntry,
  THistory extends IWritingEntryHistory,
  TSubmitDto extends ISubmitWritingEntryDto,
  TUpdateDto extends IUpdateWritingEntryDto,
  TResponseDto extends IWritingEntryResponseDto,
> {
  protected readonly logger = new Logger(this.constructor.name);

  constructor(
    protected readonly dataSource: DataSource,
    protected readonly entryRepository: Repository<TEntry>,
    protected readonly historyRepository: Repository<THistory>,
  ) {}

  /**
   * Update entry (save as draft) - unified logic
   * NEW REQUIREMENT: No version creation, no notifications
   */
  protected async updateEntryBase(entryId: string, studentId: string, updateDto: TUpdateDto, additionalValidation?: (entry: TEntry) => Promise<void>): Promise<TEntry> {
    const entry = await this.entryRepository.findOne({
      where: { id: entryId, studentId } as any,
      relations: this.getUpdateRelations(),
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    // Additional validation if provided
    if (additionalValidation) {
      await additionalValidation(entry);
    }

    // NEW REQUIREMENT: Update API should NOT create versions - this is "save as draft"
    this.logger.log(`Updating entry ${entryId} as draft (no version created)`);

    // Update content and mark as draft
    if (updateDto.content !== undefined) {
      entry.content = updateDto.content;
      entry.wordCount = this.calculateWordCount(updateDto.content);
    }

    // Apply other updates
    Object.assign(entry, updateDto);

    // NEW REQUIREMENT: Mark as draft
    entry.isDraft = true;

    return await this.entryRepository.save(entry);
  }

  /**
   * Submit entry for review - unified logic
   * NEW REQUIREMENT: Create submitted version, always notify, check submission gating
   */
  protected async submitEntryBase(entryId: string, studentId: string, submitDto: TSubmitDto, additionalValidation?: (entry: TEntry) => Promise<void>): Promise<TEntry> {
    const entry = await this.entryRepository.findOne({
      where: { id: entryId, studentId } as any,
      relations: this.getSubmitRelations(),
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    // NEW REQUIREMENT: Check if user can submit a new version
    // Allow submissions after review or if canSubmitNewVersion is true
    // Also allow resubmissions (entries that have been reviewed before)
    const hasBeenReviewed = entry.lastReviewedAt || entry.previousReviewCount > 0;
    const canSubmit =
      entry.canSubmitNewVersion ||
      entry.status === WritingEntryStatus.REVIEWED || // REVIEWED is now the final state
      hasBeenReviewed; // Allow resubmissions after any previous review

    if (!canSubmit) {
      // Provide more specific error message based on current status
      let errorMessage = 'Cannot submit new version.';

      if (entry.status === WritingEntryStatus.SUBMITTED) {
        errorMessage += ' Previous submission is still pending review by tutor.';
      } else {
        errorMessage += ' Previous submission must be reviewed by tutor first.';
      }

      throw new BadRequestException(errorMessage);
    }

    // Additional validation if provided
    if (additionalValidation) {
      await additionalValidation(entry);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // NEW REQUIREMENT: Create a submitted version in history
      const submissionNumber = entry.submittedVersionCount + 1;

      // Determine if this is a resubmission and what type
      const isResubmission = entry.submittedVersionCount > 0;
      let resubmissionType: 'after_review' | null = null;
      let previousStatus: string | null = null;

      if (isResubmission) {
        previousStatus = entry.status;
        if (entry.status === WritingEntryStatus.REVIEWED) {
          resubmissionType = 'after_review';
          entry.previousReviewCount = (entry.previousReviewCount || 0) + 1;
        }
        // Note: CONFIRMED stage removed from lifecycle
      }

      // Create submitted version with resubmission tracking
      const submittedVersion = await this.createSubmittedVersion(queryRunner, entryId, submitDto.content, submissionNumber, isResubmission, resubmissionType, previousStatus);

      // Update the entry with submission data
      entry.content = submitDto.content;
      entry.wordCount = this.calculateWordCount(submitDto.content);
      entry.status = WritingEntryStatus.SUBMITTED;

      // Apply other updates
      Object.assign(entry, submitDto);

      // NEW REQUIREMENT: Update submission tracking fields
      entry.isDraft = false;
      entry.lastSubmittedAt = new Date();
      entry.canSubmitNewVersion = false; // Will be set to true when reviewed
      entry.submittedVersionCount = submissionNumber;
      entry.currentSubmittedVersionId = submittedVersion.id;
      entry.submittedAt = new Date();

      // Update resubmission tracking fields
      entry.isResubmission = isResubmission;
      entry.resubmissionType = resubmissionType;

      const savedEntry = await queryRunner.manager.save(entry);
      await queryRunner.commitTransaction();

      // NEW REQUIREMENT: Always send notification for submissions
      try {
        await this.sendSubmissionNotification(savedEntry, submissionNumber, isResubmission, resubmissionType);
      } catch (notificationError) {
        this.logger.error(`Failed to send notification: ${notificationError.message}`, notificationError.stack);
      }

      return savedEntry;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Submit review with score (and optional correction) - unified logic
   * NEW REQUIREMENT: Score is required, correction is optional, can only review once
   * SIMPLIFIED: Direct transition from SUBMITTED to REVIEWED
   */
  protected async submitReview(entryId: string, tutorId: string, score: number, correction?: string): Promise<TEntry> {
    const entry = await this.entryRepository.findOne({
      where: { id: entryId } as any,
      relations: this.getReviewRelations(),
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    if (entry.status !== WritingEntryStatus.SUBMITTED) {
      throw new BadRequestException('Entry must be in SUBMITTED status to be reviewed');
    }

    // NEW REQUIREMENT: Check if already reviewed (can only review once)
    if (entry.reviewedBy && entry.score !== null && entry.score !== undefined) {
      throw new BadRequestException('Entry has already been reviewed. Only feedback can be added after review.');
    }

    // Validate score (this should be implemented by concrete services based on their scoring system)
    await this.validateScore(entry, score);

    // Update review status - direct transition from SUBMITTED to REVIEWED
    entry.status = WritingEntryStatus.REVIEWED;
    entry.reviewedAt = new Date();
    entry.reviewedBy = tutorId;
    entry.evaluatedAt = new Date();
    entry.evaluatedBy = tutorId;
    entry.score = score;
    entry.gainedScore = score;

    // NEW REQUIREMENT: Enable subsequent submissions after review
    entry.lastReviewedAt = new Date();
    entry.canSubmitNewVersion = true;

    const savedEntry = await this.entryRepository.save(entry);

    // Create correction if provided
    if (correction) {
      await this.createCorrection(entryId, tutorId, correction, score);
    }

    // Send review notification
    try {
      await this.sendReviewNotification(savedEntry, score, correction);
    } catch (notificationError) {
      this.logger.error(`Failed to send review notification: ${notificationError.message}`, notificationError.stack);
    }

    return savedEntry;
  }

  /**
   * Add feedback to entry - unlimited feedback allowed
   * NEW REQUIREMENT: Unlimited feedback can be given even after review
   */
  protected async addFeedback(entryId: string, tutorId: string, feedbackText: string): Promise<any> {
    const entry = await this.entryRepository.findOne({
      where: { id: entryId } as any,
      relations: this.getReviewRelations(),
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    if (!WritingEntryStatusHelper.isSubmitted(entry.status)) {
      throw new BadRequestException('Entry must be submitted to receive feedback');
    }

    // Create feedback (implementation depends on concrete service)
    const feedback = await this.createFeedback(entryId, tutorId, feedbackText);

    // Send feedback notification
    try {
      await this.sendFeedbackNotification(entry, feedback);
    } catch (notificationError) {
      this.logger.error(`Failed to send feedback notification: ${notificationError.message}`, notificationError.stack);
    }

    return feedback;
  }

  // REMOVED: confirmEntryReview method - confirm stage removed from lifecycle
  // Review submission is now the final state, no additional confirmation needed

  /**
   * Calculate word count for content
   */
  protected calculateWordCount(content: string): number {
    if (!content || content.trim().length === 0) {
      return 0;
    }
    return content.trim().split(/\s+/).length;
  }

  // Abstract methods to be implemented by concrete services
  protected abstract getUpdateRelations(): string[];
  protected abstract getSubmitRelations(): string[];
  protected abstract getReviewRelations(): string[];
  protected abstract createSubmittedVersion(
    queryRunner: any,
    entryId: string,
    content: string,
    submissionNumber: number,
    isResubmission?: boolean,
    resubmissionType?: 'after_review' | 'after_confirmation' | null,
    previousStatus?: string | null,
  ): Promise<THistory>;
  protected abstract sendSubmissionNotification(entry: TEntry, submissionNumber: number, isResubmission?: boolean, resubmissionType?: 'after_review' | 'after_confirmation' | null): Promise<void>;
  protected abstract mapEntryToResponseDto(entry: TEntry): Promise<TResponseDto>;

  // New abstract methods for review functionality
  protected abstract validateScore(entry: TEntry, score: number): Promise<void>;
  protected abstract createCorrection(entryId: string, tutorId: string, correction: string, score: number): Promise<any>;
  protected abstract createFeedback(entryId: string, tutorId: string, feedbackText: string): Promise<any>;
  protected abstract sendReviewNotification(entry: TEntry, score: number, correction?: string): Promise<void>;
  protected abstract sendFeedbackNotification(entry: TEntry, feedback: any): Promise<void>;
}
