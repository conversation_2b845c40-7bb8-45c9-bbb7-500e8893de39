import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PlanFeature, FeatureType } from '../../database/entities/plan-feature.entity';
import { CreatePlanFeatureDto, UpdatePlanFeatureDto, PlanFeatureResponseDto } from '../../database/models/plan-features.dto';

@Injectable()
export class PlanFeaturesService {
  constructor(
    @InjectRepository(PlanFeature)
    private readonly planFeatureRepository: Repository<PlanFeature>,
  ) {}

  async findAll(): Promise<PlanFeatureResponseDto[]> {
    const features = await this.planFeatureRepository.find();
    return features.map((feature) => this.toPlanFeatureResponseDto(feature));
  }

  async findById(id: string): Promise<PlanFeatureResponseDto> {
    const feature = await this.planFeatureRepository.findOne({ where: { id: id } });
    if (!feature) {
      throw new NotFoundException(`Feature with ID ${id} not found`);
    }
    return this.toPlanFeatureResponseDto(feature);
  }

  async findByType(type: FeatureType): Promise<PlanFeatureResponseDto> {
    const feature = await this.planFeatureRepository.findOne({ where: { type: type } });
    if (!feature) {
      throw new NotFoundException(`Feature with type ${type} not found`);
    }
    return this.toPlanFeatureResponseDto(feature);
  }

  async create(createPlanFeatureDto: CreatePlanFeatureDto): Promise<PlanFeatureResponseDto> {
    // Check if feature with same type already exists
    const existingFeature = await this.planFeatureRepository.findOne({
      where: { type: createPlanFeatureDto.type }, // This is a custom property, so we need to use the camel case naming convention.
    });

    if (existingFeature) {
      throw new ConflictException(`Feature with type ${createPlanFeatureDto.type} already exists`); // This is a custom property, so we need to use the camel case naming convention.
    }

    const feature = this.planFeatureRepository.create(createPlanFeatureDto);
    const savedFeature = await this.planFeatureRepository.save(feature);
    return this.toPlanFeatureResponseDto(savedFeature);
  }

  async update(id: string, updatePlanFeatureDto: UpdatePlanFeatureDto): Promise<PlanFeatureResponseDto> {
    const feature = await this.planFeatureRepository.findOne({ where: { id: id } });
    if (!feature) {
      throw new NotFoundException(`Feature with ID ${id} not found`);
    }

    // Update feature properties
    Object.assign(feature, updatePlanFeatureDto);
    const updatedFeature = await this.planFeatureRepository.save(feature);
    return this.toPlanFeatureResponseDto(updatedFeature);
  }

  async remove(id: string): Promise<void> {
    const feature = await this.planFeatureRepository.findOne({ where: { id: id } });
    if (!feature) {
      throw new NotFoundException(`Feature with ID ${id} not found`);
    }
    await this.planFeatureRepository.remove(feature);
  }

  private toPlanFeatureResponseDto(feature: PlanFeature): PlanFeatureResponseDto {
    return {
      id: feature.id,
      type: feature.type, // This is a custom property, so we need to use the camel case naming convention.
      name: feature.name, // This is a custom property, so we need to use the camel case naming convention.
      description: feature.description, // This is a custom property, so we need to use the camel case naming convention.
      createdAt: feature.createdAt,
      updatedAt: feature.updatedAt,
    };
  }
}
