import { Controller, Get, UseGuards, Query } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { GetUser } from '../../common/decorators/get-user.decorator';
import { User, UserType } from '../../database/entities/user.entity';
import { TutorMatchingService } from './tutor-matching.service';
import { StudentTutorDto, StudentTutorFilterDto } from '../../database/models/tutor-matching.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithArrayType, ApiOkResponseWithPagedListType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { MappingStatus } from '../../database/entities/student-tutor-mapping.entity';

@ApiTags('student-tutor-matching')
@Controller('student/tutors')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
@Roles(UserType.STUDENT)
export class StudentTutorMatchingController {
  constructor(private readonly tutorMatchingService: TutorMatchingService) {}

  @Get()
  @ApiOperation({ summary: 'Get all tutors assigned to the current student (deprecated, use /filter instead)' })
  @ApiOkResponseWithArrayType(StudentTutorDto, 'Tutors retrieved successfully')
  @ApiErrorResponse(404, 'Student not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getMyTutors(@GetUser() user: User, @Query() filterDto?: StudentTutorFilterDto): Promise<ApiResponse<StudentTutorDto[]>> {
    const result = await this.tutorMatchingService.getStudentTutors(user.id, filterDto);
    return ApiResponse.success(result.items, 'Tutors retrieved successfully');
  }

  @Get('filter')
  @ApiOperation({ summary: 'Get all tutors assigned to the current student with filtering and pagination' })
  @ApiQuery({
    name: 'tutorName',
    required: false,
    type: String,
    description: 'Filter by tutor name',
  })
  @ApiQuery({
    name: 'planFeatureId',
    required: false,
    type: String,
    description: 'Filter by module ID',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: MappingStatus,
    description: 'Filter by status',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by (name, moduleName, status, assignedDate)',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction',
  })
  @ApiOkResponseWithPagedListType(StudentTutorDto, 'Tutors retrieved successfully')
  @ApiErrorResponse(404, 'Student not found')
  @ApiErrorResponse(500, 'Internal server error')
  async filterMyTutors(
    @GetUser() user: User,
    @Query('tutorName') tutorName?: string,
    @Query('planFeatureId') planFeatureId?: string,
    @Query('status') status?: MappingStatus,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
  ): Promise<ApiResponse<PagedListDto<StudentTutorDto>>> {
    // Create filter DTO
    const filterDto: StudentTutorFilterDto = {
      tutorName,
      planFeatureId,
      status,
    };

    // Create pagination DTO
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC',
    };

    const tutors = await this.tutorMatchingService.getStudentTutors(user.id, filterDto, paginationDto);
    return ApiResponse.success(tutors, 'Tutors retrieved successfully');
  }
}
