import { MigrationInterface, QueryRunner } from "typeorm";

export class EssaySubmissionHistoryStatus1752404877263 implements MigrationInterface {
    name = 'EssaySubmissionHistoryStatus1752404877263'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."essay_task_submission_history_status_enum" AS ENUM('draft', 'submitted', 'reviewed', 'discarded', 'resubmitted')`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD "status" "public"."essay_task_submission_history_status_enum" NOT NULL DEFAULT 'draft'`);
        await queryRunner.query(`ALTER TYPE "public"."essay_task_submissions_status_enum" RENAME TO "essay_task_submissions_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."essay_task_submissions_status_enum" AS ENUM('draft', 'submitted', 'reviewed', 'discarded', 'resubmitted')`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "status" TYPE "public"."essay_task_submissions_status_enum" USING "status"::"text"::"public"."essay_task_submissions_status_enum"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "status" SET DEFAULT 'draft'`);
        await queryRunner.query(`DROP TYPE "public"."essay_task_submissions_status_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."essay_task_submissions_status_enum_old" AS ENUM('discarded', 'draft', 'reviewed', 'submitted')`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "status" TYPE "public"."essay_task_submissions_status_enum_old" USING "status"::"text"::"public"."essay_task_submissions_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "status" SET DEFAULT 'draft'`);
        await queryRunner.query(`DROP TYPE "public"."essay_task_submissions_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."essay_task_submissions_status_enum_old" RENAME TO "essay_task_submissions_status_enum"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "public"."essay_task_submission_history_status_enum"`);
    }

}
