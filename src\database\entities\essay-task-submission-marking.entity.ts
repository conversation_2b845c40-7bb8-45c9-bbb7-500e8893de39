import { <PERSON><PERSON><PERSON>, Column, <PERSON>T<PERSON><PERSON><PERSON>, Join<PERSON>olumn, Index } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { IsUUID } from 'class-validator';
import { EssayTaskSubmissions } from './essay-task-submissions.entity';
import { EssayTaskSubmissionHistory } from './essay-task-submission-history.entity';

@Entity()
@Index(['submissionId', 'submissionHistoryId'])
export class EssayTaskSubmissionMarking extends AuditableBaseEntity {
  @Column({
    name: 'points',
    type: 'float',
    nullable: true,
  })
  points?: number;

  @Column({
    name: 'submission_feedback',
    type: 'text',
    nullable: true,
  })
  submissionFeedback?: string;

  @Column({
    name: 'task_remarks',
    type: 'text',
    nullable: true,
  })
  taskRemarks?: string;

  @OneToOne(() => EssayTaskSubmissions, (submission) => submission.submissionMark)
  @JoinColumn({ name: 'submission' })
  submission: EssayTaskSubmissions;

  @Column({
    name: 'submission_id',
    type: 'uuid',
    nullable: true,
  })
  @IsUUID()
  submissionId: string;

  @OneToOne(() => EssayTaskSubmissionHistory, (submissionHistory) => submissionHistory.submissionMark, {nullable: true})
  @JoinColumn({ name: 'submission_history' })
  submissionHistory: EssayTaskSubmissionHistory;

  @Column({
    name: 'submission_history_id',
    type: 'uuid',
    nullable: true,
  })
  @IsUUID()
  submissionHistoryId: string;
}
