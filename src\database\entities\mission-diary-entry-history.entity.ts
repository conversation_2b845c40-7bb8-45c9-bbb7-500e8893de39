import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, Index } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { MissionDiaryEntry } from './mission-diary-entry.entity';

@Entity()
@Index(['missionEntryId', 'versionNumber'])
@Index(['missionEntryId', 'isLatest'])
@Index(['createdAt'])
export class MissionDiaryEntryHistory extends AuditableBaseEntity {
  @Column({ name: 'mission_entry_id', type: 'uuid' })
  missionEntryId: string;

  @ManyToOne(() => MissionDiaryEntry, (entry) => entry.versions)
  @JoinColumn({ name: 'mission_entry_id' })
  missionEntry: MissionDiaryEntry;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({ name: 'version_number', type: 'integer' })
  versionNumber: number;

  @Column({ name: 'is_latest', type: 'boolean', default: false })
  isLatest: boolean;

  @Column({ name: 'is_submitted_version', type: 'boolean', default: false })
  isSubmittedVersion: boolean;

  @Column({ name: 'submission_number', type: 'integer', nullable: true })
  submissionNumber: number;

  @Column({ name: 'submitted_at', type: 'timestamp', nullable: true })
  submittedAt: Date;

  // Resubmission tracking fields
  @Column({ name: 'is_resubmission', type: 'boolean', default: false })
  isResubmission: boolean;

  @Column({ name: 'resubmission_type', type: 'varchar', nullable: true })
  resubmissionType: 'after_review' | null;

  @Column({ name: 'previous_status', type: 'varchar', nullable: true })
  previousStatus: string;

  @Column({ name: 'word_count', type: 'integer' })
  wordCount: number;

  @Column({
    name: 'meta_data',
    type: 'json',
    nullable: true,
  })
  metaData: any;
}
