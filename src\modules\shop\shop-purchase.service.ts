import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, DataSource } from 'typeorm';
import { ShopItemPurchase, PaymentMethod, PurchaseStatus } from '../../database/entities/shop-item-purchase.entity';
import { ShopItem, ShopItemType } from '../../database/entities/shop-item.entity';
import { User } from '../../database/entities/user.entity';
import { PurchaseShopItemDto, ShopItemPurchaseResponseDto } from '../../database/models/shop.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { ShopFileService } from './shop-file.service';
import { ShoppingCartService } from './shopping-cart.service';
import { StudentOwnedItemService } from './student-owned-item.service';
import { RewardPointSettingService } from './reward-point-setting.service';

@Injectable()
export class ShopPurchaseService {
  private readonly logger = new Logger(ShopPurchaseService.name);

  constructor(
    @InjectRepository(ShopItemPurchase)
    private shopItemPurchaseRepository: Repository<ShopItemPurchase>,
    @InjectRepository(ShopItem)
    private shopItemRepository: Repository<ShopItem>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    private readonly shopFileService: ShopFileService,
    private readonly shoppingCartService: ShoppingCartService,
    private readonly studentOwnedItemService: StudentOwnedItemService,
    private readonly rewardPointSettingService: RewardPointSettingService,
  ) {}

  /**
   * Get the current reward point conversion rate
   */
  private async getRewardPointConversionRate(): Promise<number> {
    try {
      const activeSetting = await this.rewardPointSettingService.getActiveRewardPointSetting();
      return activeSetting.conversionRate;
    } catch (error) {
      this.logger.warn(`Could not get active reward point setting: ${error.message}. Using default conversion rate of 100.`);
      return 100; // Default conversion rate
    }
  }

  /**
   * Purchase a shop item
   * @param userId User ID making the purchase
   * @param purchaseShopItemDto Purchase data
   * @returns Purchase details
   */
  async purchaseShopItem(userId: string, purchaseShopItemDto: PurchaseShopItemDto): Promise<ShopItemPurchaseResponseDto> {
    // Start a database transaction to ensure data consistency
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check if user exists
      const user = await queryRunner.manager.findOne(User, {
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Check if shop item exists
      const shopItem = await queryRunner.manager.findOne(ShopItem, {
        where: { id: purchaseShopItemDto.shopItemId },
        relations: ['category'],
      });

      if (!shopItem) {
        throw new NotFoundException(`Shop item with ID ${purchaseShopItemDto.shopItemId} not found`);
      }

      // Check if the item is active
      if (!shopItem.isActive) {
        throw new BadRequestException(`Shop item with ID ${purchaseShopItemDto.shopItemId} is not active`);
      }

      // Check if the user has already purchased this item
      const existingPurchase = await queryRunner.manager.findOne(ShopItemPurchase, {
        where: {
          userId,
          shopItemId: purchaseShopItemDto.shopItemId,
          status: PurchaseStatus.COMPLETED,
        },
      });

      if (existingPurchase) {
        throw new BadRequestException(`You have already purchased this item`);
      }

      // Handle free items
      if (shopItem.type === ShopItemType.FREE) {
        purchaseShopItemDto.paymentMethod = PaymentMethod.FREE;
      }

      // Validate reward points payment BEFORE creating any records
      if (purchaseShopItemDto.paymentMethod === PaymentMethod.REWARD_POINTS) {
        // Check if item is purchasable with reward points
        if (!shopItem.isPurchasableInRewardpoint) {
          throw new BadRequestException({
            message: 'This item cannot be purchased with reward points',
            details: {
              itemTitle: shopItem.title,
              availablePaymentMethods: ['KCP Card', 'KCP Bank Transfer', 'KCP Virtual Account'],
              suggestion: 'Please choose a different payment method to purchase this item.'
            }
          });
        }

        // Get user's reward points balance
        const userRewardPoints = await this.shoppingCartService.getUserRewardPoints(userId);
        const finalPrice = shopItem.getFinalPrice();

        // Get conversion rate for reward points
        const conversionRate = await this.getRewardPointConversionRate();
        const requiredPoints = Math.ceil(finalPrice * conversionRate);

        if (userRewardPoints < requiredPoints) {
          const shortfall = requiredPoints - userRewardPoints;
          throw new BadRequestException({
            message: 'Insufficient reward points for this purchase',
            details: {
              itemTitle: shopItem.title,
              itemPrice: finalPrice,
              required: requiredPoints,
              available: userRewardPoints,
              shortfall: shortfall,
              conversionRate: conversionRate,
              suggestion: `You need ${shortfall} more reward points to purchase this item. You can earn more points by completing assignments, participating in activities, or purchasing other items.`
            }
          });
        }

      }

      // Create the purchase using the transaction
      const purchase = queryRunner.manager.create(ShopItemPurchase, {
        userId: userId,
        shopItemId: purchaseShopItemDto.shopItemId,
        originalPrice: shopItem.price,
        finalPrice: shopItem.getFinalPrice(),
        promotionId: shopItem.promotionId,
        discountAmount: shopItem.isOnSale() ? Number(shopItem.price) - Number(shopItem.discountedPrice) : null,
        paymentMethod: purchaseShopItemDto.paymentMethod,
        status: PurchaseStatus.COMPLETED,
        paymentDetails: purchaseShopItemDto.paymentDetails,
        notes: purchaseShopItemDto.notes || `Purchase completed via ${purchaseShopItemDto.paymentMethod}`,
      });

      // Set reward points used if payment method is reward points
      if (purchaseShopItemDto.paymentMethod === PaymentMethod.REWARD_POINTS) {
        const conversionRate = await this.getRewardPointConversionRate();
        purchase.rewardPointsUsed = Math.ceil(purchase.finalPrice * conversionRate);
      } else {
        purchase.rewardPointsUsed = 0;
      }

      const savedPurchase = await queryRunner.manager.save(purchase);

      // Deduct reward points if used (within transaction)
      if (purchase.rewardPointsUsed > 0) {
        await this.shoppingCartService.deductRewardPoints(
          userId,
          purchase.rewardPointsUsed,
          `Shop item purchase: ${shopItem.title}`
        );
        this.logger.log(`Deducted ${purchase.rewardPointsUsed} reward points for purchase of ${shopItem.title}`);
      }

      // Add item to student's owned items (within transaction)
      await this.studentOwnedItemService.addOwnedItem(userId, shopItem.id, savedPurchase.id);
      this.logger.log(`Added shop item ${shopItem.title} to student's owned items`);

      // Increment the purchase count for the shop item (within transaction)
      shopItem.purchaseCount += 1;
      await queryRunner.manager.save(shopItem);

      // Commit the transaction
      await queryRunner.commitTransaction();
      this.logger.log(`Purchase transaction committed successfully for item ${shopItem.title}`);

      // Map to response DTO
      return this.mapPurchaseToDto(savedPurchase, shopItem.title);
    } catch (error) {
      // Rollback the transaction on any error
      await queryRunner.rollbackTransaction();
      this.logger.error(`Purchase transaction rolled back due to error: ${error.message}`, error.stack);

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Error purchasing shop item: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to purchase shop item: ${error.message}`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Get user purchases
   * @param userId User ID
   * @param paginationDto Pagination parameters
   * @returns List of purchases
   */
  async getUserPurchases(userId: string, paginationDto?: PaginationDto): Promise<PagedListDto<ShopItemPurchaseResponseDto>> {
    try {
      const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC' } = paginationDto || {};
      const skip = (page - 1) * limit;

      const [purchases, totalCount] = await this.shopItemPurchaseRepository.findAndCount({
        where: { userId },
        order: { [sortBy]: sortDirection },
        skip,
        take: limit,
        relations: ['shopItem'],
      });

      // Map purchases to DTOs with file URLs
      const purchaseDtosPromises = purchases.map(async (purchase) => {
        const dto = this.mapPurchaseToDto(purchase, purchase.shopItem?.title);

        // Add file URL if available
        if (purchase.shopItem?.filePath) {
          try {
            dto.secureFileUrl = await this.shopFileService.getShopItemFileUrl(purchase.shopItemId);
          } catch (error) {
            this.logger.error(`Error getting file URL: ${error.message}`, error.stack);
          }
        }

        return dto;
      });

      const purchaseDtos = await Promise.all(purchaseDtosPromises);
      return new PagedListDto(purchaseDtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting user purchases: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get user purchases: ${error.message}`);
    }
  }

  /**
   * Get purchase by ID
   * @param id Purchase ID
   * @returns Purchase details
   */
  async getPurchaseById(id: string): Promise<ShopItemPurchaseResponseDto> {
    try {
      const purchase = await this.shopItemPurchaseRepository.findOne({
        where: { id },
        relations: ['shopItem'],
      });

      if (!purchase) {
        throw new NotFoundException(`Purchase with ID ${id} not found`);
      }

      const dto = this.mapPurchaseToDto(purchase, purchase.shopItem?.title);

      // Add file URL if available
      if (purchase.shopItem?.filePath) {
        try {
          dto.secureFileUrl = await this.shopFileService.getShopItemFileUrl(purchase.shopItemId);
        } catch (error) {
          this.logger.error(`Error getting file URL: ${error.message}`, error.stack);
        }
      }

      return dto;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error getting purchase: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get purchase: ${error.message}`);
    }
  }

  /**
   * Check if a user has purchased a specific item
   * @param userId User ID
   * @param shopItemId Shop item ID
   * @returns Whether the user has purchased the item
   */
  async hasUserPurchasedItem(userId: string, shopItemId: string): Promise<boolean> {
    try {
      const purchase = await this.shopItemPurchaseRepository.findOne({
        where: {
          userId,
          shopItemId,
          status: PurchaseStatus.COMPLETED,
        },
      });

      return !!purchase;
    } catch (error) {
      this.logger.error(`Error checking if user purchased item: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Update purchase status (admin only)
   * @param id Purchase ID
   * @param status New status
   * @param notes Optional notes
   * @returns Updated purchase
   */
  async updatePurchaseStatus(id: string, status: PurchaseStatus, notes?: string): Promise<ShopItemPurchaseResponseDto> {
    try {
      const purchase = await this.shopItemPurchaseRepository.findOne({
        where: { id },
        relations: ['shopItem'],
      });

      if (!purchase) {
        throw new NotFoundException(`Purchase with ID ${id} not found`);
      }

      // Update status and notes
      purchase.status = status;
      if (notes) {
        purchase.notes = notes;
      }

      const updatedPurchase = await this.shopItemPurchaseRepository.save(purchase);
      return this.mapPurchaseToDto(updatedPurchase, purchase.shopItem?.title);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error updating purchase status: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to update purchase status: ${error.message}`);
    }
  }

  /**
   * Get all purchases (admin only)
   * @param status Optional status filter
   * @param paginationDto Pagination parameters
   * @returns List of purchases
   */
  async getAllPurchases(status?: PurchaseStatus, paginationDto?: PaginationDto): Promise<PagedListDto<ShopItemPurchaseResponseDto>> {
    try {
      const whereConditions: FindOptionsWhere<ShopItemPurchase> = {};

      if (status) {
        whereConditions.status = status;
      }

      const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC' } = paginationDto || {};
      const skip = (page - 1) * limit;

      const [purchases, totalCount] = await this.shopItemPurchaseRepository.findAndCount({
        where: whereConditions,
        order: { [sortBy]: sortDirection },
        skip,
        take: limit,
        relations: ['shopItem', 'user'],
      });

      // Map purchases to DTOs
      const purchaseDtos = purchases.map((purchase) => this.mapPurchaseToDto(purchase, purchase.shopItem?.title, purchase.user?.name));

      return new PagedListDto(purchaseDtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting purchases: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get purchases: ${error.message}`);
    }
  }

  /**
   * Map purchase entity to DTO
   * @param purchase Purchase entity
   * @param shopItemTitle Optional shop item title
   * @param userName Optional user name
   * @returns Purchase DTO
   */
  private mapPurchaseToDto(purchase: ShopItemPurchase, shopItemTitle?: string, userName?: string): ShopItemPurchaseResponseDto {
    return {
      id: purchase.id,
      userId: purchase.userId,
      userName: userName,
      shopItemId: purchase.shopItemId,
      shopItemTitle: shopItemTitle,
      originalPrice: Number(purchase.originalPrice),
      finalPrice: Number(purchase.finalPrice),
      promotionId: purchase.promotionId,
      discountAmount: purchase.discountAmount ? Number(purchase.discountAmount) : null,
      paymentMethod: purchase.paymentMethod,
      status: purchase.status,
      paymentDetails: purchase.paymentDetails,
      notes: purchase.notes,
      createdAt: purchase.createdAt,
      updatedAt: purchase.updatedAt,
    };
  }
}
