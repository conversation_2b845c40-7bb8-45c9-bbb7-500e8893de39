import { MigrationInterface, QueryRunner } from 'typeorm';

export class QASubmissionEnhancement1745908567775 implements MigrationInterface {
  name = 'QASubmissionEnhancement1745908567775';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TYPE "public"."qa_subscription_status_enum" AS ENUM('active', 'inactive', 'suspended')`);
    await queryRunner.query(
      `CREATE TABLE "qa_subscription" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "student_id" uuid NOT NULL, "tutor_id" uuid NOT NULL, "status" "public"."qa_subscription_status_enum" NOT NULL DEFAULT 'active', "subscription_dates" jsonb, CONSTRAINT "PK_6274da3cf67dfa20c6957698b5b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "qa_question" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "question" text NOT NULL, "points" integer NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "minimum_words" integer NOT NULL, CONSTRAINT "PK_955828f714972215ef53c682ecb" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE TYPE "public"."qa_submission_status_enum" AS ENUM('draft', 'submitted', 'reviewed')`);
    await queryRunner.query(
      `CREATE TABLE "qa_submission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "assignment_id" uuid NOT NULL, "answer" text NOT NULL, "status" "public"."qa_submission_status_enum" NOT NULL DEFAULT 'draft', "submission_date" TIMESTAMP, "feedback" text, "corrections" jsonb, "reviewed_at" TIMESTAMP, "reviewed_by" uuid, CONSTRAINT "REL_064b0390e4c62a2d7797079070" UNIQUE ("assignment_id"), CONSTRAINT "PK_a9eed2229650a83c5458b3de298" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`CREATE TYPE "public"."qa_assignment_status_enum" AS ENUM('pending', 'in_progress', 'completed', 'expired')`);
    await queryRunner.query(
      `CREATE TABLE "qa_assignment" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "question_id" uuid NOT NULL, "student_id" uuid NOT NULL, "tutor_id" uuid NOT NULL, "points" integer NOT NULL, "deadline" TIMESTAMP NOT NULL, "instructions" text, "status" "public"."qa_assignment_status_enum" NOT NULL DEFAULT 'pending', "assigned_date" TIMESTAMP NOT NULL, CONSTRAINT "PK_f923d3914ff31c8a061da629d9c" PRIMARY KEY ("id"))`,
    );
    //await queryRunner.query(`ALTER TYPE "public"."plan_feature_type_enum" RENAME TO "plan_feature_type_enum_old"`);
    //await queryRunner.query(`CREATE TYPE "public"."plan_feature_type_enum" AS ENUM('hec_user_diary', 'hec_play', 'english_qa_writing', 'english_essay', 'english_novel')`);
    //await queryRunner.query(`ALTER TABLE "plan_feature" ALTER COLUMN "type" TYPE "public"."plan_feature_type_enum" USING "type"::"text"::"public"."plan_feature_type_enum"`);
    //await queryRunner.query(`DROP TYPE "public"."plan_feature_type_enum_old"`);
    await queryRunner.query(`ALTER TABLE "qa_subscription" ADD CONSTRAINT "FK_9491aafd46c7008202b13db1f6f" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(`ALTER TABLE "qa_subscription" ADD CONSTRAINT "FK_fe22a4ef6cc1bc0e7863a700c6d" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(
      `ALTER TABLE "qa_submission" ADD CONSTRAINT "FK_064b0390e4c62a2d77970790700" FOREIGN KEY ("assignment_id") REFERENCES "qa_assignment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "qa_submission" ADD CONSTRAINT "FK_71dd55c96e20f90148012dd8714" FOREIGN KEY ("reviewed_by") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(
      `ALTER TABLE "qa_assignment" ADD CONSTRAINT "FK_28d145b5d6658507977ee621a9d" FOREIGN KEY ("question_id") REFERENCES "qa_question"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "qa_assignment" ADD CONSTRAINT "FK_7f6a5ac2305799686bf435eb50b" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(`ALTER TABLE "qa_assignment" ADD CONSTRAINT "FK_c595583aee2e12fb71f3e0776c2" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_assignment" DROP CONSTRAINT "FK_c595583aee2e12fb71f3e0776c2"`);
    await queryRunner.query(`ALTER TABLE "qa_assignment" DROP CONSTRAINT "FK_7f6a5ac2305799686bf435eb50b"`);
    await queryRunner.query(`ALTER TABLE "qa_assignment" DROP CONSTRAINT "FK_28d145b5d6658507977ee621a9d"`);
    await queryRunner.query(`ALTER TABLE "qa_submission" DROP CONSTRAINT "FK_71dd55c96e20f90148012dd8714"`);
    await queryRunner.query(`ALTER TABLE "qa_submission" DROP CONSTRAINT "FK_064b0390e4c62a2d77970790700"`);
    await queryRunner.query(`ALTER TABLE "qa_subscription" DROP CONSTRAINT "FK_fe22a4ef6cc1bc0e7863a700c6d"`);
    await queryRunner.query(`ALTER TABLE "qa_subscription" DROP CONSTRAINT "FK_9491aafd46c7008202b13db1f6f"`);
    //await queryRunner.query(`CREATE TYPE "public"."plan_feature_type_enum_old" AS ENUM('hec_user_diary', 'hec_play', 'english_qa_writing', 'english_essay', 'english_novel', 'module')`);
    //await queryRunner.query(`ALTER TABLE "plan_feature" ALTER COLUMN "type" TYPE "public"."plan_feature_type_enum_old" USING "type"::"text"::"public"."plan_feature_type_enum_old"`);
    //await queryRunner.query(`DROP TYPE "public"."plan_feature_type_enum"`);
    //await queryRunner.query(`ALTER TYPE "public"."plan_feature_type_enum_old" RENAME TO "plan_feature_type_enum"`);
    await queryRunner.query(`DROP TABLE "qa_assignment"`);
    await queryRunner.query(`DROP TYPE "public"."qa_assignment_status_enum"`);
    await queryRunner.query(`DROP TABLE "qa_submission"`);
    await queryRunner.query(`DROP TYPE "public"."qa_submission_status_enum"`);
    await queryRunner.query(`DROP TABLE "qa_question"`);
    await queryRunner.query(`DROP TABLE "qa_subscription"`);
    await queryRunner.query(`DROP TYPE "public"."qa_subscription_status_enum"`);
  }
}
