import { MigrationInterface, QueryRunner } from 'typeorm';

export class StoryMakerAiEvaluation1750576722496 implements MigrationInterface {
  name = 'StoryMakerAiEvaluation1750576722496';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Update StoryMakerSubmission entity for draft/submit workflow
    await queryRunner.query(`
      ALTER TABLE "story_maker_submission" 
      ALTER COLUMN "submitted_at" DROP NOT NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_submission" 
      ADD COLUMN "status" character varying NOT NULL DEFAULT 'DRAFT'
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_submission" 
      ADD COLUMN "last_auto_saved_at" TIMESTAMP
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_submission" 
      ADD COLUMN "auto_save_count" integer NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_submission" 
      ADD COLUMN "word_count_draft" integer NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_submission" 
      ADD COLUMN "character_count_draft" integer NOT NULL DEFAULT 0
    `);

    // Update StoryMakerEvaluation entity for AI evaluation
    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      DROP COLUMN "tutor_id"
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      DROP COLUMN "corrections"
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      DROP COLUMN "feedback"
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "ai_feedback" text
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "sentence_count" integer NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "sentence_score" integer NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "creativity_score" integer NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "sentence_power_score" integer NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "participation_score" integer NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "accuracy_score" integer NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "popularity_score" integer NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "total_score" integer NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "word_count" integer NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "grammar_error_count" integer NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ADD COLUMN "ai_evaluation_data" jsonb
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_evaluation" 
      ALTER COLUMN "evaluated_at" SET NOT NULL
    `);

    // Create StoryMakerLike table
    await queryRunner.query(`
      CREATE TABLE "story_maker_like" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "submission_id" uuid NOT NULL,
        "liker_id" uuid NOT NULL,
        "liker_type" character varying NOT NULL,
        CONSTRAINT "PK_story_maker_like" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_story_maker_like_submission_liker" UNIQUE ("submission_id", "liker_id")
      )
    `);

    // Create StoryMakerShare table
    await queryRunner.query(`
      CREATE TABLE "story_maker_share" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "submission_id" uuid NOT NULL,
        "sharer_id" uuid NOT NULL,
        "shared_with_id" uuid NOT NULL,
        "share_message" character varying,
        "is_viewed" boolean NOT NULL DEFAULT false,
        "viewed_at" TIMESTAMP,
        CONSTRAINT "PK_story_maker_share" PRIMARY KEY ("id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "story_maker_like" 
      ADD CONSTRAINT "FK_story_maker_like_submission" 
      FOREIGN KEY ("submission_id") REFERENCES "story_maker_submission"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_like" 
      ADD CONSTRAINT "FK_story_maker_like_liker" 
      FOREIGN KEY ("liker_id") REFERENCES "user"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_share" 
      ADD CONSTRAINT "FK_story_maker_share_submission" 
      FOREIGN KEY ("submission_id") REFERENCES "story_maker_submission"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_share" 
      ADD CONSTRAINT "FK_story_maker_share_sharer" 
      FOREIGN KEY ("sharer_id") REFERENCES "user"("id") ON DELETE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "story_maker_share" 
      ADD CONSTRAINT "FK_story_maker_share_shared_with" 
      FOREIGN KEY ("shared_with_id") REFERENCES "user"("id") ON DELETE CASCADE
    `);

    // Create indexes for performance
    await queryRunner.query(`
      CREATE INDEX "IDX_story_maker_like_submission" ON "story_maker_like" ("submission_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_story_maker_like_liker" ON "story_maker_like" ("liker_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_story_maker_share_submission" ON "story_maker_share" ("submission_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_story_maker_share_sharer" ON "story_maker_share" ("sharer_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_story_maker_submission_status" ON "story_maker_submission" ("status")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_story_maker_evaluation_total_score" ON "story_maker_evaluation" ("total_score")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_story_maker_evaluation_total_score"`);
    await queryRunner.query(`DROP INDEX "IDX_story_maker_submission_status"`);
    await queryRunner.query(`DROP INDEX "IDX_story_maker_share_sharer"`);
    await queryRunner.query(`DROP INDEX "IDX_story_maker_share_submission"`);
    await queryRunner.query(`DROP INDEX "IDX_story_maker_like_liker"`);
    await queryRunner.query(`DROP INDEX "IDX_story_maker_like_submission"`);

    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "story_maker_share" DROP CONSTRAINT "FK_story_maker_share_shared_with"`);
    await queryRunner.query(`ALTER TABLE "story_maker_share" DROP CONSTRAINT "FK_story_maker_share_sharer"`);
    await queryRunner.query(`ALTER TABLE "story_maker_share" DROP CONSTRAINT "FK_story_maker_share_submission"`);
    await queryRunner.query(`ALTER TABLE "story_maker_like" DROP CONSTRAINT "FK_story_maker_like_liker"`);
    await queryRunner.query(`ALTER TABLE "story_maker_like" DROP CONSTRAINT "FK_story_maker_like_submission"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "story_maker_share"`);
    await queryRunner.query(`DROP TABLE "story_maker_like"`);

    // Revert StoryMakerEvaluation changes
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP COLUMN "ai_evaluation_data"`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP COLUMN "grammar_error_count"`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP COLUMN "word_count"`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP COLUMN "total_score"`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP COLUMN "popularity_score"`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP COLUMN "accuracy_score"`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP COLUMN "participation_score"`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP COLUMN "sentence_power_score"`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP COLUMN "creativity_score"`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP COLUMN "sentence_score"`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP COLUMN "sentence_count"`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP COLUMN "ai_feedback"`);

    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" ADD COLUMN "feedback" text`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" ADD COLUMN "corrections" text`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" ADD COLUMN "tutor_id" uuid NOT NULL`);
    await queryRunner.query(`ALTER TABLE "story_maker_evaluation" ALTER COLUMN "evaluated_at" DROP NOT NULL`);

    // Revert StoryMakerSubmission changes
    await queryRunner.query(`ALTER TABLE "story_maker_submission" DROP COLUMN "character_count_draft"`);
    await queryRunner.query(`ALTER TABLE "story_maker_submission" DROP COLUMN "word_count_draft"`);
    await queryRunner.query(`ALTER TABLE "story_maker_submission" DROP COLUMN "auto_save_count"`);
    await queryRunner.query(`ALTER TABLE "story_maker_submission" DROP COLUMN "last_auto_saved_at"`);
    await queryRunner.query(`ALTER TABLE "story_maker_submission" DROP COLUMN "status"`);
    await queryRunner.query(`ALTER TABLE "story_maker_submission" ALTER COLUMN "submitted_at" SET NOT NULL`);
  }
}
