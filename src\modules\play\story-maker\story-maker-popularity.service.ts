import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThanOrEqual } from 'typeorm';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { StoryMakerEvaluation } from '../../../database/entities/story-maker-evaluation.entity';
import { StoryMakerSubmission } from '../../../database/entities/story-maker-submission.entity';
import { StoryMakerLikeService } from './story-maker-like.service';
import { StoryMakerScoringService } from './story-maker-scoring.service';

@Injectable()
export class StoryMakerPopularityService {
  private readonly logger = new Logger(StoryMakerPopularityService.name);

  constructor(
    @InjectRepository(StoryMakerEvaluation)
    private readonly evaluationRepository: Repository<StoryMakerEvaluation>,
    @InjectRepository(StoryMakerSubmission)
    private readonly submissionRepository: Repository<StoryMakerSubmission>,
    private readonly storyMakerLikeService: StoryMakerLikeService,
    private readonly storyMakerScoringService: StoryMakerScoringService,
  ) {}

  /**
   * Calculate popularity score based on 24-hour like count
   * Following the requirements:
   * - 5 points: 50+ likes in 24h
   * - 4 points: 40+ likes in 24h
   * - 3 points: 30+ likes in 24h
   * - 2 points: 20+ likes in 24h
   * - 1 point: <20 likes in 24h
   *
   * Note: Uses simple like counts (not weighted) as per Story Maker requirements
   *
   * @param submissionId ID of the submission
   * @returns Popularity score (1-5)
   */
  async calculatePopularityScore(submissionId: string): Promise<number> {
    try {
      // Get simple like count within 24 hours (not weighted - Story Maker uses simple counts)
      const likesWithin24h = await this.storyMakerLikeService.getLikesWithin24Hours(submissionId);

      // Calculate score based on thresholds
      if (likesWithin24h >= 50) return 5;
      if (likesWithin24h >= 40) return 4;
      if (likesWithin24h >= 30) return 3;
      if (likesWithin24h >= 20) return 2;
      return 1;

    } catch (error) {
      this.logger.error(`Failed to calculate popularity score for submission ${submissionId}: ${error.message}`);
      return 1; // Default to minimum score
    }
  }

  /**
   * Update popularity score for a specific submission
   * @param submissionId ID of the submission to update
   */
  async updateSubmissionPopularityScore(submissionId: string): Promise<void> {
    try {
      this.logger.log(`Updating popularity score for submission ${submissionId}`);

      // Calculate new popularity score
      const newPopularityScore = await this.calculatePopularityScore(submissionId);

      // Update the evaluation record
      await this.storyMakerScoringService.updatePopularityScore(submissionId, newPopularityScore);

      this.logger.log(`Updated popularity score for submission ${submissionId}: ${newPopularityScore} points`);

    } catch (error) {
      this.logger.error(`Failed to update popularity score for submission ${submissionId}: ${error.message}`, error.stack);
    }
  }

  /**
   * Update popularity scores for all submissions within the 24-hour window
   * This method is called by the scheduled job
   */
  async updateAllPopularityScores(): Promise<void> {
    try {
      this.logger.log('Starting bulk popularity score update...');

      // Get all submissions from the last 25 hours (extra hour buffer for timezone issues)
      const twentyFiveHoursAgo = new Date();
      twentyFiveHoursAgo.setHours(twentyFiveHoursAgo.getHours() - 25);

      const recentSubmissions = await this.submissionRepository.find({
        where: {
          submittedAt: MoreThanOrEqual(twentyFiveHoursAgo),
          status: 'SUBMITTED'
        },
        select: ['id', 'submittedAt']
      });

      this.logger.log(`Found ${recentSubmissions.length} recent submissions to update`);

      // Update popularity scores in batches to avoid overwhelming the database
      const batchSize = 10;
      for (let i = 0; i < recentSubmissions.length; i += batchSize) {
        const batch = recentSubmissions.slice(i, i + batchSize);
        
        await Promise.all(
          batch.map(submission => this.updateSubmissionPopularityScore(submission.id))
        );

        // Small delay between batches to prevent database overload
        if (i + batchSize < recentSubmissions.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      this.logger.log(`Completed bulk popularity score update for ${recentSubmissions.length} submissions`);

    } catch (error) {
      this.logger.error(`Failed to update all popularity scores: ${error.message}`, error.stack);
    }
  }

  /**
   * Scheduled job to update popularity scores every hour
   * This ensures the 24-hour window is properly maintained
   */
  @Cron(CronExpression.EVERY_HOUR)
  async scheduledPopularityUpdate(): Promise<void> {
    this.logger.log('Running scheduled popularity score update...');
    await this.updateAllPopularityScores();
  }

  /**
   * Get popularity statistics for a submission
   * @param submissionId ID of the submission
   * @returns Detailed popularity statistics
   */
  async getPopularityStatistics(submissionId: string): Promise<{
    currentScore: number;
    likesWithin24h: number;
    totalLikes: number;
    studentLikes: number;
    tutorLikes: number;
    nextThreshold: number | null;
    likesNeededForNextLevel: number | null;
  }> {
    try {
      // Get current popularity score from evaluation
      const evaluation = await this.evaluationRepository.findOne({
        where: { submissionId }
      });

      const currentScore = evaluation?.popularityScore || 1;

      // Get detailed like information
      const likeDetails = await this.storyMakerLikeService.getLikeDetails(submissionId);

      // Calculate next threshold and likes needed (using simple counts, not weighted)
      const thresholds = [20, 30, 40, 50];
      let nextThreshold: number | null = null;
      let likesNeededForNextLevel: number | null = null;

      for (const threshold of thresholds) {
        if (likeDetails.likesWithin24h < threshold) {
          nextThreshold = threshold;
          likesNeededForNextLevel = threshold - likeDetails.likesWithin24h;
          break;
        }
      }

      return {
        currentScore,
        likesWithin24h: likeDetails.likesWithin24h,
        totalLikes: likeDetails.totalLikes,
        studentLikes: likeDetails.studentLikes,
        tutorLikes: likeDetails.tutorLikes,
        nextThreshold,
        likesNeededForNextLevel,
      };

    } catch (error) {
      this.logger.error(`Failed to get popularity statistics: ${error.message}`);
      return {
        currentScore: 1,
        likesWithin24h: 0,
        totalLikes: 0,
        studentLikes: 0,
        tutorLikes: 0,
        nextThreshold: 20,
        likesNeededForNextLevel: 20,
      };
    }
  }

  /**
   * Get top popular submissions within a time period
   * @param hours Number of hours to look back (default: 24)
   * @param limit Maximum number of submissions to return (default: 10)
   * @returns Array of popular submissions with their scores
   */
  async getTopPopularSubmissions(hours: number = 24, limit: number = 10): Promise<Array<{
    submissionId: string;
    popularityScore: number;
    likesWithin24h: number;
    totalLikes: number;
    submittedAt: Date;
  }>> {
    try {
      const hoursAgo = new Date();
      hoursAgo.setHours(hoursAgo.getHours() - hours);

      // Get recent submissions with evaluations
      const submissions = await this.submissionRepository.find({
        where: {
          submittedAt: MoreThanOrEqual(hoursAgo),
          status: 'SUBMITTED'
        },
        relations: ['evaluations'],
        order: { submittedAt: 'DESC' }
      });

      // Calculate popularity scores and sort
      const submissionsWithScores = await Promise.all(
        submissions.map(async (submission) => {
          const likeDetails = await this.storyMakerLikeService.getLikeDetails(submission.id);
          const popularityScore = await this.calculatePopularityScore(submission.id);

          return {
            submissionId: submission.id,
            popularityScore,
            likesWithin24h: likeDetails.likesWithin24h,
            totalLikes: likeDetails.totalLikes,
            submittedAt: submission.submittedAt,
          };
        })
      );

      // Sort by popularity score (descending) then by total likes (descending)
      submissionsWithScores.sort((a, b) => {
        if (a.popularityScore !== b.popularityScore) {
          return b.popularityScore - a.popularityScore;
        }
        return b.totalLikes - a.totalLikes;
      });

      return submissionsWithScores.slice(0, limit);

    } catch (error) {
      this.logger.error(`Failed to get top popular submissions: ${error.message}`);
      return [];
    }
  }

  /**
   * Force update popularity score for a submission (useful for testing or manual updates)
   * @param submissionId ID of the submission
   * @returns Updated popularity score
   */
  async forceUpdatePopularityScore(submissionId: string): Promise<number> {
    await this.updateSubmissionPopularityScore(submissionId);
    return this.calculatePopularityScore(submissionId);
  }
}
