import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNovelVersionTracking1747000000000 implements MigrationInterface {
  name = 'AddNovelVersionTracking1747000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create novel_entry_history table
    await queryRunner.query(`
      CREATE TABLE "novel_entry_history" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "created_by" character varying(36),
        "updated_by" character varying(36),
        "novel_entry_id" uuid NOT NULL,
        "content" text NOT NULL,
        "version_number" integer NOT NULL,
        "is_latest" boolean NOT NULL DEFAULT false,
        "word_count" integer NOT NULL,
        "meta_data" json,
        CONSTRAINT "PK_novel_entry_history" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for novel_entry_history
    await queryRunner.query(`
      CREATE INDEX "IDX_novel_entry_history_novel_entry_id_version_number" 
      ON "novel_entry_history" ("novel_entry_id", "version_number")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_novel_entry_history_novel_entry_id_is_latest" 
      ON "novel_entry_history" ("novel_entry_id", "is_latest")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_novel_entry_history_created_at" 
      ON "novel_entry_history" ("created_at")
    `);

    // Add foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "novel_entry_history" 
      ADD CONSTRAINT "FK_novel_entry_history_novel_entry_id" 
      FOREIGN KEY ("novel_entry_id") REFERENCES "novel_entry"("id") ON DELETE CASCADE
    `);

    // Add version tracking fields to novel_entry table
    await queryRunner.query(`
      ALTER TABLE "novel_entry" 
      ADD "current_version_id" uuid
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_entry" 
      ADD "total_versions" integer NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_entry" 
      ADD "first_submission_notified" boolean NOT NULL DEFAULT false
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_entry" 
      ADD "original_reviewed_version_id" uuid
    `);

    // Add foreign key constraints for novel_entry
    await queryRunner.query(`
      ALTER TABLE "novel_entry" 
      ADD CONSTRAINT "FK_novel_entry_current_version_id" 
      FOREIGN KEY ("current_version_id") REFERENCES "novel_entry_history"("id") ON DELETE SET NULL
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_entry" 
      ADD CONSTRAINT "FK_novel_entry_original_reviewed_version_id" 
      FOREIGN KEY ("original_reviewed_version_id") REFERENCES "novel_entry_history"("id") ON DELETE SET NULL
    `);

    // Add word limit fields to novel_topic table
    await queryRunner.query(`
      ALTER TABLE "novel_topic" 
      ADD "min_word_count" integer
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_topic" 
      ADD "max_word_count" integer
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove word limit fields from novel_topic table
    await queryRunner.query(`ALTER TABLE "novel_topic" DROP COLUMN "max_word_count"`);
    await queryRunner.query(`ALTER TABLE "novel_topic" DROP COLUMN "min_word_count"`);

    // Remove foreign key constraints from novel_entry
    await queryRunner.query(`ALTER TABLE "novel_entry" DROP CONSTRAINT "FK_novel_entry_original_reviewed_version_id"`);
    await queryRunner.query(`ALTER TABLE "novel_entry" DROP CONSTRAINT "FK_novel_entry_current_version_id"`);

    // Remove version tracking fields from novel_entry table
    await queryRunner.query(`ALTER TABLE "novel_entry" DROP COLUMN "original_reviewed_version_id"`);
    await queryRunner.query(`ALTER TABLE "novel_entry" DROP COLUMN "first_submission_notified"`);
    await queryRunner.query(`ALTER TABLE "novel_entry" DROP COLUMN "total_versions"`);
    await queryRunner.query(`ALTER TABLE "novel_entry" DROP COLUMN "current_version_id"`);

    // Remove foreign key constraint from novel_entry_history
    await queryRunner.query(`ALTER TABLE "novel_entry_history" DROP CONSTRAINT "FK_novel_entry_history_novel_entry_id"`);

    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_novel_entry_history_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_novel_entry_history_novel_entry_id_is_latest"`);
    await queryRunner.query(`DROP INDEX "IDX_novel_entry_history_novel_entry_id_version_number"`);

    // Drop novel_entry_history table
    await queryRunner.query(`DROP TABLE "novel_entry_history"`);
  }
}
