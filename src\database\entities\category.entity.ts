import { Entity, Column, OneToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { DiaryMission } from './diary-mission.entity';

@Entity('category')
export class Category extends AuditableBaseEntity {
  @Column({ name: 'name', type: 'varchar', length: 255, unique: true })
  name: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  @Column({ name: 'color', type: 'varchar', length: 7, nullable: true })
  color: string; // Hex color code for UI display

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ name: 'sort_order', type: 'integer', default: 0 })
  sortOrder: number;

  @OneToMany(() => DiaryMission, (mission) => mission.category)
  diaryMissions: DiaryMission[];
}
