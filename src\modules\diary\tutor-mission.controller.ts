import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Req, BadRequestException } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { TutorGuard } from '../../common/guards/tutor.guard';
import { MissionDiaryEntryService } from './mission-diary-entry.service';

import {
  MissionDiaryEntryResponseDto,
  MissionEntryFilterDto,
  AddMissionFeedbackDto,
  AddMissionCorrectionDto,
  AssignMissionScoreDto,
  AddMissionCorrectionWithScoreDto,
  MissionFeedbackResponseDto,
  MissionDiaryEntryHistoryResponseDto,
  MissionDiaryEntryVersionDto,
} from '../../database/models/mission-diary-entry.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiOkResponseWithArrayType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { PaginationDto } from '../../common/models/pagination.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';

@ApiTags('Tutor Diary Missions Management')
@Controller('diary/tutor/missions')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TutorGuard)
export class TutorMissionController {
  constructor(private readonly missionDiaryEntryService: MissionDiaryEntryService) {}

  // ===== Mission Entry Management Endpoints =====

  @Get('/entries')
  @ApiOperation({ summary: 'Get mission diary entries for the tutor' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'missionId', required: false, type: String })
  @ApiQuery({ name: 'studentId', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'createdAtFrom', required: false, type: String })
  @ApiQuery({ name: 'createdAtTo', required: false, type: String })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortDirection', required: false, enum: ['ASC', 'DESC'] })
  @ApiOkResponseWithPagedListType(MissionDiaryEntryResponseDto, 'Entries retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  async getTutorMissionEntries(@Req() req: any, @Query() params: any): Promise<ApiResponse<PagedListDto<MissionDiaryEntryResponseDto>>> {
    // Extract pagination parameters
    const paginationDto: PaginationDto = {
      page: params.page ? parseInt(params.page) : 1,
      limit: params.limit ? parseInt(params.limit) : 10,
      sortBy: params.sortBy,
      sortDirection: params.sortDirection,
    };

    // Extract filter parameters
    const filterDto: MissionEntryFilterDto = {
      missionId: params.missionId,
      studentId: params.studentId,
      status: params.status,
      createdAtFrom: params.createdAtFrom,
      createdAtTo: params.createdAtTo,
    };

    const entries = await this.missionDiaryEntryService.getTutorMissionEntries(req.user.id, filterDto, paginationDto);
    return ApiResponse.success(entries, 'Entries retrieved successfully');
  }

  @Get('/entries/:id')
  @ApiOperation({ summary: 'Get a specific mission diary entry' })
  @ApiParam({ name: 'id', description: 'Entry ID' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Entry retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Entry not found')
  async getMissionEntry(@Param('id') id: string): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.getMissionEntry(id);
    return ApiResponse.success(entry, 'Entry retrieved successfully');
  }

  @Post('/entries/:id/feedback')
  @ApiOperation({ summary: 'Add feedback to a mission diary entry' })
  @ApiParam({ name: 'id', description: 'Entry ID' })
  @ApiOkResponseWithType(MissionFeedbackResponseDto, 'Feedback added successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Entry not found')
  async addFeedback(@Req() req: any, @Param('id') id: string, @Body() feedbackDto: AddMissionFeedbackDto): Promise<ApiResponse<MissionFeedbackResponseDto>> {
    if (!id || id === 'undefined' || id.trim() === '') {
      throw new BadRequestException('Entry ID is required and cannot be undefined');
    }
    const feedback = await this.missionDiaryEntryService.addFeedback(id, req.user.id, feedbackDto);
    return ApiResponse.success(feedback, 'Feedback added successfully');
  }

  @Get('/entries/:id/feedbacks')
  @ApiOperation({
    summary: 'Get all feedbacks for a mission diary entry',
    description: 'Retrieve all feedback comments for a specific mission diary entry. Accessible to tutors who have access to the student.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the mission diary entry to get feedbacks for',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithArrayType(MissionFeedbackResponseDto, 'Feedbacks retrieved successfully')
  @ApiErrorResponse(400, 'Invalid entry ID')
  @ApiErrorResponse(404, 'Mission diary entry not found')
  @ApiErrorResponse(403, 'Forbidden - You do not have permission to view feedbacks for this entry')
  @ApiErrorResponse(500, 'Internal server error')
  async getFeedbacks(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<MissionFeedbackResponseDto[]>> {
    if (!id || id === 'undefined' || id.trim() === '') {
      throw new BadRequestException('Entry ID is required and cannot be undefined');
    }
    const feedbacks = await this.missionDiaryEntryService.getFeedbacks(id, req.user.id);
    return ApiResponse.success(feedbacks, 'Feedbacks retrieved successfully');
  }

  @Post('/entries/:id/correction')
  @ApiOperation({
    summary: 'Add correction with score to a mission diary entry',
    description: 'Provide both correction text and score for a mission diary entry in a single operation. Both fields are required.',
  })
  @ApiParam({ name: 'id', description: 'Entry ID' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Correction and score added successfully')
  @ApiErrorResponse(400, 'Bad request - Both correction and score are required')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Entry not found')
  async addCorrectionWithScore(@Req() req: any, @Param('id') id: string, @Body() correctionWithScoreDto: AddMissionCorrectionWithScoreDto): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.addCorrectionWithScore(id, req.user.id, correctionWithScoreDto);
    return ApiResponse.success(entry, 'Correction and score added successfully');
  }

  // Deprecated endpoints - kept for backward compatibility
  @Post('/entries/:id/correction-only')
  @ApiOperation({
    summary: 'Add correction to a mission diary entry (DEPRECATED)',
    deprecated: true,
    description: 'This endpoint is deprecated. Use POST /entries/:id/correction instead which requires both correction and score.',
  })
  @ApiParam({ name: 'id', description: 'Entry ID' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Correction added successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Entry not found')
  async addCorrection(@Req() req: any, @Param('id') id: string, @Body() correctionDto: AddMissionCorrectionDto): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.addCorrection(id, req.user.id, correctionDto);
    return ApiResponse.success(entry, 'Correction added successfully');
  }

  @Post('/entries/:id/score')
  @ApiOperation({
    summary: 'Assign a score to a mission diary entry (DEPRECATED)',
    deprecated: true,
    description: 'This endpoint is deprecated. Use POST /entries/:id/correction instead which requires both correction and score.',
  })
  @ApiParam({ name: 'id', description: 'Entry ID' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Score assigned successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Entry not found')
  async assignScore(@Req() req: any, @Param('id') id: string, @Body() scoreDto: AssignMissionScoreDto): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.assignScore(id, req.user.id, scoreDto);
    return ApiResponse.success(entry, 'Score assigned successfully');
  }

  // REMOVED: Confirm endpoint - confirm stage removed from lifecycle
  // Review is now the final state, no confirmation needed

  @Get('/entries/:id/history')
  @ApiOperation({
    summary: 'Get mission diary entry version history (Tutor)',
    description: "View all previous versions of a student's mission diary entry. Shows the complete history of changes made to the entry.",
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the mission diary entry',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(MissionDiaryEntryHistoryResponseDto, 'Version history retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Mission diary entry not found')
  @ApiErrorResponse(403, "Forbidden - You do not have access to this student's entry")
  @ApiErrorResponse(500, 'Internal server error')
  async getMissionEntryHistory(@Req() req: any, @Param('id') id: string): Promise<ApiResponse<MissionDiaryEntryHistoryResponseDto>> {
    const tutorId = req.user.id;

    // Get the entry to verify access and get student ID
    const entry = await this.missionDiaryEntryService.getMissionEntry(id);

    // Get the history using the student ID
    const history = await this.missionDiaryEntryService.getMissionEntryHistory(id, entry.studentId);
    return ApiResponse.success(history, 'Version history retrieved successfully');
  }

  @Get('/entries/:id/versions/:versionId')
  @ApiOperation({
    summary: 'View a specific version (Tutor)',
    description: "View the content of a specific version of a student's mission diary entry.",
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the mission diary entry',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiParam({
    name: 'versionId',
    description: 'The ID of the specific version',
    example: '456e7890-e89b-12d3-a456-426614174000',
  })
  @ApiOkResponseWithType(MissionDiaryEntryVersionDto, 'Version retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Version not found')
  @ApiErrorResponse(403, "Forbidden - You do not have access to this student's entry")
  @ApiErrorResponse(500, 'Internal server error')
  async getMissionEntryVersion(@Req() req: any, @Param('id') id: string, @Param('versionId') versionId: string): Promise<ApiResponse<MissionDiaryEntryVersionDto>> {
    const tutorId = req.user.id;

    // Get the entry to verify access and get student ID
    const entry = await this.missionDiaryEntryService.getMissionEntry(id);

    // Get the version using the student ID
    const version = await this.missionDiaryEntryService.getMissionEntryVersion(versionId, entry.studentId);
    return ApiResponse.success(version, 'Version retrieved successfully');
  }
}
