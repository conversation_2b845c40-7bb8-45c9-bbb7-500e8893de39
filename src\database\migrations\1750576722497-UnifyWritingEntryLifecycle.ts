import { MigrationInterface, QueryRunner } from 'typeorm';

export class UnifyWritingEntryLifecycle1750576722497 implements MigrationInterface {
  name = 'UnifyWritingEntryLifecycle1750576722497';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('Starting unified writing entry lifecycle migration...');

    // Add unified status field to all writing entry tables
    console.log('Adding unified_status field to diary_entry table...');
    await queryRunner.query(`
      ALTER TABLE "diary_entry"
      ADD COLUMN "unified_status" varchar DEFAULT 'draft'
    `);

    console.log('Adding unified_status field to mission_diary_entry table...');
    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry"
      ADD COLUMN "unified_status" varchar DEFAULT 'draft'
    `);

    console.log('Adding unified_status field to novel_entry table...');
    await queryRunner.query(`
      ALTER TABLE "novel_entry"
      ADD COLUMN "unified_status" varchar DEFAULT 'draft'
    `);

    // Add missing fields for unified interface compatibility
    console.log('Adding missing fields to diary_entry table...');
    await queryRunner.query(`
      ALTER TABLE "diary_entry"
      ADD COLUMN IF NOT EXISTS "word_count" integer DEFAULT 0,
      ADD COLUMN IF NOT EXISTS "submitted_at" timestamp,
      ADD COLUMN IF NOT EXISTS "reviewed_at" timestamp,
      ADD COLUMN IF NOT EXISTS "reviewed_by" varchar,
      ADD COLUMN IF NOT EXISTS "gained_score" integer
    `);

    console.log('Adding missing fields to mission_diary_entry table...');
    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry"
      ADD COLUMN IF NOT EXISTS "submitted_at" timestamp,
      ADD COLUMN IF NOT EXISTS "reviewed_at" timestamp,
      ADD COLUMN IF NOT EXISTS "evaluated_at" timestamp,
      ADD COLUMN IF NOT EXISTS "evaluated_by" varchar
    `);

    console.log('Adding missing fields to novel_entry table...');
    await queryRunner.query(`
      ALTER TABLE "novel_entry"
      ADD COLUMN IF NOT EXISTS "reviewed_by" varchar,
      ADD COLUMN IF NOT EXISTS "evaluated_at" timestamp,
      ADD COLUMN IF NOT EXISTS "evaluated_by" varchar,
      ADD COLUMN IF NOT EXISTS "gained_score" integer,
      ADD COLUMN IF NOT EXISTS "score" integer
    `);

    // Migrate existing status values to unified status
    console.log('Migrating diary entry statuses...');
    await this.migrateDiaryEntryStatuses(queryRunner);

    console.log('Migrating mission diary entry statuses...');
    await this.migrateMissionDiaryEntryStatuses(queryRunner);

    console.log('Migrating novel entry statuses...');
    await this.migrateNovelEntryStatuses(queryRunner);

    // Add check constraints for unified status
    console.log('Adding check constraints for unified status...');
    await queryRunner.query(`
      ALTER TABLE "diary_entry"
      ADD CONSTRAINT "diary_entry_unified_status_check"
      CHECK ("unified_status" IN ('draft', 'submitted', 'reviewed', 'confirmed'))
    `);

    await queryRunner.query(`
      ALTER TABLE "mission_diary_entry"
      ADD CONSTRAINT "mission_diary_entry_unified_status_check"
      CHECK ("unified_status" IN ('draft', 'submitted', 'reviewed', 'confirmed'))
    `);

    await queryRunner.query(`
      ALTER TABLE "novel_entry"
      ADD CONSTRAINT "novel_entry_unified_status_check"
      CHECK ("unified_status" IN ('draft', 'submitted', 'reviewed', 'confirmed'))
    `);

    console.log('Unified writing entry lifecycle migration completed successfully');
  }

  private async migrateDiaryEntryStatuses(queryRunner: QueryRunner): Promise<void> {
    // Map diary entry statuses to unified statuses
    const statusMappings = [
      { old: 'new', new: 'draft' },
      { old: 'submit', new: 'submitted' },
      { old: 'reviewed', new: 'reviewed' },
      { old: 'confirm', new: 'confirmed' },
    ];

    for (const mapping of statusMappings) {
      await queryRunner.query(
        `
        UPDATE "diary_entry"
        SET "unified_status" = $1
        WHERE "status" = $2
      `,
        [mapping.new, mapping.old],
      );
    }

    // Update canSubmitNewVersion based on unified status
    await queryRunner.query(`
      UPDATE "diary_entry"
      SET "can_submit_new_version" = CASE
        WHEN "unified_status" IN ('draft', 'reviewed', 'confirmed') THEN true
        ELSE false
      END
    `);
  }

  private async migrateMissionDiaryEntryStatuses(queryRunner: QueryRunner): Promise<void> {
    // Map mission diary entry statuses to unified statuses
    const statusMappings = [
      { old: 'NEW', new: 'draft' },
      { old: 'new', new: 'draft' },
      { old: 'SUBMITTED', new: 'submitted' },
      { old: 'submit', new: 'submitted' },
      { old: 'REVIEWED', new: 'reviewed' },
      { old: 'reviewed', new: 'reviewed' },
      { old: 'CONFIRMED', new: 'confirmed' },
      { old: 'confirm', new: 'confirmed' },
    ];

    for (const mapping of statusMappings) {
      await queryRunner.query(
        `
        UPDATE "mission_diary_entry"
        SET "unified_status" = $1
        WHERE "status" = $2
      `,
        [mapping.new, mapping.old],
      );
    }

    // Update canSubmitNewVersion based on unified status
    await queryRunner.query(`
      UPDATE "mission_diary_entry"
      SET "can_submit_new_version" = CASE
        WHEN "unified_status" IN ('draft', 'reviewed', 'confirmed') THEN true
        ELSE false
      END
    `);
  }

  private async migrateNovelEntryStatuses(queryRunner: QueryRunner): Promise<void> {
    // Map novel entry statuses to unified statuses
    const statusMappings = [
      { old: 'new', new: 'draft' },
      { old: 'submitted', new: 'submitted' },
      { old: 'updated', new: 'reviewed' },
      { old: 'correction_given', new: 'reviewed' },
      { old: 'reviewed', new: 'reviewed' },
      { old: 'under_review', new: 'submitted' },
      { old: 'confirmed', new: 'confirmed' },
    ];

    for (const mapping of statusMappings) {
      await queryRunner.query(
        `
        UPDATE "novel_entry"
        SET "unified_status" = $1
        WHERE "status" = $2
      `,
        [mapping.new, mapping.old],
      );
    }

    // Update canSubmitNewVersion based on unified status
    await queryRunner.query(`
      UPDATE "novel_entry"
      SET "can_submit_new_version" = CASE
        WHEN "unified_status" IN ('draft', 'reviewed', 'confirmed') THEN true
        ELSE false
      END
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('Rolling back unified writing entry lifecycle migration...');

    // Remove check constraints
    await queryRunner.query(`ALTER TABLE "novel_entry" DROP CONSTRAINT "novel_entry_unified_status_check"`);
    await queryRunner.query(`ALTER TABLE "mission_diary_entry" DROP CONSTRAINT "mission_diary_entry_unified_status_check"`);
    await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT "diary_entry_unified_status_check"`);

    // Remove unified_status columns
    await queryRunner.query(`ALTER TABLE "novel_entry" DROP COLUMN "unified_status"`);
    await queryRunner.query(`ALTER TABLE "mission_diary_entry" DROP COLUMN "unified_status"`);
    await queryRunner.query(`ALTER TABLE "diary_entry" DROP COLUMN "unified_status"`);

    console.log('Unified writing entry lifecycle migration rollback completed');
  }
}
