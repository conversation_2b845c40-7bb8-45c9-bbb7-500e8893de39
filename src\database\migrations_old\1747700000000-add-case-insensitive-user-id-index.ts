import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCaseInsensitiveUserIdIndex1747700000000 implements MigrationInterface {
  public name = 'AddCaseInsensitiveUserIdIndex1747700000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the existing unique constraint on user_id
    await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT IF EXISTS "UQ_758b8ce7c18b9d347461b30228d"`);

    // Find duplicates first
    const duplicates = await queryRunner.query(`
            SELECT LOWER(user_id) as lower_user_id, COUNT(*), array_agg(id) as ids, array_agg(user_id) as user_ids
            FROM "user"
            GROUP BY LOWER(user_id)
            HAVING COUNT(*) > 1
        `);

    // For each set of duplicates, update all but one to have a unique user_id
    for (const dup of duplicates) {
      // Skip the first one (keep it as is)
      const [keepId, ...updateIds] = dup.ids;
      for (let i = 0; i < updateIds.length; i++) {
        const id = updateIds[i];
        const oldUserId = dup.user_ids[i + 1];
        const newUserId = oldUserId + '_' + (i + 1);

        await queryRunner.query(
          `
                    UPDATE "user"
                    SET user_id = $1
                    WHERE id = $2
                `,
          [newUserId, id],
        );
      }
    }

    // Now we can safely create the unique case-insensitive index
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_user_id_lower" ON "user" (LOWER(user_id))`);

    // Add a trigger to enforce uniqueness on insert/update
    await queryRunner.query(`
            CREATE OR REPLACE FUNCTION enforce_user_id_case_insensitive_uniqueness()
            RETURNS TRIGGER AS $$
            BEGIN
                IF EXISTS (
                    SELECT 1
                    FROM "user"
                    WHERE LOWER(user_id) = LOWER(NEW.user_id)
                    AND id != NEW.id
                ) THEN
                    RAISE EXCEPTION 'User ID already exists (case-insensitive)';
                END IF;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;

            DROP TRIGGER IF EXISTS tr_user_id_case_insensitive_uniqueness ON "user";
            
            CREATE TRIGGER tr_user_id_case_insensitive_uniqueness
            BEFORE INSERT OR UPDATE ON "user"
            FOR EACH ROW
            EXECUTE FUNCTION enforce_user_id_case_insensitive_uniqueness();
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the trigger and function
    await queryRunner.query(`
            DROP TRIGGER IF EXISTS tr_user_id_case_insensitive_uniqueness ON "user";
            DROP FUNCTION IF EXISTS enforce_user_id_case_insensitive_uniqueness;
        `);

    // Drop the case-insensitive index
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_user_id_lower"`);

    // Restore the original unique constraint
    await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "UQ_758b8ce7c18b9d347461b30228d" UNIQUE ("user_id")`);
  }
}
