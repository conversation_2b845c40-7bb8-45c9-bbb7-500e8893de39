import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePaymentMethodEnumsSimple1703123456792 implements MigrationInterface {
  name = 'UpdatePaymentMethodEnumsSimple1703123456792';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Delete all existing enum values and insert new ones

    // 1. Recreate payment_transaction_payment_method_enum with correct KCP values
    await queryRunner.query(`DROP TYPE IF EXISTS "payment_transaction_payment_method_enum" CASCADE`).catch(() => {});
    await queryRunner.query(`CREATE TYPE "payment_transaction_payment_method_enum" AS ENUM('CARD', 'BANK', 'MOBX', 'VCNT')`).catch(() => {});

    // 2. Recreate shop_item_purchase_payment_method_enum with all values including kcp_virtual_account
    await queryRunner.query(`DROP TYPE IF EXISTS "shop_item_purchase_payment_method_enum" CASCADE`).catch(() => {});
    await queryRunner
      .query(`CREATE TYPE "shop_item_purchase_payment_method_enum" AS ENUM('reward_points', 'credit_card', 'free', 'kcp_card', 'kcp_bank', 'kcp_virtual_account', 'kcp_mobile')`)
      .catch(() => {});
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert to old enum values

    // 1. Recreate payment_transaction_payment_method_enum with old values
    await queryRunner.query(`DROP TYPE IF EXISTS "payment_transaction_payment_method_enum" CASCADE`).catch(() => {});
    await queryRunner.query(`CREATE TYPE "payment_transaction_payment_method_enum" AS ENUM('card', 'bank', 'mobile', 'vacct')`).catch(() => {});

    // 2. Recreate shop_item_purchase_payment_method_enum without kcp_virtual_account
    await queryRunner.query(`DROP TYPE IF EXISTS "shop_item_purchase_payment_method_enum" CASCADE`).catch(() => {});
    await queryRunner.query(`CREATE TYPE "shop_item_purchase_payment_method_enum" AS ENUM('reward_points', 'credit_card', 'free', 'kcp_card', 'kcp_bank', 'kcp_mobile')`).catch(() => {});
  }
}
