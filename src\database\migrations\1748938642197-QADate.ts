import { MigrationInterface, QueryRunner } from 'typeorm';

export class QADate1748938642197 implements MigrationInterface {
  name = 'QADate1748938642197';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_assignment" ALTER COLUMN "deadline" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "qa_assignment" ALTER COLUMN "deadline" SET NOT NULL`);
  }
}
