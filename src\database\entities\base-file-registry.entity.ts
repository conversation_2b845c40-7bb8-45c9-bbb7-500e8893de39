import { Column } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { StorageProvider } from '../../common/enums/storage.enum';

/**
 * Base class for all file registry entities
 * Contains common columns for file storage and S3 support
 */
export abstract class BaseFileRegistry extends AuditableBaseEntity {
  // Basic file information
  @Column({ name: 'file_path' })
  filePath: string;

  @Column({ name: 'file_name' })
  fileName: string;

  @Column({ name: 'mime_type', nullable: true })
  mimeType: string;

  @Column({ name: 'file_size', nullable: true })
  fileSize: number;

  // S3 Storage Support Columns
  @Column({
    name: 'storage_provider',
    type: 'enum',
    enum: StorageProvider,
    default: StorageProvider.LOCAL,
  })
  storageProvider: StorageProvider;

  @Column({ name: 'storage_key', nullable: true, length: 500 })
  storageKey: string;

  @Column({ name: 's3_bucket', nullable: true, length: 100 })
  s3Bucket: string;

  @Column({ name: 's3_region', nullable: true, length: 50 })
  s3Region: string;

  @Column({ name: 's3_etag', nullable: true, length: 100 })
  s3Etag: string;

  @Column({ name: 's3_version_id', nullable: true, length: 100 })
  s3VersionId: string;

  @Column({ name: 's3_storage_class', nullable: true, length: 50 })
  s3StorageClass: string;

  @Column({ name: 's3_server_side_encryption', nullable: true, length: 50 })
  s3ServerSideEncryption: string;

  @Column({ name: 'cdn_url', nullable: true, length: 500 })
  cdnUrl: string;

  @Column({ name: 'presigned_url_expires_at', nullable: true })
  presignedUrlExpiresAt: Date;

  @Column({ name: 'storage_metadata', type: 'jsonb', nullable: true })
  storageMetadata: any;

  // Migration Support Columns (simplified)
  @Column({ name: 'is_migrated', default: false })
  isMigrated: boolean;

  @Column({ name: 'migration_status', nullable: true, length: 20 })
  migrationStatus: string;

  @Column({ name: 'migration_error', type: 'text', nullable: true })
  migrationError: string;

  @Column({ name: 'migrated_at', nullable: true })
  migratedAt: Date;

  /**
   * Get the effective file URL based on storage provider
   */
  getFileUrl(): string {
    if (this.storageProvider === StorageProvider.S3) {
      return this.cdnUrl || this.storageKey;
    }
    return this.filePath;
  }

  /**
   * Get the storage key for the file (S3 key or local path)
   */
  getStorageKey(): string {
    return this.storageProvider === StorageProvider.S3 ? this.storageKey : this.filePath;
  }

  /**
   * Check if the file is stored in S3
   */
  isS3Storage(): boolean {
    return this.storageProvider === StorageProvider.S3;
  }

  /**
   * Check if the file is stored locally
   */
  isLocalStorage(): boolean {
    return this.storageProvider === StorageProvider.LOCAL;
  }

  /**
   * Check if migration is in progress
   */
  isMigrationInProgress(): boolean {
    return this.migrationStatus === 'in_progress' || this.migrationStatus === 'retrying';
  }

  /**
   * Check if migration has failed
   */
  hasMigrationFailed(): boolean {
    return this.migrationStatus === 'failed';
  }

  /**
   * Check if migration is completed
   */
  isMigrationCompleted(): boolean {
    return this.migrationStatus === 'completed' && this.isMigrated;
  }

  /**
   * Check if the file can be migrated
   */
  canBeMigrated(): boolean {
    return !this.isMigrationInProgress() && !this.isMigrationCompleted();
  }

  /**
   * Get S3 metadata as a formatted object
   */
  getS3Metadata(): any {
    if (!this.isS3Storage()) {
      return null;
    }

    return {
      bucket: this.s3Bucket,
      region: this.s3Region,
      key: this.storageKey,
      etag: this.s3Etag,
      versionId: this.s3VersionId,
      storageClass: this.s3StorageClass,
      serverSideEncryption: this.s3ServerSideEncryption,
      cdnUrl: this.cdnUrl,
      metadata: this.storageMetadata,
    };
  }

  /**
   * Update S3 metadata from upload result
   */
  updateS3Metadata(s3Result: any): void {
    this.storageProvider = StorageProvider.S3;
    this.s3Bucket = s3Result.bucket;
    this.s3Region = s3Result.region;
    this.storageKey = s3Result.key;
    this.s3Etag = s3Result.etag;
    this.s3VersionId = s3Result.versionId;
    this.s3StorageClass = s3Result.storageClass;
    this.s3ServerSideEncryption = s3Result.serverSideEncryption;
    this.storageMetadata = s3Result.metadata;
  }

  /**
   * Mark migration as started
   */
  startMigration(): void {
    this.migrationStatus = 'in_progress';
    this.migrationError = null;
  }

  /**
   * Mark migration as completed
   */
  completeMigration(): void {
    this.migrationStatus = 'completed';
    this.isMigrated = true;
    this.migratedAt = new Date();
    this.migrationError = null;
  }

  /**
   * Mark migration as failed
   */
  failMigration(error: string): void {
    this.migrationStatus = 'failed';
    this.migrationError = error;
  }

  /**
   * Reset migration status for retry
   */
  resetMigration(): void {
    this.migrationStatus = 'pending';
    this.migrationError = null;
    this.isMigrated = false;
    this.migratedAt = null;
  }

  /**
   * Get file size in human readable format
   */
  getFormattedFileSize(): string {
    if (!this.fileSize) return 'Unknown';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = this.fileSize;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * Check if the file is an image
   */
  isImage(): boolean {
    if (!this.mimeType) return false;
    return this.mimeType.startsWith('image/');
  }

  /**
   * Check if the file is a video
   */
  isVideo(): boolean {
    if (!this.mimeType) return false;
    return this.mimeType.startsWith('video/');
  }

  /**
   * Check if the file is a document
   */
  isDocument(): boolean {
    if (!this.mimeType) return false;
    const documentTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'text/csv',
    ];
    return documentTypes.includes(this.mimeType);
  }

  /**
   * Get file extension from filename
   */
  getFileExtension(): string {
    if (!this.fileName) return '';
    const parts = this.fileName.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
  }

  /**
   * Convert to a simple object for API responses
   */
  toSimpleObject(): any {
    return {
      id: this.id,
      fileName: this.fileName,
      mimeType: this.mimeType,
      fileSize: this.fileSize,
      formattedFileSize: this.getFormattedFileSize(),
      storageProvider: this.storageProvider,
      fileUrl: this.getFileUrl(),
      isImage: this.isImage(),
      isVideo: this.isVideo(),
      isDocument: this.isDocument(),
      fileExtension: this.getFileExtension(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
    };
  }
}
