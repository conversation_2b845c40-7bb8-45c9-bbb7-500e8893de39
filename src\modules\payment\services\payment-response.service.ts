import { Injectable, Logger } from '@nestjs/common';
import { UnifiedPaymentResponseDto } from '../dto/payment.dto';
import { PaymentTransaction } from '../../../database/entities/payment-transaction.entity';

/**
 * Service for standardizing payment responses across all APIs
 */
@Injectable()
export class PaymentResponseService {
  private readonly logger = new Logger(PaymentResponseService.name);

  /**
   * Create a standardized payment response
   */
  createPaymentResponse(transaction: PaymentTransaction, paymentUrl: string, orderId?: string): UnifiedPaymentResponseDto {
    return {
      orderId: orderId || transaction.orderId,
      paymentTransactionId: transaction.transactionId,
      paymentUrl: paymentUrl,
      status: this.mapTransactionStatus(transaction.status),
      totalAmount: transaction.amount,
      expiresAt: transaction.expiresAt,
      metadata: {
        purchaseType: transaction.purchaseType,
        referenceId: transaction.referenceId,
        currency: transaction.currency,
        paymentMethod: transaction.paymentMethod,
      },
    };
  }

  /**
   * Create error response for failed payments
   */
  createErrorResponse(orderId: string, transactionId: string, errorMessage: string, amount?: number): UnifiedPaymentResponseDto {
    return {
      orderId,
      paymentTransactionId: transactionId,
      paymentUrl: '',
      status: 'PAYMENT_FAILED',
      totalAmount: amount || 0,
      metadata: {
        error: true,
        errorMessage,
      },
    };
  }

  /**
   * Map internal transaction status to user-friendly status
   */
  private mapTransactionStatus(status: string): string {
    switch (status) {
      case 'initiated':
        return 'PAYMENT_INITIATED';
      case 'pending':
        return 'PAYMENT_PENDING';
      case 'processing':
        return 'PAYMENT_PROCESSING';
      case 'completed':
        return 'PAYMENT_COMPLETED';
      case 'failed':
        return 'PAYMENT_FAILED';
      case 'cancelled':
        return 'PAYMENT_CANCELLED';
      case 'refunded':
        return 'PAYMENT_REFUNDED';
      default:
        return 'PAYMENT_UNKNOWN';
    }
  }

  /**
   * Log payment response for debugging
   */
  logPaymentResponse(response: UnifiedPaymentResponseDto, context: string): void {
    this.logger.log(`Payment response created - Context: ${context}, ` + `Transaction: ${response.paymentTransactionId}, ` + `Status: ${response.status}, ` + `Amount: ${response.totalAmount}`);
  }
}
