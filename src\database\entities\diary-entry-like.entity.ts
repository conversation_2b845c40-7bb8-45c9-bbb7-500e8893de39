import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { DiaryEntry } from './diary-entry.entity';
import { User } from './user.entity';

/**
 * Type of user who liked the diary entry
 */
export enum LikerType {
  STUDENT = 'student',
  TUTOR = 'tutor',
}

@Entity()
@Unique(['diaryEntryId', 'likerId']) // Prevent duplicate likes
export class DiaryEntryLike extends AuditableBaseEntity {
  @Column({ name: 'diary_entry_id' })
  diaryEntryId: string;

  @ManyToOne(() => DiaryEntry, (entry) => entry.likes, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'diary_entry_id' })
  diaryEntry: DiaryEntry;

  @Column({ name: 'liker_id' })
  likerId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'liker_id' })
  liker: User;

  @Column({
    name: 'liker_type',
    type: 'enum',
    enum: LikerType,
  })
  likerType: LikerType;
}
