import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePaymentEnumsOnly1703123456795 implements MigrationInterface {
  name = 'CreatePaymentEnumsOnly1703123456795';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Just create the enum types - don't touch any tables or columns

    // Create payment_transaction enum with correct KCP values
    try {
      await queryRunner.query(`DROP TYPE IF EXISTS payment_transaction_payment_method_enum`);
      await queryRunner.query(`CREATE TYPE payment_transaction_payment_method_enum AS ENUM('CARD', 'BANK', 'MOBX', 'VCNT')`);
    } catch (error) {
      // Continue even if this fails
    }

    // Create shop_item_purchase enum with kcp_virtual_account
    try {
      await queryRunner.query(`DROP TYPE IF EXISTS shop_item_purchase_payment_method_enum`);
      await queryRunner.query(`CREATE TYPE shop_item_purchase_payment_method_enum AS ENUM('reward_points', 'credit_card', 'free', 'kcp_card', 'kcp_bank', 'kcp_virtual_account', 'kcp_mobile')`);
    } catch (error) {
      // Continue even if this fails
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert to old enum values
    try {
      await queryRunner.query(`DROP TYPE IF EXISTS payment_transaction_payment_method_enum`);
      await queryRunner.query(`CREATE TYPE payment_transaction_payment_method_enum AS ENUM('card', 'bank', 'mobile', 'vacct')`);
    } catch (error) {
      // Continue even if this fails
    }

    try {
      await queryRunner.query(`DROP TYPE IF EXISTS shop_item_purchase_payment_method_enum`);
      await queryRunner.query(`CREATE TYPE shop_item_purchase_payment_method_enum AS ENUM('reward_points', 'credit_card', 'free', 'kcp_card', 'kcp_bank', 'kcp_mobile')`);
    } catch (error) {
      // Continue even if this fails
    }
  }
}
