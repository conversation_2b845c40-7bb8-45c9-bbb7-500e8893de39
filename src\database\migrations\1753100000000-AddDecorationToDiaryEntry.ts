import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDecorationToDiaryEntry1753100000000 implements MigrationInterface {
  name = 'AddDecorationToDiaryEntry1753100000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add decoration column to diary_entry table
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      ADD COLUMN "decoration" text NULL
    `);

    // Add comment to the column for documentation
    await queryRunner.query(`
      COMMENT ON COLUMN "diary_entry"."decoration" IS 'Decoration theme for the diary entry (e.g., stars, hearts, etc.)'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove decoration column from diary_entry table
    await queryRunner.query(`
      ALTER TABLE "diary_entry" 
      DROP COLUMN "decoration"
    `);
  }
}
