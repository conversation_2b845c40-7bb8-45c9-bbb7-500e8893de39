import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateNovelCorrectionScoreNullable1746400000000 implements MigrationInterface {
  name = 'UpdateNovelCorrectionScoreNullable1746400000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Make score column nullable in novel_correction table
    await queryRunner.query(`
      ALTER TABLE "novel_correction" 
      ALTER COLUMN "score" DROP NOT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert score column to NOT NULL (this might fail if there are null values)
    await queryRunner.query(`
      ALTER TABLE "novel_correction" 
      ALTER COLUMN "score" SET NOT NULL
    `);
  }
}
