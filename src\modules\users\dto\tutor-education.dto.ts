import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsBoolean, IsDateString } from 'class-validator';

/**
 * DTO for creating a new education entry for a tutor
 */
export class CreateTutorEducationDto {
  @ApiProperty({ example: 'Bachelor of Science', description: 'Degree or certification name' })
  @IsString()
  degree: string;

  @ApiProperty({ example: 'Harvard University', description: 'Institution name' })
  @IsString()
  institution: string;

  @ApiProperty({ example: 'Computer Science', description: 'Field of study' })
  @IsString()
  fieldOfStudy: string;

  @ApiProperty({ example: '2015-09-01', description: 'Start date in YYYY-MM-DD format' })
  @IsDateString()
  startDate: string;

  @ApiProperty({ example: '2019-06-30', description: 'End date in YYYY-MM-DD format', required: false })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiProperty({ example: true, description: 'Whether this is the current education', default: false })
  @IsBoolean()
  @IsOptional()
  isCurrent?: boolean;

  @ApiProperty({ example: 'Studied advanced algorithms and data structures', description: 'Description of the education', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ example: 'Cambridge, MA', description: 'Location of the institution', required: false })
  @IsString()
  @IsOptional()
  location?: string;

  @ApiProperty({ example: '3.8 GPA', description: 'Grade or GPA', required: false })
  @IsString()
  @IsOptional()
  grade?: string;

  @ApiProperty({ example: 'Member of Computer Science Club, Participated in Hackathons', description: 'Activities and societies', required: false })
  @IsString()
  @IsOptional()
  activities?: string;
}

/**
 * DTO for updating an existing education entry for a tutor
 */
export class UpdateTutorEducationDto {
  // All fields are optional for updates
  @ApiProperty({ example: 'Bachelor of Science', description: 'Degree or certification name', required: false })
  @IsString()
  @IsOptional()
  degree?: string;

  @ApiProperty({ example: 'Harvard University', description: 'Institution name', required: false })
  @IsString()
  @IsOptional()
  institution?: string;

  @ApiProperty({ example: 'Computer Science', description: 'Field of study', required: false })
  @IsString()
  @IsOptional()
  fieldOfStudy?: string;

  @ApiProperty({ example: '2015-09-01', description: 'Start date in YYYY-MM-DD format', required: false })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiProperty({ example: '2019-06-30', description: 'End date in YYYY-MM-DD format', required: false })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiProperty({ example: true, description: 'Whether this is the current education', required: false })
  @IsBoolean()
  @IsOptional()
  isCurrent?: boolean;

  @ApiProperty({ example: 'Studied advanced algorithms and data structures', description: 'Description of the education', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ example: 'Cambridge, MA', description: 'Location of the institution', required: false })
  @IsString()
  @IsOptional()
  location?: string;

  @ApiProperty({ example: '3.8 GPA', description: 'Grade or GPA', required: false })
  @IsString()
  @IsOptional()
  grade?: string;

  @ApiProperty({ example: 'Member of Computer Science Club, Participated in Hackathons', description: 'Activities and societies', required: false })
  @IsString()
  @IsOptional()
  activities?: string;
}

/**
 * DTO for education information in responses
 */
export class TutorEducationResponseDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000', description: 'Education entry ID' })
  id: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000', description: 'Tutor ID' })
  tutorId: string;

  @ApiProperty({ example: 'Bachelor of Science', description: 'Degree or certification name' })
  degree: string;

  @ApiProperty({ example: 'Harvard University', description: 'Institution name' })
  institution: string;

  @ApiProperty({ example: 'Computer Science', description: 'Field of study' })
  fieldOfStudy: string;

  @ApiProperty({ example: '2015-09-01', description: 'Start date in YYYY-MM-DD format' })
  startDate: string;

  @ApiProperty({ example: '2019-06-30', description: 'End date in YYYY-MM-DD format', required: false })
  endDate?: string;

  @ApiProperty({ example: true, description: 'Whether this is the current education' })
  isCurrent: boolean;

  @ApiProperty({ example: 'Studied advanced algorithms and data structures', description: 'Description of the education', required: false })
  description?: string;

  @ApiProperty({ example: 'Cambridge, MA', description: 'Location of the institution', required: false })
  location?: string;

  @ApiProperty({ example: '3.8 GPA', description: 'Grade or GPA', required: false })
  grade?: string;

  @ApiProperty({ example: 'Member of Computer Science Club, Participated in Hackathons', description: 'Activities and societies', required: false })
  activities?: string;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'Date when the education entry was created' })
  createdAt: Date;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'Date when the education entry was last updated' })
  updatedAt: Date;
}
