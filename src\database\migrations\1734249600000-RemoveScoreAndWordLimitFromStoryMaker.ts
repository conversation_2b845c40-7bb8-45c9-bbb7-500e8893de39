import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveScoreAndWordLimitFromStoryMaker1734249600000 implements MigrationInterface {
    name = 'RemoveScoreAndWordLimitFromStoryMaker1734249600000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Remove the score column from story_maker table
        await queryRunner.query(`ALTER TABLE "story_maker" DROP COLUMN "score"`);
        
        // Remove the word_limit column from story_maker table
        await queryRunner.query(`ALTER TABLE "story_maker" DROP COLUMN "word_limit"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Add back the word_limit column (nullable)
        await queryRunner.query(`ALTER TABLE "story_maker" ADD "word_limit" integer`);
        
        // Add back the score column (not null, default value needed)
        await queryRunner.query(`ALTER TABLE "story_maker" ADD "score" integer NOT NULL DEFAULT 100`);
    }
}
