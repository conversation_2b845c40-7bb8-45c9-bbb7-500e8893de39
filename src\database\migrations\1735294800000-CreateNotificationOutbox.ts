import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateNotificationOutbox1735294800000 implements MigrationInterface {
  name = 'CreateNotificationOutbox1735294800000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create notification_outbox table
    await queryRunner.createTable(
      new Table({
        name: 'notification_outbox',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'userId',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'type',
            type: 'enum',
            enum: [
              'DIARY_SUBMISSION',
              'DIARY_REVIEWED',
              'DIARY_CONFIRMED',
              'MISSION_SUBMISSION',
              'MISSION_REVIEWED',
              'MISSION_CONFIRMED',
              'NOVEL_SUBMISSION',
              'NOVEL_REVIEWED',
              'QA_SUBMISSION',
              'QA_REVIEWED',
              'GENERAL',
              'SYSTEM',
              'REMINDER',
              'ACHIEVEMENT',
              'FRIEND_REQUEST',
              'FRIEND_ACCEPTED',
              'CHAT_MESSAGE',
              'SUBSCRIPTION_EXPIRY',
              'SUBSCRIPTION_RENEWED',
              'PAYMENT_SUCCESS',
              'PAYMENT_FAILED',
              'TUTOR_ASSIGNMENT',
              'TUTOR_FEEDBACK',
              'ADMIN_ANNOUNCEMENT',
            ],
            isNullable: false,
          },
          {
            name: 'title',
            type: 'varchar',
            length: '500',
            isNullable: false,
          },
          {
            name: 'message',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'options',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['pending', 'processing', 'completed', 'failed', 'retry'],
            default: "'pending'",
            isNullable: false,
          },
          {
            name: 'retryCount',
            type: 'int',
            default: 0,
            isNullable: false,
          },
          {
            name: 'maxRetries',
            type: 'int',
            default: 3,
            isNullable: false,
          },
          {
            name: 'nextRetryAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'processedAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'lastError',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Create indexes for efficient querying
    await queryRunner.query(`
      CREATE INDEX "IDX_notification_outbox_status_created" ON "notification_outbox" ("status", "createdAt");
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_notification_outbox_status_retry" ON "notification_outbox" ("status", "nextRetryAt");
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_notification_outbox_user_created" ON "notification_outbox" ("userId", "createdAt");
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_notification_outbox_type_status" ON "notification_outbox" ("type", "status");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_notification_outbox_type_status"`);
    await queryRunner.query(`DROP INDEX "IDX_notification_outbox_user_created"`);
    await queryRunner.query(`DROP INDEX "IDX_notification_outbox_status_retry"`);
    await queryRunner.query(`DROP INDEX "IDX_notification_outbox_status_created"`);

    // Drop table
    await queryRunner.dropTable('notification_outbox');
  }
}
