import { <PERSON>, Get, Post, Body, Param, Query, Req, BadRequestException, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiQuery, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { StudentFriendshipService } from './student-friendship.service';
import {
  SearchStudentsDto,
  SendFriendRequestDto,
  SendDiaryFollowRequestDto,
  RespondToRequestDto,
  FriendshipResponseDto,
  StudentSearchResultDto,
  PendingRequestsResponseDto,
  DiaryFollowRequestResponseDto,
  ConversationByFriendResponseDto,
  DiaryFollowWithSharingStatusDto,
} from '../../database/models/student-friendship.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType } from '../../common/decorators/api-response.decorator';
import { ApiErrorResponse } from '../../common/decorators/api-error-response.decorator';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';

@ApiTags('student-friendship')
@Controller('student/friends')
@ApiBearerAuth('JWT-auth')
export class StudentFriendshipController {
  private readonly logger = new Logger(StudentFriendshipController.name);

  constructor(private readonly studentFriendshipService: StudentFriendshipService) {}

  @Get('search')
  @ApiOperation({
    summary: 'Search for students',
    description: 'Search for students by name, ID, email, or phone. If no query is provided, returns all students with pagination (10 entries by default)',
  })
  @ApiQuery({
    name: 'query',
    description: 'Search query (optional - if not provided, returns all students)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'type',
    description: 'Search type (id, name, email, phone)',
    required: false,
    enum: ['id', 'name', 'email', 'phone'],
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page (default: 10)',
    required: false,
    type: Number,
  })
  @ApiOkResponseWithType(PagedListDto, 'Students found successfully')
  @ApiErrorResponse(400, 'Invalid input')
  async searchStudents(
    @Req() req: any,
    @Query('query') query?: string,
    @Query('type') type?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ): Promise<ApiResponse<PagedListDto<StudentSearchResultDto>>> {
    const userId = req.user.sub;

    const paginationDto: PaginationDto = { page, limit };
    const students = await this.studentFriendshipService.searchStudents(query, type, userId, paginationDto);

    return ApiResponse.success(students, 'Students found successfully');
  }

  @Post('request')
  @ApiOperation({
    summary: 'Send a friend request',
    description: 'Send a friend request to another student',
  })
  @ApiBody({
    type: SendFriendRequestDto,
    description: 'Friend request data',
  })
  @ApiOkResponseWithType(FriendshipResponseDto, 'Friend request sent successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Student not found')
  @ApiErrorResponse(409, 'Friend request already exists')
  async sendFriendRequest(@Req() req: any, @Body() sendFriendRequestDto: SendFriendRequestDto): Promise<ApiResponse<FriendshipResponseDto>> {
    const userId = req.user.sub;

    const friendship = await this.studentFriendshipService.sendFriendRequest(userId, sendFriendRequestDto);

    return ApiResponse.success(friendship, 'Friend request sent successfully');
  }

  @Post('diary-follow/request')
  @ApiOperation({
    summary: 'Send a diary follow request',
    description: "Send a request to follow another student's diary",
  })
  @ApiBody({
    type: SendDiaryFollowRequestDto,
    description: 'Diary follow request data',
  })
  @ApiOkResponseWithType(DiaryFollowRequestResponseDto, 'Diary follow request sent successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Student not found')
  @ApiErrorResponse(409, 'Diary follow request already exists')
  async sendDiaryFollowRequest(@Req() req: any, @Body() sendDiaryFollowRequestDto: SendDiaryFollowRequestDto): Promise<ApiResponse<DiaryFollowRequestResponseDto>> {
    const userId = req.user.sub;

    const followRequest = await this.studentFriendshipService.sendDiaryFollowRequest(userId, sendDiaryFollowRequestDto);

    return ApiResponse.success(followRequest, 'Diary follow request sent successfully');
  }

  @Get('pending')
  @ApiOperation({
    summary: 'Get pending friend requests',
    description: 'Get all pending friend requests for the current student',
  })
  @ApiOkResponseWithType(PendingRequestsResponseDto, 'Pending friend requests retrieved successfully')
  @ApiErrorResponse(404, 'Student not found')
  async getPendingFriendRequests(@Req() req: any): Promise<ApiResponse<PendingRequestsResponseDto>> {
    const userId = req.user.sub;

    const pendingRequests = await this.studentFriendshipService.getPendingFriendRequests(userId);

    return ApiResponse.success(pendingRequests, 'Pending friend requests retrieved successfully');
  }

  @Get('diary-follow/pending')
  @ApiOperation({
    summary: 'Get pending diary follow requests',
    description: 'Get all pending diary follow requests for the current student',
  })
  @ApiOkResponseWithType(DiaryFollowRequestResponseDto, 'Pending diary follow requests retrieved successfully')
  @ApiErrorResponse(404, 'Student not found')
  async getPendingDiaryFollowRequests(@Req() req: any): Promise<ApiResponse<DiaryFollowRequestResponseDto[]>> {
    const userId = req.user.sub;

    const pendingRequests = await this.studentFriendshipService.getPendingDiaryFollowRequests(userId);

    return ApiResponse.success(pendingRequests, 'Pending diary follow requests retrieved successfully');
  }

  @Post('request/:id/respond')
  @ApiOperation({
    summary: 'Respond to a friend request',
    description: 'Accept or reject a friend request',
  })
  @ApiParam({
    name: 'id',
    description: 'Friend request ID',
    type: String,
  })
  @ApiBody({
    type: RespondToRequestDto,
    description: 'Response data',
  })
  @ApiOkResponseWithType(FriendshipResponseDto, 'Friend request response sent successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Friend request not found')
  async respondToFriendRequest(@Req() req: any, @Param('id') id: string, @Body() respondToRequestDto: RespondToRequestDto): Promise<ApiResponse<FriendshipResponseDto>> {
    const userId = req.user.sub;

    const friendship = await this.studentFriendshipService.respondToFriendRequest(id, userId, respondToRequestDto);

    return ApiResponse.success(friendship, 'Friend request response sent successfully');
  }

  @Post('diary-follow/:id/respond')
  @ApiOperation({
    summary: 'Respond to a diary follow request',
    description: 'Accept or reject a diary follow request',
  })
  @ApiParam({
    name: 'id',
    description: 'Diary follow request ID',
    type: String,
  })
  @ApiBody({
    type: RespondToRequestDto,
    description: 'Response data',
  })
  @ApiOkResponseWithType(DiaryFollowRequestResponseDto, 'Diary follow request response sent successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary follow request not found')
  async respondToDiaryFollowRequest(@Req() req: any, @Param('id') id: string, @Body() respondToRequestDto: RespondToRequestDto): Promise<ApiResponse<DiaryFollowRequestResponseDto>> {
    const userId = req.user.sub;

    const followRequest = await this.studentFriendshipService.respondToDiaryFollowRequest(id, userId, respondToRequestDto);

    return ApiResponse.success(followRequest, 'Diary follow request response sent successfully');
  }

  @Get()
  @ApiOperation({
    summary: 'Get all friends',
    description: 'Get all friends for the current student',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiOkResponseWithType(PagedListDto, 'Friends retrieved successfully')
  @ApiErrorResponse(404, 'Student not found')
  async getFriends(@Req() req: any, @Query('page') page?: number, @Query('limit') limit?: number): Promise<ApiResponse<PagedListDto<FriendshipResponseDto>>> {
    const userId = req.user.sub;

    const paginationDto: PaginationDto = { page, limit };
    const friends = await this.studentFriendshipService.getFriends(userId, paginationDto);

    return ApiResponse.success(friends, 'Friends retrieved successfully');
  }

  @Get('diary-follows')
  @ApiOperation({
    summary: 'Get all diary follows',
    description: 'Get all diary follows for the current student',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiOkResponseWithType(PagedListDto, 'Diary follows retrieved successfully')
  @ApiErrorResponse(404, 'Student not found')
  async getDiaryFollows(@Req() req: any, @Query('page') page?: number, @Query('limit') limit?: number): Promise<ApiResponse<PagedListDto<DiaryFollowRequestResponseDto>>> {
    const userId = req.user.sub;

    const paginationDto: PaginationDto = { page, limit };
    const diaryFollows = await this.studentFriendshipService.getDiaryFollows(userId, paginationDto);

    return ApiResponse.success(diaryFollows, 'Diary follows retrieved successfully');
  }

  @Get('diary-follows/sharing-status')
  @ApiOperation({
    summary: 'Get diary follows with sharing status',
    description: 'Get all students who have accepted diary follow requests with sharing status for a specific entry',
  })
  @ApiQuery({
    name: 'entryId',
    required: true,
    description: 'Diary entry ID to check sharing status for',
    type: String,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    type: Number,
  })
  @ApiOkResponseWithType(PagedListDto, 'Diary follows with sharing status retrieved successfully')
  @ApiErrorResponse(400, 'Invalid entry ID')
  @ApiErrorResponse(404, 'Student not found')
  async getDiaryFollowsWithSharingStatus(
    @Req() req: any,
    @Query('entryId') entryId: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ): Promise<ApiResponse<PagedListDto<DiaryFollowWithSharingStatusDto>>> {
    const userId = req.user.sub;

    if (!entryId) {
      throw new BadRequestException('Entry ID is required');
    }

    const paginationDto: PaginationDto = { page, limit };
    const diaryFollows = await this.studentFriendshipService.getDiaryFollowsWithSharingStatus(userId, entryId, paginationDto);

    return ApiResponse.success(diaryFollows, 'Diary follows with sharing status retrieved successfully');
  }

  @Get('conversation/:userId')
  @ApiOperation({
    summary: 'Get conversation with user',
    description: 'Get or create a conversation with another user (friend, tutor, etc.) by user ID',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID to chat with (can be friend, tutor, etc.)',
    type: String,
  })
  @ApiOkResponseWithType(ConversationByFriendResponseDto, 'Conversation retrieved successfully')
  @ApiErrorResponse(400, 'Invalid user ID')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(403, 'Cannot chat with this user')
  async getConversationWithUser(@Req() req: any, @Param('userId') targetUserId: string): Promise<ApiResponse<ConversationByFriendResponseDto>> {
    const userId = req.user.sub;

    if (!targetUserId) {
      throw new BadRequestException('User ID is required');
    }

    const conversationData = await this.studentFriendshipService.getConversationWithUser(userId, targetUserId);

    return ApiResponse.success(conversationData, 'Conversation retrieved successfully');
  }

  @Get(':friendId/conversation')
  @ApiOperation({
    summary: 'Get conversation with friend',
    description: 'Get or create a conversation with a friend by friend ID (deprecated - use /conversation/:userId instead)',
  })
  @ApiParam({
    name: 'friendId',
    description: 'Friend user ID',
    type: String,
  })
  @ApiOkResponseWithType(ConversationByFriendResponseDto, 'Conversation retrieved successfully')
  @ApiErrorResponse(400, 'Invalid friend ID')
  @ApiErrorResponse(404, 'Friend not found or not friends')
  @ApiErrorResponse(403, 'Cannot chat with this user')
  async getConversationWithFriend(@Req() req: any, @Param('friendId') friendId: string): Promise<ApiResponse<ConversationByFriendResponseDto>> {
    const userId = req.user.sub;

    if (!friendId) {
      throw new BadRequestException('Friend ID is required');
    }

    const conversationData = await this.studentFriendshipService.getConversationWithFriend(userId, friendId);

    return ApiResponse.success(conversationData, 'Conversation retrieved successfully');
  }
}
