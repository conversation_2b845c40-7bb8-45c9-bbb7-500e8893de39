import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { QATaskSubmissions } from './qa-task-submissions.entity';

@Entity()
@Index(['submission', 'sequenceNumber'])
@Index(['submissionDate'])
export class QATaskSubmissionHistory extends AuditableBaseEntity {
  @ManyToOne(() => QATaskSubmissions, (submission) => submission.submissionHistory, { nullable: true })
  @JoinColumn({ name: 'submission' })
  submission: QATaskSubmissions;

  @Column({ name: 'submission_id' })
  submissionId: string;

  @Column({
    name: 'content',
    type: 'text',
  })
  content: string;

  @Column({
    name: 'word_count',
    type: 'int',
  })
  wordCount: number;

  @Column({ name: 'submission_date', type: 'timestamp' })
  submissionDate: Date;

  @Column({ name: 'sequence_number' })
  sequenceNumber: number;

  @Column({
    name: 'meta_data',
    type: 'json',
    nullable: true,
  })
  metaData?: {
    browserInfo?: string;
    ipAddress?: string;
    submissionAttempts?: number;
    lastDraftSavedAt?: Date;
    timeSpent?: number;
    wordCountDiff?: number;
    contentChanges?: {
      paragraphsAdded?: number;
      paragraphsRemoved?: number;
      significantChanges?: boolean;
    };
    reviewerNotes?: string;
    aiDetectionScore?: number;
  };
}
