# KCP Certificates Directory

This directory contains the KCP payment gateway certificates required for API authentication.

## Files

### `splCert.pem`
- **Purpose**: KCP API authentication certificate
- **Format**: PEM (Privacy-Enhanced Mail) format
- **Usage**: Used by the backend for authenticating with KCP APIs
- **Environment**: Currently contains staging certificate

## Certificate Management

### Development/Staging
The current `splCert.pem` contains the staging certificate for development and testing.

### Production
**IMPORTANT**: Replace `splCert.pem` with your production certificate before deploying to production.

### Certificate Replacement Steps

1. **Backup Current Certificate** (optional):
   ```bash
   cp certificates/splCert.pem certificates/splCert.pem.backup
   ```

2. **Replace with Production Certificate**:
   ```bash
   # Copy your production certificate to this location
   cp /path/to/your/production/cert.pem certificates/splCert.pem
   ```

3. **Verify Certificate Format**:
   ```bash
   # Check that the certificate starts and ends with proper markers
   head -1 certificates/splCert.pem  # Should show: -----BEGIN CERTIFICATE-----
   tail -1 certificates/splCert.pem  # Should show: -----<PERSON><PERSON> CERTIFICATE-----
   ```

4. **Test Certificate Loading**:
   ```bash
   # Start the application and check logs for certificate loading
   npm run start:dev
   # Look for log: "KCP certificate loaded successfully"
   ```

## Environment Configuration

The certificate path can be configured via environment variable:

```env
# Default path (relative to project root)
KCP_CERT_PATH=certificates/splCert.pem

# Alternative paths
KCP_CERT_PATH=/absolute/path/to/cert.pem
KCP_CERT_PATH=config/kcp/production.pem
```

## Security Notes

1. **File Permissions**: Ensure certificate files have appropriate permissions (readable by application only)
2. **Version Control**: Consider whether to include certificates in version control (staging: yes, production: no)
3. **Certificate Rotation**: Update certificates before expiration
4. **Backup**: Keep secure backups of production certificates

## Troubleshooting

### Certificate Loading Errors

**Error**: `Failed to load KCP certificate: ENOENT: no such file or directory`
- **Solution**: Verify the certificate file exists at the specified path
- **Check**: `ls -la certificates/splCert.pem`

**Error**: `Invalid certificate format - must be PEM format`
- **Solution**: Ensure certificate has proper PEM format markers
- **Check**: Certificate should start with `-----BEGIN CERTIFICATE-----` and end with `-----END CERTIFICATE-----`

**Error**: `KCP certificate loading failed`
- **Solution**: Check file permissions and path configuration
- **Fallback**: In staging environment, fallback certificate will be used automatically

### Fallback Behavior

In staging environment, if certificate loading fails, the system will automatically use a fallback certificate and log a warning. This ensures development/testing can continue even with certificate issues.

Production environment will fail fast if certificate cannot be loaded, ensuring security requirements are met.

## Certificate Validation

The system validates certificates on load:
1. **File Existence**: Certificate file must exist
2. **Format Validation**: Must be valid PEM format
3. **Content Validation**: Must contain certificate markers
4. **Logging**: All certificate operations are logged for debugging

## Support

For certificate-related issues:
1. Check application logs for detailed error messages
2. Verify certificate format and permissions
3. Test with staging certificate first
4. Contact KCP support for certificate provisioning issues
