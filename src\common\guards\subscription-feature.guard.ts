import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { FeatureType } from '../../database/entities/plan-feature.entity';
import { JwtPayload } from '../../modules/auth/interfaces/jwt-payload.interface';
import { UserType } from '../../database/entities/user.entity';
import { SubscriptionService } from '../services/subscription.service';
import { FEATURE_ACCESS_KEY } from '../decorators/require-feature.decorator';

@Injectable()
export class SubscriptionFeatureGuard implements CanActivate {
  private readonly logger = new Logger(SubscriptionFeatureGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Get required feature(s) from decorator
    const requiredFeature = this.reflector.get<FeatureType>(FEATURE_ACCESS_KEY, context.getHandler());

    const requiredFeatures = this.reflector.get<FeatureType[]>(FEATURE_ACCESS_KEY, context.getHandler());

    const requiredAllFeatures = this.reflector.get<FeatureType[]>(`${FEATURE_ACCESS_KEY}_all`, context.getHandler());

    // Also check class-level decorators
    const classRequiredFeature = this.reflector.get<FeatureType>(FEATURE_ACCESS_KEY, context.getClass());

    const classRequiredFeatures = this.reflector.get<FeatureType[]>(FEATURE_ACCESS_KEY, context.getClass());

    const classRequiredAllFeatures = this.reflector.get<FeatureType[]>(`${FEATURE_ACCESS_KEY}_all`, context.getClass());

    // Use class-level if method-level not specified
    const finalRequiredFeature = requiredFeature || classRequiredFeature;
    const finalRequiredFeatures = requiredFeatures || classRequiredFeatures;
    const finalRequiredAllFeatures = requiredAllFeatures || classRequiredAllFeatures;

    // If no feature requirement is specified, allow access
    if (!finalRequiredFeature && !finalRequiredFeatures && !finalRequiredAllFeatures) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user as JwtPayload;

    // If no user is present, deny access (should be caught by JWT guard first)
    if (!user) {
      this.logger.warn(`Feature access check failed: No user in request - ${request.method} ${request.url}`);
      return false;
    }

    // Allow admin and tutor access without feature validation
    if (user.type === UserType.ADMIN || user.type === UserType.TUTOR) {
      this.logger.log(`Feature access granted for ${user.type} user ${user.id} - ${request.method} ${request.url}`);
      return true;
    }

    // Only validate features for students
    if (user.type !== UserType.STUDENT) {
      this.logger.log(`Feature access granted for non-student user ${user.id} (${user.type}) - ${request.method} ${request.url}`);
      return true;
    }

    try {
      // Validate feature access
      if (finalRequiredAllFeatures && Array.isArray(finalRequiredAllFeatures)) {
        // All features required
        await this.subscriptionService.validateFeatureAccess(user, undefined, finalRequiredAllFeatures, true);
      } else if (finalRequiredFeatures && Array.isArray(finalRequiredFeatures)) {
        // Any feature sufficient (array of features)
        await this.subscriptionService.validateFeatureAccess(user, undefined, finalRequiredFeatures, false);
      } else if (finalRequiredFeature) {
        // Single feature required
        await this.subscriptionService.validateFeatureAccess(user, finalRequiredFeature);
      }

      this.logger.log(`Feature access granted for student ${user.id} - ${request.method} ${request.url}`);
      return true;
    } catch (error) {
      this.logger.warn(`Feature access denied for student ${user.id}: ${error.message} - ${request.method} ${request.url}`);
      throw error; // Re-throw to return proper error response
    }
  }
}
