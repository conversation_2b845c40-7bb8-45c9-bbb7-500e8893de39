import { <PERSON>, Get, UseGuards, Param, BadRequestException, Query } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, Api<PERSON>peration, ApiParam, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { GetUser } from '../../common/decorators/get-user.decorator';
import { User, UserType } from '../../database/entities/user.entity';
import { TutorMatchingService } from './tutor-matching.service';
import { TutorStudentDto, TutorStudentFlattenedDto } from '../../database/models/tutor-matching.dto';
import { ConversationByFriendResponseDto } from '../../database/models/student-friendship.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithArrayType, ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';

@ApiTags('tutor-student-matching')
@Controller('tutor/students')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
@Roles(UserType.TUTOR)
export class TutorStudentMatchingController {
  constructor(private readonly tutorMatchingService: TutorMatchingService) {}

  @Get()
  @ApiOperation({
    summary: 'Get all students assigned to the current tutor',
    description: 'Returns a paginated list of students grouped by user with their assigned modules'
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (default: 1)' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (default: 10)' })
  @ApiOkResponseWithPagedListType(TutorStudentFlattenedDto, 'Students retrieved successfully')
  @ApiErrorResponse(404, 'Tutor not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getMyStudents(
    @GetUser() user: User,
    @Query() paginationDto: PaginationDto
  ): Promise<ApiResponse<PagedListDto<TutorStudentFlattenedDto>>> {
    const students = await this.tutorMatchingService.getTutorStudentsFlattened(user.id, paginationDto);
    return ApiResponse.success(students, 'Students retrieved successfully');
  }

  @Get('conversation/:studentId')
  @ApiOperation({
    summary: 'Get conversation with student',
    description: 'Get or create a conversation with a student by student ID',
  })
  @ApiParam({
    name: 'studentId',
    description: 'Student user ID to chat with',
    type: String,
  })
  @ApiOkResponseWithType(ConversationByFriendResponseDto, 'Conversation retrieved successfully')
  @ApiErrorResponse(400, 'Invalid student ID')
  @ApiErrorResponse(404, 'Student not found or not assigned to tutor')
  @ApiErrorResponse(403, 'Cannot chat with this student')
  async getConversationWithStudent(@GetUser() user: User, @Param('studentId') studentId: string): Promise<ApiResponse<ConversationByFriendResponseDto>> {
    if (!studentId) {
      throw new BadRequestException('Student ID is required');
    }

    const conversationData = await this.tutorMatchingService.getConversationWithStudent(user.id, studentId);

    return ApiResponse.success(conversationData, 'Conversation retrieved successfully');
  }
}
