import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsActiveToWaterfallSet1750155428151 implements MigrationInterface {
  name = 'AddIsActiveToWaterfallSet1750155428151';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "waterfall_set" ADD "is_active" boolean NOT NULL DEFAULT true`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "waterfall_set" DROP COLUMN "is_active"`);
  }
}
