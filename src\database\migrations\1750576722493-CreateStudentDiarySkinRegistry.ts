import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateStudentDiarySkinRegistry1750576722493 implements MigrationInterface {
  name = 'CreateStudentDiarySkinRegistry1750576722493';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum for storage provider
    await queryRunner.query(`CREATE TYPE "public"."student_diary_skin_registry_storage_provider_enum" AS ENUM('local', 's3')`);

    // Create student_diary_skin_registry table
    await queryRunner.query(
      `CREATE TABLE "student_diary_skin_registry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying, "file_size" integer, "storage_provider" "public"."student_diary_skin_registry_storage_provider_enum" NOT NULL DEFAULT 'local', "storage_key" character varying(500), "s3_bucket" character varying(100), "s3_region" character varying(50), "s3_etag" character varying(100), "s3_version_id" character varying(100), "s3_storage_class" character varying(50), "s3_server_side_encryption" character varying(50), "cdn_url" character varying(500), "presigned_url_expires_at" TIMESTAMP, "storage_metadata" jsonb, "is_migrated" boolean NOT NULL DEFAULT false, "migration_status" character varying(20), "migration_error" text, "migrated_at" TIMESTAMP, "student_diary_skin_id" uuid NOT NULL, "student_id" character varying NOT NULL, "user_id" character varying, CONSTRAINT "PK_26a4d80db230fc412cf74f5a74b" PRIMARY KEY ("id"))`,
    );

    // Add foreign key constraint
    await queryRunner.query(
      `ALTER TABLE "student_diary_skin_registry" ADD CONSTRAINT "FK_849be1d6268f38bb3f201511edd" FOREIGN KEY ("student_diary_skin_id") REFERENCES "student_diary_skin"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraint
    await queryRunner.query(`ALTER TABLE "student_diary_skin_registry" DROP CONSTRAINT "FK_849be1d6268f38bb3f201511edd"`);

    // Drop table
    await queryRunner.query(`DROP TABLE "student_diary_skin_registry"`);

    // Drop enum
    await queryRunner.query(`DROP TYPE "public"."student_diary_skin_registry_storage_provider_enum"`);
  }
}
