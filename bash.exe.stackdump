Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDDD5C0000 ntdll.dll
7FFDDB9E0000 KERNEL32.DLL
7FFDDA6F0000 KERNELBASE.dll
7FFDDB810000 USER32.dll
7FFDDB320000 win32u.dll
7FFDDCCA0000 GDI32.dll
7FFDDAAF0000 gdi32full.dll
7FFDDADB0000 msvcp_win.dll
7FFDDB1D0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDDD4B0000 advapi32.dll
7FFDDCBF0000 msvcrt.dll
7FFDDCB40000 sechost.dll
7FFDDB560000 RPCRT4.dll
7FFDD9BE0000 CRYPTBASE.DLL
7FFDDB130000 bcryptPrimitives.dll
7FFDDBAB0000 IMM32.DLL
