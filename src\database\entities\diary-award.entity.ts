import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

/**
 * Period for diary awards
 * @enum {string}
 */
export enum DiaryAwardPeriod {
  /** Weekly awards given at the end of each week */
  WEEKLY = 'weekly',
  /** Monthly awards given at the end of each month */
  MONTHLY = 'monthly',
  /** Quarterly awards given at the end of each quarter */
  QUARTERLY = 'quarterly',
}

@Entity()
export class DiaryAward extends AuditableBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({
    name: 'period',
    type: 'enum',
    enum: DiaryAwardPeriod,
  })
  period: DiaryAwardPeriod;

  @Column({ name: 'period_start_date', type: 'date' })
  periodStartDate: Date;

  @Column({ name: 'period_end_date', type: 'date' })
  periodEndDate: Date;

  @Column({ name: 'total_score', type: 'int' })
  totalScore: number;

  @Column({ name: 'award_title', nullable: true })
  awardTitle: string;

  @Column({ name: 'award_description', nullable: true, type: 'text' })
  awardDescription: string;
}
