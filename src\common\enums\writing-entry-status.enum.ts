/**
 * Unified status enum for all writing entries (diary, mission diary, novel)
 * This ensures consistent lifecycle across all writing modules
 *
 * LIFECYCLE: DRAFT → SUBMITTED → REVIEWED (Final State)
 * Note: CONFIRMED stage has been removed from the lifecycle
 */
export enum WritingEntryStatus {
  /** Entry is being drafted by the student */
  DRAFT = 'draft',

  /** Entry has been submitted for review */
  SUBMITTED = 'submitted',

  /** Entry has been reviewed by tutor (feedback/correction given) - FINAL STATE */
  REVIEWED = 'reviewed',

  // Legacy values for backward compatibility
  /** @deprecated Use DRAFT instead */
  LEGACY_NEW = 'new',

  /** @deprecated Use SUBMITTED instead */
  LEGACY_SUBMIT = 'submit',

  /** @deprecated Use REVIEWED instead */
  LEGACY_REVIEWED = 'reviewed_legacy',

  /** @deprecated CONFIRM stage removed - maps to REVIEWED */
  LEGACY_CONFIRM = 'confirm',

  // Novel-specific legacy values
  /** @deprecated Use SUBMITTED instead */
  LEGACY_NOVEL_SUBMITTED = 'novel_submitted',

  /** @deprecated Use REVIEWED instead */
  LEGACY_NOVEL_UPDATED = 'updated',

  /** @deprecated Use REVIEWED instead */
  LEGACY_NOVEL_CORRECTION_GIVEN = 'correction_given',

  // Mission-specific legacy values
  /** @deprecated Use DRAFT instead */
  LEGACY_MISSION_NEW = 'NEW',

  /** @deprecated Use SUBMITTED instead */
  LEGACY_MISSION_SUBMITTED = 'SUBMITTED',

  /** @deprecated Use REVIEWED instead */
  LEGACY_MISSION_REVIEWED = 'REVIEWED',

  /** @deprecated CONFIRM stage removed - maps to REVIEWED */
  LEGACY_MISSION_CONFIRMED = 'CONFIRMED',
}

/**
 * Helper functions for status management
 */
export class WritingEntryStatusHelper {
  /**
   * Get all valid statuses for a writing entry
   */
  static getAllValidStatuses(): WritingEntryStatus[] {
    return [WritingEntryStatus.DRAFT, WritingEntryStatus.SUBMITTED, WritingEntryStatus.REVIEWED];
  }

  /**
   * Get all legacy statuses that should be mapped to new statuses
   */
  static getAllLegacyStatuses(): WritingEntryStatus[] {
    return [
      WritingEntryStatus.LEGACY_NEW,
      WritingEntryStatus.LEGACY_SUBMIT,
      WritingEntryStatus.LEGACY_REVIEWED,
      WritingEntryStatus.LEGACY_CONFIRM,
      WritingEntryStatus.LEGACY_NOVEL_SUBMITTED,
      WritingEntryStatus.LEGACY_NOVEL_UPDATED,
      WritingEntryStatus.LEGACY_NOVEL_CORRECTION_GIVEN,
      WritingEntryStatus.LEGACY_MISSION_NEW,
      WritingEntryStatus.LEGACY_MISSION_SUBMITTED,
      WritingEntryStatus.LEGACY_MISSION_REVIEWED,
      WritingEntryStatus.LEGACY_MISSION_CONFIRMED,
    ];
  }

  /**
   * Map legacy status to new unified status
   */
  static mapLegacyToUnified(legacyStatus: string): WritingEntryStatus {
    const statusMap: Record<string, WritingEntryStatus> = {
      // Diary legacy mappings
      new: WritingEntryStatus.DRAFT,
      submit: WritingEntryStatus.SUBMITTED,
      reviewed: WritingEntryStatus.REVIEWED,
      confirm: WritingEntryStatus.REVIEWED, // CONFIRM stage removed - map to REVIEWED

      // Novel legacy mappings
      submitted: WritingEntryStatus.SUBMITTED,
      updated: WritingEntryStatus.SUBMITTED, // Simplified: map to SUBMITTED
      correction_given: WritingEntryStatus.REVIEWED,
      under_review: WritingEntryStatus.SUBMITTED, // Map to SUBMITTED instead
      confirmed: WritingEntryStatus.REVIEWED, // CONFIRM stage removed - map to REVIEWED

      // Mission legacy mappings
      NEW: WritingEntryStatus.DRAFT,
      SUBMITTED: WritingEntryStatus.SUBMITTED,
      REVIEWED: WritingEntryStatus.REVIEWED,
      CONFIRMED: WritingEntryStatus.REVIEWED, // CONFIRM stage removed - map to REVIEWED
    };

    return statusMap[legacyStatus] || WritingEntryStatus.DRAFT;
  }

  /**
   * Check if status allows new submissions
   */
  static canSubmitNewVersion(status: WritingEntryStatus): boolean {
    return [
      WritingEntryStatus.DRAFT,
      WritingEntryStatus.REVIEWED, // REVIEWED is now the final state, allows resubmission
    ].includes(status);
  }

  /**
   * Check if status indicates entry has been submitted
   */
  static isSubmitted(status: WritingEntryStatus): boolean {
    return [WritingEntryStatus.SUBMITTED, WritingEntryStatus.REVIEWED].includes(status);
  }

  /**
   * Check if status indicates entry has been reviewed
   */
  static isReviewed(status: WritingEntryStatus): boolean {
    return [
      WritingEntryStatus.REVIEWED, // REVIEWED is now the final state
    ].includes(status);
  }

  /**
   * Get next valid status transitions
   * NEW LIFECYCLE: DRAFT → SUBMITTED → REVIEWED (Final State)
   */
  static getValidTransitions(currentStatus: WritingEntryStatus): WritingEntryStatus[] {
    const transitions: Record<WritingEntryStatus, WritingEntryStatus[]> = {
      [WritingEntryStatus.DRAFT]: [WritingEntryStatus.SUBMITTED],
      [WritingEntryStatus.SUBMITTED]: [WritingEntryStatus.REVIEWED],
      [WritingEntryStatus.REVIEWED]: [WritingEntryStatus.SUBMITTED], // Can resubmit after review

      // Legacy statuses - map to their unified equivalents
      [WritingEntryStatus.LEGACY_NEW]: [WritingEntryStatus.SUBMITTED],
      [WritingEntryStatus.LEGACY_SUBMIT]: [WritingEntryStatus.REVIEWED],
      [WritingEntryStatus.LEGACY_REVIEWED]: [WritingEntryStatus.SUBMITTED], // Can resubmit
      [WritingEntryStatus.LEGACY_CONFIRM]: [WritingEntryStatus.SUBMITTED], // Legacy confirm allows resubmit
      [WritingEntryStatus.LEGACY_NOVEL_SUBMITTED]: [WritingEntryStatus.REVIEWED],
      [WritingEntryStatus.LEGACY_NOVEL_UPDATED]: [WritingEntryStatus.SUBMITTED],
      [WritingEntryStatus.LEGACY_NOVEL_CORRECTION_GIVEN]: [WritingEntryStatus.SUBMITTED],
      [WritingEntryStatus.LEGACY_MISSION_NEW]: [WritingEntryStatus.SUBMITTED],
      [WritingEntryStatus.LEGACY_MISSION_SUBMITTED]: [WritingEntryStatus.REVIEWED],
      [WritingEntryStatus.LEGACY_MISSION_REVIEWED]: [WritingEntryStatus.SUBMITTED],
      [WritingEntryStatus.LEGACY_MISSION_CONFIRMED]: [WritingEntryStatus.SUBMITTED],
    };

    return transitions[currentStatus] || [];
  }
}
