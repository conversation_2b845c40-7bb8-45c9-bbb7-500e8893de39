import { En<PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, Index } from 'typeorm';

@Entity('token_blacklist')
@Index(['tokenHash'], { unique: true })
@Index(['expiresAt'])
export class TokenBlacklist {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'token_hash', type: 'varchar', length: 64 })
  tokenHash: string; // SHA-256 hash of the token for security

  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Column({ name: 'expires_at', type: 'timestamp' })
  expiresAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'reason', type: 'varchar', length: 50, default: 'logout' })
  reason: string; // 'logout', 'password_change', 'security_breach', etc.
}
