import { Logger } from '@nestjs/common';
import { Repository, In } from 'typeorm';
import { Plan, PlanType, SubscriptionType } from '../database/entities/plan.entity';
import { PlanFeature, FeatureType } from '../database/entities/plan-feature.entity';

export async function seedPlanFeatures(logger: Logger, planFeatureRepository: Repository<PlanFeature>): Promise<void> {
  logger.log('Seeding plan features...');

  // Define the features to seed
  const featuresToSeed = [
    {
      type: FeatureType.HEC_USER_DIARY,
      name: 'Hec User Diary',
      description: 'Access to the HEC User Diary platform',
    },
    {
      type: FeatureType.HEC_PLAY,
      name: 'HEC Play',
      description: 'Access to the HEC Play platform',
    },
    {
      type: FeatureType.ENGLISH_QA_WRITING,
      name: 'English Q&A Writing Platform',
      description: 'Access to the English Q&A Writing Platform',
    },
    {
      type: FeatureType.ENGLISH_ESSAY,
      name: 'English Essay Platform',
      description: 'Access to the English Essay Platform',
    },
    {
      type: FeatureType.ENGLISH_NOVEL,
      name: 'English Novel Platform',
      description: 'Access to the English Novel Platform',
    },
  ];

  // Create each feature
  for (const featureData of featuresToSeed) {
    // Check if feature already exists
    const existingFeature = await planFeatureRepository.findOne({ where: { type: featureData.type } });

    if (existingFeature) {
      logger.log(`Feature ${featureData.name} already exists, skipping...`);
      continue;
    }

    // Create feature
    const feature = new PlanFeature();
    feature.type = featureData.type;
    feature.name = featureData.name;
    feature.description = featureData.description;
    feature.createdAt = new Date();
    feature.updatedAt = new Date();

    await planFeatureRepository.save(feature);

    logger.log(`Created feature: ${featureData.name}`);
  }

  logger.log('Plan features seeding completed.');
}

export async function seedPlans(logger: Logger, planRepository: Repository<Plan>, planFeatureRepository: Repository<PlanFeature>): Promise<void> {
  logger.log('Seeding plans...');

  // Get all features
  const hecUserDiary = await planFeatureRepository.findOne({ where: { type: FeatureType.HEC_USER_DIARY } });
  const hecPlay = await planFeatureRepository.findOne({ where: { type: FeatureType.HEC_PLAY } });
  const englishQA = await planFeatureRepository.findOne({ where: { type: FeatureType.ENGLISH_QA_WRITING } });
  const englishEssay = await planFeatureRepository.findOne({ where: { type: FeatureType.ENGLISH_ESSAY } });
  const englishNovel = await planFeatureRepository.findOne({ where: { type: FeatureType.ENGLISH_NOVEL } });

  if (!hecUserDiary || !hecPlay || !englishQA || !englishEssay || !englishNovel) {
    throw new Error('One or more required features not found. Please seed features first.');
  }

  // Define the plans to seed
  const plansToSeed = [
    // Starter Plans
    {
      name: 'Starter Monthly',
      type: PlanType.STARTER,
      subscriptionType: SubscriptionType.MONTHLY,
      description: 'Starter Plan - ₩11 /month - Includes HEC User Diary and HEC Play',
      price: 11,
      durationDays: 30,
      autoRenew: true,
      legacyFeatures: [
        {
          type: FeatureType.HEC_USER_DIARY,
          name: 'Hec User Diary',
          description: 'Access to the HEC User Diary platform',
          isActive: true,
        },
        {
          type: FeatureType.HEC_PLAY,
          name: 'HEC Play',
          description: 'Access to the HEC Play platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_QA_WRITING,
          name: 'English Q&A Writing Platform',
          description: 'Access to the English Q&A Writing Platform',
          isActive: false,
        },
        {
          type: FeatureType.ENGLISH_ESSAY,
          name: 'English Essay Platform',
          description: 'Access to the English Essay Platform',
          isActive: false,
        },
        {
          type: FeatureType.ENGLISH_NOVEL,
          name: 'English Novel Platform',
          description: 'Access to the English Novel Platform',
          isActive: false,
        },
      ],
      featureIds: [hecUserDiary.id, hecPlay.id],
      isActive: true,
    },
    {
      name: 'Starter Yearly',
      type: PlanType.STARTER,
      subscriptionType: SubscriptionType.YEARLY,
      description: 'Starter Plan - ₩110 /year - Includes HEC User Diary and HEC Play',
      price: 110,
      durationDays: 365,
      autoRenew: true,
      legacyFeatures: [
        {
          type: FeatureType.HEC_USER_DIARY,
          name: 'Hec User Diary',
          description: 'Access to the HEC User Diary platform',
          isActive: true,
        },
        {
          type: FeatureType.HEC_PLAY,
          name: 'HEC Play',
          description: 'Access to the HEC Play platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_QA_WRITING,
          name: 'English Q&A Writing Platform',
          description: 'Access to the English Q&A Writing Platform',
          isActive: false,
        },
        {
          type: FeatureType.ENGLISH_ESSAY,
          name: 'English Essay Platform',
          description: 'Access to the English Essay Platform',
          isActive: false,
        },
        {
          type: FeatureType.ENGLISH_NOVEL,
          name: 'English Novel Platform',
          description: 'Access to the English Novel Platform',
          isActive: false,
        },
      ],
      featureIds: [hecUserDiary.id, hecPlay.id],
      isActive: true,
    },

    // Standard Plans
    {
      name: 'Standard Monthly',
      type: PlanType.STANDARD,
      subscriptionType: SubscriptionType.MONTHLY,
      description: 'Standard Plan - ₩21 /month - Includes HEC User Diary, HEC Play, and English Q&A Writing Platform',
      price: 21,
      durationDays: 30,
      autoRenew: true,
      legacyFeatures: [
        {
          type: FeatureType.HEC_USER_DIARY,
          name: 'Hec User Diary',
          description: 'Access to the HEC User Diary platform',
          isActive: true,
        },
        {
          type: FeatureType.HEC_PLAY,
          name: 'HEC Play',
          description: 'Access to the HEC Play platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_QA_WRITING,
          name: 'English Q&A Writing Platform',
          description: 'Access to the English Q&A Writing Platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_ESSAY,
          name: 'English Essay Platform',
          description: 'Access to the English Essay Platform',
          isActive: false,
        },
        {
          type: FeatureType.ENGLISH_NOVEL,
          name: 'English Novel Platform',
          description: 'Access to the English Novel Platform',
          isActive: false,
        },
      ],
      featureIds: [hecUserDiary.id, hecPlay.id, englishQA.id],
      isActive: true,
    },
    {
      name: 'Standard Yearly',
      type: PlanType.STANDARD,
      subscriptionType: SubscriptionType.YEARLY,
      description: 'Standard Plan - ₩210 /year - Includes HEC User Diary, HEC Play, and English Q&A Writing Platform',
      price: 210,
      durationDays: 365,
      autoRenew: true,
      legacyFeatures: [
        {
          type: FeatureType.HEC_USER_DIARY,
          name: 'Hec User Diary',
          description: 'Access to the HEC User Diary platform',
          isActive: true,
        },
        {
          type: FeatureType.HEC_PLAY,
          name: 'HEC Play',
          description: 'Access to the HEC Play platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_QA_WRITING,
          name: 'English Q&A Writing Platform',
          description: 'Access to the English Q&A Writing Platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_ESSAY,
          name: 'English Essay Platform',
          description: 'Access to the English Essay Platform',
          isActive: false,
        },
        {
          type: FeatureType.ENGLISH_NOVEL,
          name: 'English Novel Platform',
          description: 'Access to the English Novel Platform',
          isActive: false,
        },
      ],
      featureIds: [hecUserDiary.id, hecPlay.id, englishQA.id],
      isActive: true,
    },

    // Pro Plans
    {
      name: 'Pro Monthly',
      type: PlanType.PRO,
      subscriptionType: SubscriptionType.MONTHLY,
      description: 'Pro Plan - ₩31 /month - Includes HEC User Diary, HEC Play, English Q&A Writing Platform, and English Essay Platform',
      price: 31,
      durationDays: 30,
      autoRenew: true,
      legacyFeatures: [
        {
          type: FeatureType.HEC_USER_DIARY,
          name: 'Hec User Diary',
          description: 'Access to the HEC User Diary platform',
          isActive: true,
        },
        {
          type: FeatureType.HEC_PLAY,
          name: 'HEC Play',
          description: 'Access to the HEC Play platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_QA_WRITING,
          name: 'English Q&A Writing Platform',
          description: 'Access to the English Q&A Writing Platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_ESSAY,
          name: 'English Essay Platform',
          description: 'Access to the English Essay Platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_NOVEL,
          name: 'English Novel Platform',
          description: 'Access to the English Novel Platform',
          isActive: false,
        },
      ],
      featureIds: [hecUserDiary.id, hecPlay.id, englishQA.id, englishEssay.id],
      isActive: true,
    },
    {
      name: 'Pro Yearly',
      type: PlanType.PRO,
      subscriptionType: SubscriptionType.YEARLY,
      description: 'Pro Plan - ₩310 /year - Includes HEC User Diary, HEC Play, English Q&A Writing Platform, and English Essay Platform',
      price: 310,
      durationDays: 365,
      autoRenew: true,
      legacyFeatures: [
        {
          type: FeatureType.HEC_USER_DIARY,
          name: 'Hec User Diary',
          description: 'Access to the HEC User Diary platform',
          isActive: true,
        },
        {
          type: FeatureType.HEC_PLAY,
          name: 'HEC Play',
          description: 'Access to the HEC Play platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_QA_WRITING,
          name: 'English Q&A Writing Platform',
          description: 'Access to the English Q&A Writing Platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_ESSAY,
          name: 'English Essay Platform',
          description: 'Access to the English Essay Platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_NOVEL,
          name: 'English Novel Platform',
          description: 'Access to the English Novel Platform',
          isActive: false,
        },
      ],
      featureIds: [hecUserDiary.id, hecPlay.id, englishQA.id, englishEssay.id],
      isActive: true,
    },

    // Ultimate Plans
    {
      name: 'Ultimate Monthly',
      type: PlanType.ULTIMATE,
      subscriptionType: SubscriptionType.MONTHLY,
      description: 'Ultimate Plan - ₩41 /month - Includes all features: HEC User Diary, HEC Play, English Q&A Writing Platform, English Essay Platform, and English Novel Platform',
      price: 41,
      durationDays: 30,
      autoRenew: true,
      legacyFeatures: [
        {
          type: FeatureType.HEC_USER_DIARY,
          name: 'Hec User Diary',
          description: 'Access to the HEC User Diary platform',
          isActive: true,
        },
        {
          type: FeatureType.HEC_PLAY,
          name: 'HEC Play',
          description: 'Access to the HEC Play platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_QA_WRITING,
          name: 'English Q&A Writing Platform',
          description: 'Access to the English Q&A Writing Platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_ESSAY,
          name: 'English Essay Platform',
          description: 'Access to the English Essay Platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_NOVEL,
          name: 'English Novel Platform',
          description: 'Access to the English Novel Platform',
          isActive: true,
        },
      ],
      featureIds: [hecUserDiary.id, hecPlay.id, englishQA.id, englishEssay.id, englishNovel.id],
      isActive: true,
    },
    {
      name: 'Ultimate Yearly',
      type: PlanType.ULTIMATE,
      subscriptionType: SubscriptionType.YEARLY,
      description: 'Ultimate Plan - ₩410 /year - Includes all features: HEC User Diary, HEC Play, English Q&A Writing Platform, English Essay Platform, and English Novel Platform',
      price: 410,
      durationDays: 365,
      autoRenew: true,
      legacyFeatures: [
        {
          type: FeatureType.HEC_USER_DIARY,
          name: 'Hec User Diary',
          description: 'Access to the HEC User Diary platform',
          isActive: true,
        },
        {
          type: FeatureType.HEC_PLAY,
          name: 'HEC Play',
          description: 'Access to the HEC Play platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_QA_WRITING,
          name: 'English Q&A Writing Platform',
          description: 'Access to the English Q&A Writing Platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_ESSAY,
          name: 'English Essay Platform',
          description: 'Access to the English Essay Platform',
          isActive: true,
        },
        {
          type: FeatureType.ENGLISH_NOVEL,
          name: 'English Novel Platform',
          description: 'Access to the English Novel Platform',
          isActive: true,
        },
      ],
      featureIds: [hecUserDiary.id, hecPlay.id, englishQA.id, englishEssay.id, englishNovel.id],
      isActive: true,
    },
  ];

  // Create each plan
  for (const planData of plansToSeed) {
    // Check if plan already exists
    const existingPlan = await planRepository.findOne({ where: { name: planData.name } });

    if (existingPlan) {
      logger.log(`Plan ${planData.name} already exists, skipping...`);
      continue;
    }

    // Create plan
    const plan = new Plan();
    plan.name = planData.name;
    plan.type = planData.type;
    plan.subscriptionType = planData.subscriptionType;
    plan.description = planData.description;
    plan.price = planData.price;
    plan.durationDays = planData.durationDays;
    plan.autoRenew = planData.autoRenew;
    plan.legacyFeatures = planData.legacyFeatures;
    plan.isActive = planData.isActive;
    plan.createdAt = new Date();
    plan.updatedAt = new Date();

    // Get features
    if (planData.featureIds && planData.featureIds.length > 0) {
      const features = await planFeatureRepository.find({
        where: {
          id: In(planData.featureIds),
        },
      });
      plan.planFeatures = features;
    }

    await planRepository.save(plan);

    logger.log(`Created plan: ${planData.name}`);
  }

  logger.log('Plans seeding completed.');
}
