import { ApiProperty } from '@nestjs/swagger';
import { TutorApprovalResponseDto } from '../../database/models/tutor-approval.dto';

/**
 * Base API response wrapper
 */
export class BaseApiResponse {
  @ApiProperty({ example: true, description: 'Indicates if the request was successful' })
  success: boolean;

  @ApiProperty({ description: 'Response message', example: 'Operation completed successfully' })
  message: string;

  @ApiProperty({ description: 'Timestamp of the response', example: '2023-07-25T12:34:56.789Z' })
  timestamp: string;
}

/**
 * API response for tutor approval list
 */
export class TutorApprovalListResponse extends BaseApiResponse {
  @ApiProperty({
    description: 'List of tutor approval requests',
    type: [TutorApprovalResponseDto],
  })
  data: TutorApprovalResponseDto[];
}

/**
 * API response for a single tutor approval
 */
export class TutorApprovalResponse extends BaseApiResponse {
  @ApiProperty({
    description: 'Tutor approval request details',
    type: TutorApprovalResponseDto,
  })
  data: TutorApprovalResponseDto;
}

/**
 * Error response for validation errors
 */
export class ValidationErrorResponse extends BaseApiResponse {
  @ApiProperty({ example: false, description: 'Indicates that the request failed' })
  success: boolean;

  @ApiProperty({
    example: {
      approvalId: ['approvalId must be a UUID'],
      adminNotes: ['adminNotes must be a string'],
    },
    description: 'Validation errors by field',
  })
  error: Record<string, string[]>;
}

/**
 * General error response
 */
export class GeneralErrorResponse extends BaseApiResponse {
  @ApiProperty({ example: false, description: 'Indicates that the request failed' })
  success: boolean;

  @ApiProperty({
    example: 'Tutor approval request not found',
    description: 'Error details',
  })
  error: string;
}
