import { Injectable, NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DiaryEntryLike, LikerType } from '../../database/entities/diary-entry-like.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { User } from '../../database/entities/user.entity';

@Injectable()
export class DiaryLikeService {
  constructor(
    @InjectRepository(DiaryEntryLike)
    private readonly diaryEntryLikeRepository: Repository<DiaryEntryLike>,
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Add a like to a diary entry
   * @param diaryEntryId ID of the diary entry to like
   * @param likerId ID of the user liking the entry
   * @param likerType Type of user (student or tutor)
   * @returns The created like entity
   */ async addLike(diaryEntryId: string, likerId: string, likerType: LikerType): Promise<DiaryEntryLike> {
    // Check if diary entry exists
    const entry = await this.diaryEntryRepository.findOne({ where: { id: diaryEntryId } });
    if (!entry) {
      throw new NotFoundException('Diary entry not found');
    }

    // Validate user type
    if (likerType !== LikerType.STUDENT && likerType !== LikerType.TUTOR) {
      throw new ForbiddenException('Only students and tutors can like diary entries');
    }

    // Check if user has already liked this entry
    const existingLike = await this.diaryEntryLikeRepository.findOne({
      where: { diaryEntryId, likerId },
    });

    if (existingLike) {
      throw new ConflictException('User has already liked this diary entry');
    }

    // Create new like
    const like = this.diaryEntryLikeRepository.create({
      diaryEntryId,
      likerId,
      likerType,
    });

    return this.diaryEntryLikeRepository.save(like);
  }

  /**
   * Remove a like from a diary entry
   * @param diaryEntryId ID of the diary entry
   * @param likerId ID of the user who liked the entry
   */
  async removeLike(diaryEntryId: string, likerId: string): Promise<void> {
    const like = await this.diaryEntryLikeRepository.findOne({
      where: { diaryEntryId, likerId },
    });

    if (!like) {
      throw new NotFoundException('Like not found');
    }

    await this.diaryEntryLikeRepository.remove(like);
  }

  /**
   * Get the number of likes for a diary entry
   * @param diaryEntryId ID of the diary entry
   * @returns The number of likes
   */
  async getLikeCount(diaryEntryId: string): Promise<number> {
    return this.diaryEntryLikeRepository.count({
      where: { diaryEntryId },
    });
  }

  /**
   * Check if a user has liked a diary entry
   * @param diaryEntryId ID of the diary entry
   * @param likerId ID of the user
   * @returns True if the user has liked the entry, false otherwise
   */
  async hasUserLiked(diaryEntryId: string, likerId: string): Promise<boolean> {
    const count = await this.diaryEntryLikeRepository.count({
      where: { diaryEntryId, likerId },
    });
    return count > 0;
  }
}
