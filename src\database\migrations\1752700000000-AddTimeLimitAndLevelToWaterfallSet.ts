import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTimeLimitAndLevelToWaterfallQuestion1752700000000 implements MigrationInterface {
  name = 'AddTimeLimitAndLevelToWaterfallQuestion1752700000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add time_limit_in_seconds column to waterfall_question table
    await queryRunner.query(`
      ALTER TABLE "waterfall_question"
      ADD COLUMN "time_limit_in_seconds" integer
    `);

    // Add level column to waterfall_question table
    await queryRunner.query(`
      ALTER TABLE "waterfall_question"
      ADD COLUMN "level" integer
    `);

    // Add comments to explain the new fields
    await queryRunner.query(`
      COMMENT ON COLUMN "waterfall_question"."time_limit_in_seconds" IS
      'Time limit for answering this specific question in seconds. NULL means no time limit.'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "waterfall_question"."level" IS
      'Difficulty level of the question as a number (e.g., 1=Easy, 2=Medium, 3=Hard).'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove level column from waterfall_question table
    await queryRunner.query(`
      ALTER TABLE "waterfall_question"
      DROP COLUMN "level"
    `);

    // Remove time_limit_in_seconds column from waterfall_question table
    await queryRunner.query(`
      ALTER TABLE "waterfall_question"
      DROP COLUMN "time_limit_in_seconds"
    `);
  }
}
