import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCategoryToDiaryMission1752900000000 implements MigrationInterface {
  name = 'AddCategoryToDiaryMission1752900000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add category column to diary_mission table
    await queryRunner.query(`
      ALTER TABLE "diary_mission"
      ADD COLUMN "category" varchar(255) NOT NULL DEFAULT 'General'
    `);

    // Remove the default constraint after adding the column
    await queryRunner.query(`
      ALTER TABLE "diary_mission"
      ALTER COLUMN "category" DROP DEFAULT
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove category column from diary_mission table
    await queryRunner.query(`
      ALTER TABLE "diary_mission"
      DROP COLUMN "category"
    `);
  }
}
