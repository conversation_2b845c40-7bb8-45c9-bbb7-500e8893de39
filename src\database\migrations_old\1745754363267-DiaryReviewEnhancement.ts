import { MigrationInterface, QueryRunner } from 'typeorm';

export class DiaryReviewEnhancement1745754363267 implements MigrationInterface {
  name = 'DiaryReviewEnhancement1745754363267';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "diary_correction" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "diary_entry_id" uuid NOT NULL, "tutor_id" uuid NOT NULL, "correction_text" text NOT NULL, "score" integer NOT NULL, "comments" text, CONSTRAINT "REL_4e34f738eafe1ca1709ab1d2f0" UNIQUE ("diary_entry_id"), CONSTRAINT "PK_eba4d3f4490c81d2f04ff024518" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "diary_settings_template" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying NOT NULL, "level" integer NOT NULL, "word_limit" integer NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "description" text, CONSTRAINT "PK_c5568ec2fdd7b2c2229ac684a28" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "diary_entry_settings" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "diary_entry_id" uuid NOT NULL, "settings_template_id" uuid NOT NULL, "title" character varying NOT NULL, "level" integer NOT NULL, "word_limit" integer NOT NULL, CONSTRAINT "REL_71e908d2df1da6e93365d9a531" UNIQUE ("diary_entry_id"), CONSTRAINT "PK_bfebdc41074afd95bfce0eaa65f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "diary_entry" ADD "thanks_message" text`);
    await queryRunner.query(`ALTER TYPE "public"."diary_entry_status_enum" RENAME TO "diary_entry_status_enum_old"`);
    await queryRunner.query(`CREATE TYPE "public"."diary_entry_status_enum" AS ENUM('new', 'submit', 'confirm')`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "status" TYPE "public"."diary_entry_status_enum" USING "status"::"text"::"public"."diary_entry_status_enum"`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "status" SET DEFAULT 'new'`);
    await queryRunner.query(`DROP TYPE "public"."diary_entry_status_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "diary_correction" ADD CONSTRAINT "FK_4e34f738eafe1ca1709ab1d2f0e" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE "diary_correction" ADD CONSTRAINT "FK_66ebd72441e2688c9c5700bcaeb" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(
      `ALTER TABLE "diary_entry_settings" ADD CONSTRAINT "FK_71e908d2df1da6e93365d9a531d" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "diary_entry_settings" ADD CONSTRAINT "FK_f5ab83e89e7538fecbb2f30c209" FOREIGN KEY ("settings_template_id") REFERENCES "diary_settings_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "diary_entry_settings" DROP CONSTRAINT "FK_f5ab83e89e7538fecbb2f30c209"`);
    await queryRunner.query(`ALTER TABLE "diary_entry_settings" DROP CONSTRAINT "FK_71e908d2df1da6e93365d9a531d"`);
    await queryRunner.query(`ALTER TABLE "diary_correction" DROP CONSTRAINT "FK_66ebd72441e2688c9c5700bcaeb"`);
    await queryRunner.query(`ALTER TABLE "diary_correction" DROP CONSTRAINT "FK_4e34f738eafe1ca1709ab1d2f0e"`);
    await queryRunner.query(`CREATE TYPE "public"."diary_entry_status_enum_old" AS ENUM('draft', 'submitted', 'under_review', 'reviewed')`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "status" DROP DEFAULT`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "status" TYPE "public"."diary_entry_status_enum_old" USING "status"::"text"::"public"."diary_entry_status_enum_old"`);
    await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "status" SET DEFAULT 'draft'`);
    await queryRunner.query(`DROP TYPE "public"."diary_entry_status_enum"`);
    await queryRunner.query(`ALTER TYPE "public"."diary_entry_status_enum_old" RENAME TO "diary_entry_status_enum"`);
    await queryRunner.query(`ALTER TABLE "diary_entry" DROP COLUMN "thanks_message"`);
    await queryRunner.query(`DROP TABLE "diary_entry_settings"`);
    await queryRunner.query(`DROP TABLE "diary_settings_template"`);
    await queryRunner.query(`DROP TABLE "diary_correction"`);
  }
}
