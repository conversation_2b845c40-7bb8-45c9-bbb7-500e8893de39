<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HEC Documentation</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/github-markdown-css@5.1.0/github-markdown.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/vs2015.min.css">
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script>
        // Initialize marked with default options
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof marked !== 'undefined') {
                console.log('Marked library loaded successfully');

                // Configure marked to use highlight.js
                if (typeof hljs !== 'undefined') {
                    console.log('Highlight.js loaded successfully');
                    marked.setOptions({
                        highlight: function(code, lang) {
                            if (lang && hljs.getLanguage(lang)) {
                                return hljs.highlight(code, { language: lang }).value;
                            }
                            return hljs.highlightAuto(code).value;
                        }
                    });
                }
            } else {
                console.error('Marked library not loaded');
            }
        });
    </script>
    <style>
        :root {
            --sidebar-bg: #2c3e50;
            --sidebar-text: #ecf0f1;
            --sidebar-hover: #34495e;
            --sidebar-active: #3498db;
            --main-bg: #f9f9f9;
            --content-bg: #ffffff;
            --text-color: #333333;
            --link-color: #3498db;
            --code-bg: #282c34;
            --code-text: #abb2bf;
            --border-color: #e0e0e0;
            --heading-color: #2c3e50;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            height: 100vh;
            overflow: hidden;
            background-color: var(--main-bg);
            color: var(--text-color);
        }

        .sidebar {
            width: 300px;
            background-color: var(--sidebar-bg);
            border-right: 1px solid var(--border-color);
            overflow-y: auto;
            padding: 20px;
            box-sizing: border-box;
            height: 100%;
            color: var(--sidebar-text);
        }

        .sidebar h2 {
            margin-top: 0;
            color: var(--sidebar-text);
            font-size: 1.5em;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding-bottom: 10px;
        }

        .sidebar h3 {
            margin-top: 20px;
            color: var(--sidebar-text);
            font-size: 1.2em;
        }

        .sidebar ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        .sidebar li {
            margin-bottom: 8px;
        }

        .sidebar a {
            color: var(--sidebar-text);
            text-decoration: none;
            display: block;
            padding: 8px 10px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .sidebar a:hover {
            background-color: var(--sidebar-hover);
        }

        .sidebar a.active {
            background-color: var(--sidebar-active);
            color: white;
            font-weight: 500;
        }

        .content {
            flex: 1;
            overflow-y: auto;
            padding: 0;
            box-sizing: border-box;
            height: 100%;
            background-color: var(--content-bg);
        }

        .markdown-body {
            box-sizing: border-box;
            min-width: 200px;
            max-width: 980px;
            margin: 0 auto;
            padding: 45px;
            background-color: var(--content-bg);
            color: var(--text-color);
        }

        @media (max-width: 767px) {
            .markdown-body {
                padding: 15px;
            }
        }

        .search-box {
            margin-bottom: 20px;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 10px 12px;
            border: none;
            border-radius: 4px;
            box-sizing: border-box;
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--sidebar-text);
            font-size: 14px;
        }

        .search-box input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .search-box input:focus {
            outline: none;
            background-color: rgba(255, 255, 255, 0.15);
        }

        .doc-category {
            margin-bottom: 20px;
        }

        .doc-category-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: var(--sidebar-text);
            text-transform: uppercase;
            font-size: 0.8em;
            letter-spacing: 1px;
            opacity: 0.8;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
        }

        .doc-category-title:hover {
            opacity: 1;
        }

        .doc-category-title::after {
            content: "▼";
            font-size: 0.8em;
            transition: transform 0.3s;
        }

        .doc-category-title.collapsed::after {
            transform: rotate(-90deg);
        }

        .doc-category-content {
            max-height: 1000px;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
        }

        .doc-category-content.collapsed {
            max-height: 0;
        }

        .toggle-sidebar {
            display: none;
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1000;
            background-color: var(--sidebar-active);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -300px;
                transition: left 0.3s ease;
                z-index: 999;
            }

            .sidebar.open {
                left: 0;
            }

            .toggle-sidebar {
                display: block;
            }

            .content {
                margin-left: 0;
            }
        }

        /* Style for the table of contents */
        .toc {
            background-color: rgba(0, 0, 0, 0.03);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .toc ul {
            padding-left: 20px;
        }

        /* Make sure links in the markdown content are properly styled */
        .markdown-body a {
            color: var(--link-color);
            text-decoration: none;
        }

        .markdown-body a:hover {
            text-decoration: underline;
        }

        /* Style for code blocks */
        .markdown-body pre {
            background-color: var(--code-bg);
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .markdown-body pre code {
            background-color: transparent;
            color: var(--code-text);
            padding: 0;
            font-family: 'Fira Code', 'Consolas', 'Monaco', 'Andale Mono', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
        }

        .markdown-body code {
            background-color: rgba(27, 31, 35, 0.05);
            border-radius: 3px;
            padding: 0.2em 0.4em;
            font-family: 'Fira Code', 'Consolas', 'Monaco', 'Andale Mono', 'Ubuntu Mono', monospace;
        }

        /* Style for tables */
        .markdown-body table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            overflow: auto;
        }

        .markdown-body table th,
        .markdown-body table td {
            border: 1px solid var(--border-color);
            padding: 12px 16px;
            text-align: left;
            background-color: white;
        }

        .markdown-body table th {
            background-color: #2c3e50;
            color: white;
            font-weight: 600;
            border-color: #2c3e50;
        }

        /* Remove all row styling */
        .markdown-body table tr {
            background-color: white !important;
        }

        /* Remove hover effect */
        .markdown-body table tr:hover {
            background-color: white !important;
        }

        /* Style for blockquotes */
        .markdown-body blockquote {
            border-left: 4px solid var(--sidebar-active);
            padding-left: 16px;
            color: #666;
            margin: 16px 0;
        }

        /* Style for headings */
        .markdown-body h1,
        .markdown-body h2,
        .markdown-body h3,
        .markdown-body h4,
        .markdown-body h5,
        .markdown-body h6 {
            color: var(--heading-color);
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
        }

        .markdown-body h1 {
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.3em;
        }

        .markdown-body h2 {
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.3em;
        }

        /* Style for lists */
        .markdown-body ul,
        .markdown-body ol {
            padding-left: 2em;
        }

        .markdown-body li {
            margin: 0.25em 0;
        }

        /* Style for horizontal rule */
        .markdown-body hr {
            height: 0.25em;
            padding: 0;
            margin: 24px 0;
            background-color: var(--border-color);
            border: 0;
        }
    </style>
</head>
<body>
    <button class="toggle-sidebar" id="toggleSidebar">☰</button>

    <div class="sidebar" id="sidebar">
        <h2>HEC Documentation</h2>

        <div class="search-box">
            <input type="text" id="searchDocs" placeholder="Search documentation...">
        </div>

        <div id="docsList">
            <!-- Documentation list will be populated here -->
            <div class="loading">Loading documentation list...</div>
        </div>
    </div>

    <div class="content">
        <div id="markdownContent" class="markdown-body">
            <div class="loading">Select a document from the sidebar to view its contents.</div>
        </div>
    </div>

    <script>
        // Available documentation files
        const documentationFiles = [
            {
                category: '1. System Documentation',
                files: [
                    { path: 'project-overview.md', title: 'Project Overview' },
                    { path: 'architecture/SYSTEM_ARCHITECTURE.md', title: 'System Architecture' },
                    { path: 'architecture/DATABASE_SCHEMA.md', title: 'Database Schema' },
                    { path: 'architecture/AUTHENTICATION_FLOW.md', title: 'Authentication Flow' },
                    { path: 'automatic-tutor-assignment.md', title: 'Automatic Tutor Assignment' },
                    { path: 'architecture/user-stories.md', title: 'User Stories' }
                ]
            },
            {
                category: '2. API Reference',
                files: [
                    { path: 'api-documentation/README.md', title: 'API Overview' },
                    { path: 'api-documentation/1-authentication-module.md', title: 'Authentication API' },
                    { path: 'api-documentation/2-users-module.md', title: 'Users API' },
                    { path: 'api-documentation/3-plans-module.md', title: 'Plans API' },
                    { path: 'api-documentation/4-diary-module.md', title: 'Diary API' },
                    { path: 'api-documentation/5-shop-module.md', title: 'Shop API' },
                    { path: 'api-documentation/7-promotion-management.md', title: 'Promotion Management API' },
                    { path: 'api-documentation/6-tutor-approval-module.md', title: 'Tutor API' },
                    { path: 'api-documentation/7-common-services.md', title: 'Common Services' },
                    { path: 'api-documentation/award-management-api.md', title: 'Award Management API' },
                    { path: 'api-documentation/backlog-summary.md', title: 'Feature Backlog' }
                ]
            },
            {
                category: '3. Integration Guides',
                files: [
                    { path: 'frontend-integration/README.md', title: 'Integration Overview' },
                    { path: 'frontend-integration/API_INTEGRATION_GUIDE.md', title: 'API Integration Guide' },
                    { path: 'frontend/AUTHENTICATION_INTEGRATION.md', title: 'Authentication Integration' },
                    { path: 'frontend-integration/diary-module-integration.md', title: 'Diary Module Integration' },
                    { path: 'implementation/diary-mission-management-integration.md', title: 'Diary Mission Integration' },
                    { path: 'milestone-progress-summary.md', title: '📊 Milestone 1 Progress Summary' },
                    { path: 'frontend-integration/shop-integration-flow-revised.md', title: 'Shop Integration' },
                    { path: 'frontend-integration/member-management-integration-flow.md', title: 'Member Management' },
                    { path: 'frontend-integration/diary-module-integration-flow.md', title: 'Diary Module' },
                    { path: 'frontend-integration/promotion-management-integration.md', title: 'Promotion Management Integration' },
                    { path: 'frontend/CHAT_FRONTEND_INTEGRATION.md', title: 'Chat Integration' },
                    { path: 'frontend/NOTIFICATION_SYSTEM_INTEGRATION.md', title: 'Notification Integration' },
                    { path: 'frontend-integration/student-friendship-integration.md', title: 'Student Friendship Integration' },
                    { path: 'frontend-integration/tutor-assignment-integration.md', title: 'Tutor Assignment Integration' },
                    {path:'frontend-integration/tutor-permission-integration-flow.md', title: 'Tutor Permission Integration'},
                    {path:'frontend-integration/AWARD_MANAGEMENT_SYSTEM.md', title: 'Award Management System Integration'},
                    { path: 'frontend-integration/payment-integration-flow.md', title: '💳 Payment Integration Flow' }
                ]
            },
            {
                category: '4. Development Resources',
                files: [
                    { path: 'conventions/DEVELOPMENT_CONVENTION.md', title: 'Development Conventions' },
                    { path: 'conventions/TESTING_CONVENTION.md', title: 'Testing Conventions' },
                    { path: 'conventions/FILE_UPLOAD_CONVENTION.md', title: 'File Upload Conventions' },
                    { path: 'conventions/API_VERSIONING_CONVENTION.md', title: 'API Versioning' },
                    { path: 'conventions/FEATURE_FLAG_CONVENTION.md', title: 'Feature Flags' },
                    { path: 'conventions/API_RESPONSE_CONVENTION.md', title: 'API Response Conventions' },
                    { path: 'conventions/DATABASE_CONVENTION.md', title: 'Database Conventions' },
                    { path: 'devops/DEPLOYMENT_GUIDE.md', title: 'Deployment Guide' },
                    { path: 'implementation/implementation-guide.md', title: 'Implementation Guide' },
                    { path: 'documentation-structure.md', title: 'Documentation Structure' }
                ]
            },
            {
                category: '5. API Testing Flow',
                files: [
                    { path: 'api-testing/api-testing-overview.md', title: 'API Testing Overview' },
                    { path: 'PAYMENT_API_TESTING_GUIDE.md', title: '💳 Payment API Testing Guide' },
                    { path: 'api-testing/authentication-api-testing.md', title: 'Authentication API Testing' },
                    { path: 'api-testing/users-api-testing.md', title: 'Users API Testing' },
                    { path: 'api-testing/plans-api-testing.md', title: 'Plans API Testing' },
                    { path: 'api-testing/diary-api-testing.md', title: 'Diary API Testing' },
                    { path: 'testing/diary-mission-management-testing.md', title: 'Diary Mission Testing' },
                    { path: 'api-testing/shop-api-testing.md', title: 'Shop API Testing' },
                    { path: 'testing/promotion-management-testing.md', title: 'Promotion Management Testing' },
                    { path: 'api-testing/notification-api-testing.md', title: 'Notification API Testing' },
                    { path: 'api-testing/student-friendship-api-testing.md', title: 'Student Friendship API Testing' },
                    { path: 'api-testing/tutor-assignment-api-testing.md', title: 'Tutor Assignment API Testing' },
                    { path: 'testing/award-api-testing.md', title: 'Award Management API Testing' }
                ]
            }
        ];

        // Function to get URL parameters
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            const results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Function to update URL with the current path
        function updateUrl(path) {
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.set('path', path);
            window.history.pushState({}, '', newUrl.toString());
        }

        // Function to load and render Markdown
        function loadMarkdown(path) {
            if (!path) {
                document.getElementById('markdownContent').innerHTML = '<div class="loading">Select a document from the sidebar to view its contents.</div>';
                return;
            }

            // Add /docs/ prefix if not present
            const fullPath = path.startsWith('/docs/') ? path : `/docs/${path}`;

            // Update active link in sidebar
            const links = document.querySelectorAll('.sidebar a');
            links.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('data-path') === path) {
                    link.classList.add('active');

                    // Expand the category containing this link
                    const categoryContent = link.closest('.doc-category-content');
                    const categoryTitle = categoryContent.previousElementSibling;

                    // Remove collapsed class from the category
                    categoryContent.classList.remove('collapsed');
                    categoryTitle.classList.remove('collapsed');
                }
            });

            // Fetch the Markdown file
            fetch(fullPath)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(markdown => {
                    try {
                        let html = '';

                        // Check if marked library is available
                        if (typeof marked !== 'undefined') {
                            // Configure marked to use highlight.js for code highlighting
                            if (typeof hljs !== 'undefined') {
                                marked.setOptions({
                                    highlight: function(code, lang) {
                                        if (lang && hljs.getLanguage(lang)) {
                                            return hljs.highlight(code, { language: lang }).value;
                                        }
                                        return hljs.highlightAuto(code).value;
                                    }
                                });
                            }

                            // Use marked to parse markdown
                            html = marked.parse(markdown);
                        } else {
                            // Simple fallback markdown parser
                            html = simpleParse(markdown);
                        }

                        // Render the content
                        document.getElementById('markdownContent').innerHTML = html;

                        // Apply syntax highlighting to code blocks if highlight.js is available
                        if (typeof hljs !== 'undefined') {
                            document.querySelectorAll('pre code').forEach((block) => {
                                hljs.highlightElement(block);
                            });
                        }

                        // Update page title
                        const title = path.split('/').pop().replace('.md', '') || 'HEC Documentation';
                        document.title = title;

                        // Scroll to top
                        document.querySelector('.content').scrollTop = 0;
                    } catch (error) {
                        console.error('Error parsing markdown:', error);
                        document.getElementById('markdownContent').innerHTML = `
                            <h1>Error</h1>
                            <p>Failed to parse Markdown file: ${path}</p>
                            <p>Error: ${error.message}</p>
                            <p><a href="${fullPath}" target="_blank">View raw Markdown file</a></p>
                            <h2>Raw Content</h2>
                            <pre>${markdown.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
                        `;
                    }

                    // Simple markdown parser as fallback
                    function simpleParse(text) {
                        // Replace code blocks with language
                        text = text.replace(/```([a-z]*)\n([\s\S]*?)```/g, function(match, language, code) {
                            const escapedCode = code.replace(/</g, '&lt;').replace(/>/g, '&gt;');
                            return `<pre><code class="language-${language || 'plaintext'}">${escapedCode}</code></pre>`;
                        });

                        // Replace code blocks without language
                        text = text.replace(/```([\s\S]*?)```/g, function(match, code) {
                            const escapedCode = code.replace(/</g, '&lt;').replace(/>/g, '&gt;');
                            return `<pre><code class="language-plaintext">${escapedCode}</code></pre>`;
                        });

                        // Replace headers
                        text = text.replace(/^# (.*?)$/gm, '<h1>$1</h1>');
                        text = text.replace(/^## (.*?)$/gm, '<h2>$1</h2>');
                        text = text.replace(/^### (.*?)$/gm, '<h3>$1</h3>');
                        text = text.replace(/^#### (.*?)$/gm, '<h4>$1</h4>');
                        text = text.replace(/^##### (.*?)$/gm, '<h5>$1</h5>');
                        text = text.replace(/^###### (.*?)$/gm, '<h6>$1</h6>');

                        // Replace links
                        text = text.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>');

                        // Replace inline code
                        text = text.replace(/`([^`]+)`/g, '<code>$1</code>');

                        // Replace bold text
                        text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                        text = text.replace(/__(.*?)__/g, '<strong>$1</strong>');

                        // Replace italic text
                        text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
                        text = text.replace(/_(.*?)_/g, '<em>$1</em>');

                        // Replace horizontal rule
                        text = text.replace(/^---$/gm, '<hr>');
                        text = text.replace(/^\*\*\*$/gm, '<hr>');

                        // Replace lists
                        text = text.replace(/^\* (.*?)$/gm, '<li>$1</li>');
                        text = text.replace(/^- (.*?)$/gm, '<li>$1</li>');
                        text = text.replace(/^(\d+)\. (.*?)$/gm, '<li>$2</li>');

                        // Replace blockquotes
                        text = text.replace(/^> (.*?)$/gm, '<blockquote>$1</blockquote>');

                        // Replace paragraphs (lines that don't start with HTML tags)
                        text = text.replace(/^(?!<[a-z])(.*?)$/gm, function(match) {
                            if (match.trim() === '') return '';
                            return '<p>' + match + '</p>';
                        });

                        // Replace multiple list items with ul/ol
                        text = text.replace(/<li>.*?<\/li>(\s*<li>.*?<\/li>)+/g, function(match) {
                            return '<ul>' + match + '</ul>';
                        });

                        // Clean up any double paragraph tags
                        text = text.replace(/<p><p>/g, '<p>');
                        text = text.replace(/<\/p><\/p>/g, '</p>');

                        return text;
                    }
                })
                .catch(error => {
                    console.error('Error loading Markdown file:', error);
                    document.getElementById('markdownContent').innerHTML = `
                        <h1>Error</h1>
                        <p>Failed to load Markdown file: ${path}</p>
                        <p>Error: ${error.message}</p>
                        <p><a href="${fullPath}" target="_blank">Try viewing raw Markdown file</a></p>
                    `;
                });
        }

        // Function to load markdown and update URL
        function loadMarkdownAndUpdateUrl(path) {
            updateUrl(path);
            loadMarkdown(path);
        }

        // Function to populate the documentation list
        function populateDocsList() {
            const docsListElement = document.getElementById('docsList');
            let html = '';

            documentationFiles.forEach(category => {
                html += `<div class="doc-category">`;
                html += `<div class="doc-category-title">${category.category}</div>`;
                html += `<div class="doc-category-content">`;
                html += `<ul>`;

                category.files.forEach(file => {
                    html += `<li><a href="javascript:void(0)" data-path="${file.path}" onclick="loadMarkdownAndUpdateUrl('${file.path}')">${file.title}</a></li>`;
                });

                html += `</ul>`;
                html += `</div>`;
                html += `</div>`;
            });

            docsListElement.innerHTML = html;

            // Add click event listeners to category titles
            document.querySelectorAll('.doc-category-title').forEach(title => {
                // Initialize all categories as collapsed by default
                title.classList.add('collapsed');
                title.nextElementSibling.classList.add('collapsed');

                title.addEventListener('click', function() {
                    this.classList.toggle('collapsed');
                    this.nextElementSibling.classList.toggle('collapsed');
                });
            });
        }

        // Function to filter documentation list
        function filterDocsList(searchTerm) {
            const links = document.querySelectorAll('.sidebar a');
            const searchTermLower = searchTerm.toLowerCase();
            const categories = document.querySelectorAll('.doc-category');

            // Reset all categories and items to visible
            categories.forEach(category => {
                category.style.display = 'block';
                const content = category.querySelector('.doc-category-content');
                const title = category.querySelector('.doc-category-title');

                // Expand all categories when searching
                if (searchTerm) {
                    content.classList.remove('collapsed');
                    title.classList.remove('collapsed');
                }

                const items = category.querySelectorAll('li');
                items.forEach(item => {
                    item.style.display = 'block';
                });
            });

            if (!searchTerm) {
                return; // If search is empty, show everything
            }

            // Hide items that don't match
            links.forEach(link => {
                const title = link.textContent.toLowerCase();
                const path = link.getAttribute('data-path').toLowerCase();

                if (!(title.includes(searchTermLower) || path.includes(searchTermLower))) {
                    link.parentElement.style.display = 'none';
                }
            });

            // Hide empty categories
            categories.forEach(category => {
                const visibleItems = category.querySelectorAll('li[style="display: block;"]');
                if (visibleItems.length === 0) {
                    category.style.display = 'none';
                }
            });
        }

        // Toggle sidebar on mobile
        document.getElementById('toggleSidebar').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('open');
        });

        // Search functionality
        document.getElementById('searchDocs').addEventListener('input', function() {
            filterDocsList(this.value);
        });

        // Initialize the documentation browser
        document.addEventListener('DOMContentLoaded', function() {
            // Populate the documentation list
            populateDocsList();

            // Check for path parameter in URL
            const urlPath = getUrlParameter('path');
            if (urlPath) {
                loadMarkdown(urlPath);

                // Expand the category containing the active document
                setTimeout(() => {
                    const activeLink = document.querySelector('.sidebar a.active');
                    if (activeLink) {
                        const categoryContent = activeLink.closest('.doc-category-content');
                        const categoryTitle = categoryContent.previousElementSibling;

                        // Remove collapsed class from the category
                        categoryContent.classList.remove('collapsed');
                        categoryTitle.classList.remove('collapsed');
                    }
                }, 100);
            }

            // Handle clicks on links in the markdown content
            document.getElementById('markdownContent').addEventListener('click', function(e) {
                if (e.target.tagName === 'A') {
                    const href = e.target.getAttribute('href');
                    if (href && typeof href === 'string' && href.endsWith('.md')) {
                        e.preventDefault();

                        // Handle relative paths
                        let newPath = href;
                        if (href.startsWith('/')) {
                            // Absolute path within the docs directory
                            newPath = href.substring(1); // Remove leading slash
                        } else if (!href.startsWith('http')) {
                            // Get the current path from the URL
                            const currentPath = getUrlParameter('path') || '';
                            // Relative path - resolve against current document
                            const currentDir = currentPath.split('/').slice(0, -1).join('/');
                            if (href.startsWith('./')) {
                                newPath = currentDir ? `${currentDir}/${href.substring(2)}` : href.substring(2);
                            } else if (href.startsWith('../')) {
                                const parts = currentDir.split('/');
                                parts.pop(); // Go up one directory
                                newPath = `${parts.join('/')}/${href.substring(3)}`;
                            } else {
                                newPath = currentDir ? `${currentDir}/${href}` : href;
                            }
                        }

                        loadMarkdownAndUpdateUrl(newPath);
                    }
                }
            });
        });
    </script>
</body>
</html>
