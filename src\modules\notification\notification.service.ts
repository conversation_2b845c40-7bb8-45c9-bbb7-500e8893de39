import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Not, IsNull, LessThanOrEqual, <PERSON>Than } from 'typeorm';
import { Notification, NotificationType } from '../../database/entities/notification.entity';
import { NotificationDelivery, NotificationChannel, DeliveryStatus } from '../../database/entities/notification-delivery.entity';
import { UserNotificationPreference } from '../../database/entities/user-notification-preference.entity';
import { User } from '../../database/entities/user.entity';
import { CreateNotificationDto, NotificationResponseDto, NotificationFilterDto } from '../../database/models/notification.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { EmailService } from '../email/email.service';
import { NotificationChannelService } from './notification-channel.service';
import { ConfigService } from '@nestjs/config';
import { getCurrentUTCDate, addMinutesUTC } from '../../common/utils/date-utils';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    @InjectRepository(NotificationDelivery)
    private readonly notificationDeliveryRepository: Repository<NotificationDelivery>,
    @InjectRepository(UserNotificationPreference)
    private readonly userNotificationPreferenceRepository: Repository<UserNotificationPreference>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly emailService: EmailService,
    private readonly channelService: NotificationChannelService,
    private readonly configService: ConfigService,
    private readonly deeplinkService: DeeplinkService,
  ) {}

  /**
   * Create a notification and deliver it through appropriate channels
   * @param createNotificationDto Notification data
   * @returns Created notification
   */
  async notify(createNotificationDto: CreateNotificationDto): Promise<NotificationResponseDto> {
    try {
      // Check if user exists
      const user = await this.userRepository.findOne({ where: { id: createNotificationDto.userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${createNotificationDto.userId} not found`);
      }

      // Create notification
      const notification = this.notificationRepository.create({
        userId: createNotificationDto.userId,
        type: createNotificationDto.type,
        title: createNotificationDto.title,
        message: createNotificationDto.message,
        relatedEntityId: createNotificationDto.relatedEntityId,
        relatedEntityType: createNotificationDto.relatedEntityType,
        isRead: false,
      });

      const savedNotification = await this.notificationRepository.save(notification);
      this.logger.log(`Created notification ${savedNotification.id} for user ${savedNotification.userId}`);

      // Determine delivery channels
      const channels = await this.channelService.determineChannels(createNotificationDto.userId, createNotificationDto.type, createNotificationDto.channels);

      this.logger.log(`Determined channels for notification to user ${createNotificationDto.userId}: ${channels.join(', ')}`);

      // Create delivery records
      const deliveryPromises = channels.map((channel) => this.createDeliveryRecord(savedNotification.id, channel));

      const deliveries = await Promise.all(deliveryPromises);
      this.logger.log(`Created ${deliveries.length} delivery records for notification ${savedNotification.id}`);

      // Process deliveries (send emails, push notifications, etc.)
      await this.processDeliveries(deliveries, savedNotification, createNotificationDto.htmlContent);

      // Return notification with delivery status
      return this.mapToResponseDto(savedNotification, deliveries);
    } catch (error) {
      this.logger.error(`Error creating notification: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get notifications for a user
   * @param userId User ID
   * @param filterDto Filter criteria
   * @param paginationDto Pagination options
   * @returns Paged list of notifications
   */
  async getUserNotifications(userId: string, filterDto?: NotificationFilterDto, paginationDto?: PaginationDto): Promise<PagedListDto<NotificationResponseDto>> {
    try {
      // Build where clause
      const whereClause: any = { userId };

      if (filterDto?.type) {
        whereClause.type = filterDto.type;
      }

      if (filterDto?.isRead !== undefined) {
        whereClause.isRead = filterDto.isRead;
      }

      // Get total count
      const totalCount = await this.notificationRepository.count({ where: whereClause });

      // Apply pagination
      const options: any = { where: whereClause };

      if (paginationDto) {
        const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC' } = paginationDto;
        const skip = (page - 1) * limit;

        options.skip = skip;
        options.take = limit;
        options.order = { [sortBy]: sortDirection };
      } else {
        options.order = { createdAt: 'DESC' };
      }

      // Get notifications
      const notifications = await this.notificationRepository.find(options);

      // Get delivery status for each notification
      const notificationIds = notifications.map((n) => n.id);
      const deliveries =
        notificationIds.length > 0
          ? await this.notificationDeliveryRepository.find({
              where: { notificationId: In(notificationIds) },
            })
          : [];

      // Group deliveries by notification ID
      const deliveriesByNotificationId = deliveries.reduce(
        (acc, delivery) => {
          if (!acc[delivery.notificationId]) {
            acc[delivery.notificationId] = [];
          }
          acc[delivery.notificationId].push(delivery);
          return acc;
        },
        {} as Record<string, NotificationDelivery[]>,
      );

      // Map to response DTOs
      const notificationDtos = notifications.map((notification) => this.mapToResponseDto(notification, deliveriesByNotificationId[notification.id] || []));

      return new PagedListDto(notificationDtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting notifications for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Mark a notification as read or unread
   * @param notificationId Notification ID
   * @param userId User ID
   * @param isRead Whether to mark as read or unread
   * @returns Updated notification
   */
  async markNotificationRead(notificationId: string, userId: string, isRead: boolean): Promise<NotificationResponseDto> {
    try {
      // Find notification
      const notification = await this.notificationRepository.findOne({
        where: { id: notificationId, userId },
      });

      if (!notification) {
        throw new NotFoundException(`Notification with ID ${notificationId} not found for user ${userId}`);
      }

      // Update read status
      notification.isRead = isRead;
      notification.readAt = isRead ? getCurrentUTCDate() : null;

      const updatedNotification = await this.notificationRepository.save(notification);

      // If marking as read, also update in-app delivery status
      if (isRead) {
        const inAppDelivery = await this.notificationDeliveryRepository.findOne({
          where: { notificationId, channel: NotificationChannel.IN_APP },
        });

        if (inAppDelivery) {
          inAppDelivery.status = DeliveryStatus.READ;
          await this.notificationDeliveryRepository.save(inAppDelivery);
        }
      }

      // Get delivery status
      const deliveries = await this.notificationDeliveryRepository.find({
        where: { notificationId },
      });

      return this.mapToResponseDto(updatedNotification, deliveries);
    } catch (error) {
      this.logger.error(`Error marking notification ${notificationId} as ${isRead ? 'read' : 'unread'}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   * @param userId User ID
   * @returns Number of notifications marked as read
   */
  async markAllNotificationsRead(userId: string): Promise<number> {
    try {
      // Update all unread notifications
      const result = await this.notificationRepository.update({ userId, isRead: false }, { isRead: true, readAt: getCurrentUTCDate() });

      // Update in-app delivery status
      if (result.affected > 0) {
        const unreadNotifications = await this.notificationRepository.find({
          where: { userId, isRead: true, readAt: Not(IsNull()) },
          select: ['id'],
        });

        const notificationIds = unreadNotifications.map((n) => n.id);

        await this.notificationDeliveryRepository.update(
          { notificationId: In(notificationIds), channel: NotificationChannel.IN_APP, status: Not(DeliveryStatus.READ) },
          { status: DeliveryStatus.READ },
        );
      }

      return result.affected || 0;
    } catch (error) {
      this.logger.error(`Error marking all notifications as read for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get notification preferences for a user
   * @param userId User ID
   * @returns User's notification preferences
   */
  async getUserNotificationPreferences(userId: string): Promise<UserNotificationPreference[]> {
    try {
      // Check if user exists
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Get preferences
      return this.userNotificationPreferenceRepository.find({
        where: { userId },
      });
    } catch (error) {
      this.logger.error(`Error getting notification preferences for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update notification preference for a user
   * @param userId User ID
   * @param notificationType Notification type
   * @param channel Notification channel
   * @param isEnabled Whether to enable or disable
   * @returns Updated preference
   */
  async updateNotificationPreference(userId: string, notificationType: NotificationType, channel: NotificationChannel, isEnabled: boolean): Promise<UserNotificationPreference> {
    try {
      // Check if user exists
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Find existing preference or create new one
      let preference = await this.userNotificationPreferenceRepository.findOne({
        where: { userId, notificationType, channel },
      });

      if (!preference) {
        preference = this.userNotificationPreferenceRepository.create({
          userId,
          notificationType,
          channel,
          isEnabled,
        });
      } else {
        preference.isEnabled = isEnabled;
      }

      return this.userNotificationPreferenceRepository.save(preference);
    } catch (error) {
      this.logger.error(`Error updating notification preference for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process a delivery and handle success/failure
   * @param delivery The delivery to process
   * @param deleteOnSuccess Whether to delete the delivery record on success
   * @returns True if the delivery was processed successfully, false otherwise
   */
  async processDeliveryWithErrorHandling(delivery: NotificationDelivery, deleteOnSuccess: boolean = true): Promise<boolean> {
    try {
      // Update status to pending before processing
      delivery.status = DeliveryStatus.PENDING;
      delivery.lastRetryAt = getCurrentUTCDate();
      await this.notificationDeliveryRepository.save(delivery);

      // Process the delivery
      await this.processDelivery(delivery, delivery.notification);

      // If successful and deleteOnSuccess is true, delete the delivery record
      if (deleteOnSuccess) {
        this.logger.log(`Successfully processed delivery ${delivery.id}, deleting record`);
        await this.notificationDeliveryRepository.delete(delivery.id);
      } else {
        // Otherwise, mark as sent
        delivery.status = DeliveryStatus.SENT;
        delivery.sentAt = getCurrentUTCDate();
        delivery.errorMessage = null;
        delivery.errorCode = null;
        delivery.errorDetails = null;
        delivery.nextRetryAt = null;
        await this.notificationDeliveryRepository.save(delivery);
      }

      this.logger.log(`Successfully processed delivery ${delivery.id} for notification ${delivery.notificationId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error processing delivery ${delivery.id}: ${error.message}`, error.stack);

      // Handle the error by updating the delivery record
      await this.handleDeliveryError(delivery, error);
      return false;
    }
  }

  /**
   * Handle a delivery error by updating the delivery record
   * @param delivery The delivery that failed
   * @param error The error that occurred
   */
  private async handleDeliveryError(delivery: NotificationDelivery, error: Error | { message: string; code?: string; details?: string }): Promise<void> {
    try {
      // Update retry count and error information
      delivery.retryCount++;
      delivery.errorMessage = error.message;
      delivery.status = DeliveryStatus.FAILED;

      if (delivery.retryCount >= (delivery.maxRetries || 3)) {
        // Mark as permanently failed if max retries reached
        delivery.status = DeliveryStatus.FAILED_PERMANENT;
        delivery.nextRetryAt = null;
        this.logger.warn(`Delivery ${delivery.id} has reached maximum retries and is marked as permanently failed`);
      } else {
        // Exponential backoff: 5, 25, 125 minutes
        const backoffMinutes = Math.pow(5, delivery.retryCount);
        // Cap at 24 hours
        const cappedMinutes = Math.min(backoffMinutes, 24 * 60);
        delivery.nextRetryAt = addMinutesUTC(getCurrentUTCDate(), cappedMinutes);
        delivery.status = DeliveryStatus.RETRY_SCHEDULED;

        this.logger.log(`Delivery ${delivery.id} scheduled for retry at ${delivery.nextRetryAt.toISOString()}`);
      }

      await this.notificationDeliveryRepository.save(delivery);
    } catch (saveError) {
      this.logger.error(`Error updating delivery ${delivery.id} after failure: ${saveError.message}`, saveError.stack);
      // We don't rethrow here to avoid masking the original error
    }
  }

  /**
   * Retry failed notification deliveries
   * @returns Number of deliveries retried
   */
  async retryFailedDeliveries(): Promise<number> {
    try {
      const now = getCurrentUTCDate();

      // Find failed deliveries due for retry
      const failedDeliveries = await this.notificationDeliveryRepository.find({
        where: [
          {
            status: DeliveryStatus.FAILED,
            nextRetryAt: LessThanOrEqual(now),
            retryCount: LessThan(3), // Max 3 retries
          },
          {
            status: DeliveryStatus.RETRY_SCHEDULED,
            nextRetryAt: LessThanOrEqual(now),
          },
        ],
        relations: ['notification'],
      });

      this.logger.log(`Found ${failedDeliveries.length} failed deliveries to retry`);

      // Process each delivery
      let successCount = 0;
      for (const delivery of failedDeliveries) {
        const success = await this.processDeliveryWithErrorHandling(delivery, true);
        if (success) {
          successCount++;
        }
      }

      return successCount;
    } catch (error) {
      this.logger.error(`Error retrying failed deliveries: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete old failed deliveries
   * @param olderThan Date threshold for deletion
   * @returns Result of the delete operation
   */
  async deleteOldFailedDeliveries(olderThan: Date): Promise<{ affected?: number }> {
    try {
      return this.notificationDeliveryRepository.delete({
        status: DeliveryStatus.FAILED_PERMANENT,
        updatedAt: LessThan(olderThan),
      });
    } catch (error) {
      this.logger.error(`Error deleting old failed deliveries: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Count deliveries by status
   * @param status The status to count
   * @returns Number of deliveries with the given status
   */
  async countDeliveriesByStatus(status: DeliveryStatus): Promise<number> {
    try {
      return this.notificationDeliveryRepository.count({
        where: { status },
      });
    } catch (error) {
      this.logger.error(`Error counting deliveries with status ${status}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Count all failed deliveries (including permanently failed and scheduled for retry)
   * @returns Total number of failed deliveries
   */
  async countAllFailedDeliveries(): Promise<number> {
    try {
      return this.notificationDeliveryRepository.count({
        where: [{ status: DeliveryStatus.FAILED }, { status: DeliveryStatus.FAILED_PERMANENT }, { status: DeliveryStatus.RETRY_SCHEDULED }],
      });
    } catch (error) {
      this.logger.error(`Error counting all failed deliveries: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Retry a specific failed delivery
   * @param deliveryId ID of the delivery to retry
   * @returns Updated delivery
   */
  async retryFailedDelivery(deliveryId: string): Promise<NotificationDelivery> {
    try {
      // Find the delivery
      const delivery = await this.notificationDeliveryRepository.findOne({
        where: { id: deliveryId },
        relations: ['notification'],
      });

      if (!delivery) {
        throw new NotFoundException(`Delivery with ID ${deliveryId} not found`);
      }

      if (delivery.status !== DeliveryStatus.FAILED && delivery.status !== DeliveryStatus.FAILED_PERMANENT && delivery.status !== DeliveryStatus.RETRY_SCHEDULED) {
        throw new BadRequestException(`Delivery with ID ${deliveryId} is not in a failed state`);
      }

      // Reset retry count and status
      delivery.retryCount = 0;
      delivery.status = DeliveryStatus.PENDING;
      delivery.nextRetryAt = null;
      delivery.lastRetryAt = getCurrentUTCDate();
      delivery.errorMessage = null;
      delivery.errorCode = null;
      delivery.errorDetails = null;

      await this.notificationDeliveryRepository.save(delivery);

      // Create a copy of the delivery for the response
      const deliveryCopy = { ...delivery };

      // Process the delivery with error handling
      const success = await this.processDeliveryWithErrorHandling(delivery, true);

      if (success) {
        // Return the delivery info with sent status for the response
        deliveryCopy.status = DeliveryStatus.SENT;
        deliveryCopy.sentAt = getCurrentUTCDate();
        return deliveryCopy as NotificationDelivery;
      } else {
        // If processing failed, throw an error
        throw new Error(`Failed to process delivery ${deliveryId}`);
      }
    } catch (error) {
      this.logger.error(`Error retrying failed delivery ${deliveryId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create a delivery record for a notification
   * @param notificationId Notification ID
   * @param channel Delivery channel
   * @returns Created delivery record
   */
  private async createDeliveryRecord(notificationId: string, channel: NotificationChannel): Promise<NotificationDelivery> {
    const delivery = this.notificationDeliveryRepository.create({
      notificationId,
      channel,
      status: DeliveryStatus.PENDING,
      retryCount: 0,
    });

    return this.notificationDeliveryRepository.save(delivery);
  }

  /**
   * Process deliveries for a notification
   * @param deliveries Delivery records
   * @param notification Notification
   * @param htmlContent Optional HTML content for email notifications
   */
  private async processDeliveries(deliveries: NotificationDelivery[], notification: Notification, htmlContent?: string): Promise<void> {
    this.logger.log(`Processing ${deliveries.length} deliveries for notification ${notification.id} to user ${notification.userId}`);

    // Process each delivery in parallel
    const results = await Promise.allSettled(deliveries.map((delivery) => this.processDelivery(delivery, notification, htmlContent)));

    // Log results
    const successful = results.filter((r) => r.status === 'fulfilled').length;
    const failed = results.filter((r) => r.status === 'rejected').length;

    this.logger.log(`Processed ${deliveries.length} deliveries for notification ${notification.id}: ${successful} successful, ${failed} failed`);

    // Log any failures
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        this.logger.error(`Delivery ${deliveries[index].id} (${deliveries[index].channel}) failed: ${result.reason}`);
      }
    });
  }

  /**
   * Process a single delivery
   * @param delivery Delivery record
   * @param notification Notification
   * @param htmlContent Optional HTML content for email notifications
   */
  async processDelivery(delivery: NotificationDelivery, notification: Notification, htmlContent?: string): Promise<void> {
    try {
      switch (delivery.channel) {
        case NotificationChannel.IN_APP:
          // In-app notifications are automatically marked as sent
          delivery.status = DeliveryStatus.SENT;
          delivery.sentAt = getCurrentUTCDate();
          break;

        case NotificationChannel.EMAIL:
          await this.sendEmailNotification(notification, htmlContent);
          delivery.status = DeliveryStatus.SENT;
          delivery.sentAt = getCurrentUTCDate();
          break;

        case NotificationChannel.PUSH:
          await this.sendPushNotification(notification, htmlContent);
          delivery.status = DeliveryStatus.SENT;
          delivery.sentAt = getCurrentUTCDate();
          break;

        case NotificationChannel.MOBILE:
          await this.sendMobileNotification(notification, htmlContent);
          delivery.status = DeliveryStatus.SENT;
          delivery.sentAt = getCurrentUTCDate();
          break;

        case NotificationChannel.SMS:
          await this.sendSmsNotification(notification);
          delivery.status = DeliveryStatus.SENT;
          delivery.sentAt = getCurrentUTCDate();
          break;

        case NotificationChannel.REALTIME_MESSAGE:
          await this.sendRealtimeMessage(notification);
          delivery.status = DeliveryStatus.SENT;
          delivery.sentAt = getCurrentUTCDate();
          break;

        default:
          throw new Error(`Unsupported notification channel: ${delivery.channel}`);
      }

      await this.notificationDeliveryRepository.save(delivery);
    } catch (error) {
      this.logger.error(`Error processing delivery ${delivery.id}: ${error.message}`, error.stack);

      // Update delivery status
      delivery.status = DeliveryStatus.FAILED;
      delivery.errorMessage = error.message;
      delivery.retryCount++;

      // Set next retry time (5 minutes)
      delivery.nextRetryAt = addMinutesUTC(getCurrentUTCDate(), 5);

      await this.notificationDeliveryRepository.save(delivery);

      // Re-throw error
      throw error;
    }
  }

  /**
   * Send an email notification
   * @param notification Notification
   * @param htmlContent Optional HTML content for the email
   */
  private async sendEmailNotification(notification: Notification, htmlContent?: string): Promise<void> {
    // Get user email
    const user = await this.userRepository.findOne({ where: { id: notification.userId } });
    if (!user || !user.email) {
      throw new Error(`User ${notification.userId} has no email address`);
    }

    // Generate web URL for the notification if not provided in htmlContent
    const webUrl = this.getWebUrlForNotification(notification);

    // If no custom HTML content is provided, create one with the web URL
    let finalHtmlContent = htmlContent;
    if (!finalHtmlContent && notification.relatedEntityId) {
      // Create a basic HTML content with a link to the relevant page
      finalHtmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: #333;">${notification.title}</h2>
          </div>
          <div style="margin-bottom: 20px;">
            <p>Hello ${user.name || 'there'},</p>
            <p>${notification.message}</p>
            <p>You can view more details <a href="${webUrl}">here</a>.</p>
          </div>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
            <p>This is an automated message from the HEC system.</p>
            <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
          </div>
        </div>
      `;
    }

    // Send email based on notification type
    switch (notification.type) {
      case NotificationType.DIARY_SUBMISSION:
        await this.emailService.sendDiarySubmissionEmail(user.email, user.name, notification.title, notification.message, finalHtmlContent);
        break;

      case NotificationType.DIARY_REVIEW:
        await this.emailService.sendDiaryReviewEmail(user.email, user.name, notification.title, notification.message, finalHtmlContent);
        break;

      case NotificationType.DIARY_FEEDBACK:
        await this.emailService.sendDiaryReviewEmail(
          // Reuse the review email template for feedback
          user.email,
          user.name,
          notification.title,
          notification.message,
          finalHtmlContent,
        );
        break;

      case NotificationType.TUTOR_ASSIGNMENT:
        await this.emailService.sendTutorAssignmentEmail(user.email, user.name, notification.title, notification.message, finalHtmlContent);
        break;

      case NotificationType.TUTOR_VERIFICATION:
        // Use generic notification email for tutor verification
        await this.emailService.sendGenericNotificationEmail(user.email, user.name, notification.title, notification.message, finalHtmlContent);
        break;

      case NotificationType.AWARD_WINNER:
        await this.emailService.sendAwardWinnerEmail(user.email, user.name, notification.title, notification.message, finalHtmlContent);
        break;

      default:
        // For other types, send a generic email
        await this.emailService.sendGenericNotificationEmail(user.email, user.name, notification.title, notification.message, finalHtmlContent);
    }

    this.logger.log(`Email notification sent to ${user.email} (${notification.userId}): ${notification.title}`);
  }

  /**
   * Send a push notification
   * @param notification Notification
   * @param htmlContent Optional HTML content that might be used for rich push notifications
   */
  private async sendPushNotification(notification: Notification, htmlContent?: string): Promise<void> {
    // Get user
    const user = await this.userRepository.findOne({ where: { id: notification.userId } });
    if (!user) {
      throw new Error(`User ${notification.userId} not found`);
    }

    // In a real implementation, we would:
    // 1. Get the user's device tokens from a database
    // 2. Format the notification payload based on the notification type
    // 3. Send the notification to each device using FCM or similar
    // 4. Handle success/failure for each device

    // Mock implementation for now
    try {
      // Simulate getting device tokens
      this.logger.log(`[MOCK] Getting device tokens for user ${user.name} (${notification.userId})`);

      // Prepare notification payload
      const payload = {
        title: notification.title,
        body: notification.message,
        data: {
          notificationId: notification.id,
          type: notification.type,
          relatedEntityId: notification.relatedEntityId,
          relatedEntityType: notification.relatedEntityType,
          // Include a deep link that can be used to navigate to the relevant screen in the app
          deepLink: this.getDeepLinkForNotification(notification),
        },
        // Add rich content flag if HTML content is provided
        hasRichContent: !!htmlContent,
      };

      this.logger.log(`[MOCK] Push notification payload prepared: ${JSON.stringify(payload)}`);

      // Simulate sending to FCM
      this.logger.log(`[MOCK] Push notification sent to user ${user.name} (${notification.userId}): ${notification.title}`);

      // In a real implementation, we would return success/failure information
      return;
    } catch (error) {
      this.logger.error(`Error sending push notification to user ${user.id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Generate a deep link URL for a notification based on its type and related entity
   * @param notification The notification to generate a deep link for
   * @returns A deep link URL that can be used to navigate to the relevant screen in the app
   */
  private getDeepLinkForNotification(notification: Notification): string {
    return this.deeplinkService.getDeepLink(DeeplinkType.NOTIFICATION, {
      id: notification.relatedEntityId,
      notificationType: notification.type,
      relatedEntityType: notification.relatedEntityType,
    });
  }

  /**
   * Generate a web URL for a notification based on its type and related entity
   * @param notification The notification to generate a web URL for
   * @returns A web URL that can be used in email notifications
   */
  private getWebUrlForNotification(notification: Notification): string {
    return this.deeplinkService.getWebLink(DeeplinkType.NOTIFICATION, {
      id: notification.relatedEntityId,
      notificationType: notification.type,
      relatedEntityType: notification.relatedEntityType,
    });
  }

  /**
   * Send a mobile notification
   * @param notification Notification
   * @param htmlContent Optional HTML content that might be used for rich mobile notifications
   */
  private async sendMobileNotification(notification: Notification, htmlContent?: string): Promise<void> {
    // Get user
    const user = await this.userRepository.findOne({ where: { id: notification.userId } });
    if (!user) {
      throw new Error(`User ${notification.userId} not found`);
    }

    // In a real implementation, this might use a different service than push notifications
    // or have different formatting requirements
    try {
      // Prepare notification payload - similar to push but might have mobile-specific formatting
      const payload = {
        title: notification.title,
        body: notification.message,
        data: {
          notificationId: notification.id,
          type: notification.type,
          relatedEntityId: notification.relatedEntityId,
          relatedEntityType: notification.relatedEntityType,
          deepLink: this.getDeepLinkForNotification(notification),
        },
        // Mobile notifications might support rich content
        richContent: htmlContent ? true : false,
      };

      this.logger.log(`[MOCK] Mobile notification payload prepared: ${JSON.stringify(payload)}`);
      this.logger.log(`[MOCK] Mobile notification sent to user ${user.name} (${notification.userId}): ${notification.title}`);

      return;
    } catch (error) {
      this.logger.error(`Error sending mobile notification to user ${user.id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send an SMS notification
   * @param notification Notification
   */
  private async sendSmsNotification(notification: Notification): Promise<void> {
    // Get user
    const user = await this.userRepository.findOne({ where: { id: notification.userId } });
    if (!user || !user.phone) {
      throw new Error(`User ${notification.userId} has no phone number`);
    }

    // TODO: Implement SMS notification logic
    // This would typically involve a service like Twilio, Nexmo, or AWS SNS

    this.logger.log(`[MOCK] SMS notification sent to ${user.phone}: ${notification.title}`);
  }

  /**
   * Send a real-time message
   * @param notification Notification
   */
  private async sendRealtimeMessage(notification: Notification): Promise<void> {
    // Get user
    const user = await this.userRepository.findOne({ where: { id: notification.userId } });
    if (!user) {
      throw new Error(`User ${notification.userId} not found`);
    }

    // TODO: Implement real-time messaging logic
    // This would typically involve a WebSocket connection or similar

    this.logger.log(`[MOCK] Real-time message sent to user ${user.name} (${notification.userId}): ${notification.title}`);

    // In a real implementation, you would:
    // 1. Check if the user is online (has an active WebSocket connection)
    // 2. Send the message through the WebSocket connection
    // 3. Handle offline users (maybe store the message for later delivery)
  }

  /**
   * Map a notification entity to a response DTO
   * @param notification Notification entity
   * @param deliveries Delivery records
   * @returns Notification response DTO
   */
  private mapToResponseDto(notification: Notification, deliveries: NotificationDelivery[]): NotificationResponseDto {
    return {
      id: notification.id,
      userId: notification.userId,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      relatedEntityId: notification.relatedEntityId,
      relatedEntityType: notification.relatedEntityType,
      isRead: notification.isRead,
      readAt: notification.readAt,
      createdAt: notification.createdAt,
      deliveryStatus: deliveries.map((delivery) => ({
        channel: delivery.channel,
        status: delivery.status,
        sentAt: delivery.sentAt,
      })),
    };
  }
}
