import { Controller, Get, Post, Put, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { UserType } from '../../database/entities/user.entity';
import { MappingStatus } from '../../database/entities/student-tutor-mapping.entity';
import { TutorMatchingService } from './tutor-matching.service';
import { AssignTutorDto, UpdateTutorAssignmentDto, TutorAssignmentResponseDto, TutorWorkloadDto, AssignmentFilterDto, AutoAssignTutorsDto, ChangeStudentTutorDto, ChangeStudentTutorResponseDto } from '../../database/models/tutor-matching.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';

@ApiTags('admin-tutor-matching')
@Controller('admin/tutor-matching')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
@Roles(UserType.ADMIN)
export class AdminTutorMatchingController {
  constructor(private readonly tutorMatchingService: TutorMatchingService) {}

  @Post('assign')
  @ApiOperation({ summary: 'Assign a tutor to a student (admin only)' })
  @ApiOkResponseWithType(TutorAssignmentResponseDto, 'Tutor assigned successfully')
  @ApiErrorResponse(404, 'Student, tutor, or module not found')
  @ApiErrorResponse(400, 'Invalid student or tutor')
  @ApiErrorResponse(409, 'Student already has a tutor for this module')
  @ApiErrorResponse(500, 'Internal server error')
  async assignTutor(@Body() assignTutorDto: AssignTutorDto): Promise<ApiResponse<TutorAssignmentResponseDto>> {
    const assignment = await this.tutorMatchingService.assignTutor(assignTutorDto);
    return ApiResponse.success(assignment, 'Tutor assigned successfully');
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a tutor assignment (admin only)' })
  @ApiOkResponseWithType(TutorAssignmentResponseDto, 'Assignment updated successfully')
  @ApiErrorResponse(404, 'Assignment or tutor not found')
  @ApiErrorResponse(400, 'Invalid tutor')
  @ApiErrorResponse(500, 'Internal server error')
  async updateAssignment(@Param('id') id: string, @Body() updateDto: UpdateTutorAssignmentDto): Promise<ApiResponse<TutorAssignmentResponseDto>> {
    const assignment = await this.tutorMatchingService.updateAssignment(id, updateDto);
    return ApiResponse.success(assignment, 'Assignment updated successfully');
  }

  @Get()
  @ApiOperation({ summary: 'Get all tutor assignments (admin only)' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortDirection', required: false, enum: ['ASC', 'DESC'] })
  @ApiQuery({ name: 'studentName', required: false, type: String })
  @ApiQuery({ name: 'tutorName', required: false, type: String })
  @ApiQuery({ name: 'planFeatureId', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, enum: ['active', 'inactive'] })
  @ApiOkResponseWithType(PagedListDto, 'Assignments retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getAllAssignments(
    @Query('studentName') studentName?: string,
    @Query('tutorName') tutorName?: string,
    @Query('planFeatureId') planFeatureId?: string,
    @Query('status') status?: MappingStatus,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
  ): Promise<ApiResponse<PagedListDto<TutorAssignmentResponseDto>>> {
    // Create filter DTO
    const filterDto: AssignmentFilterDto = {
      studentName,
      tutorName,
      planFeatureId,
      status,
    };

    // Create pagination DTO
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC',
    };

    const assignments = await this.tutorMatchingService.getAllAssignments(filterDto, paginationDto);
    return ApiResponse.success(assignments, 'Assignments retrieved successfully');
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific assignment (admin only)' })
  @ApiOkResponseWithType(TutorAssignmentResponseDto, 'Assignment retrieved successfully')
  @ApiErrorResponse(404, 'Assignment not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getAssignment(@Param('id') id: string): Promise<ApiResponse<TutorAssignmentResponseDto>> {
    const assignment = await this.tutorMatchingService.getAssignmentById(id);
    return ApiResponse.success(assignment, 'Assignment retrieved successfully');
  }

  @Get('workload/tutors')
  @ApiOperation({ summary: 'Get tutor workload statistics (admin only)' })
  @ApiOkResponseWithType(Array, 'Tutor workloads retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getTutorWorkloads(): Promise<ApiResponse<TutorWorkloadDto[]>> {
    const workloads = await this.tutorMatchingService.getTutorWorkloads();
    return ApiResponse.success(workloads, 'Tutor workloads retrieved successfully');
  }

  @Post('auto-assign')
  @ApiOperation({ summary: 'Auto-assign tutors to students (admin only)' })
  @ApiOkResponseWithType(Array, 'Tutors auto-assigned successfully')
  @ApiErrorResponse(404, 'Plan feature (module) not found')
  @ApiErrorResponse(400, 'No tutors or students available for assignment')
  @ApiErrorResponse(500, 'Internal server error')
  async autoAssignTutors(@Body() autoAssignDto: AutoAssignTutorsDto): Promise<ApiResponse<TutorAssignmentResponseDto[]>> {
    const assignments = await this.tutorMatchingService.autoAssignTutors(autoAssignDto);
    return ApiResponse.success(assignments, 'Tutors auto-assigned successfully');
  }

  @Post('change-student-tutor')
  @ApiOperation({
    summary: 'Change a student\'s tutor for all modules (admin only)',
    description: 'Changes the tutor for a student across all modules in their active plan. Implements ONE TUTOR PER STUDENT policy.'
  })
  @ApiOkResponseWithType(ChangeStudentTutorResponseDto, 'Student tutor changed successfully')
  @ApiErrorResponse(404, 'Student, tutor, or active plan not found')
  @ApiErrorResponse(400, 'Invalid student or tutor, or student already assigned to this tutor')
  @ApiErrorResponse(500, 'Internal server error')
  async changeStudentTutor(@Body() changeDto: ChangeStudentTutorDto): Promise<ApiResponse<ChangeStudentTutorResponseDto>> {
    const result = await this.tutorMatchingService.changeStudentTutor(changeDto);
    return ApiResponse.success(result, 'Student tutor changed successfully');
  }
}
